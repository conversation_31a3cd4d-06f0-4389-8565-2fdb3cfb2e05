<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限控制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.logged-in {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.logged-out {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .hidden {
            display: none !important;
        }
        .visible {
            display: block !important;
        }
        #tool-contents {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        #tools-login-required {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SuperSpider 权限控制测试</h1>
        
        <div class="test-section">
            <h2>当前状态</h2>
            <div id="login-status" class="status logged-out">未登录</div>
            <button class="btn" onclick="simulateLogin()">模拟登录</button>
            <button class="btn" onclick="simulateLogout()">模拟退出</button>
        </div>

        <div class="test-section">
            <h2>工具区域显示测试</h2>
            
            <!-- 未登录提示 -->
            <div id="tools-login-required" style="display: block;">
                <h3>🔒 需要登录</h3>
                <p>内容获取工具仅对登录用户开放，请先登录或注册账号。</p>
            </div>

            <!-- 工具内容区域 -->
            <div id="tool-contents" style="display: none;">
                <h3>🛠️ 工具内容</h3>
                <div class="tool-content-wrapper" id="douyin-tool-content">
                    <h4>抖音视频解析</h4>
                    <p>解析抖音分享链接，获取无水印视频下载地址。</p>
                    <input type="url" placeholder="https://v.douyin.com/..." style="width: 100%; padding: 8px; margin: 5px 0;">
                    <button class="btn">解析视频</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>测试结果</h2>
            <div id="test-results">
                <p>等待测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟全局登录状态变量
        window.isLoggedIn = false;

        // 模拟登录
        function simulateLogin() {
            window.isLoggedIn = true;
            updateUI();
            updateTestResults();
        }

        // 模拟退出
        function simulateLogout() {
            window.isLoggedIn = false;
            updateUI();
            updateTestResults();
        }

        // 更新UI显示
        function updateUI() {
            const loginStatus = document.getElementById('login-status');
            const toolsLoginRequired = document.getElementById('tools-login-required');
            const toolContents = document.getElementById('tool-contents');

            if (window.isLoggedIn) {
                // 已登录状态
                loginStatus.textContent = '已登录';
                loginStatus.className = 'status logged-in';
                
                // 隐藏登录提示，显示工具内容
                toolsLoginRequired.style.display = 'none';
                toolContents.style.display = 'block';
            } else {
                // 未登录状态
                loginStatus.textContent = '未登录';
                loginStatus.className = 'status logged-out';
                
                // 显示登录提示，隐藏工具内容
                toolsLoginRequired.style.display = 'block';
                toolContents.style.display = 'none';
            }
        }

        // 更新测试结果
        function updateTestResults() {
            const testResults = document.getElementById('test-results');
            const toolsLoginRequired = document.getElementById('tools-login-required');
            const toolContents = document.getElementById('tool-contents');
            
            const loginRequiredVisible = toolsLoginRequired.style.display !== 'none';
            const toolContentsVisible = toolContents.style.display !== 'none';
            
            let result = `<h4>测试结果 (${new Date().toLocaleTimeString()})</h4>`;
            result += `<p><strong>登录状态:</strong> ${window.isLoggedIn ? '已登录' : '未登录'}</p>`;
            result += `<p><strong>登录提示显示:</strong> ${loginRequiredVisible ? '✅ 显示' : '❌ 隐藏'}</p>`;
            result += `<p><strong>工具内容显示:</strong> ${toolContentsVisible ? '✅ 显示' : '❌ 隐藏'}</p>`;
            
            // 检查是否符合预期
            const expectedLoginRequired = !window.isLoggedIn;
            const expectedToolContents = window.isLoggedIn;
            
            const loginRequiredCorrect = loginRequiredVisible === expectedLoginRequired;
            const toolContentsCorrect = toolContentsVisible === expectedToolContents;
            
            result += `<h4>验证结果:</h4>`;
            result += `<p>登录提示显示正确: ${loginRequiredCorrect ? '✅ 通过' : '❌ 失败'}</p>`;
            result += `<p>工具内容显示正确: ${toolContentsCorrect ? '✅ 通过' : '❌ 失败'}</p>`;
            
            if (loginRequiredCorrect && toolContentsCorrect) {
                result += `<p style="color: green; font-weight: bold;">🎉 所有测试通过！权限控制正常工作。</p>`;
            } else {
                result += `<p style="color: red; font-weight: bold;">⚠️ 测试失败！权限控制存在问题。</p>`;
            }
            
            testResults.innerHTML = result;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateUI();
            updateTestResults();
        });
    </script>
</body>
</html>
