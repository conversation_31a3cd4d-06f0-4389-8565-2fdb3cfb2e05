{"version": 3, "file": "VideoPreview-DYkibY1b.js", "sources": ["../../../../js/icons/src/Maximise.svelte", "../../../../js/video/shared/VideoTimeline.svelte", "../../../../js/video/shared/VideoControls.svelte", "../../../../js/video/shared/Player.svelte", "../../../../js/video/shared/VideoPreview.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { onMount, onDestroy } from \"svelte\";\n\n\texport let videoElement: HTMLVideoElement;\n\texport let trimmedDuration: number | null;\n\texport let dragStart: number;\n\texport let dragEnd: number;\n\texport let loadingTimeline: boolean;\n\n\tlet thumbnails: string[] = [];\n\tlet numberOfThumbnails = 10;\n\tlet intervalId: ReturnType<typeof setInterval> | undefined;\n\tlet videoDuration: number;\n\n\tlet leftHandlePosition = 0;\n\tlet rightHandlePosition = 100;\n\n\tlet dragging: string | null = null;\n\n\tconst startDragging = (side: string | null): void => {\n\t\tdragging = side;\n\t};\n\n\t$: loadingTimeline = thumbnails.length !== numberOfThumbnails;\n\n\tconst stopDragging = (): void => {\n\t\tdragging = null;\n\t};\n\n\tconst drag = (event: { clientX: number }, distance?: number): void => {\n\t\tif (dragging) {\n\t\t\tconst timeline = document.getElementById(\"timeline\");\n\n\t\t\tif (!timeline) return;\n\n\t\t\tconst rect = timeline.getBoundingClientRect();\n\t\t\tlet newPercentage = ((event.clientX - rect.left) / rect.width) * 100;\n\n\t\t\tif (distance) {\n\t\t\t\t// Move handle based on arrow key press\n\t\t\t\tnewPercentage =\n\t\t\t\t\tdragging === \"left\"\n\t\t\t\t\t\t? leftHandlePosition + distance\n\t\t\t\t\t\t: rightHandlePosition + distance;\n\t\t\t} else {\n\t\t\t\t// Move handle based on mouse drag\n\t\t\t\tnewPercentage = ((event.clientX - rect.left) / rect.width) * 100;\n\t\t\t}\n\n\t\t\tnewPercentage = Math.max(0, Math.min(newPercentage, 100)); // Keep within 0 and 100\n\n\t\t\tif (dragging === \"left\") {\n\t\t\t\tleftHandlePosition = Math.min(newPercentage, rightHandlePosition);\n\n\t\t\t\t// Calculate the new time and set it for the videoElement\n\t\t\t\tconst newTimeLeft = (leftHandlePosition / 100) * videoDuration;\n\t\t\t\tvideoElement.currentTime = newTimeLeft;\n\n\t\t\t\tdragStart = newTimeLeft;\n\t\t\t} else if (dragging === \"right\") {\n\t\t\t\trightHandlePosition = Math.max(newPercentage, leftHandlePosition);\n\n\t\t\t\tconst newTimeRight = (rightHandlePosition / 100) * videoDuration;\n\t\t\t\tvideoElement.currentTime = newTimeRight;\n\n\t\t\t\tdragEnd = newTimeRight;\n\t\t\t}\n\n\t\t\tconst startTime = (leftHandlePosition / 100) * videoDuration;\n\t\t\tconst endTime = (rightHandlePosition / 100) * videoDuration;\n\t\t\ttrimmedDuration = endTime - startTime;\n\n\t\t\tleftHandlePosition = leftHandlePosition;\n\t\t\trightHandlePosition = rightHandlePosition;\n\t\t}\n\t};\n\n\tconst moveHandle = (e: KeyboardEvent): void => {\n\t\tif (dragging) {\n\t\t\t// Calculate the movement distance as a percentage of the video duration\n\t\t\tconst distance = (1 / videoDuration) * 100;\n\n\t\t\tif (e.key === \"ArrowLeft\") {\n\t\t\t\tdrag({ clientX: 0 }, -distance);\n\t\t\t} else if (e.key === \"ArrowRight\") {\n\t\t\t\tdrag({ clientX: 0 }, distance);\n\t\t\t}\n\t\t}\n\t};\n\n\tconst generateThumbnail = (): void => {\n\t\tconst canvas = document.createElement(\"canvas\");\n\t\tconst ctx = canvas.getContext(\"2d\");\n\t\tif (!ctx) return;\n\n\t\tcanvas.width = videoElement.videoWidth;\n\t\tcanvas.height = videoElement.videoHeight;\n\n\t\tctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n\n\t\tconst thumbnail: string = canvas.toDataURL(\"image/jpeg\", 0.7);\n\t\tthumbnails = [...thumbnails, thumbnail];\n\t};\n\n\tonMount(() => {\n\t\tconst loadMetadata = (): void => {\n\t\t\tvideoDuration = videoElement.duration;\n\n\t\t\tconst interval = videoDuration / numberOfThumbnails;\n\t\t\tlet captures = 0;\n\n\t\t\tconst onSeeked = (): void => {\n\t\t\t\tgenerateThumbnail();\n\t\t\t\tcaptures++;\n\n\t\t\t\tif (captures < numberOfThumbnails) {\n\t\t\t\t\tvideoElement.currentTime += interval;\n\t\t\t\t} else {\n\t\t\t\t\tvideoElement.removeEventListener(\"seeked\", onSeeked);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tvideoElement.addEventListener(\"seeked\", onSeeked);\n\t\t\tvideoElement.currentTime = 0;\n\t\t};\n\n\t\tif (videoElement.readyState >= 1) {\n\t\t\tloadMetadata();\n\t\t} else {\n\t\t\tvideoElement.addEventListener(\"loadedmetadata\", loadMetadata);\n\t\t}\n\t});\n\n\tonDestroy(() => {\n\t\twindow.removeEventListener(\"mousemove\", drag);\n\t\twindow.removeEventListener(\"mouseup\", stopDragging);\n\t\twindow.removeEventListener(\"keydown\", moveHandle);\n\n\t\tif (intervalId !== undefined) {\n\t\t\tclearInterval(intervalId);\n\t\t}\n\t});\n\n\tonMount(() => {\n\t\twindow.addEventListener(\"mousemove\", drag);\n\t\twindow.addEventListener(\"mouseup\", stopDragging);\n\t\twindow.addEventListener(\"keydown\", moveHandle);\n\t});\n</script>\n\n<div class=\"container\">\n\t{#if loadingTimeline}\n\t\t<div class=\"load-wrap\">\n\t\t\t<span aria-label=\"loading timeline\" class=\"loader\" />\n\t\t</div>\n\t{:else}\n\t\t<div id=\"timeline\" class=\"thumbnail-wrapper\">\n\t\t\t<button\n\t\t\t\taria-label=\"start drag handle for trimming video\"\n\t\t\t\tclass=\"handle left\"\n\t\t\t\ton:mousedown={() => startDragging(\"left\")}\n\t\t\t\ton:blur={stopDragging}\n\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\tif (e.key === \"ArrowLeft\" || e.key == \"ArrowRight\") {\n\t\t\t\t\t\tstartDragging(\"left\");\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t\tstyle=\"left: {leftHandlePosition}%;\"\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\tclass=\"opaque-layer\"\n\t\t\t\tstyle=\"left: {leftHandlePosition}%; right: {100 - rightHandlePosition}%\"\n\t\t\t/>\n\n\t\t\t{#each thumbnails as thumbnail, i (i)}\n\t\t\t\t<img src={thumbnail} alt={`frame-${i}`} draggable=\"false\" />\n\t\t\t{/each}\n\t\t\t<button\n\t\t\t\taria-label=\"end drag handle for trimming video\"\n\t\t\t\tclass=\"handle right\"\n\t\t\t\ton:mousedown={() => startDragging(\"right\")}\n\t\t\t\ton:blur={stopDragging}\n\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\tif (e.key === \"ArrowLeft\" || e.key == \"ArrowRight\") {\n\t\t\t\t\t\tstartDragging(\"right\");\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t\tstyle=\"left: {rightHandlePosition}%;\"\n\t\t\t/>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.load-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t}\n\t.loader {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tbackground-color: var(--border-color-accent-subdued);\n\t\tanimation: shadowPulse 2s linear infinite;\n\t\tbox-shadow:\n\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\tmargin: var(--spacing-md);\n\t\tborder-radius: 50%;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tscale: 0.5;\n\t}\n\n\t@keyframes shadowPulse {\n\t\t33% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: #fff;\n\t\t}\n\t\t66% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: var(--border-color-accent-subdued);\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\t\tbackground: #fff;\n\t\t}\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: var(--spacing-lg) var(--spacing-lg) 0 var(--spacing-lg);\n\t}\n\n\t#timeline {\n\t\tdisplay: flex;\n\t\theight: var(--size-10);\n\t\tflex: 1;\n\t\tposition: relative;\n\t}\n\n\timg {\n\t\tflex: 1 1 auto;\n\t\tmin-width: 0;\n\t\tobject-fit: cover;\n\t\theight: var(--size-12);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tuser-select: none;\n\t\tz-index: 1;\n\t}\n\n\t.handle {\n\t\twidth: 3px;\n\t\tbackground-color: var(--color-accent);\n\t\tcursor: ew-resize;\n\t\theight: var(--size-12);\n\t\tz-index: 3;\n\t\tposition: absolute;\n\t}\n\n\t.opaque-layer {\n\t\tbackground-color: rgba(230, 103, 40, 0.25);\n\t\tborder: 1px solid var(--color-accent);\n\t\theight: var(--size-12);\n\t\tposition: absolute;\n\t\tz-index: 2;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Un<PERSON>, <PERSON><PERSON>, Clear } from \"@gradio/icons\";\n\timport VideoTimeline from \"./VideoTimeline.svelte\";\n\timport { trimVideo } from \"./utils\";\n\timport { FFmpeg } from \"@ffmpeg/ffmpeg\";\n\timport loadFfmpeg from \"./utils\";\n\timport { onMount } from \"svelte\";\n\timport { format_time } from \"@gradio/utils\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let videoElement: HTMLVideoElement;\n\n\texport let showRedo = false;\n\texport let interactive = true;\n\texport let mode = \"\";\n\texport let handle_reset_value: () => void;\n\texport let handle_trim_video: (videoBlob: Blob) => void;\n\texport let processingVideo = false;\n\texport let i18n: (key: string) => string;\n\texport let value: FileData | null = null;\n\texport let show_download_button = false;\n\texport let handle_clear: () => void = () => {};\n\texport let has_change_history = false;\n\n\tlet ffmpeg: FFmpeg;\n\n\tonMount(async () => {\n\t\tffmpeg = await loadFfmpeg();\n\t});\n\n\t$: if (mode === \"edit\" && trimmedDuration === null && videoElement)\n\t\ttrimmedDuration = videoElement.duration;\n\n\tlet trimmedDuration: number | null = null;\n\tlet dragStart = 0;\n\tlet dragEnd = 0;\n\n\tlet loadingTimeline = false;\n\n\tconst toggleTrimmingMode = (): void => {\n\t\tif (mode === \"edit\") {\n\t\t\tmode = \"\";\n\t\t\ttrimmedDuration = videoElement.duration;\n\t\t} else {\n\t\t\tmode = \"edit\";\n\t\t}\n\t};\n</script>\n\n<div class=\"container\" class:hidden={mode !== \"edit\"}>\n\t{#if mode === \"edit\"}\n\t\t<div class=\"timeline-wrapper\">\n\t\t\t<VideoTimeline\n\t\t\t\t{videoElement}\n\t\t\t\tbind:dragStart\n\t\t\t\tbind:dragEnd\n\t\t\t\tbind:trimmedDuration\n\t\t\t\tbind:loadingTimeline\n\t\t\t/>\n\t\t</div>\n\t{/if}\n\n\t<div class=\"controls\" data-testid=\"waveform-controls\">\n\t\t{#if mode === \"edit\" && trimmedDuration !== null}\n\t\t\t<time\n\t\t\t\taria-label=\"duration of selected region in seconds\"\n\t\t\t\tclass:hidden={loadingTimeline}>{format_time(trimmedDuration)}</time\n\t\t\t>\n\t\t\t<div class=\"edit-buttons\">\n\t\t\t\t<button\n\t\t\t\t\tclass:hidden={loadingTimeline}\n\t\t\t\t\tclass=\"text-button\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tmode = \"\";\n\t\t\t\t\t\tprocessingVideo = true;\n\t\t\t\t\t\ttrimVideo(ffmpeg, dragStart, dragEnd, videoElement)\n\t\t\t\t\t\t\t.then((videoBlob) => {\n\t\t\t\t\t\t\t\thandle_trim_video(videoBlob);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\t\tprocessingVideo = false;\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}}>Trim</button\n\t\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"text-button\"\n\t\t\t\t\tclass:hidden={loadingTimeline}\n\t\t\t\t\ton:click={toggleTrimmingMode}>Cancel</button\n\t\t\t\t>\n\t\t\t</div>\n\t\t{:else}\n\t\t\t<div />\n\t\t{/if}\n\t</div>\n</div>\n\n<ModifyUpload\n\t{i18n}\n\ton:clear={() => handle_clear()}\n\tdownload={show_download_button ? value?.url : null}\n>\n\t{#if showRedo && mode === \"\"}\n\t\t<IconButton\n\t\t\tIcon={Undo}\n\t\t\tlabel=\"Reset video to initial value\"\n\t\t\tdisabled={processingVideo || !has_change_history}\n\t\t\ton:click={() => {\n\t\t\t\thandle_reset_value();\n\t\t\t\tmode = \"\";\n\t\t\t}}\n\t\t/>\n\t{/if}\n\n\t{#if interactive && mode === \"\"}\n\t\t<IconButton\n\t\t\tIcon={Trim}\n\t\t\tlabel=\"Trim video to selection\"\n\t\t\tdisabled={processingVideo}\n\t\t\ton:click={toggleTrimmingMode}\n\t\t/>\n\t{/if}\n</ModifyUpload>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t}\n\ttime {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: bold;\n\t\tpadding-left: var(--spacing-xs);\n\t}\n\n\t.timeline-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 100%;\n\t}\n\n\t.text-button {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t\tpadding: 0 5px;\n\t\tmargin-left: 5px;\n\t}\n\n\t.text-button:hover,\n\t.text-button:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.controls {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin: var(--spacing-lg);\n\t\toverflow: hidden;\n\t}\n\n\t.edit-buttons {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-sm);\n\t}\n\n\t@media (max-width: 320px) {\n\t\t.controls {\n\t\t\tflex-direction: column;\n\t\t\talign-items: flex-start;\n\t\t}\n\n\t\t.edit-buttons {\n\t\t\tmargin-top: var(--spacing-sm);\n\t\t}\n\n\t\t.controls * {\n\t\t\tmargin: var(--spacing-sm);\n\t\t}\n\n\t\t.controls .text-button {\n\t\t\tmargin-left: 0;\n\t\t}\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Play, Pause, Maximise, Undo } from \"@gradio/icons\";\n\timport Video from \"./Video.svelte\";\n\timport VideoControls from \"./VideoControls.svelte\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { prepare_files } from \"@gradio/client\";\n\timport { format_time } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let root = \"\";\n\texport let src: string;\n\texport let subtitle: string | null = null;\n\texport let mirror: boolean;\n\texport let autoplay: boolean;\n\texport let loop: boolean;\n\texport let label = \"test\";\n\texport let interactive = false;\n\texport let handle_change: (video: FileData) => void = () => {};\n\texport let handle_reset_value: () => void = () => {};\n\texport let upload: Client[\"upload\"];\n\texport let is_stream: boolean | undefined;\n\texport let i18n: I18nFormatter;\n\texport let show_download_button = false;\n\texport let value: FileData | null = null;\n\texport let handle_clear: () => void = () => {};\n\texport let has_change_history = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tstop: undefined;\n\t\tend: undefined;\n\t\tclear: undefined;\n\t}>();\n\n\tlet time = 0;\n\tlet duration: number;\n\tlet paused = true;\n\tlet video: HTMLVideoElement;\n\tlet processingVideo = false;\n\n\tfunction handleMove(e: TouchEvent | MouseEvent): void {\n\t\tif (!duration) return;\n\n\t\tif (e.type === \"click\") {\n\t\t\thandle_click(e as MouseEvent);\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.type !== \"touchmove\" && !((e as MouseEvent).buttons & 1)) return;\n\n\t\tconst clientX =\n\t\t\te.type === \"touchmove\"\n\t\t\t\t? (e as TouchEvent).touches[0].clientX\n\t\t\t\t: (e as MouseEvent).clientX;\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (clientX - left)) / (right - left);\n\t}\n\n\tasync function play_pause(): Promise<void> {\n\t\tif (document.fullscreenElement != video) {\n\t\t\tconst isPlaying =\n\t\t\t\tvideo.currentTime > 0 &&\n\t\t\t\t!video.paused &&\n\t\t\t\t!video.ended &&\n\t\t\t\tvideo.readyState > video.HAVE_CURRENT_DATA;\n\n\t\t\tif (!isPlaying) {\n\t\t\t\tawait video.play();\n\t\t\t} else video.pause();\n\t\t}\n\t}\n\n\tfunction handle_click(e: MouseEvent): void {\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (e.clientX - left)) / (right - left);\n\t}\n\n\tfunction handle_end(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n\n\tconst handle_trim_video = async (videoBlob: Blob): Promise<void> => {\n\t\tlet _video_blob = new File([videoBlob], \"video.mp4\");\n\t\tconst val = await prepare_files([_video_blob]);\n\t\tlet value = ((await upload(val, root))?.filter(Boolean) as FileData[])[0];\n\n\t\thandle_change(value);\n\t};\n\n\tfunction open_full_screen(): void {\n\t\tvideo.requestFullscreen();\n\t}\n\n\t$: time = time || 0;\n\t$: duration = duration || 0;\n</script>\n\n<div class=\"wrap\">\n\t<div class=\"mirror-wrap\" class:mirror>\n\t\t<Video\n\t\t\t{src}\n\t\t\tpreload=\"auto\"\n\t\t\t{autoplay}\n\t\t\t{loop}\n\t\t\t{is_stream}\n\t\t\ton:click={play_pause}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:error\n\t\t\ton:ended={handle_end}\n\t\t\tbind:currentTime={time}\n\t\t\tbind:duration\n\t\t\tbind:paused\n\t\t\tbind:node={video}\n\t\t\tdata-testid={`${label}-player`}\n\t\t\t{processingVideo}\n\t\t\ton:loadstart\n\t\t\ton:loadeddata\n\t\t\ton:loadedmetadata\n\t\t>\n\t\t\t<track kind=\"captions\" src={subtitle} default />\n\t\t</Video>\n\t</div>\n\n\t<div class=\"controls\">\n\t\t<div class=\"inner\">\n\t\t\t<span\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"play-pause-replay-button\"\n\t\t\t\ton:click={play_pause}\n\t\t\t\ton:keydown={play_pause}\n\t\t\t>\n\t\t\t\t{#if time === duration}\n\t\t\t\t\t<Undo />\n\t\t\t\t{:else if paused}\n\t\t\t\t\t<Play />\n\t\t\t\t{:else}\n\t\t\t\t\t<Pause />\n\t\t\t\t{/if}\n\t\t\t</span>\n\n\t\t\t<span class=\"time\">{format_time(time)} / {format_time(duration)}</span>\n\n\t\t\t<!-- TODO: implement accessible video timeline for 4.0 -->\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t\t\t<progress\n\t\t\t\tvalue={time / duration || 0}\n\t\t\t\ton:mousemove={handleMove}\n\t\t\t\ton:touchmove|preventDefault={handleMove}\n\t\t\t\ton:click|stopPropagation|preventDefault={handle_click}\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"full-screen\"\n\t\t\t\ton:click={open_full_screen}\n\t\t\t\ton:keypress={open_full_screen}\n\t\t\t>\n\t\t\t\t<Maximise />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n{#if interactive}\n\t<VideoControls\n\t\tvideoElement={video}\n\t\tshowRedo\n\t\t{handle_trim_video}\n\t\t{handle_reset_value}\n\t\tbind:processingVideo\n\t\t{value}\n\t\t{i18n}\n\t\t{show_download_button}\n\t\t{handle_clear}\n\t\t{has_change_history}\n\t/>\n{/if}\n\n<style lang=\"postcss\">\n\tspan {\n\t\ttext-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n\t}\n\n\tprogress {\n\t\tmargin-right: var(--size-3);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-2);\n\t}\n\n\tprogress::-webkit-progress-bar {\n\t\tborder-radius: 2px;\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\t}\n\n\tprogress::-webkit-progress-value {\n\t\tbackground-color: rgba(255, 255, 255, 0.9);\n\t}\n\n\t.mirror {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.mirror-wrap {\n\t\tposition: relative;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t}\n\n\t.controls {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\topacity: 0;\n\t\ttransition: 500ms;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--color-grey-800);\n\t\tpadding: var(--size-2) var(--size-1);\n\t\twidth: calc(100% - 0.375rem * 2);\n\t\twidth: calc(100% - var(--size-2) * 2);\n\t}\n\t.wrap:hover .controls {\n\t\topacity: 1;\n\t}\n\n\t.inner {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.icon {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\twidth: var(--size-6);\n\t\tcolor: white;\n\t}\n\n\t.time {\n\t\tflex-shrink: 0;\n\t\tmargin-right: var(--size-3);\n\t\tmargin-left: var(--size-3);\n\t\tcolor: white;\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\t.wrap {\n\t\tposition: relative;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t\tborder-radius: var(--radius-xl);\n\t}\n\t.wrap :global(video) {\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate, tick } from \"svelte\";\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tIconButton,\n\t\tShareButton,\n\t\tIconButtonWrapper\n\t} from \"@gradio/atoms\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { Video, Download } from \"@gradio/icons\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport Player from \"./Player.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let label: string | undefined = undefined;\n\texport let show_label = true;\n\texport let autoplay: boolean;\n\texport let show_share_button = true;\n\texport let show_download_button = true;\n\texport let loop: boolean;\n\texport let i18n: I18nFormatter;\n\texport let upload: Client[\"upload\"];\n\texport let display_icon_button_wrapper_top_corner = false;\n\n\tlet old_value: FileData | null = null;\n\tlet old_subtitle: FileData | null = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t\tload: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n\n\tafterUpdate(async () => {\n\t\t// needed to bust subtitle caching issues on Chrome\n\t\tif (\n\t\t\tvalue !== old_value &&\n\t\t\tsubtitle !== old_subtitle &&\n\t\t\told_subtitle !== null\n\t\t) {\n\t\t\told_value = value;\n\t\t\tvalue = null;\n\t\t\tawait tick();\n\t\t\tvalue = old_value;\n\t\t}\n\t\told_value = value;\n\t\told_subtitle = subtitle;\n\t});\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n{#if !value || value.url === undefined}\n\t<Empty unpadded_box={true} size=\"large\"><Video /></Empty>\n{:else}\n\t{#key value.url}\n\t\t<Player\n\t\t\tsrc={value.url}\n\t\t\tsubtitle={subtitle?.url}\n\t\t\tis_stream={value.is_stream}\n\t\t\t{autoplay}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:stop\n\t\t\ton:end\n\t\t\ton:loadedmetadata={() => {\n\t\t\t\t// Deal with `<video>`'s `loadedmetadata` event as `VideoPreview`'s `load` event\n\t\t\t\t// to represent not only the video is loaded but also the metadata is loaded\n\t\t\t\t// so its dimensions (w/h) are known. This is used for Chatbot's auto scroll.\n\t\t\t\tdispatch(\"load\");\n\t\t\t}}\n\t\t\tmirror={false}\n\t\t\t{label}\n\t\t\t{loop}\n\t\t\tinteractive={false}\n\t\t\t{upload}\n\t\t\t{i18n}\n\t\t/>\n\t{/key}\n\t<div data-testid=\"download-div\">\n\t\t<IconButtonWrapper\n\t\t\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\n\t\t>\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink\n\t\t\t\t\thref={value.is_stream\n\t\t\t\t\t\t? value.url?.replace(\"playlist.m3u8\", \"playlist-file\")\n\t\t\t\t\t\t: value.url}\n\t\t\t\t\tdownload={value.orig_name || value.path}\n\t\t\t\t>\n\t\t\t\t\t<IconButton Icon={Download} label=\"Download\" />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if show_share_button}\n\t\t\t\t<ShareButton\n\t\t\t\t\t{i18n}\n\t\t\t\t\ton:error\n\t\t\t\t\ton:share\n\t\t\t\t\t{value}\n\t\t\t\t\tformatter={async (value) => {\n\t\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\t\tlet url = await uploadToHuggingFace(value.data, \"url\");\n\t\t\t\t\t\treturn url;\n\t\t\t\t\t}}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t</div>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "onMount", "onDestroy", "ctx", "i", "set_style", "div0", "div1", "button0", "button1", "div", "attr", "img", "img_src_value", "create_if_block", "numberOfThumbnails", "videoElement", "$$props", "trimmedDuration", "dragStart", "dragEnd", "loadingTimeline", "thumbnails", "videoDuration", "leftHandlePosition", "rightHandlePosition", "dragging", "startDragging", "side", "stopDragging", "drag", "event", "distance", "timeline", "rect", "newPercentage", "newTimeLeft", "$$invalidate", "newTimeRight", "startTime", "endTime", "moveHandle", "e", "generateThumbnail", "canvas", "thumbnail", "loadMetadata", "interval", "captures", "onSeeked", "mousedown_handler", "mousedown_handler_1", "t0_value", "format_time", "time", "dirty", "set_data", "t0", "Undo", "iconbutton_changes", "<PERSON><PERSON>", "create_if_block_1", "if_block0", "create_if_block_3", "create_if_block_2", "toggle_class", "showRedo", "interactive", "mode", "handle_reset_value", "handle_trim_video", "processingVideo", "i18n", "value", "show_download_button", "handle_clear", "has_change_history", "ffmpeg", "loadFfmpeg", "toggleTrimmingMode", "trimVideo", "videoBlob", "track", "track_src_value", "t2_value", "t4_value", "div4", "div3", "div2", "span0", "span1", "progress", "current", "t2", "t4", "root", "src", "subtitle", "mirror", "autoplay", "loop", "label", "handle_change", "upload", "is_stream", "dispatch", "createEventDispatcher", "duration", "paused", "video", "handleMove", "handle_click", "clientX", "left", "right", "play_pause", "handle_end", "_video_blob", "val", "prepare_files", "open_full_screen", "afterUpdate", "previous_key", "safe_not_equal", "player_changes", "downloadlink_changes", "Download", "Video", "blocklabel_changes", "show_label", "show_share_button", "display_icon_button_wrapper_top_corner", "old_value", "old_subtitle", "tick", "uploadToHuggingFace"], "mappings": "s6CAAAA,GAcKC,EAAAC,EAAAC,CAAA,EAHJC,GAECF,EAAAG,CAAA,oXCZQ,CAAA,QAAAC,GAAA,UAAAC,IAA0B,OAAA,qJA8K1BC,EAAU,CAAA,CAAA,aAAkBA,EAAC,EAAA,kBAAlC,OAAIC,GAAA,EAAA,uRARSD,EAAkB,CAAA,EAAA,GAAA,wDAKlBA,EAAkB,CAAA,EAAA,GAAA,EAAYE,EAAAC,EAAA,QAAA,IAAMH,EAAmB,CAAA,EAAA,GAAA,+GAgBvDA,EAAmB,CAAA,EAAA,GAAA,+EAhCnCR,GAkCKC,EAAAW,EAAAT,CAAA,EAjCJC,GAWCQ,EAAAC,CAAA,UAEDT,GAGCQ,EAAAD,CAAA,mEAKDP,GAWCQ,EAAAE,CAAA,6CA5BSN,EAAY,CAAA,CAAA,4DAqBZA,EAAY,CAAA,CAAA,uDAfPA,EAAkB,CAAA,EAAA,GAAA,kBAKlBA,EAAkB,CAAA,EAAA,GAAA,OAAYE,EAAAC,EAAA,QAAA,IAAMH,EAAmB,CAAA,EAAA,GAAA,aAG/DA,EAAU,CAAA,CAAA,oDAaFA,EAAmB,CAAA,EAAA,GAAA,uPApCnCR,GAEKC,EAAAc,EAAAZ,CAAA,sGAsBOK,EAAS,EAAA,CAAA,GAAAQ,EAAAC,EAAA,MAAAC,CAAA,uBAAgBV,EAAC,EAAA,CAAA,EAAA,8EAApCR,GAA2DC,EAAAgB,EAAAd,CAAA,+BAAjDK,EAAS,EAAA,CAAA,qCAAgBA,EAAC,EAAA,CAAA,+EAzBlCA,EAAe,CAAA,EAAAW,qGADrBnB,GA0CKC,EAAAc,EAAAZ,CAAA,6HAtLAiB,GAAqB,sBAPd,GAAA,CAAA,aAAAC,CAAA,EAAAC,EACA,CAAA,gBAAAC,CAAA,EAAAD,EACA,CAAA,UAAAE,CAAA,EAAAF,EACA,CAAA,QAAAG,CAAA,EAAAH,EACA,CAAA,gBAAAI,CAAA,EAAAJ,EAEPK,EAAA,CAAA,EAGAC,EAEAC,EAAqB,EACrBC,EAAsB,IAEtBC,EAA0B,WAExBC,EAAiBC,GAAA,CACtBF,EAAWE,GAKNC,EAAA,IAAA,CACLH,EAAW,MAGNI,EAAA,CAAQC,EAA4BC,IAAA,CACrC,GAAAN,EAAA,OACGO,EAAW,SAAS,eAAe,UAAU,EAE9C,GAAA,CAAAA,EAAA,OAEC,MAAAC,EAAOD,EAAS,wBAClB,IAAAE,GAAkBJ,EAAM,QAAUG,EAAK,MAAQA,EAAK,MAAS,OAE7DF,EAEHG,EACCT,IAAa,OACVF,EAAqBQ,EACrBP,EAAsBO,EAG1BG,GAAkBJ,EAAM,QAAUG,EAAK,MAAQA,EAAK,MAAS,IAG9DC,EAAgB,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAe,GAAG,CAAA,EAEnDT,IAAa,OAAA,KAChBF,EAAqB,KAAK,IAAIW,EAAeV,CAAmB,CAAA,EAG1D,MAAAW,EAAeZ,EAAqB,IAAOD,EACjDc,EAAA,EAAArB,EAAa,YAAcoB,EAAApB,CAAA,MAE3BG,EAAYiB,CAAA,UACFV,IAAa,QAAA,KACvBD,EAAsB,KAAK,IAAIU,EAAeX,CAAkB,CAAA,EAE1D,MAAAc,EAAgBb,EAAsB,IAAOF,EACnDc,EAAA,EAAArB,EAAa,YAAcsB,EAAAtB,CAAA,MAE3BI,EAAUkB,CAAA,EAGL,MAAAC,EAAaf,EAAqB,IAAOD,EACzCiB,EAAWf,EAAsB,IAAOF,EAC9Cc,EAAA,EAAAnB,EAAkBsB,EAAUD,CAAA,kBAOxBE,EAAcC,GAAA,CACf,GAAAhB,EAAA,CAEG,MAAAM,EAAY,EAAIT,EAAiB,IAEnCmB,EAAE,MAAQ,YACbZ,EAAO,CAAA,QAAS,IAAME,CAAQ,EACpBU,EAAE,MAAQ,cACpBZ,EAAO,CAAA,QAAS,GAAKE,CAAQ,IAK1BW,EAAA,IAAA,OACCC,EAAS,SAAS,cAAc,QAAQ,EACxCzC,EAAMyC,EAAO,WAAW,IAAI,EAC7B,GAAA,CAAAzC,EAAA,OAELyC,EAAO,MAAQ5B,EAAa,WAC5B4B,EAAO,OAAS5B,EAAa,YAE7Bb,EAAI,UAAUa,EAAc,EAAG,EAAG4B,EAAO,MAAOA,EAAO,MAAM,QAEvDC,EAAoBD,EAAO,UAAU,aAAc,EAAG,EAC5DP,EAAA,EAAAf,EAAA,CAAA,GAAiBA,EAAYuB,CAAS,CAAA,GAGvC5C,GAAA,IAAA,CACO,MAAA6C,EAAA,IAAA,CACLvB,EAAgBP,EAAa,SAEvB,MAAA+B,EAAWxB,EAAgBR,OAC7BiC,EAAW,EAET,MAAAC,EAAA,IAAA,CACLN,IACAK,IAEIA,EAAWjC,GACdsB,EAAA,EAAArB,EAAa,aAAe+B,EAAA/B,CAAA,EAE5BA,EAAa,oBAAoB,SAAUiC,CAAQ,GAIrDjC,EAAa,iBAAiB,SAAUiC,CAAQ,EAChDZ,EAAA,EAAArB,EAAa,YAAc,EAAAA,CAAA,GAGxBA,EAAa,YAAc,EAC9B8B,IAEA9B,EAAa,iBAAiB,iBAAkB8B,CAAY,IAI9D5C,GAAA,IAAA,CACC,OAAO,oBAAoB,YAAa4B,CAAI,EAC5C,OAAO,oBAAoB,UAAWD,CAAY,EAClD,OAAO,oBAAoB,UAAWY,CAAU,IAOjDxC,GAAA,IAAA,CACC,OAAO,iBAAiB,YAAa6B,CAAI,EACzC,OAAO,iBAAiB,UAAWD,CAAY,EAC/C,OAAO,iBAAiB,UAAWY,CAAU,IAcvB,MAAAS,EAAA,IAAAvB,EAAc,MAAM,IAE3Be,GAAC,EACTA,EAAE,MAAQ,aAAeA,EAAE,KAAO,eACrCf,EAAc,MAAM,GAiBFwB,EAAA,IAAAxB,EAAc,OAAO,IAE5Be,GAAC,EACTA,EAAE,MAAQ,aAAeA,EAAE,KAAO,eACrCf,EAAc,OAAO,oQAlKvBU,EAAA,EAAAhB,EAAkBC,EAAW,SAAWP,EAAA,2hCCjBnB,EAAA,OAAA,8kBA+CvBpB,EAQKC,EAAAc,EAAAZ,CAAA,ycAgCJH,EAAMC,EAAAc,EAAAZ,CAAA,4CAzB2BsD,EAAAC,GAAYlD,EAAe,EAAA,CAAA,EAAA,6PAA7CA,EAAe,EAAA,CAAA,yDAIdA,EAAe,EAAA,CAAA,yDAgBfA,EAAe,EAAA,CAAA,oDAtB/BR,EAGAC,EAAA0D,EAAAxD,CAAA,mBACAH,EAqBKC,EAAAc,EAAAZ,CAAA,EApBJC,GAcAW,EAAAF,CAAA,UACAT,GAIAW,EAAAD,CAAA,0CADWN,EAAkB,EAAA,CAAA,iBArBGoD,EAAA,MAAAH,KAAAA,EAAAC,GAAYlD,EAAe,EAAA,CAAA,EAAA,KAAAqD,GAAAC,EAAAL,CAAA,yBAA7CjD,EAAe,EAAA,CAAA,yBAIdA,EAAe,EAAA,CAAA,yBAgBfA,EAAe,EAAA,CAAA,6FAiBzBuD,wCAEI,SAAAvD,OAAoBA,EAAkB,EAAA,2FAAtCoD,EAAA,OAAAI,EAAA,SAAAxD,OAAoBA,EAAkB,EAAA,kJAU1CyD,4CAEIzD,EAAe,CAAA,mBACfA,EAAkB,EAAA,CAAA,oFADlBA,EAAe,CAAA,yHAhBtBA,EAAQ,CAAA,GAAIA,EAAI,CAAA,IAAK,IAAE0D,GAAA1D,CAAA,IAYvBA,EAAW,CAAA,GAAIA,EAAI,CAAA,IAAK,IAAEW,GAAAX,CAAA,4GAZ1BA,EAAQ,CAAA,GAAIA,EAAI,CAAA,IAAK,4GAYrBA,EAAW,CAAA,GAAIA,EAAI,CAAA,IAAK,sOA/DxB2D,EAAA3D,OAAS,QAAM4D,GAAA5D,CAAA,kBAad,OAAAA,EAAS,CAAA,IAAA,QAAUA,QAAoB,KAAI6D,mEAoCvC7D,EAAoB,CAAA,EAAGA,MAAO,IAAM,oQAlDV8D,GAAA1D,EAAA,SAAAJ,OAAS,MAAM,UAApDR,EA6CKC,EAAAW,EAAAT,CAAA,yBAhCJC,GA+BKQ,EAAAD,CAAA,gDA3CAH,OAAS,gLADsB8D,GAAA1D,EAAA,SAAAJ,OAAS,MAAM,qDAkDzCA,EAAoB,CAAA,EAAGA,MAAO,IAAM,oMAzFnC,GAAA,CAAA,aAAAa,CAAA,EAAAC,GAEA,SAAAiD,EAAW,EAAA,EAAAjD,GACX,YAAAkD,EAAc,EAAA,EAAAlD,GACd,KAAAmD,EAAO,EAAA,EAAAnD,EACP,CAAA,mBAAAoD,CAAA,EAAApD,EACA,CAAA,kBAAAqD,CAAA,EAAArD,GACA,gBAAAsD,EAAkB,EAAA,EAAAtD,EAClB,CAAA,KAAAuD,CAAA,EAAAvD,GACA,MAAAwD,EAAyB,IAAA,EAAAxD,GACzB,qBAAAyD,EAAuB,EAAA,EAAAzD,EACvB,CAAA,aAAA0D,EAAA,IAAA,OACA,mBAAAC,EAAqB,EAAA,EAAA3D,EAE5B4D,EAEJ5E,GAAA,SAAA,MACC4E,EAAe,MAAAC,GAAA,CAAA,QAMZ5D,EAAiC,KACjCC,EAAY,EACZC,EAAU,EAEVC,EAAkB,GAEhB,MAAA0D,EAAA,IAAA,CACDX,IAAS,YACZA,EAAO,EAAA,EACP/B,EAAA,GAAAnB,EAAkBF,EAAa,QAAA,OAE/BoD,EAAO,MAAA,sIA6BJ/B,EAAA,EAAA+B,EAAO,EAAE,EACT/B,EAAA,EAAAkC,EAAkB,EAAI,EACtBS,GAAUH,EAAQ1D,EAAWC,EAASJ,CAAY,EAChD,KAAMiE,GAAS,CACfX,EAAkBW,CAAS,IAE3B,KAAI,IAAA,CACJ5C,EAAA,EAAAkC,EAAkB,EAAK,YA2B3BF,IACAhC,EAAA,EAAA+B,EAAO,EAAE,SAVIO,ojBApETP,IAAS,QAAUlD,IAAoB,MAAQF,GAAAqB,EAAA,GACrDnB,EAAkBF,EAAa,QAAA,23DChCM,EAAA,OAAA,4GA8HRb,EAAQ,CAAA,CAAA,GAAAQ,EAAAuE,EAAA,MAAAC,CAAA,uBAApCxF,GAA+CC,EAAAsF,EAAApF,CAAA,8BAAnBK,EAAQ,CAAA,CAAA,mmBAkDvBA,EAAK,EAAA,sWAALA,EAAK,EAAA,yZA3BEiF,EAAA/B,GAAYlD,EAAI,EAAA,CAAA,EAAA,OAAMkF,EAAAhC,GAAYlD,EAAQ,EAAA,CAAA,EAAA,qNA7B9CA,EAAK,CAAA,CAAA,wEAJHA,EAAI,EAAA,IAAA,wBAAJA,EAAI,EAAA,yEAGXA,EAAK,EAAA,IAAA,iBAALA,EAAK,EAAA,uKARNA,EAAU,EAAA,CAAA,8EAIVA,EAAU,EAAA,CAAA,6HAyBd,OAAAA,QAASA,EAAQ,EAAA,EAAA,EAEZA,EAAM,EAAA,EAAA,+CAgCfA,EAAW,CAAA,GAAAW,GAAAX,CAAA,oIAzByB,KAAG,6TAMjCA,EAAI,EAAA,EAAGA,EAAQ,EAAA,GAAI,6PApD9BR,GAsEKC,EAAA0F,EAAAxF,CAAA,EArEJC,EAwBKuF,EAAAhF,CAAA,sBAELP,EA0CKuF,EAAAC,CAAA,EAzCJxF,EAwCKwF,EAAAC,CAAA,EAvCJzF,EAeMyF,EAAAC,CAAA,wBAEN1F,EAAsEyF,EAAAE,CAAA,8BAKtE3F,EAKCyF,EAAAG,CAAA,SAED5F,EASKyF,EAAAjF,CAAA,0EAjCMJ,EAAU,EAAA,CAAA,iBACRA,EAAU,EAAA,CAAA,mBAkBRA,EAAU,EAAA,CAAA,sBACKA,EAAU,EAAA,CAAA,CAAA,qBACEA,EAAY,EAAA,CAAA,CAAA,CAAA,eAQ3CA,EAAgB,EAAA,CAAA,kBACbA,EAAgB,EAAA,CAAA,8JA/CdA,EAAK,CAAA,CAAA,mIAJHA,EAAI,EAAA,sJAGXA,EAAK,EAAA,iLA8BI,CAAAyF,GAAArC,EAAA,CAAA,EAAA,QAAA6B,KAAAA,EAAA/B,GAAYlD,EAAI,EAAA,CAAA,EAAA,KAAAqD,GAAAqC,EAAAT,CAAA,GAAM,CAAAQ,GAAArC,EAAA,CAAA,EAAA,QAAA8B,KAAAA,EAAAhC,GAAYlD,EAAQ,EAAA,CAAA,EAAA,KAAAqD,GAAAsC,EAAAT,CAAA,0BAMtDlF,EAAI,EAAA,EAAGA,EAAQ,EAAA,GAAI,iBAmBzBA,EAAW,CAAA,4UArKJ,KAAA4F,EAAO,EAAA,EAAA9E,EACP,CAAA,IAAA+E,CAAA,EAAA/E,GACA,SAAAgF,EAA0B,IAAA,EAAAhF,EAC1B,CAAA,OAAAiF,CAAA,EAAAjF,EACA,CAAA,SAAAkF,CAAA,EAAAlF,EACA,CAAA,KAAAmF,CAAA,EAAAnF,GACA,MAAAoF,EAAQ,MAAA,EAAApF,GACR,YAAAkD,EAAc,EAAA,EAAAlD,EACd,CAAA,cAAAqF,EAAA,IAAA,MACA,CAAA,mBAAAjC,EAAA,IAAA,MACA,CAAA,OAAAkC,CAAA,EAAAtF,EACA,CAAA,UAAAuF,CAAA,EAAAvF,EACA,CAAA,KAAAuD,CAAA,EAAAvD,GACA,qBAAAyD,EAAuB,EAAA,EAAAzD,GACvB,MAAAwD,EAAyB,IAAA,EAAAxD,EACzB,CAAA,aAAA0D,EAAA,IAAA,OACA,mBAAAC,EAAqB,EAAA,EAAA3D,QAE1BwF,EAAWC,SAQbpD,EAAO,EACPqD,EACAC,EAAS,GACTC,EACAtC,EAAkB,YAEbuC,EAAWpE,EAAA,CACd,GAAA,CAAAiE,EAAA,OAED,GAAAjE,EAAE,OAAS,QAAA,CACdqE,EAAarE,CAAe,YAIzBA,EAAE,OAAS,aAAkB,EAAAA,EAAiB,QAAU,GAAA,OAEtD,MAAAsE,EACLtE,EAAE,OAAS,YACPA,EAAiB,QAAQ,CAAC,EAAE,QAC5BA,EAAiB,SACd,KAAAuE,GAAM,MAAAC,EAAA,EACbxE,EAAE,cACD,6BACFY,EAAQqD,GAAYK,EAAUC,KAAUC,GAAQD,GAAA,EAGlC,eAAAE,GAAA,CACV,SAAS,mBAAqBN,IAEhCA,EAAM,YAAc,GACnB,CAAAA,EAAM,SACNA,EAAM,OACPA,EAAM,WAAaA,EAAM,kBAInBA,EAAM,cADNA,EAAM,iBAKNE,EAAarE,EAAA,OACb,KAAAuE,EAAM,MAAAC,EAAA,EACbxE,EAAE,cACD,wBACFL,EAAA,GAAAiB,EAAQqD,GAAYjE,EAAE,QAAUuE,IAAUC,GAAQD,EAAA,EAG1C,SAAAG,GAAA,CACRX,EAAS,MAAM,EACfA,EAAS,KAAK,QAGTnC,GAA2B,MAAAW,GAAA,KAC5BoC,EAAkB,IAAA,KAAA,CAAMpC,CAAS,EAAG,WAAW,EAC7C,MAAAqC,GAAA,MAAYC,IAAeF,CAAW,CAAA,EACxC5C,IAAAA,IAAAA,MAAgB8B,EAAOe,GAAKvB,CAAI,IAAI,OAAO,OAAO,EAAiB,CAAC,EAExEO,EAAc7B,EAAK,GAGX,SAAA+C,IAAA,CACRX,EAAM,kBAAA,iBAoBavD,EAAImB,+EAGXoC,EAAKpC,84BApBlBpC,EAAA,GAAGiB,EAAOA,GAAQ,CAAA,uBAClBjB,EAAA,GAAGsE,EAAWA,GAAY,CAAA,2lECpGjB,CAAA,sBAAAD,GAAA,YAAAe,UAAgD,EAAA,OAAA,0CA+DnD,IAAAC,EAAAvH,KAAM,+DA0BUA,EAAsC,EAAA,0JAF5DR,GA4BKC,EAAAc,EAAAZ,CAAA,4BApDCyD,EAAA,GAAAoE,GAAAD,EAAAA,EAAAvH,KAAM,GAAG,qHA0BOA,EAAsC,EAAA,iOA5BvC,4SAId,IAAAA,KAAM,IACD,SAAAA,MAAU,IACT,UAAAA,KAAM,+BAYT,oCAGK,2MAjBRoD,EAAA,IAAAqE,EAAA,IAAAzH,KAAM,KACDoD,EAAA,IAAAqE,EAAA,SAAAzH,MAAU,KACToD,EAAA,IAAAqE,EAAA,UAAAzH,KAAM,kQA0BT,KAAAA,EAAM,CAAA,EAAA,UACTA,KAAM,KAAK,QAAQ,gBAAiB,eAAe,EACnDA,KAAM,IACC,SAAAA,EAAM,CAAA,EAAA,WAAaA,KAAM,gHAH7BoD,EAAA,IAAAsE,EAAA,KAAA1H,EAAM,CAAA,EAAA,UACTA,KAAM,KAAK,QAAQ,gBAAiB,eAAe,EACnDA,KAAM,KACCoD,EAAA,IAAAsE,EAAA,SAAA1H,EAAM,CAAA,EAAA,WAAaA,KAAM,4LAEjB2H,GAAQ,MAAA,UAAA,yeAPvB3H,EAAoB,CAAA,GAAA6D,GAAA7D,CAAA,IAUpBA,EAAiB,CAAA,GAAA0D,GAAA1D,CAAA,8GAVjBA,EAAoB,CAAA,4GAUpBA,EAAiB,CAAA,ubA1CM4H,GAAc,MAAA5H,MAAS,gDAChD,MAAA,CAAAA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,MAAQ,OAAS,iLADOoD,EAAA,IAAAyE,EAAA,MAAA7H,MAAS,6SA3C1C,MAAAsE,EAAyB,IAAA,EAAAxD,GACzB,SAAAgF,EAA4B,IAAA,EAAAhF,GAC5B,MAAAoF,EAA4B,MAAA,EAAApF,GAC5B,WAAAgH,EAAa,EAAA,EAAAhH,EACb,CAAA,SAAAkF,CAAA,EAAAlF,GACA,kBAAAiH,EAAoB,EAAA,EAAAjH,GACpB,qBAAAyD,EAAuB,EAAA,EAAAzD,EACvB,CAAA,KAAAmF,CAAA,EAAAnF,EACA,CAAA,KAAAuD,CAAA,EAAAvD,EACA,CAAA,OAAAsF,CAAA,EAAAtF,GACA,uCAAAkH,EAAyC,EAAA,EAAAlH,EAEhDmH,EAA6B,KAC7BC,EAAgC,WAE9B5B,EAAWC,KAWjBe,GAAA,SAAA,CAGEhD,IAAU2D,GACVnC,IAAaoC,GACbA,IAAiB,OAEjBD,EAAY3D,MACZA,EAAQ,IAAA,EACF,MAAA6D,GAAA,MACN7D,EAAQ2D,CAAA,GAETA,EAAY3D,EACZ4D,EAAepC,iJAsBbQ,EAAS,MAAM,WA8BIhC,GACZA,QACW8D,GAAoB9D,EAAM,IAAW,EADlC,ujBApErBA,GAASgC,EAAS,SAAUhC,CAAK"}