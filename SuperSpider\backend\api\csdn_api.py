#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN API模块
提供CSDN文章解析和下载相关的API
"""

import logging
import os
import asyncio
import tempfile
import traceback
from typing import Dict, Any, Optional
from datetime import datetime

from flask import Blueprint, request, jsonify
from flask_login import login_required

from ..utils.mailer import QQMailer
from ..utils.settings import QQ_EMAIL, QQ_AUTH_CODE
from ..utils.permissions import require_permission
from ..spiders.csdn_spider import CSDNSpider

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图 - 修复路径问题
csdn_api = Blueprint('csdn_api', __name__)

def create_email_service() -> Optional[QQMailer]:
    """
    创建邮件服务

    Returns:
        邮件服务实例，如果未配置则返回None
    """
    if QQ_EMAIL and QQ_AUTH_CODE:
        return QQMailer(QQ_EMAIL, QQ_AUTH_CODE)
    return None

@csdn_api.route('/parse', methods=['POST'])
# @require_permission('platform', 'csdn')  # 临时注释掉权限检查
def parse_article():
    """
    解析CSDN文章链接并生成截图发送邮件

    请求体:
        {
            "article_url": "文章URL",
            "email": "接收邮箱地址"
        }

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "content": "文章内容",
                "url": "文章URL",
                "publish_time": "发布时间",
                "read_count": "阅读量"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        article_url = data.get('article_url')
        email = data.get('email')
        format_type = data.get('format', 'html')  # 默认HTML格式

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        if not email:
            return jsonify({
                "success": False,
                "message": "请提供接收邮箱地址",
                "data": None
            }), 400

        # 初始化CSDN爬虫
        spider = CSDNSpider()

        # 解析文章 (后端自动处理VIP账号)
        logger.info(f"开始解析CSDN文章: {article_url}")
        article_result = spider.parse_article(article_url)

        if not article_result or not article_result.get('success'):
            error_msg = article_result.get('error', '文章解析失败，请检查URL是否正确') if article_result else '文章解析失败'
            return jsonify({
                "success": False,
                "message": error_msg,
                "data": None
            }), 400

        article_data = article_result.get('data', {})

        # 生成截图并发送邮件（异步处理）
        try:
            # 在后台异步处理截图和邮件发送
            import threading
            thread = threading.Thread(
                target=_process_screenshot_and_email,
                args=(article_url, email, article_data, format_type)
            )
            thread.daemon = True
            thread.start()

            logger.info(f"已启动后台任务处理HTML生成和邮件发送")

        except Exception as e:
            logger.warning(f"启动后台任务失败: {e}")

        # 返回解析结果
        return jsonify({
            "success": True,
            "message": "文章解析成功，HTML文件正在生成并将发送到您的邮箱",
            "data": article_data
        }), 200

    except Exception as e:
        logger.error(f"解析文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

@csdn_api.route('/download', methods=['POST'])
@require_permission('platform', 'csdn')
def download_article():
    """
    下载CSDN文章并发送到邮箱

    请求体:
        {
            "article_url": "文章URL",
            "email": "接收邮箱地址"
        }

    响应:
        {
            "success": true,
            "message": "下载完成，已发送至邮箱",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "file_path": "文件路径"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        article_url = data.get('article_url')
        email = data.get('email')

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        if not email:
            return jsonify({
                "success": False,
                "message": "请提供接收邮箱地址",
                "data": None
            }), 400

        # TODO: 实现CSDN爬虫类和下载逻辑
        # 目前返回模拟数据
        return jsonify({
            "success": True,
            "message": "下载完成，已发送至邮箱（模拟数据）",
            "data": {
                "title": "CSDN文章示例",
                "author": "示例作者",
                "file_path": "/path/to/example.pdf"
            }
        }), 200

    except Exception as e:
        logger.error(f"下载文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

@csdn_api.route('/status', methods=['GET'])
def check_status():
    """
    检查CSDN API服务状态

    响应:
        {
            "success": true,
            "message": "服务正常",
            "data": {
                "status": "ready",
                "email": {
                    "available": true
                }
            }
        }
    """
    try:
        # 检查邮件服务
        mailer = create_email_service()
        email_available = mailer is not None

        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "status": "ready",
                "email": {
                    "available": email_available
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"检查状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": None
        }), 500

# 添加GET方法的解析接口
@csdn_api.route('/parse', methods=['GET'])
@require_permission('platform', 'csdn')
def parse_article_get():
    """
    解析CSDN文章链接（GET方法）

    查询参数:
        article_url: 文章URL

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "content": "文章内容"
            }
        }
    """
    try:
        article_url = request.args.get('article_url')

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        # TODO: 实现CSDN爬虫类和解析逻辑
        # 目前返回模拟数据
        return jsonify({
            "success": True,
            "message": "解析成功（模拟数据）",
            "data": {
                "title": "CSDN文章示例",
                "author": "示例作者",
                "content": "这是一个示例CSDN文章内容"
            }
        }), 200

    except Exception as e:
        logger.error(f"解析文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

def _process_screenshot_and_email(article_url: str, email: str, article_data: dict, format_type: str = 'html'):
    """
    后台处理文件生成和邮件发送

    Args:
        article_url: 文章URL
        email: 接收邮箱
        article_data: 文章数据
        format_type: 输出格式 (html, pdf, markdown)
    """
    try:
        logger.info(f"开始后台处理{format_type.upper()}文件生成和邮件发送: {article_url}")

        # 重新获取完整的文章内容
        spider = CSDNSpider()
        full_article_result = spider.parse_article(article_url)

        if not full_article_result or not full_article_result.get('success'):
            logger.error("无法获取完整文章内容")
            return

        full_article_data = full_article_result.get('data', {})

        # 根据格式类型生成不同的文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_type == 'html':
            filename = f"csdn_article_{timestamp}.html"
            output_path = spider.generate_file(full_article_data, 'html')
            logger.info(f"HTML文件生成成功: {output_path}")

        elif format_type == 'markdown':
            filename = f"csdn_article_{timestamp}.md"
            output_path = spider.generate_file(full_article_data, 'markdown')
            logger.info(f"Markdown文件生成成功: {output_path}")

        elif format_type == 'pdf':
            filename = f"csdn_article_{timestamp}.pdf"
            try:
                output_path = spider.generate_file(full_article_data, 'pdf')
                logger.info(f"PDF文件生成成功: {output_path}")
            except Exception as e:
                logger.error(f"PDF生成失败: {e}")
                # 降级为HTML格式
                output_path = spider.generate_file(full_article_data, 'html')
                logger.info(f"PDF生成失败，已生成HTML文件: {output_path}")

        else:
            logger.error(f"不支持的格式类型: {format_type}")
            return

        # 发送邮件
        try:
            _send_html_email(email, full_article_data, output_path)
            logger.info(f"CSDN文章处理完成，邮件已发送到: {email}")
        except Exception as email_error:
            logger.error(f"邮件发送失败: {email}")
            logger.error(f"邮件错误详情: {email_error}")

    except Exception as e:
        logger.error(f"后台处理失败: {e}")
        logger.error(traceback.format_exc())

def _generate_article_html(article_data: dict, article_url: str) -> str:
    """
    生成文章HTML内容

    Args:
        article_data: 文章数据
        article_url: 文章URL

    Returns:
        str: HTML内容
    """
    # 获取文章内容，优先使用HTML格式，如果没有则使用纯文本
    content = article_data.get('content', '')
    if not content:
        content = article_data.get('content_text', '')
        if content:
            # 将纯文本转换为HTML段落
            content = '<p>' + content.replace('\n\n', '</p><p>').replace('\n', '<br>') + '</p>'

    if not content:
        content = article_data.get('summary', '<p>无法获取文章内容</p>')

    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{article_data.get('title', '未知标题')}</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1000px;
                margin: 0 auto;
                padding: 20px;
                background-color: #fff;
            }}
            .header {{
                border-bottom: 2px solid #007acc;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }}
            .title {{
                font-size: 28px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                line-height: 1.3;
            }}
            .meta {{
                color: #666;
                font-size: 14px;
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
            }}
            .meta-item {{
                display: flex;
                align-items: center;
            }}
            .meta-label {{
                font-weight: bold;
                margin-right: 5px;
            }}
            .content {{
                font-size: 16px;
                line-height: 1.8;
                color: #333;
            }}
            .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {{
                color: #333;
                margin-top: 30px;
                margin-bottom: 15px;
                font-weight: bold;
            }}
            .content h1 {{ font-size: 24px; }}
            .content h2 {{ font-size: 22px; }}
            .content h3 {{ font-size: 20px; }}
            .content h4 {{ font-size: 18px; }}
            .content p {{
                margin-bottom: 15px;
            }}
            .content pre {{
                background-color: #f6f8fa;
                border: 1px solid #e1e4e8;
                border-radius: 6px;
                padding: 16px;
                overflow-x: auto;
                font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                font-size: 14px;
                line-height: 1.45;
                margin: 20px 0;
            }}
            .content code {{
                background-color: #f6f8fa;
                border-radius: 3px;
                padding: 2px 4px;
                font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                font-size: 14px;
            }}
            .content blockquote {{
                border-left: 4px solid #dfe2e5;
                padding-left: 16px;
                margin: 20px 0;
                color: #6a737d;
            }}
            .content ul, .content ol {{
                padding-left: 30px;
                margin-bottom: 15px;
            }}
            .content li {{
                margin-bottom: 5px;
            }}
            .footer {{
                margin-top: 50px;
                padding-top: 20px;
                border-top: 1px solid #e1e4e8;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
            .source-link {{
                display: inline-block;
                margin-top: 10px;
                padding: 8px 16px;
                background-color: #007acc;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                transition: background-color 0.3s;
            }}
            .source-link:hover {{
                background-color: #005a9e;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1 class="title">{article_data.get('title', '未知标题')}</h1>
            <div class="meta">
                <div class="meta-item">
                    <span class="meta-label">作者:</span>
                    <span>{article_data.get('author', '未知作者')}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">发布时间:</span>
                    <span>{article_data.get('publish_time', '未知时间')}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">阅读量:</span>
                    <span>{article_data.get('read_count', '0')}</span>
                </div>
            </div>
        </div>

        <div class="content">
            {content}
        </div>

        <div class="footer">
            <p>文章来源: CSDN</p>
            <a href="{article_url}" target="_blank" class="source-link">查看原文</a>
            <p style="margin-top: 20px; font-size: 12px;">
                本文件由 SuperSpider 生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </p>
        </div>
    </body>
    </html>
    """

    return html_template

def _generate_article_markdown(article_data: dict, article_url: str) -> str:
    """
    生成文章Markdown内容

    Args:
        article_data: 文章数据
        article_url: 文章URL

    Returns:
        str: Markdown内容
    """
    # 获取文章内容
    content = article_data.get('content_text', article_data.get('content', ''))

    # 如果是HTML内容，尝试转换为Markdown
    if content.startswith('<'):
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            content = soup.get_text()
        except:
            pass

    markdown_template = f"""# {article_data.get('title', '未知标题')}

**作者**: {article_data.get('author', '未知作者')}
**发布时间**: {article_data.get('publish_time', '未知时间')}
**阅读量**: {article_data.get('read_count', '0')}
**原文链接**: [{article_url}]({article_url})

---

{content}

---

*本文件由 SuperSpider 生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

    return markdown_template

def _generate_article_pdf(article_data: dict, article_url: str, output_path: str) -> bool:
    """
    生成文章PDF文件 - 使用pdfkit和wkhtmltopdf

    Args:
        article_data: 文章数据
        article_url: 文章URL
        output_path: 输出路径

    Returns:
        bool: 是否成功
    """
    try:
        # 先生成HTML内容
        html_content = _generate_article_html(article_data, article_url)

        # 使用pdfkit生成PDF
        try:
            import pdfkit

            # 配置wkhtmltopdf路径
            config = pdfkit.configuration(wkhtmltopdf=r'D:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')

            # PDF生成选项
            options = {
                'page-size': 'A4',
                'margin-top': '0.75in',
                'margin-right': '0.75in',
                'margin-bottom': '0.75in',
                'margin-left': '0.75in',
                'encoding': "UTF-8",
                'no-outline': None,
                'enable-local-file-access': None,
                'print-media-type': None,
                'disable-smart-shrinking': None,
                'zoom': 1.0
            }

            # 生成PDF
            pdfkit.from_string(html_content, output_path, options=options, configuration=config)
            logger.info(f"PDF生成成功: {output_path}")
            return True

        except ImportError:
            logger.warning("pdfkit未安装，尝试使用备用方法")
        except Exception as pdf_error:
            logger.warning(f"PDF生成失败: {pdf_error}，尝试使用备用方法")

        # 备用方案：保存为HTML文件
        html_path = output_path.replace('.pdf', '.html')
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 重命名为PDF扩展名（实际是HTML）
        import shutil
        shutil.move(html_path, output_path)

        logger.info("PDF生成功能不可用，已生成HTML文件")
        return True

    except Exception as e:
        logger.error(f"PDF生成失败: {e}")
        return False

def _send_html_email(email: str, article_data: dict, html_path: str):
    """
    发送HTML文件邮件

    Args:
        email: 接收邮箱
        article_data: 文章数据
        html_path: HTML文件路径
    """
    try:
        # 创建邮件服务
        mailer = create_email_service()
        if not mailer:
            logger.error("邮件服务未配置")
            return

        # 根据文件类型确定邮件内容
        file_extension = html_path.split('.')[-1].lower()

        if file_extension == 'html':
            file_type_name = "HTML文件"
            file_icon = "📄"
            advantages = [
                "✅ 保留完整的文章内容和格式",
                "✅ 代码块语法高亮显示",
                "✅ 可以复制文本和代码",
                "✅ 文件体积小，加载快速",
                "✅ 支持所有浏览器打开"
            ]
            usage_tip = "下载附件后用浏览器打开即可查看完整内容。"
        elif file_extension == 'md':
            file_type_name = "Markdown文件"
            file_icon = "📝"
            advantages = [
                "✅ 纯文本格式，兼容性强",
                "✅ 支持所有Markdown编辑器",
                "✅ 可以直接编辑和修改",
                "✅ 文件体积极小",
                "✅ 适合技术文档管理"
            ]
            usage_tip = "下载附件后用任何文本编辑器或Markdown编辑器打开。"
        elif file_extension == 'pdf':
            file_type_name = "PDF文件"
            file_icon = "📋"
            advantages = [
                "✅ 格式固定，打印友好",
                "✅ 支持所有设备查看",
                "✅ 专业文档格式",
                "✅ 便于分享和存档",
                "✅ 保持原始排版"
            ]
            usage_tip = "下载附件后用PDF阅读器打开即可查看。"
        else:
            file_type_name = "文件"
            file_icon = "📎"
            advantages = ["✅ 完整保存文章内容"]
            usage_tip = "请下载附件查看内容。"

        # 构建邮件内容
        subject = f"CSDN文章{file_type_name} - {article_data.get('title', '未知标题')}"

        advantages_html = '\n'.join([f"<li>{adv}</li>" for adv in advantages])

        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                    🎯 SuperSpider - CSDN文章{file_type_name}
                </h2>

                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #2c3e50;">📄 文章信息</h3>
                    <p><strong>标题：</strong>{article_data.get('title', '未知标题')}</p>
                    <p><strong>作者：</strong>{article_data.get('author', '未知作者')}</p>
                    <p><strong>发布时间：</strong>{article_data.get('publish_time', '未知时间')}</p>
                    <p><strong>阅读量：</strong>{article_data.get('read_count', '0')}</p>
                    <p><strong>原文链接：</strong><a href="{article_data.get('url', '')}" target="_blank">{article_data.get('url', '')}</a></p>
                </div>

                <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #27ae60;">{file_icon} 文章{file_type_name}</h3>
                    <p>文章的完整内容已保存为{file_type_name}并作为附件发送。</p>
                    <p><strong>🌟 {file_type_name}优势：</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        {advantages_html}
                    </ul>
                    <p><strong>💡 使用方法：</strong>{usage_tip}</p>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
                    <p>此邮件由 SuperSpider 自动发送</p>
                    <p style="font-size: 12px;">如有问题，请联系技术支持</p>
                </div>
            </div>
        </body>
        </html>
        """

        # 发送邮件
        success = mailer.send_email_with_attachment(
            to_email=email,
            subject=subject,
            html_content=html_content,
            attachment_path=html_path,
            attachment_name=f"{article_data.get('title', 'CSDN文章')}.{file_extension}"
        )

        if success:
            logger.info(f"邮件发送成功: {email}")
        else:
            logger.error(f"邮件发送失败: {email}")

        # 清理临时文件
        try:
            if os.path.exists(html_path):
                os.remove(html_path)
                logger.info(f"已清理临时文件: {html_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"发送邮件失败: {e}")
        logger.error(traceback.format_exc())

def _send_screenshot_email(email: str, article_data: dict, screenshot_path: str):
    """
    发送截图邮件

    Args:
        email: 接收邮箱
        article_data: 文章数据
        screenshot_path: 截图文件路径
    """
    try:
        # 创建邮件服务
        mailer = create_email_service()
        if not mailer:
            logger.error("邮件服务未配置")
            return

        # 构建邮件内容
        subject = f"CSDN文章截图 - {article_data.get('title', '未知标题')}"

        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                    CSDN文章截图
                </h2>

                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #2c3e50;">文章信息</h3>
                    <p><strong>标题：</strong>{article_data.get('title', '未知标题')}</p>
                    <p><strong>作者：</strong>{article_data.get('author', '未知作者')}</p>
                    <p><strong>发布时间：</strong>{article_data.get('publish_time', '未知时间')}</p>
                    <p><strong>阅读量：</strong>{article_data.get('read_count', '0')}</p>
                    <p><strong>原文链接：</strong><a href="{article_data.get('url', '')}" target="_blank">{article_data.get('url', '')}</a></p>
                </div>

                <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #27ae60;">📸 文章截图</h3>
                    <p>文章的完整截图已作为附件发送，请查看附件获取完整内容。</p>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
                    <p>此邮件由 SuperSpider 自动发送</p>
                    <p style="font-size: 12px;">如有问题，请联系技术支持</p>
                </div>
            </div>
        </body>
        </html>
        """

        # 发送邮件
        success = mailer.send_email_with_attachment(
            to_email=email,
            subject=subject,
            html_content=html_content,
            attachment_path=screenshot_path,
            attachment_name=f"{article_data.get('title', 'CSDN文章')}_截图.png"
        )

        if success:
            logger.info(f"邮件发送成功: {email}")
        else:
            logger.error(f"邮件发送失败: {email}")

        # 清理临时文件
        try:
            if os.path.exists(screenshot_path):
                os.remove(screenshot_path)
                logger.info(f"已清理临时文件: {screenshot_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"发送邮件失败: {e}")
        logger.error(traceback.format_exc())