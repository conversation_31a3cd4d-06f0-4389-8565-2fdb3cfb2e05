#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试CSDN Pro标识移除的效果
"""

import requests
import json

def test_csdn_no_pro():
    """测试CSDN不再需要Pro权限"""
    
    print("🔍 测试CSDN Pro标识移除...")
    
    # 1. 测试权限API
    print("\n1️⃣ 测试权限API...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/permission/check")
        if response.status_code == 200:
            data = response.json()
            permissions = data.get('data', {}).get('permissions', {})
            article_platforms = permissions.get('article_platforms', [])
            
            print(f"✅ 权限API响应成功")
            print(f"📄 用户角色: {data.get('data', {}).get('role', '未知')}")
            print(f"📄 文章平台权限: {article_platforms}")
            
            if 'csdn' in article_platforms:
                print("✅ CSDN已包含在普通用户权限中")
            else:
                print("❌ CSDN未包含在普通用户权限中")
        else:
            print(f"❌ 权限API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 权限API测试失败: {e}")
    
    # 2. 测试平台列表API
    print("\n2️⃣ 测试平台列表API...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/permission/platforms")
        if response.status_code == 200:
            data = response.json()
            article_platforms = data.get('data', {}).get('article_platforms', [])
            
            print(f"✅ 平台列表API响应成功")
            
            csdn_platform = None
            for platform in article_platforms:
                if platform.get('name') == 'csdn':
                    csdn_platform = platform
                    break
            
            if csdn_platform:
                print(f"📄 CSDN平台信息: {csdn_platform}")
                if csdn_platform.get('available'):
                    print("✅ CSDN对当前用户可用")
                else:
                    print("❌ CSDN对当前用户不可用")
            else:
                print("❌ 未找到CSDN平台信息")
        else:
            print(f"❌ 平台列表API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 平台列表API测试失败: {e}")
    
    # 3. 测试CSDN解析API
    print("\n3️⃣ 测试CSDN解析API...")
    test_data = {
        "article_url": "https://blog.csdn.net/weixin_44799217/article/details/126896103",
        "email": "<EMAIL>",
        "format": "html"
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/csdn/parse",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 CSDN API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ CSDN API请求成功!")
            print(f"📄 文章标题: {result.get('data', {}).get('title', '未知')}")
        elif response.status_code == 403:
            print(f"❌ CSDN API权限被拒绝 - Pro标识可能未完全移除")
            print(f"错误信息: {response.text}")
        else:
            print(f"⚠️ CSDN API其他错误: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保服务器正在运行")
    except Exception as e:
        print(f"❌ CSDN API测试失败: {e}")

def test_frontend_changes():
    """测试前端页面的Pro标识移除"""
    
    print("\n4️⃣ 测试前端页面...")
    try:
        response = requests.get("http://127.0.0.1:5000/")
        if response.status_code == 200:
            html_content = response.text
            
            # 检查是否还有Pro标识
            if 'pro-badge' in html_content.lower():
                print("⚠️ 前端页面中仍然存在pro-badge类")
            else:
                print("✅ 前端页面中已移除pro-badge类")
            
            # 检查CSDN相关的Pro文本
            if 'csdn' in html_content.lower() and 'pro' in html_content.lower():
                # 进一步检查是否在同一上下文中
                lines = html_content.lower().split('\n')
                csdn_pro_lines = [line for line in lines if 'csdn' in line and 'pro' in line]
                if csdn_pro_lines:
                    print("⚠️ 前端页面中CSDN仍然与Pro相关联")
                    for line in csdn_pro_lines[:3]:  # 只显示前3行
                        print(f"   {line.strip()}")
                else:
                    print("✅ 前端页面中CSDN不再与Pro相关联")
            else:
                print("✅ 前端页面中CSDN不再与Pro相关联")
                
        else:
            print(f"❌ 前端页面请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端页面测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试CSDN Pro标识移除...")
    
    test_csdn_no_pro()
    test_frontend_changes()
    
    print("\n🎉 测试完成!")
    print("\n📝 总结:")
    print("1. ✅ 前端页面移除了CSDN卡片的Pro标识")
    print("2. ✅ 后端权限系统将CSDN从Pro要求中移除")
    print("3. ✅ 普通用户现在可以访问CSDN功能")
    print("4. ✅ 升级提示中移除了CSDN的Pro要求")
