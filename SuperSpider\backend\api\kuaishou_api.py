#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快手API模块
提供快手视频解析相关的API
"""

import logging
import traceback

from flask import Blueprint, request, jsonify
from flask_login import login_required

from ..spiders.kuaishou_spider import <PERSON><PERSON>houSpider
from ..utils.permissions import require_permission

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
kuaishou_api = Blueprint('kuaishou_api', __name__, url_prefix='/kuaishou')

@kuaishou_api.route('/parse', methods=['POST'])
@require_permission('platform', 'kuaishou')
def parse_video():
    """
    解析快手视频链接

    请求体:
        {
            "video_url": "视频URL"
        }

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": [
                {
                    "title": "视频标题",
                    "videoUrl": "视频链接",
                    "author": "作者名字"
                }
            ]
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        video_url = data.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建快手爬虫
        spider = KuaishouSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

@kuaishou_api.route('/status', methods=['GET'])
def check_status():
    """
    检查快手API服务状态

    响应:
        {
            "success": true,
            "message": "服务正常",
            "data": {
                "spider": {
                    "name": "快手爬虫",
                    "status": "ready"
                }
            }
        }
    """
    try:
        # 创建爬虫实例
        spider = KuaishouSpider()
        spider_status = spider.check_status()

        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "spider": spider_status
            }
        }), 200

    except Exception as e:
        logger.error(f"检查状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": None
        }), 500

# 添加GET方法的解析接口
@kuaishou_api.route('/parse', methods=['GET'])
@require_permission('platform', 'kuaishou')
def parse_video_get():
    """
    解析快手视频链接（GET方法）

    查询参数:
        video_url: 视频URL

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": [
                {
                    "title": "视频标题",
                    "videoUrl": "视频链接",
                    "author": "作者名字"
                }
            ]
        }
    """
    try:
        video_url = request.args.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建快手爬虫
        spider = KuaishouSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500