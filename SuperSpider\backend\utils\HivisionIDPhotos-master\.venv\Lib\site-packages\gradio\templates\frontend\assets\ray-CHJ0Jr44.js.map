{"version": 3, "file": "ray-CHJ0Jr44.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Culling/ray.core.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Culling/ray.js"], "sourcesContent": ["import { <PERSON>psilon } from \"../Maths/math.constants.js\";\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector.js\";\nimport { BuildArray } from \"../Misc/arrayTools.js\";\nimport { IntersectionInfo } from \"../Collisions/intersectionInfo.js\";\nimport { PickingInfo } from \"../Collisions/pickingInfo.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { _ImportHelper } from \"../import.helper.js\";\n/**\n * Use this object to customize mesh picking behavior\n */\nexport const PickingCustomization = {\n    internalPickerForMesh: undefined,\n};\n/**\n * Class representing a ray with position and direction\n */\nexport class Ray {\n    /**\n     * Creates a new ray\n     * @param origin origin point\n     * @param direction direction\n     * @param length length of the ray\n     * @param epsilon The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants)\n     */\n    constructor(\n    /** origin point */\n    origin, \n    /** direction */\n    direction, \n    /** [Number.MAX_VALUE] length of the ray */\n    length = Number.MAX_VALUE, \n    /** [Epsilon] The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants) */\n    epsilon = Epsilon) {\n        this.origin = origin;\n        this.direction = direction;\n        this.length = length;\n        this.epsilon = epsilon;\n    }\n    // Methods\n    /**\n     * Clone the current ray\n     * @returns a new ray\n     */\n    clone() {\n        return new Ray(this.origin.clone(), this.direction.clone(), this.length);\n    }\n    /**\n     * Checks if the ray intersects a box\n     * This does not account for the ray length by design to improve perfs.\n     * @param minimum bound of the box\n     * @param maximum bound of the box\n     * @param intersectionTreshold extra extend to be added to the box in all direction\n     * @returns if the box was hit\n     */\n    intersectsBoxMinMax(minimum, maximum, intersectionTreshold = 0) {\n        const newMinimum = Ray._TmpVector3[0].copyFromFloats(minimum.x - intersectionTreshold, minimum.y - intersectionTreshold, minimum.z - intersectionTreshold);\n        const newMaximum = Ray._TmpVector3[1].copyFromFloats(maximum.x + intersectionTreshold, maximum.y + intersectionTreshold, maximum.z + intersectionTreshold);\n        let d = 0.0;\n        let maxValue = Number.MAX_VALUE;\n        let inv;\n        let min;\n        let max;\n        let temp;\n        if (Math.abs(this.direction.x) < 0.0000001) {\n            if (this.origin.x < newMinimum.x || this.origin.x > newMaximum.x) {\n                return false;\n            }\n        }\n        else {\n            inv = 1.0 / this.direction.x;\n            min = (newMinimum.x - this.origin.x) * inv;\n            max = (newMaximum.x - this.origin.x) * inv;\n            if (max === -Infinity) {\n                max = Infinity;\n            }\n            if (min > max) {\n                temp = min;\n                min = max;\n                max = temp;\n            }\n            d = Math.max(min, d);\n            maxValue = Math.min(max, maxValue);\n            if (d > maxValue) {\n                return false;\n            }\n        }\n        if (Math.abs(this.direction.y) < 0.0000001) {\n            if (this.origin.y < newMinimum.y || this.origin.y > newMaximum.y) {\n                return false;\n            }\n        }\n        else {\n            inv = 1.0 / this.direction.y;\n            min = (newMinimum.y - this.origin.y) * inv;\n            max = (newMaximum.y - this.origin.y) * inv;\n            if (max === -Infinity) {\n                max = Infinity;\n            }\n            if (min > max) {\n                temp = min;\n                min = max;\n                max = temp;\n            }\n            d = Math.max(min, d);\n            maxValue = Math.min(max, maxValue);\n            if (d > maxValue) {\n                return false;\n            }\n        }\n        if (Math.abs(this.direction.z) < 0.0000001) {\n            if (this.origin.z < newMinimum.z || this.origin.z > newMaximum.z) {\n                return false;\n            }\n        }\n        else {\n            inv = 1.0 / this.direction.z;\n            min = (newMinimum.z - this.origin.z) * inv;\n            max = (newMaximum.z - this.origin.z) * inv;\n            if (max === -Infinity) {\n                max = Infinity;\n            }\n            if (min > max) {\n                temp = min;\n                min = max;\n                max = temp;\n            }\n            d = Math.max(min, d);\n            maxValue = Math.min(max, maxValue);\n            if (d > maxValue) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Checks if the ray intersects a box\n     * This does not account for the ray lenght by design to improve perfs.\n     * @param box the bounding box to check\n     * @param intersectionTreshold extra extend to be added to the BoundingBox in all direction\n     * @returns if the box was hit\n     */\n    intersectsBox(box, intersectionTreshold = 0) {\n        return this.intersectsBoxMinMax(box.minimum, box.maximum, intersectionTreshold);\n    }\n    /**\n     * If the ray hits a sphere\n     * @param sphere the bounding sphere to check\n     * @param intersectionTreshold extra extend to be added to the BoundingSphere in all direction\n     * @returns true if it hits the sphere\n     */\n    intersectsSphere(sphere, intersectionTreshold = 0) {\n        const x = sphere.center.x - this.origin.x;\n        const y = sphere.center.y - this.origin.y;\n        const z = sphere.center.z - this.origin.z;\n        const pyth = x * x + y * y + z * z;\n        const radius = sphere.radius + intersectionTreshold;\n        const rr = radius * radius;\n        if (pyth <= rr) {\n            return true;\n        }\n        const dot = x * this.direction.x + y * this.direction.y + z * this.direction.z;\n        if (dot < 0.0) {\n            return false;\n        }\n        const temp = pyth - dot * dot;\n        return temp <= rr;\n    }\n    /**\n     * If the ray hits a triange\n     * @param vertex0 triangle vertex\n     * @param vertex1 triangle vertex\n     * @param vertex2 triangle vertex\n     * @returns intersection information if hit\n     */\n    intersectsTriangle(vertex0, vertex1, vertex2) {\n        const edge1 = Ray._TmpVector3[0];\n        const edge2 = Ray._TmpVector3[1];\n        const pvec = Ray._TmpVector3[2];\n        const tvec = Ray._TmpVector3[3];\n        const qvec = Ray._TmpVector3[4];\n        vertex1.subtractToRef(vertex0, edge1);\n        vertex2.subtractToRef(vertex0, edge2);\n        Vector3.CrossToRef(this.direction, edge2, pvec);\n        const det = Vector3.Dot(edge1, pvec);\n        if (det === 0) {\n            return null;\n        }\n        const invdet = 1 / det;\n        this.origin.subtractToRef(vertex0, tvec);\n        const bv = Vector3.Dot(tvec, pvec) * invdet;\n        if (bv < -this.epsilon || bv > 1.0 + this.epsilon) {\n            return null;\n        }\n        Vector3.CrossToRef(tvec, edge1, qvec);\n        const bw = Vector3.Dot(this.direction, qvec) * invdet;\n        if (bw < -this.epsilon || bv + bw > 1.0 + this.epsilon) {\n            return null;\n        }\n        //check if the distance is longer than the predefined length.\n        const distance = Vector3.Dot(edge2, qvec) * invdet;\n        if (distance > this.length) {\n            return null;\n        }\n        return new IntersectionInfo(1 - bv - bw, bv, distance);\n    }\n    /**\n     * Checks if ray intersects a plane\n     * @param plane the plane to check\n     * @returns the distance away it was hit\n     */\n    intersectsPlane(plane) {\n        let distance;\n        const result1 = Vector3.Dot(plane.normal, this.direction);\n        if (Math.abs(result1) < 9.99999997475243e-7) {\n            return null;\n        }\n        else {\n            const result2 = Vector3.Dot(plane.normal, this.origin);\n            distance = (-plane.d - result2) / result1;\n            if (distance < 0.0) {\n                if (distance < -9.99999997475243e-7) {\n                    return null;\n                }\n                else {\n                    return 0;\n                }\n            }\n            return distance;\n        }\n    }\n    /**\n     * Calculate the intercept of a ray on a given axis\n     * @param axis to check 'x' | 'y' | 'z'\n     * @param offset from axis interception (i.e. an offset of 1y is intercepted above ground)\n     * @returns a vector containing the coordinates where 'axis' is equal to zero (else offset), or null if there is no intercept.\n     */\n    intersectsAxis(axis, offset = 0) {\n        switch (axis) {\n            case \"y\": {\n                const t = (this.origin.y - offset) / this.direction.y;\n                if (t > 0) {\n                    return null;\n                }\n                return new Vector3(this.origin.x + this.direction.x * -t, offset, this.origin.z + this.direction.z * -t);\n            }\n            case \"x\": {\n                const t = (this.origin.x - offset) / this.direction.x;\n                if (t > 0) {\n                    return null;\n                }\n                return new Vector3(offset, this.origin.y + this.direction.y * -t, this.origin.z + this.direction.z * -t);\n            }\n            case \"z\": {\n                const t = (this.origin.z - offset) / this.direction.z;\n                if (t > 0) {\n                    return null;\n                }\n                return new Vector3(this.origin.x + this.direction.x * -t, this.origin.y + this.direction.y * -t, offset);\n            }\n            default:\n                return null;\n        }\n    }\n    /**\n     * Checks if ray intersects a mesh. The ray is defined in WORLD space. A mesh triangle can be picked both from its front and back sides,\n     * irrespective of orientation.\n     * @param mesh the mesh to check\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\n     * @param onlyBoundingInfo defines a boolean indicating if picking should only happen using bounding info (false by default)\n     * @param worldToUse defines the world matrix to use to get the world coordinate of the intersection point\n     * @param skipBoundingInfo a boolean indicating if we should skip the bounding info check\n     * @returns picking info of the intersection\n     */\n    intersectsMesh(mesh, fastCheck, trianglePredicate, onlyBoundingInfo = false, worldToUse, skipBoundingInfo = false) {\n        const tm = TmpVectors.Matrix[0];\n        mesh.getWorldMatrix().invertToRef(tm);\n        if (this._tmpRay) {\n            Ray.TransformToRef(this, tm, this._tmpRay);\n        }\n        else {\n            this._tmpRay = Ray.Transform(this, tm);\n        }\n        return mesh.intersects(this._tmpRay, fastCheck, trianglePredicate, onlyBoundingInfo, worldToUse, skipBoundingInfo);\n    }\n    /**\n     * Checks if ray intersects a mesh\n     * @param meshes the meshes to check\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\n     * @param results array to store result in\n     * @returns Array of picking infos\n     */\n    intersectsMeshes(meshes, fastCheck, results) {\n        if (results) {\n            results.length = 0;\n        }\n        else {\n            results = [];\n        }\n        for (let i = 0; i < meshes.length; i++) {\n            const pickInfo = this.intersectsMesh(meshes[i], fastCheck);\n            if (pickInfo.hit) {\n                results.push(pickInfo);\n            }\n        }\n        results.sort(this._comparePickingInfo);\n        return results;\n    }\n    _comparePickingInfo(pickingInfoA, pickingInfoB) {\n        if (pickingInfoA.distance < pickingInfoB.distance) {\n            return -1;\n        }\n        else if (pickingInfoA.distance > pickingInfoB.distance) {\n            return 1;\n        }\n        else {\n            return 0;\n        }\n    }\n    /**\n     * Intersection test between the ray and a given segment within a given tolerance (threshold)\n     * @param sega the first point of the segment to test the intersection against\n     * @param segb the second point of the segment to test the intersection against\n     * @param threshold the tolerance margin, if the ray doesn't intersect the segment but is close to the given threshold, the intersection is successful\n     * @returns the distance from the ray origin to the intersection point if there's intersection, or -1 if there's no intersection\n     */\n    intersectionSegment(sega, segb, threshold) {\n        const o = this.origin;\n        const u = TmpVectors.Vector3[0];\n        const rsegb = TmpVectors.Vector3[1];\n        const v = TmpVectors.Vector3[2];\n        const w = TmpVectors.Vector3[3];\n        segb.subtractToRef(sega, u);\n        this.direction.scaleToRef(Ray._Rayl, v);\n        o.addToRef(v, rsegb);\n        sega.subtractToRef(o, w);\n        const a = Vector3.Dot(u, u); // always >= 0\n        const b = Vector3.Dot(u, v);\n        const c = Vector3.Dot(v, v); // always >= 0\n        const d = Vector3.Dot(u, w);\n        const e = Vector3.Dot(v, w);\n        const D = a * c - b * b; // always >= 0\n        let sN, sD = D; // sc = sN / sD, default sD = D >= 0\n        let tN, tD = D; // tc = tN / tD, default tD = D >= 0\n        // compute the line parameters of the two closest points\n        if (D < Ray._Smallnum) {\n            // the lines are almost parallel\n            sN = 0.0; // force using point P0 on segment S1\n            sD = 1.0; // to prevent possible division by 0.0 later\n            tN = e;\n            tD = c;\n        }\n        else {\n            // get the closest points on the infinite lines\n            sN = b * e - c * d;\n            tN = a * e - b * d;\n            if (sN < 0.0) {\n                // sc < 0 => the s=0 edge is visible\n                sN = 0.0;\n                tN = e;\n                tD = c;\n            }\n            else if (sN > sD) {\n                // sc > 1 => the s=1 edge is visible\n                sN = sD;\n                tN = e + b;\n                tD = c;\n            }\n        }\n        if (tN < 0.0) {\n            // tc < 0 => the t=0 edge is visible\n            tN = 0.0;\n            // recompute sc for this edge\n            if (-d < 0.0) {\n                sN = 0.0;\n            }\n            else if (-d > a) {\n                sN = sD;\n            }\n            else {\n                sN = -d;\n                sD = a;\n            }\n        }\n        else if (tN > tD) {\n            // tc > 1 => the t=1 edge is visible\n            tN = tD;\n            // recompute sc for this edge\n            if (-d + b < 0.0) {\n                sN = 0;\n            }\n            else if (-d + b > a) {\n                sN = sD;\n            }\n            else {\n                sN = -d + b;\n                sD = a;\n            }\n        }\n        // finally do the division to get sc and tc\n        const sc = Math.abs(sN) < Ray._Smallnum ? 0.0 : sN / sD;\n        const tc = Math.abs(tN) < Ray._Smallnum ? 0.0 : tN / tD;\n        // get the difference of the two closest points\n        const qtc = TmpVectors.Vector3[4];\n        v.scaleToRef(tc, qtc);\n        const qsc = TmpVectors.Vector3[5];\n        u.scaleToRef(sc, qsc);\n        qsc.addInPlace(w);\n        const dP = TmpVectors.Vector3[6];\n        qsc.subtractToRef(qtc, dP); // = S1(sc) - S2(tc)\n        const isIntersected = tc > 0 && tc <= this.length && dP.lengthSquared() < threshold * threshold; // return intersection result\n        if (isIntersected) {\n            return qsc.length();\n        }\n        return -1;\n    }\n    /**\n     * Update the ray from viewport position\n     * @param x position\n     * @param y y position\n     * @param viewportWidth viewport width\n     * @param viewportHeight viewport height\n     * @param world world matrix\n     * @param view view matrix\n     * @param projection projection matrix\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\n     * @returns this ray updated\n     */\n    update(x, y, viewportWidth, viewportHeight, world, view, projection, enableDistantPicking = false) {\n        if (enableDistantPicking) {\n            // With world matrices having great values (like 8000000000 on 1 or more scaling or position axis),\n            // multiplying view/projection/world and doing invert will result in loss of float precision in the matrix.\n            // One way to fix it is to compute the ray with world at identity then transform the ray in object space.\n            // This is slower (2 matrix inverts instead of 1) but precision is preserved.\n            // This is hidden behind `EnableDistantPicking` flag (default is false)\n            if (!Ray._RayDistant) {\n                Ray._RayDistant = Ray.Zero();\n            }\n            Ray._RayDistant.unprojectRayToRef(x, y, viewportWidth, viewportHeight, Matrix.IdentityReadOnly, view, projection);\n            const tm = TmpVectors.Matrix[0];\n            world.invertToRef(tm);\n            Ray.TransformToRef(Ray._RayDistant, tm, this);\n        }\n        else {\n            this.unprojectRayToRef(x, y, viewportWidth, viewportHeight, world, view, projection);\n        }\n        return this;\n    }\n    // Statics\n    /**\n     * Creates a ray with origin and direction of 0,0,0\n     * @returns the new ray\n     */\n    static Zero() {\n        return new Ray(Vector3.Zero(), Vector3.Zero());\n    }\n    /**\n     * Creates a new ray from screen space and viewport\n     * @param x position\n     * @param y y position\n     * @param viewportWidth viewport width\n     * @param viewportHeight viewport height\n     * @param world world matrix\n     * @param view view matrix\n     * @param projection projection matrix\n     * @returns new ray\n     */\n    static CreateNew(x, y, viewportWidth, viewportHeight, world, view, projection) {\n        const result = Ray.Zero();\n        return result.update(x, y, viewportWidth, viewportHeight, world, view, projection);\n    }\n    /**\n     * Function will create a new transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\n     * transformed to the given world matrix.\n     * @param origin The origin point\n     * @param end The end point\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\n     * @returns the new ray\n     */\n    static CreateNewFromTo(origin, end, world = Matrix.IdentityReadOnly) {\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\n        return Ray.CreateFromToToRef(origin, end, result, world);\n    }\n    /**\n     * Function will update a transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\n     * transformed to the given world matrix.\n     * @param origin The origin point\n     * @param end The end point\n     * @param result the object to store the result\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\n     * @returns the ref ray\n     */\n    static CreateFromToToRef(origin, end, result, world = Matrix.IdentityReadOnly) {\n        result.origin.copyFrom(origin);\n        const direction = end.subtractToRef(origin, result.direction);\n        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);\n        result.length = length;\n        result.direction.normalize();\n        return Ray.TransformToRef(result, world, result);\n    }\n    /**\n     * Transforms a ray by a matrix\n     * @param ray ray to transform\n     * @param matrix matrix to apply\n     * @returns the resulting new ray\n     */\n    static Transform(ray, matrix) {\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\n        Ray.TransformToRef(ray, matrix, result);\n        return result;\n    }\n    /**\n     * Transforms a ray by a matrix\n     * @param ray ray to transform\n     * @param matrix matrix to apply\n     * @param result ray to store result in\n     * @returns the updated result ray\n     */\n    static TransformToRef(ray, matrix, result) {\n        Vector3.TransformCoordinatesToRef(ray.origin, matrix, result.origin);\n        Vector3.TransformNormalToRef(ray.direction, matrix, result.direction);\n        result.length = ray.length;\n        result.epsilon = ray.epsilon;\n        const dir = result.direction;\n        const len = dir.length();\n        if (!(len === 0 || len === 1)) {\n            const num = 1.0 / len;\n            dir.x *= num;\n            dir.y *= num;\n            dir.z *= num;\n            result.length *= len;\n        }\n        return result;\n    }\n    /**\n     * Unproject a ray from screen space to object space\n     * @param sourceX defines the screen space x coordinate to use\n     * @param sourceY defines the screen space y coordinate to use\n     * @param viewportWidth defines the current width of the viewport\n     * @param viewportHeight defines the current height of the viewport\n     * @param world defines the world matrix to use (can be set to Identity to go to world space)\n     * @param view defines the view matrix to use\n     * @param projection defines the projection matrix to use\n     */\n    unprojectRayToRef(sourceX, sourceY, viewportWidth, viewportHeight, world, view, projection) {\n        const matrix = TmpVectors.Matrix[0];\n        world.multiplyToRef(view, matrix);\n        matrix.multiplyToRef(projection, matrix);\n        matrix.invert();\n        const engine = EngineStore.LastCreatedEngine;\n        const nearScreenSource = TmpVectors.Vector3[0];\n        nearScreenSource.x = (sourceX / viewportWidth) * 2 - 1;\n        nearScreenSource.y = -((sourceY / viewportHeight) * 2 - 1);\n        nearScreenSource.z = engine?.useReverseDepthBuffer ? 1 : engine?.isNDCHalfZRange ? 0 : -1;\n        // far Z need to be close but < to 1 or camera projection matrix with maxZ = 0 will NaN\n        const farScreenSource = TmpVectors.Vector3[1].copyFromFloats(nearScreenSource.x, nearScreenSource.y, 1.0 - 1e-8);\n        const nearVec3 = TmpVectors.Vector3[2];\n        const farVec3 = TmpVectors.Vector3[3];\n        Vector3._UnprojectFromInvertedMatrixToRef(nearScreenSource, matrix, nearVec3);\n        Vector3._UnprojectFromInvertedMatrixToRef(farScreenSource, matrix, farVec3);\n        this.origin.copyFrom(nearVec3);\n        farVec3.subtractToRef(nearVec3, this.direction);\n        this.direction.normalize();\n    }\n}\nRay._TmpVector3 = BuildArray(6, Vector3.Zero);\nRay._RayDistant = Ray.Zero();\nRay._Smallnum = 0.00000001;\nRay._Rayl = 10e8;\n/**\n * Creates a ray that can be used to pick in the scene\n * @param scene defines the scene to use for the picking\n * @param x defines the x coordinate of the origin (on-screen)\n * @param y defines the y coordinate of the origin (on-screen)\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\n * @param camera defines the camera to use for the picking\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\n * @returns a Ray\n */\nexport function CreatePickingRay(scene, x, y, world, camera, cameraViewSpace = false) {\n    const result = Ray.Zero();\n    CreatePickingRayToRef(scene, x, y, world, result, camera, cameraViewSpace);\n    return result;\n}\n/**\n * Creates a ray that can be used to pick in the scene\n * @param scene defines the scene to use for the picking\n * @param x defines the x coordinate of the origin (on-screen)\n * @param y defines the y coordinate of the origin (on-screen)\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\n * @param result defines the ray where to store the picking ray\n * @param camera defines the camera to use for the picking\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\n * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\n * @returns the current scene\n */\nexport function CreatePickingRayToRef(scene, x, y, world, result, camera, cameraViewSpace = false, enableDistantPicking = false) {\n    const engine = scene.getEngine();\n    if (!camera && !(camera = scene.activeCamera)) {\n        return scene;\n    }\n    const cameraViewport = camera.viewport;\n    const renderHeight = engine.getRenderHeight();\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\n    // Moving coordinates to local viewport world\n    const levelInv = 1 / engine.getHardwareScalingLevel();\n    x = x * levelInv - vx;\n    y = y * levelInv - (renderHeight - vy - height);\n    result.update(x, y, width, height, world ? world : Matrix.IdentityReadOnly, cameraViewSpace ? Matrix.IdentityReadOnly : camera.getViewMatrix(), camera.getProjectionMatrix(), enableDistantPicking);\n    return scene;\n}\n/**\n * Creates a ray that can be used to pick in the scene\n * @param scene defines the scene to use for the picking\n * @param x defines the x coordinate of the origin (on-screen)\n * @param y defines the y coordinate of the origin (on-screen)\n * @param camera defines the camera to use for the picking\n * @returns a Ray\n */\nexport function CreatePickingRayInCameraSpace(scene, x, y, camera) {\n    const result = Ray.Zero();\n    CreatePickingRayInCameraSpaceToRef(scene, x, y, result, camera);\n    return result;\n}\n/**\n * Creates a ray that can be used to pick in the scene\n * @param scene defines the scene to use for the picking\n * @param x defines the x coordinate of the origin (on-screen)\n * @param y defines the y coordinate of the origin (on-screen)\n * @param result defines the ray where to store the picking ray\n * @param camera defines the camera to use for the picking\n * @returns the current scene\n */\nexport function CreatePickingRayInCameraSpaceToRef(scene, x, y, result, camera) {\n    if (!PickingInfo) {\n        return scene;\n    }\n    const engine = scene.getEngine();\n    if (!camera && !(camera = scene.activeCamera)) {\n        throw new Error(\"Active camera not set\");\n    }\n    const cameraViewport = camera.viewport;\n    const renderHeight = engine.getRenderHeight();\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\n    const identity = Matrix.Identity();\n    // Moving coordinates to local viewport world\n    const levelInv = 1 / engine.getHardwareScalingLevel();\n    x = x * levelInv - vx;\n    y = y * levelInv - (renderHeight - vy - height);\n    result.update(x, y, width, height, identity, identity, camera.getProjectionMatrix());\n    return scene;\n}\nfunction InternalPickForMesh(pickingInfo, rayFunction, mesh, world, fastCheck, onlyBoundingInfo, trianglePredicate, skipBoundingInfo) {\n    const ray = rayFunction(world, mesh.enableDistantPicking);\n    const result = mesh.intersects(ray, fastCheck, trianglePredicate, onlyBoundingInfo, world, skipBoundingInfo);\n    if (!result || !result.hit) {\n        return null;\n    }\n    if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\n        return null;\n    }\n    return result;\n}\nfunction InternalPick(scene, rayFunction, predicate, fastCheck, onlyBoundingInfo, trianglePredicate) {\n    let pickingInfo = null;\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\n        const mesh = scene.meshes[meshIndex];\n        if (predicate) {\n            if (!predicate(mesh, -1)) {\n                continue;\n            }\n        }\n        else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\n            continue;\n        }\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\n        if (mesh.hasThinInstances && mesh.thinInstanceEnablePicking) {\n            // first check if the ray intersects the whole bounding box/sphere of the mesh\n            const result = picker(pickingInfo, rayFunction, mesh, world, true, true, trianglePredicate);\n            if (result) {\n                if (onlyBoundingInfo) {\n                    // the user only asked for a bounding info check so we can return\n                    return result;\n                }\n                const tmpMatrix = TmpVectors.Matrix[1];\n                const thinMatrices = mesh.thinInstanceGetWorldMatrices();\n                for (let index = 0; index < thinMatrices.length; index++) {\n                    if (predicate && !predicate(mesh, index)) {\n                        continue;\n                    }\n                    const thinMatrix = thinMatrices[index];\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\n                    const result = picker(pickingInfo, rayFunction, mesh, tmpMatrix, fastCheck, onlyBoundingInfo, trianglePredicate, true);\n                    if (result) {\n                        pickingInfo = result;\n                        pickingInfo.thinInstanceIndex = index;\n                        if (fastCheck) {\n                            return pickingInfo;\n                        }\n                    }\n                }\n            }\n        }\n        else {\n            const result = picker(pickingInfo, rayFunction, mesh, world, fastCheck, onlyBoundingInfo, trianglePredicate);\n            if (result) {\n                pickingInfo = result;\n                if (fastCheck) {\n                    return pickingInfo;\n                }\n            }\n        }\n    }\n    return pickingInfo || new PickingInfo();\n}\nfunction InternalMultiPick(scene, rayFunction, predicate, trianglePredicate) {\n    if (!PickingInfo) {\n        return null;\n    }\n    const pickingInfos = [];\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\n        const mesh = scene.meshes[meshIndex];\n        if (predicate) {\n            if (!predicate(mesh, -1)) {\n                continue;\n            }\n        }\n        else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\n            continue;\n        }\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\n        if (mesh.hasThinInstances && mesh.thinInstanceEnablePicking) {\n            const result = picker(null, rayFunction, mesh, world, true, true, trianglePredicate);\n            if (result) {\n                const tmpMatrix = TmpVectors.Matrix[1];\n                const thinMatrices = mesh.thinInstanceGetWorldMatrices();\n                for (let index = 0; index < thinMatrices.length; index++) {\n                    if (predicate && !predicate(mesh, index)) {\n                        continue;\n                    }\n                    const thinMatrix = thinMatrices[index];\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\n                    const result = picker(null, rayFunction, mesh, tmpMatrix, false, false, trianglePredicate, true);\n                    if (result) {\n                        result.thinInstanceIndex = index;\n                        pickingInfos.push(result);\n                    }\n                }\n            }\n        }\n        else {\n            const result = picker(null, rayFunction, mesh, world, false, false, trianglePredicate);\n            if (result) {\n                pickingInfos.push(result);\n            }\n        }\n    }\n    return pickingInfos;\n}\n/** Launch a ray to try to pick a mesh in the scene using only bounding information of the main mesh (not using submeshes)\n * @param scene defines the scene to use for the picking\n * @param x position on screen\n * @param y position on screen\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\n * @param fastCheck defines if the first intersection will be used (and not the closest)\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\n * @returns a PickingInfo (Please note that some info will not be set like distance, bv, bu and everything that cannot be capture by only using bounding infos)\n */\nexport function PickWithBoundingInfo(scene, x, y, predicate, fastCheck, camera) {\n    if (!PickingInfo) {\n        return null;\n    }\n    const result = InternalPick(scene, (world) => {\n        if (!scene._tempPickingRay) {\n            scene._tempPickingRay = Ray.Zero();\n        }\n        CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null);\n        return scene._tempPickingRay;\n    }, predicate, fastCheck, true);\n    if (result) {\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\n    }\n    return result;\n}\n/** Launch a ray to try to pick a mesh in the scene\n * @param scene defines the scene to use for the picking\n * @param x position on screen\n * @param y position on screen\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\n * @param fastCheck defines if the first intersection will be used (and not the closest)\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\n * @param _enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\n * @returns a PickingInfo\n */\nexport function Pick(scene, x, y, predicate, fastCheck, camera, trianglePredicate, _enableDistantPicking = false) {\n    const result = InternalPick(scene, (world, enableDistantPicking) => {\n        if (!scene._tempPickingRay) {\n            scene._tempPickingRay = Ray.Zero();\n        }\n        CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null, false, enableDistantPicking);\n        return scene._tempPickingRay;\n    }, predicate, fastCheck, false, trianglePredicate);\n    if (result) {\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\n    }\n    return result;\n}\n/**\n * Use the given ray to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\n * irrespective of orientation.\n * @param scene defines the scene to use for the picking\n * @param ray The ray to use to pick meshes\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must have isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\n * @param fastCheck defines if the first intersection will be used (and not the closest)\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\n * @returns a PickingInfo\n */\nexport function PickWithRay(scene, ray, predicate, fastCheck, trianglePredicate) {\n    const result = InternalPick(scene, (world) => {\n        if (!scene._pickWithRayInverseMatrix) {\n            scene._pickWithRayInverseMatrix = Matrix.Identity();\n        }\n        world.invertToRef(scene._pickWithRayInverseMatrix);\n        if (!scene._cachedRayForTransform) {\n            scene._cachedRayForTransform = Ray.Zero();\n        }\n        Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\n        return scene._cachedRayForTransform;\n    }, predicate, fastCheck, false, trianglePredicate);\n    if (result) {\n        result.ray = ray;\n    }\n    return result;\n}\n/**\n * Launch a ray to try to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\n * irrespective of orientation.\n * @param scene defines the scene to use for the picking\n * @param x X position on screen\n * @param y Y position on screen\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\n * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\n * @returns an array of PickingInfo\n */\nexport function MultiPick(scene, x, y, predicate, camera, trianglePredicate) {\n    return InternalMultiPick(scene, (world) => CreatePickingRay(scene, x, y, world, camera || null), predicate, trianglePredicate);\n}\n/**\n * Launch a ray to try to pick a mesh in the scene\n * @param scene defines the scene to use for the picking\n * @param ray Ray to use\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\n * @returns an array of PickingInfo\n */\nexport function MultiPickWithRay(scene, ray, predicate, trianglePredicate) {\n    return InternalMultiPick(scene, (world) => {\n        if (!scene._pickWithRayInverseMatrix) {\n            scene._pickWithRayInverseMatrix = Matrix.Identity();\n        }\n        world.invertToRef(scene._pickWithRayInverseMatrix);\n        if (!scene._cachedRayForTransform) {\n            scene._cachedRayForTransform = Ray.Zero();\n        }\n        Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\n        return scene._cachedRayForTransform;\n    }, predicate, trianglePredicate);\n}\n/**\n * Gets a ray in the forward direction from the camera.\n * @param camera Defines the camera to use to get the ray from\n * @param length Defines the length of the ray to create\n * @param transform Defines the transform to apply to the ray, by default the world matrix is used to create a workd space ray\n * @param origin Defines the start point of the ray which defaults to the camera position\n * @returns the forward ray\n */\nexport function GetForwardRay(camera, length = 100, transform, origin) {\n    return GetForwardRayToRef(camera, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\n}\n/**\n * Gets a ray in the forward direction from the camera.\n * @param camera Defines the camera to use to get the ray from\n * @param refRay the ray to (re)use when setting the values\n * @param length Defines the length of the ray to create\n * @param transform Defines the transform to apply to the ray, by default the world matrx is used to create a workd space ray\n * @param origin Defines the start point of the ray which defaults to the camera position\n * @returns the forward ray\n */\nexport function GetForwardRayToRef(camera, refRay, length = 100, transform, origin) {\n    if (!transform) {\n        transform = camera.getWorldMatrix();\n    }\n    refRay.length = length;\n    if (origin) {\n        refRay.origin.copyFrom(origin);\n    }\n    else {\n        refRay.origin.copyFrom(camera.position);\n    }\n    const forward = TmpVectors.Vector3[2];\n    forward.set(0, 0, camera._scene.useRightHandedSystem ? -1 : 1);\n    const worldForward = TmpVectors.Vector3[3];\n    Vector3.TransformNormalToRef(forward, transform, worldForward);\n    Vector3.NormalizeToRef(worldForward, refRay.direction);\n    return refRay;\n}\n/**\n * Initialize the minimal interdependecies between the Ray and Scene and Camera\n * @param sceneClass defines the scene prototype to use\n * @param cameraClass defines the camera prototype to use\n */\nexport function AddRayExtensions(sceneClass, cameraClass) {\n    if (cameraClass) {\n        cameraClass.prototype.getForwardRay = function (length = 100, transform, origin) {\n            return GetForwardRayToRef(this, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\n        };\n        cameraClass.prototype.getForwardRayToRef = function (refRay, length = 100, transform, origin) {\n            return GetForwardRayToRef(this, refRay, length, transform, origin);\n        };\n    }\n    if (!sceneClass) {\n        return;\n    }\n    _ImportHelper._IsPickingAvailable = true;\n    sceneClass.prototype.createPickingRay = function (x, y, world, camera, cameraViewSpace = false) {\n        return CreatePickingRay(this, x, y, world, camera, cameraViewSpace);\n    };\n}\n//# sourceMappingURL=ray.core.js.map", "import { Scene } from \"../scene.js\";\nimport { Camera } from \"../Cameras/camera.js\";\nimport { AddRayExtensions, CreatePickingRayInCameraSpace, CreatePickingRayInCameraSpaceToRef, CreatePickingRayToRef, MultiPick, MultiPickWithRay, Pick, PickWithBoundingInfo, PickWithRay, } from \"./ray.core.js\";\nexport * from \"./ray.core.js\";\n// Picking\nAddRayExtensions(Scene, Camera);\nScene.prototype.createPickingRayToRef = function (x, y, world, result, camera, cameraViewSpace = false, enableDistantPicking = false) {\n    return CreatePickingRayToRef(this, x, y, world, result, camera, cameraViewSpace, enableDistantPicking);\n};\nScene.prototype.createPickingRayInCameraSpace = function (x, y, camera) {\n    return CreatePickingRayInCameraSpace(this, x, y, camera);\n};\nScene.prototype.createPickingRayInCameraSpaceToRef = function (x, y, result, camera) {\n    return CreatePickingRayInCameraSpaceToRef(this, x, y, result, camera);\n};\nScene.prototype.pickWithBoundingInfo = function (x, y, predicate, fastCheck, camera) {\n    return PickWithBoundingInfo(this, x, y, predicate, fastCheck, camera);\n};\nScene.prototype.pick = function (x, y, predicate, fastCheck, camera, trianglePredicate, _enableDistantPicking = false) {\n    return Pick(this, x, y, predicate, fastCheck, camera, trianglePredicate, _enableDistantPicking);\n};\nScene.prototype.pickWithRay = function (ray, predicate, fastCheck, trianglePredicate) {\n    return PickWithRay(this, ray, predicate, fastCheck, trianglePredicate);\n};\nScene.prototype.multiPick = function (x, y, predicate, camera, trianglePredicate) {\n    return MultiPick(this, x, y, predicate, camera, trianglePredicate);\n};\nScene.prototype.multiPickWithRay = function (ray, predicate, trianglePredicate) {\n    return MultiPickWithRay(this, ray, predicate, trianglePredicate);\n};\n//# sourceMappingURL=ray.js.map"], "names": ["PickingCustomization", "<PERSON>", "origin", "direction", "length", "epsilon", "Epsilon", "minimum", "maximum", "intersectionTreshold", "newMinimum", "newMaximum", "d", "maxValue", "inv", "min", "max", "temp", "box", "sphere", "x", "y", "z", "pyth", "radius", "rr", "dot", "vertex0", "vertex1", "vertex2", "edge1", "edge2", "pvec", "tvec", "qvec", "Vector3", "det", "invdet", "bv", "bw", "distance", "IntersectionInfo", "plane", "result1", "result2", "axis", "offset", "mesh", "fastCheck", "trianglePredicate", "onlyBoundingInfo", "worldToUse", "skipBoundingInfo", "tm", "TmpVectors", "meshes", "results", "i", "pickInfo", "pickingInfoA", "pickingInfoB", "sega", "segb", "threshold", "o", "u", "rsegb", "v", "w", "a", "b", "c", "e", "D", "sN", "sD", "tN", "tD", "sc", "tc", "qtc", "qsc", "dP", "viewportWidth", "viewportHeight", "world", "view", "projection", "enableDistantPicking", "Matrix", "end", "result", "ray", "matrix", "dir", "len", "num", "sourceX", "sourceY", "engine", "EngineStore", "nearScreenSource", "farScreenSource", "nearVec3", "farVec3", "BuildArray", "CreatePickingRay", "scene", "camera", "cameraViewSpace", "CreatePickingRayToRef", "cameraViewport", "renderHeight", "vx", "vy", "width", "height", "levelInv", "CreatePickingRayInCameraSpace", "CreatePickingRayInCameraSpaceToRef", "PickingInfo", "identity", "InternalPickFor<PERSON>esh", "pickingInfo", "rayFunction", "InternalPick", "predicate", "computeWorldMatrixForCamera", "currentCamera", "picker", "meshIndex", "forceCompute", "tmpMatrix", "thinMatrices", "index", "InternalMultiPick", "pickingInfos", "PickWithBoundingInfo", "Pick", "_enableDistantPicking", "PickWithRay", "MultiPick", "MultiPickWithRay", "GetForwardRay", "transform", "GetForwardRayToRef", "refRay", "forward", "worldForward", "AddRayExtensions", "sceneClass", "cameraClass", "_ImportHelper", "Scene", "Camera"], "mappings": "sKAUY,MAACA,EAAuB,CAChC,sBAAuB,MAC3B,EAIO,MAAMC,CAAI,CAQb,YAEAC,EAEAC,EAEAC,EAAS,OAAO,UAEhBC,EAAUC,EAAS,CACf,KAAK,OAASJ,EACd,KAAK,UAAYC,EACjB,KAAK,OAASC,EACd,KAAK,QAAUC,CAClB,CAMD,OAAQ,CACJ,OAAO,IAAIJ,EAAI,KAAK,OAAO,MAAO,EAAE,KAAK,UAAU,MAAK,EAAI,KAAK,MAAM,CAC1E,CASD,oBAAoBM,EAASC,EAASC,EAAuB,EAAG,CAC5D,MAAMC,EAAaT,EAAI,YAAY,CAAC,EAAE,eAAeM,EAAQ,EAAIE,EAAsBF,EAAQ,EAAIE,EAAsBF,EAAQ,EAAIE,CAAoB,EACnJE,EAAaV,EAAI,YAAY,CAAC,EAAE,eAAeO,EAAQ,EAAIC,EAAsBD,EAAQ,EAAIC,EAAsBD,EAAQ,EAAIC,CAAoB,EACzJ,IAAIG,EAAI,EACJC,EAAW,OAAO,UAClBC,EACAC,EACAC,EACAC,EACJ,GAAI,KAAK,IAAI,KAAK,UAAU,CAAC,EAAI,MAC7B,GAAI,KAAK,OAAO,EAAIP,EAAW,GAAK,KAAK,OAAO,EAAIC,EAAW,EAC3D,MAAO,WAIXG,EAAM,EAAM,KAAK,UAAU,EAC3BC,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKI,EACvCE,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKG,EACnCE,IAAQ,OACRA,EAAM,KAEND,EAAMC,IACNC,EAAOF,EACPA,EAAMC,EACNA,EAAMC,GAEVL,EAAI,KAAK,IAAIG,EAAKH,CAAC,EACnBC,EAAW,KAAK,IAAIG,EAAKH,CAAQ,EAC7BD,EAAIC,EACJ,MAAO,GAGf,GAAI,KAAK,IAAI,KAAK,UAAU,CAAC,EAAI,MAC7B,GAAI,KAAK,OAAO,EAAIH,EAAW,GAAK,KAAK,OAAO,EAAIC,EAAW,EAC3D,MAAO,WAIXG,EAAM,EAAM,KAAK,UAAU,EAC3BC,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKI,EACvCE,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKG,EACnCE,IAAQ,OACRA,EAAM,KAEND,EAAMC,IACNC,EAAOF,EACPA,EAAMC,EACNA,EAAMC,GAEVL,EAAI,KAAK,IAAIG,EAAKH,CAAC,EACnBC,EAAW,KAAK,IAAIG,EAAKH,CAAQ,EAC7BD,EAAIC,EACJ,MAAO,GAGf,GAAI,KAAK,IAAI,KAAK,UAAU,CAAC,EAAI,MAC7B,GAAI,KAAK,OAAO,EAAIH,EAAW,GAAK,KAAK,OAAO,EAAIC,EAAW,EAC3D,MAAO,WAIXG,EAAM,EAAM,KAAK,UAAU,EAC3BC,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKI,EACvCE,GAAOL,EAAW,EAAI,KAAK,OAAO,GAAKG,EACnCE,IAAQ,OACRA,EAAM,KAEND,EAAMC,IACNC,EAAOF,EACPA,EAAMC,EACNA,EAAMC,GAEVL,EAAI,KAAK,IAAIG,EAAKH,CAAC,EACnBC,EAAW,KAAK,IAAIG,EAAKH,CAAQ,EAC7BD,EAAIC,EACJ,MAAO,GAGf,MAAO,EACV,CAQD,cAAcK,EAAKT,EAAuB,EAAG,CACzC,OAAO,KAAK,oBAAoBS,EAAI,QAASA,EAAI,QAAST,CAAoB,CACjF,CAOD,iBAAiBU,EAAQV,EAAuB,EAAG,CAC/C,MAAMW,EAAID,EAAO,OAAO,EAAI,KAAK,OAAO,EAClCE,EAAIF,EAAO,OAAO,EAAI,KAAK,OAAO,EAClCG,EAAIH,EAAO,OAAO,EAAI,KAAK,OAAO,EAClCI,EAAOH,EAAIA,EAAIC,EAAIA,EAAIC,EAAIA,EAC3BE,EAASL,EAAO,OAASV,EACzBgB,EAAKD,EAASA,EACpB,GAAID,GAAQE,EACR,MAAO,GAEX,MAAMC,EAAMN,EAAI,KAAK,UAAU,EAAIC,EAAI,KAAK,UAAU,EAAIC,EAAI,KAAK,UAAU,EAC7E,OAAII,EAAM,EACC,GAEEH,EAAOG,EAAMA,GACXD,CAClB,CAQD,mBAAmBE,EAASC,EAASC,EAAS,CAC1C,MAAMC,EAAQ7B,EAAI,YAAY,CAAC,EACzB8B,EAAQ9B,EAAI,YAAY,CAAC,EACzB+B,EAAO/B,EAAI,YAAY,CAAC,EACxBgC,EAAOhC,EAAI,YAAY,CAAC,EACxBiC,EAAOjC,EAAI,YAAY,CAAC,EAC9B2B,EAAQ,cAAcD,EAASG,CAAK,EACpCD,EAAQ,cAAcF,EAASI,CAAK,EACpCI,EAAQ,WAAW,KAAK,UAAWJ,EAAOC,CAAI,EAC9C,MAAMI,EAAMD,EAAQ,IAAIL,EAAOE,CAAI,EACnC,GAAII,IAAQ,EACR,OAAO,KAEX,MAAMC,EAAS,EAAID,EACnB,KAAK,OAAO,cAAcT,EAASM,CAAI,EACvC,MAAMK,EAAKH,EAAQ,IAAIF,EAAMD,CAAI,EAAIK,EACrC,GAAIC,EAAK,CAAC,KAAK,SAAWA,EAAK,EAAM,KAAK,QACtC,OAAO,KAEXH,EAAQ,WAAWF,EAAMH,EAAOI,CAAI,EACpC,MAAMK,EAAKJ,EAAQ,IAAI,KAAK,UAAWD,CAAI,EAAIG,EAC/C,GAAIE,EAAK,CAAC,KAAK,SAAWD,EAAKC,EAAK,EAAM,KAAK,QAC3C,OAAO,KAGX,MAAMC,EAAWL,EAAQ,IAAIJ,EAAOG,CAAI,EAAIG,EAC5C,OAAIG,EAAW,KAAK,OACT,KAEJ,IAAIC,EAAiB,EAAIH,EAAKC,EAAID,EAAIE,CAAQ,CACxD,CAMD,gBAAgBE,EAAO,CACnB,IAAIF,EACJ,MAAMG,EAAUR,EAAQ,IAAIO,EAAM,OAAQ,KAAK,SAAS,EACxD,GAAI,KAAK,IAAIC,CAAO,EAAI,oBACpB,OAAO,KAEN,CACD,MAAMC,EAAUT,EAAQ,IAAIO,EAAM,OAAQ,KAAK,MAAM,EAErD,OADAF,GAAY,CAACE,EAAM,EAAIE,GAAWD,EAC9BH,EAAW,EACPA,EAAW,qBACJ,KAGA,EAGRA,CACV,CACJ,CAOD,eAAeK,EAAMC,EAAS,EAAG,CAC7B,OAAQD,EAAI,CACR,IAAK,IAAK,CACN,MAAM,GAAK,KAAK,OAAO,EAAIC,GAAU,KAAK,UAAU,EACpD,OAAI,EAAI,EACG,KAEJ,IAAIX,EAAQ,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,EAAGW,EAAQ,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,CAAC,CAC1G,CACD,IAAK,IAAK,CACN,MAAM,GAAK,KAAK,OAAO,EAAIA,GAAU,KAAK,UAAU,EACpD,OAAI,EAAI,EACG,KAEJ,IAAIX,EAAQW,EAAQ,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,EAAG,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,CAAC,CAC1G,CACD,IAAK,IAAK,CACN,MAAM,GAAK,KAAK,OAAO,EAAIA,GAAU,KAAK,UAAU,EACpD,OAAI,EAAI,EACG,KAEJ,IAAIX,EAAQ,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,EAAG,KAAK,OAAO,EAAI,KAAK,UAAU,EAAI,CAAC,EAAGW,CAAM,CAC1G,CACD,QACI,OAAO,IACd,CACJ,CAYD,eAAeC,EAAMC,EAAWC,EAAmBC,EAAmB,GAAOC,EAAYC,EAAmB,GAAO,CAC/G,MAAMC,EAAKC,EAAW,OAAO,CAAC,EAC9B,OAAAP,EAAK,eAAc,EAAG,YAAYM,CAAE,EAChC,KAAK,QACLpD,EAAI,eAAe,KAAMoD,EAAI,KAAK,OAAO,EAGzC,KAAK,QAAUpD,EAAI,UAAU,KAAMoD,CAAE,EAElCN,EAAK,WAAW,KAAK,QAASC,EAAWC,EAAmBC,EAAkBC,EAAYC,CAAgB,CACpH,CAQD,iBAAiBG,EAAQP,EAAWQ,EAAS,CACrCA,EACAA,EAAQ,OAAS,EAGjBA,EAAU,CAAA,EAEd,QAASC,EAAI,EAAGA,EAAIF,EAAO,OAAQE,IAAK,CACpC,MAAMC,EAAW,KAAK,eAAeH,EAAOE,CAAC,EAAGT,CAAS,EACrDU,EAAS,KACTF,EAAQ,KAAKE,CAAQ,CAE5B,CACD,OAAAF,EAAQ,KAAK,KAAK,mBAAmB,EAC9BA,CACV,CACD,oBAAoBG,EAAcC,EAAc,CAC5C,OAAID,EAAa,SAAWC,EAAa,SAC9B,GAEFD,EAAa,SAAWC,EAAa,SACnC,EAGA,CAEd,CAQD,oBAAoBC,EAAMC,EAAMC,EAAW,CACvC,MAAMC,EAAI,KAAK,OACTC,EAAIX,EAAW,QAAQ,CAAC,EACxBY,EAAQZ,EAAW,QAAQ,CAAC,EAC5Ba,EAAIb,EAAW,QAAQ,CAAC,EACxBc,EAAId,EAAW,QAAQ,CAAC,EAC9BQ,EAAK,cAAcD,EAAMI,CAAC,EAC1B,KAAK,UAAU,WAAWhE,EAAI,MAAOkE,CAAC,EACtCH,EAAE,SAASG,EAAGD,CAAK,EACnBL,EAAK,cAAcG,EAAGI,CAAC,EACvB,MAAMC,EAAIlC,EAAQ,IAAI8B,EAAGA,CAAC,EACpBK,EAAInC,EAAQ,IAAI8B,EAAGE,CAAC,EACpBI,EAAIpC,EAAQ,IAAIgC,EAAGA,CAAC,EACpB,EAAIhC,EAAQ,IAAI8B,EAAGG,CAAC,EACpBI,EAAIrC,EAAQ,IAAIgC,EAAGC,CAAC,EACpBK,EAAIJ,EAAIE,EAAID,EAAIA,EACtB,IAAII,EAAIC,EAAKF,EACTG,EAAIC,EAAKJ,EAETA,EAAIxE,EAAI,WAERyE,EAAK,EACLC,EAAK,EACLC,EAAKJ,EACLK,EAAKN,IAILG,EAAKJ,EAAIE,EAAID,EAAI,EACjBK,EAAKP,EAAIG,EAAIF,EAAI,EACbI,EAAK,GAELA,EAAK,EACLE,EAAKJ,EACLK,EAAKN,GAEAG,EAAKC,IAEVD,EAAKC,EACLC,EAAKJ,EAAIF,EACTO,EAAKN,IAGTK,EAAK,GAELA,EAAK,EAED,CAAC,EAAI,EACLF,EAAK,EAEA,CAAC,EAAIL,EACVK,EAAKC,GAGLD,EAAK,CAAC,EACNC,EAAKN,IAGJO,EAAKC,IAEVD,EAAKC,EAED,CAAC,EAAIP,EAAI,EACTI,EAAK,EAEA,CAAC,EAAIJ,EAAID,EACdK,EAAKC,GAGLD,EAAK,CAAC,EAAIJ,EACVK,EAAKN,IAIb,MAAMS,EAAK,KAAK,IAAIJ,CAAE,EAAIzE,EAAI,UAAY,EAAMyE,EAAKC,EAC/CI,EAAK,KAAK,IAAIH,CAAE,EAAI3E,EAAI,UAAY,EAAM2E,EAAKC,EAE/CG,EAAM1B,EAAW,QAAQ,CAAC,EAChCa,EAAE,WAAWY,EAAIC,CAAG,EACpB,MAAMC,EAAM3B,EAAW,QAAQ,CAAC,EAChCW,EAAE,WAAWa,EAAIG,CAAG,EACpBA,EAAI,WAAWb,CAAC,EAChB,MAAMc,EAAK5B,EAAW,QAAQ,CAAC,EAG/B,OAFA2B,EAAI,cAAcD,EAAKE,CAAE,EACHH,EAAK,GAAKA,GAAM,KAAK,QAAUG,EAAG,gBAAkBnB,EAAYA,EAE3EkB,EAAI,SAER,EACV,CAaD,OAAO7D,EAAGC,EAAG8D,EAAeC,EAAgBC,EAAOC,EAAMC,EAAYC,EAAuB,GAAO,CAC/F,GAAIA,EAAsB,CAMjBvF,EAAI,cACLA,EAAI,YAAcA,EAAI,QAE1BA,EAAI,YAAY,kBAAkBmB,EAAGC,EAAG8D,EAAeC,EAAgBK,EAAO,iBAAkBH,EAAMC,CAAU,EAChH,MAAMlC,EAAKC,EAAW,OAAO,CAAC,EAC9B+B,EAAM,YAAYhC,CAAE,EACpBpD,EAAI,eAAeA,EAAI,YAAaoD,EAAI,IAAI,CAC/C,MAEG,KAAK,kBAAkBjC,EAAGC,EAAG8D,EAAeC,EAAgBC,EAAOC,EAAMC,CAAU,EAEvF,OAAO,IACV,CAMD,OAAO,MAAO,CACV,OAAO,IAAItF,EAAIkC,EAAQ,KAAM,EAAEA,EAAQ,KAAI,CAAE,CAChD,CAYD,OAAO,UAAUf,EAAGC,EAAG8D,EAAeC,EAAgBC,EAAOC,EAAMC,EAAY,CAE3E,OADetF,EAAI,OACL,OAAOmB,EAAGC,EAAG8D,EAAeC,EAAgBC,EAAOC,EAAMC,CAAU,CACpF,CASD,OAAO,gBAAgBrF,EAAQwF,EAAKL,EAAQI,EAAO,iBAAkB,CACjE,MAAME,EAAS,IAAI1F,EAAI,IAAIkC,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACjE,OAAOlC,EAAI,kBAAkBC,EAAQwF,EAAKC,EAAQN,CAAK,CAC1D,CAUD,OAAO,kBAAkBnF,EAAQwF,EAAKC,EAAQN,EAAQI,EAAO,iBAAkB,CAC3EE,EAAO,OAAO,SAASzF,CAAM,EAC7B,MAAMC,EAAYuF,EAAI,cAAcxF,EAAQyF,EAAO,SAAS,EACtDvF,EAAS,KAAK,KAAKD,EAAU,EAAIA,EAAU,EAAIA,EAAU,EAAIA,EAAU,EAAIA,EAAU,EAAIA,EAAU,CAAC,EAC1G,OAAAwF,EAAO,OAASvF,EAChBuF,EAAO,UAAU,YACV1F,EAAI,eAAe0F,EAAQN,EAAOM,CAAM,CAClD,CAOD,OAAO,UAAUC,EAAKC,EAAQ,CAC1B,MAAMF,EAAS,IAAI1F,EAAI,IAAIkC,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACjE,OAAAlC,EAAI,eAAe2F,EAAKC,EAAQF,CAAM,EAC/BA,CACV,CAQD,OAAO,eAAeC,EAAKC,EAAQF,EAAQ,CACvCxD,EAAQ,0BAA0ByD,EAAI,OAAQC,EAAQF,EAAO,MAAM,EACnExD,EAAQ,qBAAqByD,EAAI,UAAWC,EAAQF,EAAO,SAAS,EACpEA,EAAO,OAASC,EAAI,OACpBD,EAAO,QAAUC,EAAI,QACrB,MAAME,EAAMH,EAAO,UACbI,EAAMD,EAAI,SAChB,GAAI,EAAEC,IAAQ,GAAKA,IAAQ,GAAI,CAC3B,MAAMC,EAAM,EAAMD,EAClBD,EAAI,GAAKE,EACTF,EAAI,GAAKE,EACTF,EAAI,GAAKE,EACTL,EAAO,QAAUI,CACpB,CACD,OAAOJ,CACV,CAWD,kBAAkBM,EAASC,EAASf,EAAeC,EAAgBC,EAAOC,EAAMC,EAAY,CACxF,MAAMM,EAASvC,EAAW,OAAO,CAAC,EAClC+B,EAAM,cAAcC,EAAMO,CAAM,EAChCA,EAAO,cAAcN,EAAYM,CAAM,EACvCA,EAAO,OAAM,EACb,MAAMM,EAASC,EAAY,kBACrBC,EAAmB/C,EAAW,QAAQ,CAAC,EAC7C+C,EAAiB,EAAKJ,EAAUd,EAAiB,EAAI,EACrDkB,EAAiB,EAAI,EAAGH,EAAUd,EAAkB,EAAI,GACxDiB,EAAiB,EAAIF,GAAQ,sBAAwB,EAAIA,GAAQ,gBAAkB,EAAI,GAEvF,MAAMG,EAAkBhD,EAAW,QAAQ,CAAC,EAAE,eAAe+C,EAAiB,EAAGA,EAAiB,EAAG,EAAM,IAAI,EACzGE,EAAWjD,EAAW,QAAQ,CAAC,EAC/BkD,EAAUlD,EAAW,QAAQ,CAAC,EACpCnB,EAAQ,kCAAkCkE,EAAkBR,EAAQU,CAAQ,EAC5EpE,EAAQ,kCAAkCmE,EAAiBT,EAAQW,CAAO,EAC1E,KAAK,OAAO,SAASD,CAAQ,EAC7BC,EAAQ,cAAcD,EAAU,KAAK,SAAS,EAC9C,KAAK,UAAU,WAClB,CACL,CACAtG,EAAI,YAAcwG,EAAW,EAAGtE,EAAQ,IAAI,EAC5ClC,EAAI,YAAcA,EAAI,OACtBA,EAAI,UAAY,KAChBA,EAAI,MAAQ,IAWL,SAASyG,EAAiBC,EAAOvF,EAAGC,EAAGgE,EAAOuB,EAAQC,EAAkB,GAAO,CAClF,MAAMlB,EAAS1F,EAAI,OACnB,OAAA6G,EAAsBH,EAAOvF,EAAGC,EAAGgE,EAAOM,EAAQiB,EAAQC,CAAe,EAClElB,CACX,CAaO,SAASmB,EAAsBH,EAAOvF,EAAGC,EAAGgE,EAAOM,EAAQiB,EAAQC,EAAkB,GAAOrB,EAAuB,GAAO,CAC7H,MAAMW,EAASQ,EAAM,YACrB,GAAI,CAACC,GAAU,EAAEA,EAASD,EAAM,cAC5B,OAAOA,EAEX,MAAMI,EAAiBH,EAAO,SACxBI,EAAeb,EAAO,kBACtB,CAAE,EAAGc,EAAI,EAAGC,EAAI,MAAAC,EAAO,OAAAC,GAAWL,EAAe,SAASZ,EAAO,eAAgB,EAAEa,CAAY,EAE/FK,EAAW,EAAIlB,EAAO,wBAAuB,EACnD,OAAA/E,EAAIA,EAAIiG,EAAWJ,EACnB5F,EAAIA,EAAIgG,GAAYL,EAAeE,EAAKE,GACxCzB,EAAO,OAAOvE,EAAGC,EAAG8F,EAAOC,EAAQ/B,GAAgBI,EAAO,iBAAkBoB,EAAkBpB,EAAO,iBAAmBmB,EAAO,cAAe,EAAEA,EAAO,sBAAuBpB,CAAoB,EAC3LmB,CACX,CASO,SAASW,EAA8BX,EAAOvF,EAAGC,EAAGuF,EAAQ,CAC/D,MAAMjB,EAAS1F,EAAI,OACnB,OAAAsH,EAAmCZ,EAAOvF,EAAGC,EAAGsE,EAAQiB,CAAM,EACvDjB,CACX,CAUO,SAAS4B,EAAmCZ,EAAOvF,EAAGC,EAAGsE,EAAQiB,EAAQ,CAC5E,GAAI,CAACY,EACD,OAAOb,EAEX,MAAMR,EAASQ,EAAM,YACrB,GAAI,CAACC,GAAU,EAAEA,EAASD,EAAM,cAC5B,MAAM,IAAI,MAAM,uBAAuB,EAE3C,MAAMI,EAAiBH,EAAO,SACxBI,EAAeb,EAAO,kBACtB,CAAE,EAAGc,EAAI,EAAGC,EAAI,MAAAC,EAAO,OAAAC,GAAWL,EAAe,SAASZ,EAAO,eAAgB,EAAEa,CAAY,EAC/FS,EAAWhC,EAAO,WAElB4B,EAAW,EAAIlB,EAAO,wBAAuB,EACnD,OAAA/E,EAAIA,EAAIiG,EAAWJ,EACnB5F,EAAIA,EAAIgG,GAAYL,EAAeE,EAAKE,GACxCzB,EAAO,OAAOvE,EAAGC,EAAG8F,EAAOC,EAAQK,EAAUA,EAAUb,EAAO,oBAAqB,CAAA,EAC5ED,CACX,CACA,SAASe,EAAoBC,EAAaC,EAAa7E,EAAMsC,EAAOrC,EAAWE,EAAkBD,EAAmBG,EAAkB,CAClI,MAAMwC,EAAMgC,EAAYvC,EAAOtC,EAAK,oBAAoB,EAClD4C,EAAS5C,EAAK,WAAW6C,EAAK5C,EAAWC,EAAmBC,EAAkBmC,EAAOjC,CAAgB,EAI3G,MAHI,CAACuC,GAAU,CAACA,EAAO,KAGnB,CAAC3C,GAAa2E,GAAe,MAAQhC,EAAO,UAAYgC,EAAY,SAC7D,KAEJhC,CACX,CACA,SAASkC,EAAalB,EAAOiB,EAAaE,EAAW9E,EAAWE,EAAkBD,EAAmB,CACjG,IAAI0E,EAAc,KAClB,MAAMI,EAA8B,CAAC,EAAEpB,EAAM,eAAiBA,EAAM,cAAc,OAAS,GAAKA,EAAM,yBAA2BA,EAAM,cACjIqB,EAAgBrB,EAAM,wBAA0BA,EAAM,aACtDsB,EAASjI,EAAqB,uBAAyB0H,EAC7D,QAASQ,EAAY,EAAGA,EAAYvB,EAAM,OAAO,OAAQuB,IAAa,CAClE,MAAMnF,EAAO4D,EAAM,OAAOuB,CAAS,EACnC,GAAIJ,GACA,GAAI,CAACA,EAAU/E,EAAM,EAAE,EACnB,iBAGC,CAACA,EAAK,aAAe,CAACA,EAAK,WAAa,CAACA,EAAK,WACnD,SAEJ,MAAMoF,EAAeJ,GAA+BhF,EAAK,6BAA4B,EAC/EsC,EAAQtC,EAAK,mBAAmBoF,EAAcH,CAAa,EACjE,GAAIjF,EAAK,kBAAoBA,EAAK,0BAA2B,CAEzD,MAAM4C,EAASsC,EAAON,EAAaC,EAAa7E,EAAMsC,EAAO,GAAM,GAAMpC,CAAiB,EAC1F,GAAI0C,EAAQ,CACR,GAAIzC,EAEA,OAAOyC,EAEX,MAAMyC,EAAY9E,EAAW,OAAO,CAAC,EAC/B+E,EAAetF,EAAK,+BAC1B,QAASuF,EAAQ,EAAGA,EAAQD,EAAa,OAAQC,IAAS,CACtD,GAAIR,GAAa,CAACA,EAAU/E,EAAMuF,CAAK,EACnC,SAEeD,EAAaC,CAAK,EAC1B,cAAcjD,EAAO+C,CAAS,EACzC,MAAMzC,EAASsC,EAAON,EAAaC,EAAa7E,EAAMqF,EAAWpF,EAAWE,EAAkBD,EAAmB,EAAI,EACrH,GAAI0C,IACAgC,EAAchC,EACdgC,EAAY,kBAAoBW,EAC5BtF,GACA,OAAO2E,CAGlB,CACJ,CACJ,KACI,CACD,MAAMhC,EAASsC,EAAON,EAAaC,EAAa7E,EAAMsC,EAAOrC,EAAWE,EAAkBD,CAAiB,EAC3G,GAAI0C,IACAgC,EAAchC,EACV3C,GACA,OAAO2E,CAGlB,CACJ,CACD,OAAOA,GAAe,IAAIH,CAC9B,CACA,SAASe,EAAkB5B,EAAOiB,EAAaE,EAAW7E,EAAmB,CACzE,GAAI,CAACuE,EACD,OAAO,KAEX,MAAMgB,EAAe,CAAA,EACfT,EAA8B,CAAC,EAAEpB,EAAM,eAAiBA,EAAM,cAAc,OAAS,GAAKA,EAAM,yBAA2BA,EAAM,cACjIqB,EAAgBrB,EAAM,wBAA0BA,EAAM,aACtDsB,EAASjI,EAAqB,uBAAyB0H,EAC7D,QAASQ,EAAY,EAAGA,EAAYvB,EAAM,OAAO,OAAQuB,IAAa,CAClE,MAAMnF,EAAO4D,EAAM,OAAOuB,CAAS,EACnC,GAAIJ,GACA,GAAI,CAACA,EAAU/E,EAAM,EAAE,EACnB,iBAGC,CAACA,EAAK,aAAe,CAACA,EAAK,WAAa,CAACA,EAAK,WACnD,SAEJ,MAAMoF,EAAeJ,GAA+BhF,EAAK,6BAA4B,EAC/EsC,EAAQtC,EAAK,mBAAmBoF,EAAcH,CAAa,EACjE,GAAIjF,EAAK,kBAAoBA,EAAK,2BAE9B,GADekF,EAAO,KAAML,EAAa7E,EAAMsC,EAAO,GAAM,GAAMpC,CAAiB,EACvE,CACR,MAAMmF,EAAY9E,EAAW,OAAO,CAAC,EAC/B+E,EAAetF,EAAK,+BAC1B,QAASuF,EAAQ,EAAGA,EAAQD,EAAa,OAAQC,IAAS,CACtD,GAAIR,GAAa,CAACA,EAAU/E,EAAMuF,CAAK,EACnC,SAEeD,EAAaC,CAAK,EAC1B,cAAcjD,EAAO+C,CAAS,EACzC,MAAMzC,EAASsC,EAAO,KAAML,EAAa7E,EAAMqF,EAAW,GAAO,GAAOnF,EAAmB,EAAI,EAC3F0C,IACAA,EAAO,kBAAoB2C,EAC3BE,EAAa,KAAK7C,CAAM,EAE/B,CACJ,MAEA,CACD,MAAMA,EAASsC,EAAO,KAAML,EAAa7E,EAAMsC,EAAO,GAAO,GAAOpC,CAAiB,EACjF0C,GACA6C,EAAa,KAAK7C,CAAM,CAE/B,CACJ,CACD,OAAO6C,CACX,CAUO,SAASC,EAAqB9B,EAAOvF,EAAGC,EAAGyG,EAAW9E,EAAW4D,EAAQ,CAC5E,GAAI,CAACY,EACD,OAAO,KAEX,MAAM7B,EAASkC,EAAalB,EAAQtB,IAC3BsB,EAAM,kBACPA,EAAM,gBAAkB1G,EAAI,QAEhC6G,EAAsBH,EAAOvF,EAAGC,EAAGgE,EAAOsB,EAAM,gBAAiBC,GAAU,IAAI,EACxED,EAAM,iBACdmB,EAAW9E,EAAW,EAAI,EAC7B,OAAI2C,IACAA,EAAO,IAAMe,EAAiBC,EAAOvF,EAAGC,EAAGoE,EAAO,SAAQ,EAAImB,GAAU,IAAI,GAEzEjB,CACX,CAYO,SAAS+C,EAAK/B,EAAOvF,EAAGC,EAAGyG,EAAW9E,EAAW4D,EAAQ3D,EAAmB0F,EAAwB,GAAO,CAC9G,MAAMhD,EAASkC,EAAalB,EAAO,CAACtB,EAAOG,KAClCmB,EAAM,kBACPA,EAAM,gBAAkB1G,EAAI,QAEhC6G,EAAsBH,EAAOvF,EAAGC,EAAGgE,EAAOsB,EAAM,gBAAiBC,GAAU,KAAM,GAAOpB,CAAoB,EACrGmB,EAAM,iBACdmB,EAAW9E,EAAW,GAAOC,CAAiB,EACjD,OAAI0C,IACAA,EAAO,IAAMe,EAAiBC,EAAOvF,EAAGC,EAAGoE,EAAO,SAAQ,EAAImB,GAAU,IAAI,GAEzEjB,CACX,CAWO,SAASiD,EAAYjC,EAAOf,EAAKkC,EAAW9E,EAAWC,EAAmB,CAC7E,MAAM0C,EAASkC,EAAalB,EAAQtB,IAC3BsB,EAAM,4BACPA,EAAM,0BAA4BlB,EAAO,YAE7CJ,EAAM,YAAYsB,EAAM,yBAAyB,EAC5CA,EAAM,yBACPA,EAAM,uBAAyB1G,EAAI,QAEvCA,EAAI,eAAe2F,EAAKe,EAAM,0BAA2BA,EAAM,sBAAsB,EAC9EA,EAAM,wBACdmB,EAAW9E,EAAW,GAAOC,CAAiB,EACjD,OAAI0C,IACAA,EAAO,IAAMC,GAEVD,CACX,CAYO,SAASkD,EAAUlC,EAAOvF,EAAGC,EAAGyG,EAAWlB,EAAQ3D,EAAmB,CACzE,OAAOsF,EAAkB5B,EAAQtB,GAAUqB,EAAiBC,EAAOvF,EAAGC,EAAGgE,EAAOuB,GAAU,IAAI,EAAGkB,EAAW7E,CAAiB,CACjI,CASO,SAAS6F,EAAiBnC,EAAOf,EAAKkC,EAAW7E,EAAmB,CACvE,OAAOsF,EAAkB5B,EAAQtB,IACxBsB,EAAM,4BACPA,EAAM,0BAA4BlB,EAAO,YAE7CJ,EAAM,YAAYsB,EAAM,yBAAyB,EAC5CA,EAAM,yBACPA,EAAM,uBAAyB1G,EAAI,QAEvCA,EAAI,eAAe2F,EAAKe,EAAM,0BAA2BA,EAAM,sBAAsB,EAC9EA,EAAM,wBACdmB,EAAW7E,CAAiB,CACnC,CASO,SAAS8F,GAAcnC,EAAQxG,EAAS,IAAK4I,EAAW9I,EAAQ,CACnE,OAAO+I,EAAmBrC,EAAQ,IAAI3G,EAAIkC,EAAQ,KAAM,EAAEA,EAAQ,KAAI,EAAI/B,CAAM,EAAGA,EAAQ4I,EAAW9I,CAAM,CAChH,CAUO,SAAS+I,EAAmBrC,EAAQsC,EAAQ9I,EAAS,IAAK4I,EAAW9I,EAAQ,CAC3E8I,IACDA,EAAYpC,EAAO,kBAEvBsC,EAAO,OAAS9I,EACZF,EACAgJ,EAAO,OAAO,SAAShJ,CAAM,EAG7BgJ,EAAO,OAAO,SAAStC,EAAO,QAAQ,EAE1C,MAAMuC,EAAU7F,EAAW,QAAQ,CAAC,EACpC6F,EAAQ,IAAI,EAAG,EAAGvC,EAAO,OAAO,qBAAuB,GAAK,CAAC,EAC7D,MAAMwC,EAAe9F,EAAW,QAAQ,CAAC,EACzC,OAAAnB,EAAQ,qBAAqBgH,EAASH,EAAWI,CAAY,EAC7DjH,EAAQ,eAAeiH,EAAcF,EAAO,SAAS,EAC9CA,CACX,CAMO,SAASG,EAAiBC,EAAYC,EAAa,CAClDA,IACAA,EAAY,UAAU,cAAgB,SAAUnJ,EAAS,IAAK4I,EAAW9I,EAAQ,CAC7E,OAAO+I,EAAmB,KAAM,IAAIhJ,EAAIkC,EAAQ,KAAM,EAAEA,EAAQ,KAAI,EAAI/B,CAAM,EAAGA,EAAQ4I,EAAW9I,CAAM,CACtH,EACQqJ,EAAY,UAAU,mBAAqB,SAAUL,EAAQ9I,EAAS,IAAK4I,EAAW9I,EAAQ,CAC1F,OAAO+I,EAAmB,KAAMC,EAAQ9I,EAAQ4I,EAAW9I,CAAM,CAC7E,GAESoJ,IAGLE,EAAc,oBAAsB,GACpCF,EAAW,UAAU,iBAAmB,SAAUlI,EAAGC,EAAGgE,EAAOuB,EAAQC,EAAkB,GAAO,CAC5F,OAAOH,EAAiB,KAAMtF,EAAGC,EAAGgE,EAAOuB,EAAQC,CAAe,CAC1E,EACA,CCp6BAwC,EAAiBI,EAAOC,CAAM,EAC9BD,EAAM,UAAU,sBAAwB,SAAUrI,EAAGC,EAAGgE,EAAOM,EAAQiB,EAAQC,EAAkB,GAAOrB,EAAuB,GAAO,CAClI,OAAOsB,EAAsB,KAAM1F,EAAGC,EAAGgE,EAAOM,EAAQiB,EAAQC,EAAiBrB,CAAoB,CACzG,EACAiE,EAAM,UAAU,8BAAgC,SAAUrI,EAAGC,EAAGuF,EAAQ,CACpE,OAAOU,EAA8B,KAAMlG,EAAGC,EAAGuF,CAAM,CAC3D,EACA6C,EAAM,UAAU,mCAAqC,SAAUrI,EAAGC,EAAGsE,EAAQiB,EAAQ,CACjF,OAAOW,EAAmC,KAAMnG,EAAGC,EAAGsE,EAAQiB,CAAM,CACxE,EACA6C,EAAM,UAAU,qBAAuB,SAAUrI,EAAGC,EAAGyG,EAAW9E,EAAW4D,EAAQ,CACjF,OAAO6B,EAAqB,KAAMrH,EAAGC,EAAGyG,EAAW9E,EAAW4D,CAAM,CACxE,EACA6C,EAAM,UAAU,KAAO,SAAUrI,EAAGC,EAAGyG,EAAW9E,EAAW4D,EAAQ3D,EAAmB0F,EAAwB,GAAO,CACnH,OAAOD,EAAK,KAAMtH,EAAGC,EAAGyG,EAAW9E,EAAW4D,EAAQ3D,EAAmB0F,CAAqB,CAClG,EACAc,EAAM,UAAU,YAAc,SAAU7D,EAAKkC,EAAW9E,EAAWC,EAAmB,CAClF,OAAO2F,EAAY,KAAMhD,EAAKkC,EAAW9E,EAAWC,CAAiB,CACzE,EACAwG,EAAM,UAAU,UAAY,SAAUrI,EAAGC,EAAGyG,EAAWlB,EAAQ3D,EAAmB,CAC9E,OAAO4F,EAAU,KAAMzH,EAAGC,EAAGyG,EAAWlB,EAAQ3D,CAAiB,CACrE,EACAwG,EAAM,UAAU,iBAAmB,SAAU7D,EAAKkC,EAAW7E,EAAmB,CAC5E,OAAO6F,EAAiB,KAAMlD,EAAKkC,EAAW7E,CAAiB,CACnE", "x_google_ignoreList": [0, 1]}