<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; font-size: 40px;">
  <div style="display: flex; align-items: center;">
    <img src="https://swanhub.co/git/repo/ZeYiLin%2FHivisionIDPhotos/file/preview?ref=master&path=assets/hivision_logo.png" alt="HivisionIDPhotos" style="width: 65px; height: 65px; margin-right: 10px;" onerror="this.style.display='none';" loading="lazy">
    <b style="color: #6e9abb;">HivisionIDPhotos</b><span style="font-size: 18px; color: #638fb3; margin-left: 10px;"> v1.3.1</span>
  </div>
  <div style="display: flex; justify-content: center; align-items: center; text-align: center;">
      <a href="https://github.com/xiaolin199912/HivisionIDPhotos"><img alt="Github" src="https://img.shields.io/static/v1?label=GitHub&message=GitHub&color=black" onerror="this.style.display='none';"></a> &ensp;
      <a href="https://github.com/xiaolin199912/HivisionIDPhotos/stargazers"><img alt="GitHub stars" src="https://img.shields.io/github/stars/zeyi-lin/hivisionidphotos?color=ffcb47&labelColor=black&style=flat-square" onerror="this.style.display='none';"></a> &ensp;
      <a href="https://swanlab.cn?utm_source=hivision_demo"><img alt="SwanLab" src="https://img.shields.io/badge/Training%20by-SwanLab-4cb55e" onerror="this.style.display='none';"></a> &ensp;
      <a href="https://github.com/Zeyi-Lin/HivisionIDPhotos/blob/master/docs/api_CN.md" target="_blank"><img alt="Static Badge" src="https://img.shields.io/badge/API_Docs-API文档-315bce" onerror="this.style.display='none';"></a>
  </div>
</div>
