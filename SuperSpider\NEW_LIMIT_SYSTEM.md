# 新的限制系统设计文档

## 📋 **系统概述**

根据你的需求，我重新设计了限制系统，实现了真正的使用限制控制：

### 🎯 **核心理念**
- **搜索即下载**: 每次搜索操作同时消耗1次下载和1次API调用
- **分钟级API限制**: 防止用户短时间内频繁调用
- **每日自动重置**: 凌晨12点自动重置所有计数器

## 📊 **限制配置**

### 👤 **普通用户 (normal_user)**
- 📥 **每日搜索限制**: 5次
- 🔄 **API调用限制**: 3次/分钟
- 🔓 **可用平台**: 抖音、快手、哔哩哔哩、CSDN

### 💎 **Pro用户 (pro_user)**
- 📥 **每日搜索限制**: 50次
- 🔄 **API调用限制**: 10次/分钟
- 🔓 **可用平台**: 抖音、快手、哔哩哔哩、CSDN、知乎

### 👑 **超级管理员 (super_admin)**
- 📥 **每日搜索限制**: 无限制
- 🔄 **API调用限制**: 无限制
- 🔓 **可用平台**: 全部平台 + 管理功能

## 🛠️ **技术实现**

### 1. **数据库模型** (`backend/models/user_usage.py`)

```python
class UserUsage(db.Model):
    """用户使用统计表"""
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    date = db.Column(db.Date, default=datetime.date.today)
    download_count = db.Column(db.Integer, default=0)        # 当日下载次数
    api_call_count = db.Column(db.Integer, default=0)        # 当日API调用次数
    api_calls_this_minute = db.Column(db.Integer, default=0) # 本分钟API调用次数
    current_minute = db.Column(db.DateTime)                  # 当前分钟开始时间
```

### 2. **权限装饰器** (`backend/utils/permissions.py`)

```python
@require_search_limit()
def parse_article():
    """搜索限制装饰器会自动检查并记录使用情况"""
    pass
```

### 3. **定时任务** (`backend/utils/scheduler.py`)

```python
def daily_reset_task():
    """每日凌晨12点重置所有用户的使用统计"""
    pass
```

## 🔄 **工作流程**

### 📱 **用户搜索流程**

1. **用户发起搜索请求**
   ```
   POST /api/csdn/parse
   ```

2. **权限检查**
   - ✅ 检查平台权限 (`@require_permission`)
   - ✅ 检查API调用频率 (每分钟限制)
   - ✅ 检查每日下载次数 (每日限制)

3. **执行搜索**
   - 如果通过所有检查，执行实际搜索
   - 返回搜索结果

4. **记录使用统计**
   - 📊 API调用次数 +1
   - 📊 下载次数 +1
   - 📊 更新本分钟API调用计数

### ⏰ **每日重置流程**

1. **定时检查** (每分钟检查一次)
2. **凌晨12:00-12:05** 执行重置
3. **重置内容**:
   - 🔄 创建新的每日使用记录
   - 🔄 重置VIP账号使用统计
   - 🔄 清理过期Pro用户

## 📡 **API接口**

### 1. **获取今日使用情况**
```http
GET /api/usage/today
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "download_count": 2,
    "api_call_count": 8,
    "remaining_downloads": 3,
    "remaining_api_calls": 1,
    "limits": {
      "download_limit": 5,
      "api_rate_limit": 3
    },
    "reset_time": "2024-01-02T00:00:00"
  }
}
```

### 2. **获取使用摘要**
```http
GET /api/usage/stats/summary
```

### 3. **获取使用历史**
```http
GET /api/usage/history?days=7
```

## 🚫 **限制响应**

### API调用频率超限 (429)
```json
{
  "success": false,
  "message": "API调用频率超限，每分钟最多3次，请稍后再试",
  "data": {
    "remaining_api_calls": 0,
    "remaining_downloads": 3,
    "limit_type": "api_rate"
  }
}
```

### 每日下载超限 (429)
```json
{
  "success": false,
  "message": "今日下载次数已达上限(5次)，请明天再试或升级账户",
  "data": {
    "remaining_api_calls": 2,
    "remaining_downloads": 0,
    "limit_type": "download_limit"
  }
}
```

## 🎨 **前端显示**

### 用户权限页面更新
- 📊 显示 "每日搜索限制: 5次" 而不是 "每日下载限制: 5"
- 📊 显示 "API调用限制: 3次/分钟" 而不是 "3/分钟"

### 实时使用情况
- 📈 今日搜索: 2/5
- 📈 本分钟API: 1/3
- ⏰ 下次重置: 明天 00:00

## 🧪 **测试验证**

### 运行测试脚本
```bash
python test_new_limits.py
```

### 测试场景
1. ✅ 连续搜索测试 (验证每日限制)
2. ✅ 快速连续请求 (验证分钟限制)
3. ✅ 权限显示测试
4. ✅ 使用统计API测试

## 📝 **配置文件位置**

- **用户权限配置**: `backend/models/user.py` → `_get_default_permissions()`
- **限制检查逻辑**: `backend/utils/permissions.py` → `check_rate_limit()`
- **定时任务配置**: `backend/utils/scheduler.py` → `daily_reset_task()`
- **API装饰器**: `backend/api/csdn_api.py` → `@require_search_limit()`

## 🔧 **管理员功能**

### 手动重置用户统计
```http
POST /api/usage/reset
{
  "user_id": 1,
  "reset_type": "today"
}
```

### 查看所有用户统计
```http
GET /api/usage/stats/all?days=7
```

## 🎉 **优势特点**

1. **🎯 精确控制**: 真正实现了使用次数限制
2. **⚡ 实时生效**: 立即生效，无需重启
3. **🔄 自动重置**: 每日自动重置，无需手动干预
4. **📊 详细统计**: 提供丰富的使用统计信息
5. **🛡️ 防滥用**: 分钟级限制防止短时间内大量请求
6. **👥 角色区分**: 不同用户角色享有不同限制
7. **📱 前端友好**: 提供完整的API供前端显示

## 🚀 **部署说明**

1. **数据库迁移**: 需要创建 `user_usage` 表
2. **启动调度器**: 在应用启动时调用 `init_scheduler()`
3. **API注册**: 注册 `usage_api` 蓝图
4. **前端更新**: 更新权限显示页面

---

**🎊 新的限制系统已完全实现，符合你的所有需求！**
