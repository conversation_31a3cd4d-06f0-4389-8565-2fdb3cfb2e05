#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
带登录的限制系统测试
"""

import requests
import json

def test_with_login():
    """带登录的测试"""
    
    print("🔍 测试带登录的限制系统...")
    
    base_url = "http://127.0.0.1:5000/api"
    session = requests.Session()
    
    # 1. 先登录
    print("\n1️⃣ 尝试登录...")
    login_data = {
        "username": "admin",  # 假设有admin用户
        "password": "admin123"
    }
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功: {result.get('message', '')}")
                user_info = result.get('data', {})
                print(f"👤 用户信息: {user_info.get('username', 'N/A')} ({user_info.get('role', 'N/A')})")
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 2. 测试使用统计API
    print("\n2️⃣ 测试使用统计API...")
    try:
        response = session.get(f"{base_url}/usage/today")
        print(f"今日使用情况响应: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                usage_data = data.get('data', {})
                print(f"✅ 今日使用情况:")
                print(f"   📥 下载次数: {usage_data.get('download_count', 0)}")
                print(f"   🔄 API调用: {usage_data.get('api_call_count', 0)}")
                print(f"   ⏱️ 本分钟API: {usage_data.get('api_calls_this_minute', 0)}")
                
                limits = usage_data.get('limits', {})
                print(f"   📊 限制: 下载{limits.get('download_limit', 'N/A')}/天, API{limits.get('api_rate_limit', 'N/A')}/分钟")
            else:
                print(f"❌ 获取使用情况失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 3. 测试CSDN搜索
    print("\n3️⃣ 测试CSDN搜索...")
    test_data = {
        "article_url": "https://blog.csdn.net/weixin_44799217/article/details/126896103",
        "email": "<EMAIL>",
        "format": "html"
    }
    
    try:
        response = session.post(f"{base_url}/csdn/parse", json=test_data)
        print(f"CSDN搜索响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ CSDN搜索成功")
                data = result.get('data', {})
                print(f"   📄 文章标题: {data.get('title', 'N/A')}")
            else:
                print(f"⚠️ CSDN搜索失败: {result.get('message', '未知错误')}")
        elif response.status_code == 429:
            result = response.json()
            print(f"🚫 达到限制: {result.get('message', '限制超出')}")
            limit_data = result.get('data', {})
            print(f"   📊 剩余下载: {limit_data.get('remaining_downloads', 'N/A')}")
            print(f"   📊 剩余API调用: {limit_data.get('remaining_api_calls', 'N/A')}")
        else:
            print(f"❌ 其他错误: {response.status_code}")
            try:
                result = response.json()
                print(f"   错误信息: {result.get('message', response.text[:100])}")
            except:
                print(f"   错误信息: {response.text[:100]}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 4. 再次检查使用情况
    print("\n4️⃣ 再次检查使用情况...")
    try:
        response = session.get(f"{base_url}/usage/today")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                usage_data = data.get('data', {})
                print(f"✅ 更新后的使用情况:")
                print(f"   📥 下载次数: {usage_data.get('download_count', 0)}")
                print(f"   🔄 API调用: {usage_data.get('api_call_count', 0)}")
                print(f"   ⏱️ 本分钟API: {usage_data.get('api_calls_this_minute', 0)}")
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    return True

def test_register_and_login():
    """测试注册和登录"""
    
    print("\n🔐 测试注册和登录...")
    
    base_url = "http://127.0.0.1:5000/api"
    session = requests.Session()
    
    # 尝试注册一个测试用户
    register_data = {
        "username": "testuser",
        "password": "test123456",
        "phone": "13800138000",
        "sms_code": "123456"  # 假设的验证码
    }
    
    try:
        print("📝 尝试注册测试用户...")
        response = session.post(f"{base_url}/auth/register", json=register_data)
        print(f"注册响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"注册结果: {result.get('message', 'N/A')}")
        else:
            print(f"注册可能失败，继续尝试登录...")
            
    except Exception as e:
        print(f"注册异常: {e}")
    
    # 尝试登录
    login_data = {
        "username": "testuser",
        "password": "test123456"
    }
    
    try:
        print("🔑 尝试登录测试用户...")
        response = session.post(f"{base_url}/auth/login", json=login_data)
        print(f"登录响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功")
                user_info = result.get('data', {})
                print(f"👤 用户: {user_info.get('username', 'N/A')} ({user_info.get('role', 'N/A')})")
                return session
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 开始带登录的限制系统测试...")
    
    # 先尝试用已有用户登录
    success = test_with_login()
    
    if not success:
        print("\n🔄 尝试注册新用户...")
        session = test_register_and_login()
        
        if session:
            print("✅ 新用户登录成功，继续测试...")
            # 这里可以继续用新用户测试
        else:
            print("❌ 无法登录，测试结束")
    
    print("\n🎉 测试完成!")
