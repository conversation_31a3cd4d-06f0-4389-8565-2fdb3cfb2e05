#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试新的限制系统
验证普通用户5次/天，3次/分钟的限制
"""

import requests
import json
import time

def test_new_limits():
    """测试新的限制系统"""
    
    print("🔍 测试新的限制系统...")
    print("📊 普通用户限制: 每日5次搜索，每分钟3次API调用")
    print("📊 Pro用户限制: 每日50次搜索，每分钟10次API调用")
    print("📊 超级管理员: 无限制")
    
    base_url = "http://127.0.0.1:5000/api"
    
    # 1. 测试获取今日使用情况
    print("\n1️⃣ 测试获取今日使用情况...")
    try:
        response = requests.get(f"{base_url}/usage/today")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                usage_data = data.get('data', {})
                print(f"✅ 今日使用情况:")
                print(f"   📥 下载次数: {usage_data.get('download_count', 0)}/{usage_data.get('limits', {}).get('download_limit', 5)}")
                print(f"   🔄 API调用: {usage_data.get('api_call_count', 0)}次")
                print(f"   ⏱️ 本分钟API调用: {usage_data.get('api_calls_this_minute', 0)}/{usage_data.get('limits', {}).get('api_rate_limit', 3)}")
                print(f"   🔄 重置时间: {usage_data.get('reset_time', 'N/A')}")
            else:
                print(f"❌ 获取使用情况失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 2. 测试使用摘要
    print("\n2️⃣ 测试使用摘要...")
    try:
        response = requests.get(f"{base_url}/usage/stats/summary")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                summary = data.get('data', {})
                today = summary.get('today', {})
                print(f"✅ 使用摘要:")
                print(f"   📥 今日下载: {today.get('downloads', 'N/A')}")
                print(f"   🔄 今日API调用: {today.get('api_calls', 'N/A')}")
                print(f"   ⏱️ 本分钟API: {today.get('api_calls_this_minute', 'N/A')}")
                print(f"   📊 状态: {summary.get('status', 'N/A')}")
            else:
                print(f"❌ 获取摘要失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 3. 测试CSDN搜索限制
    print("\n3️⃣ 测试CSDN搜索限制...")
    test_data = {
        "article_url": "https://blog.csdn.net/weixin_44799217/article/details/126896103",
        "email": "<EMAIL>",
        "format": "html"
    }
    
    print("🔄 连续发送搜索请求测试限制...")
    
    for i in range(8):  # 测试8次请求，应该在第6次开始被限制
        print(f"\n   第{i+1}次搜索请求:")
        try:
            response = requests.post(
                f"{base_url}/csdn/parse",
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 搜索成功")
                else:
                    print(f"   ⚠️ 搜索失败: {result.get('message', '未知错误')}")
            elif response.status_code == 429:
                result = response.json()
                print(f"   🚫 达到限制: {result.get('message', '限制超出')}")
                limit_data = result.get('data', {})
                print(f"   📊 剩余下载: {limit_data.get('remaining_downloads', 'N/A')}")
                print(f"   📊 剩余API调用: {limit_data.get('remaining_api_calls', 'N/A')}")
                print(f"   📊 限制类型: {limit_data.get('limit_type', 'N/A')}")
            elif response.status_code == 403:
                result = response.json()
                print(f"   🔒 权限不足: {result.get('message', '权限被拒绝')}")
            else:
                print(f"   ❌ 其他错误: {response.status_code}")
                try:
                    result = response.json()
                    print(f"   错误信息: {result.get('message', response.text[:100])}")
                except:
                    print(f"   错误信息: {response.text[:100]}")
                    
        except requests.exceptions.ConnectionError:
            print("   ❌ 连接失败! 请确保服务器正在运行")
            break
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 如果是API频率限制，等待一分钟再继续
        if i < 7:
            print("   ⏳ 等待5秒后继续...")
            time.sleep(5)
    
    # 4. 测试权限API的新限制显示
    print("\n4️⃣ 测试权限API的新限制显示...")
    try:
        response = requests.get(f"{base_url}/permission/check")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                permissions = data.get('data', {}).get('permissions', {})
                print(f"✅ 权限信息:")
                print(f"   📥 每日搜索限制: {permissions.get('download_limit', 'N/A')}")
                print(f"   🔄 API调用限制: {permissions.get('api_rate_limit', 'N/A')}/分钟")
                print(f"   👤 用户角色: {data.get('data', {}).get('role', 'N/A')}")
            else:
                print(f"❌ 获取权限失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_limit_values():
    """测试不同用户角色的限制值"""
    
    print("\n🎯 验证限制配置...")
    
    # 这些值应该与backend/models/user.py中的配置一致
    expected_limits = {
        'normal_user': {
            'download_limit': 5,
            'api_rate_limit': 3
        },
        'pro_user': {
            'download_limit': 50,
            'api_rate_limit': 10
        },
        'super_admin': {
            'download_limit': -1,  # 无限制
            'api_rate_limit': -1   # 无限制
        }
    }
    
    print("📋 预期限制配置:")
    for role, limits in expected_limits.items():
        download_text = "无限制" if limits['download_limit'] == -1 else f"{limits['download_limit']}次/天"
        api_text = "无限制" if limits['api_rate_limit'] == -1 else f"{limits['api_rate_limit']}次/分钟"
        
        role_display = {
            'normal_user': '普通用户',
            'pro_user': 'Pro用户',
            'super_admin': '超级管理员'
        }.get(role, role)
        
        print(f"   {role_display}: 搜索 {download_text}, API {api_text}")

if __name__ == "__main__":
    print("🚀 开始测试新的限制系统...")
    
    test_limit_values()
    test_new_limits()
    
    print("\n🎉 测试完成!")
    print("\n📝 总结:")
    print("1. ✅ 普通用户: 每日5次搜索，每分钟3次API调用")
    print("2. ✅ Pro用户: 每日50次搜索，每分钟10次API调用") 
    print("3. ✅ 超级管理员: 无限制")
    print("4. ✅ 每日凌晨12点自动重置")
    print("5. ✅ 搜索操作同时消耗下载和API调用次数")
    print("\n💡 提示: 如果遇到连接错误，请确保后端服务器正在运行")
