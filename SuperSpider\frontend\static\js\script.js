/**
 * SuperSpider前端脚本
 * 处理页面交互和API请求
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签切换
    initTabSwitcher();

    // 初始化表单提交
    initFormSubmit();

    // 检查用户登录状态并更新工具区域显示
    if (typeof checkAuthStatus === 'function') {
        checkAuthStatus();
    }

    // 初始化平台工具切换器
    initPlatformToolSwitcher();

    // 初始化用户认证相关功能
    if (typeof initAuthFeatures === 'function') {
        initAuthFeatures();
    }

    // 初始化下载历史记录功能
    if (typeof initDownloadFeatures === 'function') {
        initDownloadFeatures();
    }

    // 初始化设置功能
    initSettingsFeatures();

    // 初始化主页搜索功能
    initHeroSearch();

    // 初始化反馈按钮
    initFeedbackButton();

    // 初始化更多平台按钮
    initMorePlatformsButton();

    // 初始化平台分类标签页
    initPlatformTabs();

    // 初始化平台搜索功能
    initPlatformSearch();

    // 初始化CSDN表单（移到initFormSubmit内部）

    // 立即初始化导航栏功能
    initNavigation();

    // 再次延迟初始化，确保覆盖其他可能的干扰
    setTimeout(() => {
        initNavigation();
    }, 500);
});

/**
 * 初始化标签页切换功能
 */
function initTabSwitcher() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabId = btn.getAttribute('data-tab');

            // 移除所有活动状态
            tabBtns.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 设置当前活动标签
            btn.classList.add('active');
            document.getElementById(tabId + '-tab').classList.add('active');
        });
    });
}

/**
 * 初始化导航栏功能
 */
function initNavigation() {
    console.log('初始化导航栏...');

    // 获取导航链接
    const navLinks = document.querySelectorAll('.main-nav a');
    console.log('找到导航链接数量:', navLinks.length);

    if (navLinks.length === 0) {
        console.error('未找到导航链接');
        return;
    }

    // 移除所有active类
    navLinks.forEach(link => {
        link.classList.remove('active');
        console.log('移除active类:', link.textContent);
    });

    // 设置首页为活动状态
    navLinks[0].classList.add('active');
    console.log('设置首页为活动状态');

    // 为每个导航链接添加点击事件
    navLinks.forEach((link, index) => {
        // 移除可能存在的onclick属性
        link.removeAttribute('onclick');

        link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('点击导航链接:', this.textContent);

            // 移除所有active类和内联样式
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
                // 清除可能存在的内联样式
                navLink.style.removeProperty('color');
                navLink.removeAttribute('style');
            });

            // 添加active类到当前链接
            this.classList.add('active');
            console.log('设置active类到:', this.textContent);

            // 处理滚动
            const href = this.getAttribute('href');
            if (href === '#' || index === 0) {
                // 首页
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            } else if (href && href.startsWith('#')) {
                // 其他锚点
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    const headerHeight = 80;
                    const targetTop = targetElement.offsetTop - headerHeight - 20;

                    window.scrollTo({
                        top: Math.max(0, targetTop),
                        behavior: 'smooth'
                    });
                }
            }
        }, true); // 使用捕获阶段
    });

    console.log('导航栏初始化完成');
}

/**
 * 新增: 初始化平台工具切换功能
 */
function initPlatformToolSwitcher() {
    const platformSelectBtns = document.querySelectorAll('.platform-select-btn');
    const toolContentWrappers = document.querySelectorAll('.tool-content-wrapper');
    const toolsSection = document.getElementById('tools');

    // 修复函数：确保所有工具内容都在同一个容器内
    function fixToolContents() {
        const toolContents = document.getElementById('tool-contents');
        if (!toolContents) return;

        // 检查是否有嵌套的工具内容
        const nestedToolContents = toolContents.querySelectorAll('#tool-contents');
        if (nestedToolContents.length > 0) {
            // 移除嵌套的工具内容容器，保留其子元素
            nestedToolContents.forEach(nestedContainer => {
                const parent = nestedContainer.parentNode;
                while (nestedContainer.firstChild) {
                    parent.insertBefore(nestedContainer.firstChild, nestedContainer);
                }
                parent.removeChild(nestedContainer);
            });
        }

        // 检查工具内容是否嵌套在其他工具内容中
        toolContents.querySelectorAll('.tool-content-wrapper').forEach(wrapper => {
            const nestedWrappers = wrapper.querySelectorAll('.tool-content-wrapper');
            if (nestedWrappers.length > 0) {
                // 将嵌套的工具内容移动到主容器中
                nestedWrappers.forEach(nestedWrapper => {
                    toolContents.appendChild(nestedWrapper);
                });
            }
        });
    }

    // 尝试修复工具内容结构
    fixToolContents();

    // 确保默认只有一个工具是活动的
    function ensureSingleActiveToolContent() {
        let activeFound = false;

        toolContentWrappers.forEach(wrapper => {
            if (wrapper.classList.contains('active') && !activeFound) {
                activeFound = true;
                wrapper.style.display = 'block';
            } else {
                wrapper.classList.remove('active');
                wrapper.style.display = 'none';
            }
        });

        // 如果没有活动工具，默认激活第一个工具
        if (!activeFound && toolContentWrappers.length > 0) {
            const defaultTool = toolContentWrappers[0];
            if (defaultTool) {
                defaultTool.classList.add('active');
                defaultTool.style.display = 'block';
            }
        }
    }

    // 确保初始状态只有一个工具是活动的
    ensureSingleActiveToolContent();

    platformSelectBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetToolId = btn.getAttribute('data-target-tool');

            // 隐藏所有工具内容并清空解析结果
            toolContentWrappers.forEach(wrapper => {
                wrapper.classList.remove('active');
                wrapper.style.display = 'none';
                // 清空该工具的解析结果
                clearToolResults(wrapper.id);
            });

            // 显示目标工具内容
            const targetToolElement = document.getElementById(targetToolId);
            if (targetToolElement) {
                targetToolElement.classList.add('active');
                targetToolElement.style.display = 'block';
            }

            // 平滑滚动到工具区域
            if (toolsSection) {
                toolsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    });
}

/**
 * 清空指定工具的解析结果
 * @param {string} toolId - 工具容器ID
 */
function clearToolResults(toolId) {
    // 根据不同的工具ID清空对应的结果
    switch (toolId) {
        case 'kuaishou-tool-content':
            clearKuaishouResults();
            break;
        case 'douyin-tool-content':
            clearDouyinResults();
            break;
        case 'bilibili-tool-content':
            clearBilibiliResults();
            break;
        case 'csdn-tool-content':
            clearCsdnResults();
            break;
    }
}

/**
 * 清空CSDN解析结果
 */
function clearCsdnResults() {
    // 清空结果显示区域
    const resultContainer = document.getElementById('csdn-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空状态信息
    const statusContainer = document.getElementById('csdn-status');
    if (statusContainer) {
        const messageElement = statusContainer.querySelector('.status-message');
        if (messageElement) {
            messageElement.textContent = '';
        }
        statusContainer.style.display = 'none';
    }

    // 重置进度条
    setProgress('csdn-progress', 0);

    // 清空表单（可选）
    const form = document.getElementById('csdn-form');
    if (form) {
        // 不清空表单，保留用户输入
    }
}

/**
 * 清空快手解析结果
 */
function clearKuaishouResults() {
    // 清空输入框
    const inputField = document.getElementById('kuaishou-url');
    if (inputField) {
        inputField.value = '';
    }

    // 隐藏结果容器
    const resultContainer = document.getElementById('kuaishou-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空视频播放器
    const videoPlayer = document.getElementById('kuaishou-video-player');
    if (videoPlayer) {
        videoPlayer.src = '';
        videoPlayer.load();
    }

    // 清空文本内容
    const elements = [
        'video-title', 'video-author', 'video-url'
    ];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '';
        }
    });

    // 隐藏状态容器
    const statusContainer = document.getElementById('kuaishou-status');
    if (statusContainer) {
        statusContainer.style.display = 'none';
    }
}

/**
 * 清空抖音解析结果
 */
function clearDouyinResults() {
    // 清空输入框
    const inputField = document.getElementById('douyin-url');
    if (inputField) {
        inputField.value = '';
    }

    // 隐藏结果容器
    const resultContainer = document.getElementById('douyin-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空视频播放器
    const videoPlayer = document.getElementById('douyin-video-player');
    if (videoPlayer) {
        videoPlayer.src = '';
        videoPlayer.load();
    }

    // 清空文本内容
    const elements = [
        'douyin-video-title', 'douyin-video-author', 'douyin-video-url'
    ];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '';
        }
    });

    // 隐藏状态容器
    const statusContainer = document.getElementById('douyin-status');
    if (statusContainer) {
        statusContainer.style.display = 'none';
    }
}

/**
 * 清空哔哩哔哩解析结果
 */
function clearBilibiliResults() {
    // 清空输入框
    const inputField = document.getElementById('bilibili-url');
    if (inputField) {
        inputField.value = '';
    }

    // 隐藏结果容器
    const resultContainer = document.getElementById('bilibili-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空视频播放器
    const videoPlayer = document.getElementById('bilibili-video-preview');
    if (videoPlayer) {
        videoPlayer.src = '';
        videoPlayer.load();
    }

    // 清空文本内容
    const elements = [
        'bilibili-video-title', 'bilibili-video-author', 'bilibili-video-url'
    ];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '';
        }
    });

    // 隐藏状态容器
    const statusContainer = document.getElementById('bilibili-status');
    if (statusContainer) {
        statusContainer.style.display = 'none';
    }


}

/**
 * 清空CSDN解析结果
 */
function clearCsdnResults() {
    // 清空输入框
    const inputField = document.getElementById('csdn-url');
    if (inputField) {
        inputField.value = '';
    }

    // 隐藏结果容器
    const resultContainer = document.getElementById('csdn-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空文本内容
    const elements = [
        'csdn-article-title', 'csdn-article-author', 'csdn-article-content'
    ];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '';
        }
    });

    // 隐藏状态容器
    const statusContainer = document.getElementById('csdn-status');
    if (statusContainer) {
        statusContainer.style.display = 'none';
    }


}

/**
 * 初始化所有表单提交功能
 */
function initFormSubmit() {
    // 通用函数
    function showStatus(containerId, message, type) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.style.display = 'block';

        const messageEl = container.querySelector('.status-message');
        if (messageEl) {
            messageEl.textContent = message;
            // 支持的类型: success, error, info, warning
            messageEl.className = 'status-message ' + (type || '');
        }
    }

    function setProgress(progressId, percent) {
        const progressBar = document.getElementById(progressId);
        if (!progressBar) return;

        progressBar.style.width = percent + '%';
    }

    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }

    function isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    // 辅助函数：触发文件下载
    function triggerDownload(url, filename) {
        if (!url || url === '#') {
            console.error('无效的下载URL');
            return false;
        }

        try {
            // 创建一个临时的a元素来触发下载
            const tempLink = document.createElement('a');
            tempLink.href = url;
            tempLink.download = filename || 'download';
            tempLink.style.display = 'none';
            document.body.appendChild(tempLink);
            tempLink.click();

            // 延迟移除临时元素
            setTimeout(() => {
                document.body.removeChild(tempLink);
            }, 100);

            return true;
        } catch (error) {
            console.error('触发下载失败:', error);
            return false;
        }
    }

    // CSDN文章下载
    const articleForm = document.getElementById('article-form');
    if (articleForm) {
        articleForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const articleUrl = document.getElementById('article-url').value;
            const email = document.getElementById('article-email').value;

            if (!isValidUrl(articleUrl) || !isValidEmail(email)) {
                showStatus('article-status', '请输入有效的文章链接和邮箱地址', 'error');
                return;
            }

            showStatus('article-status', '正在处理您的请求...', 'info');
            setProgress('article-progress', 30);

            // 模拟API请求
            setTimeout(() => {
                setProgress('article-progress', 100);
                showStatus('article-status', '文章下载请求已提交，请检查您的邮箱', 'success');
            }, 1500);
        });
    }

    // CSDN资源下载
    const resourceForm = document.getElementById('resource-form');
    if (resourceForm) {
        resourceForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const resourceUrl = document.getElementById('resource-url').value;
            const email = document.getElementById('resource-email').value;

            if (!isValidUrl(resourceUrl) || !isValidEmail(email)) {
                showStatus('resource-status', '请输入有效的资源链接和邮箱地址', 'error');
                return;
            }

            showStatus('resource-status', '正在处理您的请求...', 'info');
            setProgress('resource-progress', 30);

            // 模拟API请求
            setTimeout(() => {
                setProgress('resource-progress', 100);
                showStatus('resource-status', '资源下载请求已提交，请检查您的邮箱', 'success');
            }, 1500);
        });
    }

    // 快手视频解析
    const kuaishouForm = document.getElementById('kuaishou-form');
    if (kuaishouForm) {
        let kuaishouProcessing = false; // 防重复提交标志

        kuaishouForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 防止重复提交
            if (kuaishouProcessing) {
                showStatus('kuaishou-status', '正在处理中，请稍候...', 'warning');
                return;
            }

            const videoUrl = document.getElementById('kuaishou-url').value;

            if (!isValidUrl(videoUrl)) {
                showStatus('kuaishou-status', '请输入有效的视频链接', 'error');
                return;
            }

            // 设置处理中状态
            kuaishouProcessing = true;
            const submitBtn = kuaishouForm.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 解析中...';
            submitBtn.disabled = true;

            document.getElementById('kuaishou-result').style.display = 'none';
            showStatus('kuaishou-status', '正在解析视频信息...', 'info');
            setProgress('kuaishou-progress', 30);

            // 使用 fetch 调用后端API
            fetch('/api/kuaishou/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ video_url: videoUrl })
            })
            .then(response => {
                setProgress('kuaishou-progress', 70);
                if (!response.ok) {
                    if (response.status === 429) {
                        // 特殊处理429错误，先尝试解析响应体
                        return response.json().then(data => {
                            throw new Error(data.user_message || data.message || '操作太频繁，请稍后再试');
                        }).catch(() => {
                            throw new Error('操作太频繁，请稍后再试');
                        });
                    }
                    throw new Error(`网络错误! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                setProgress('kuaishou-progress', 100);

                if (data.success && data.data) {
                    const videoInfo = Array.isArray(data.data) ? data.data[0] : data.data;

                    // 更新结果区域
                    document.getElementById('video-title').textContent = videoInfo.title || '未知标题';
                    document.getElementById('video-author').textContent = videoInfo.author || '未知作者';

                    // 设置下载提醒
                    const videoUrlLink = document.getElementById('video-url');
                    if (videoUrlLink) {
                        videoUrlLink.innerHTML = '<i class="fas fa-info-circle"></i> 点击视频右下角三个点进行下载';
                    }

                    // 使用 title 字段生成建议的文件名 (title 字段名保持不变)
                    const suggestedFilename = videoInfo.title ? `${videoInfo.title}.mp4` : 'kuaishou_video.mp4';

                    // 点击事件处理已移除

                    // 设置视频预览
                    const videoPlayer = document.getElementById('video-preview');
                    if (videoPlayer && videoInfo.videoUrl) {
                        videoPlayer.src = videoInfo.videoUrl;
                        videoPlayer.load(); // 重新加载视频元素

                        // 添加错误处理
                        videoPlayer.onerror = function() {
                            console.error('视频加载失败');
                            showStatus('kuaishou-status', '视频预览加载失败，但您仍可下载视频', 'warning');
                            videoPlayer.style.display = 'none';
                        };

                        // 视频加载成功
                        videoPlayer.onloadeddata = function() {
                            console.log('视频预览加载成功');
                        };

                        // 视频播放器已设置完成
                        // 搜索记录在解析成功后立即创建，无需等待用户播放
                    }

                    // 直接下载按钮已移除

                    // 新增: 打印建议的文件名
                    console.log('建议的文件名:', suggestedFilename);

                    // 显示结果区域
                    document.getElementById('kuaishou-result').style.display = 'block';
                    showStatus('kuaishou-status', '解析成功!', 'success');

                    // 解析成功后立即创建搜索记录
                    if (window.isLoggedIn) {
                        createKuaishouDownloadRecord(videoInfo);
                    }
                } else {
                    // 检查是否是限制错误
                    if (data.error_type && (data.error_type === 'rate_limit' || data.error_type === 'daily_limit')) {
                        showLimitError(data, 'kuaishou');
                    } else {
                        showStatus('kuaishou-status', '解析失败: ' + (data.message || '无法获取视频信息'), 'error');
                    }
                    setProgress('kuaishou-progress', 0);
                }
            })
            .catch(error => {
                console.error('解析快手视频时出错:', error);
                setProgress('kuaishou-progress', 0);

                // 检查是否是限制错误
                if (error.message.includes('操作太频繁') || error.message.includes('已用完') || error.message.includes('已达上限')) {
                    // 尝试解析错误信息中的限制数据
                    const errorData = {
                        message: error.message,
                        user_message: error.message,
                        error_type: error.message.includes('频繁') ? 'rate_limit' : 'daily_limit'
                    };
                    showLimitError(errorData, 'kuaishou');
                } else {
                    showStatus('kuaishou-status', '解析失败: ' + error.message, 'error');
                }
            })
            .finally(() => {
                // 恢复按钮状态
                kuaishouProcessing = false;
                submitBtn.innerHTML = '<i class="fas fa-search"></i> 解析视频';
                submitBtn.disabled = false;
            });
        });
    }

    // 抖音视频解析
    const douyinForm = document.getElementById('douyin-form');
    if (douyinForm) {
        let douyinProcessing = false; // 防重复提交标志

        douyinForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 防止重复提交
            if (douyinProcessing) {
                showStatus('douyin-status', '正在处理中，请稍候...', 'warning');
                return;
            }

            const videoUrl = document.getElementById('douyin-url').value;

            if (!isValidUrl(videoUrl)) {
                showStatus('douyin-status', '请输入有效的视频链接', 'error');
                return;
            }

            // 设置处理中状态
            douyinProcessing = true;
            const submitBtn = douyinForm.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 解析中...';
            submitBtn.disabled = true;

            document.getElementById('douyin-result').style.display = 'none';
            showStatus('douyin-status', '正在解析视频信息...', 'info');
            setProgress('douyin-progress', 30);

            // 使用 fetch 调用后端API
            fetch('/api/douyin/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ video_url: videoUrl })
            })
            .then(response => {
                setProgress('douyin-progress', 70);
                if (!response.ok) {
                    if (response.status === 429) {
                        // 特殊处理429错误，先尝试解析响应体
                        return response.json().then(data => {
                            throw new Error(data.user_message || data.message || '操作太频繁，请稍后再试');
                        }).catch(() => {
                            throw new Error('操作太频繁，请稍后再试');
                        });
                    }
                    throw new Error(`网络错误! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                setProgress('douyin-progress', 100);

                console.log('抖音API返回的完整数据:', data);

                if (data.success && data.data) {
                    const videoInfo = Array.isArray(data.data) ? data.data[0] : data.data;
                    console.log('解析后的视频信息:', videoInfo);

                    // 更新结果区域
                    document.getElementById('douyin-video-title').textContent = videoInfo.title || '未知标题';
                    document.getElementById('douyin-video-author').textContent = videoInfo.author || '未知作者';

                    // 使用 title 字段生成建议的文件名
                    const suggestedVideoFilename = videoInfo.title ? `${videoInfo.title}.mp4` : 'douyin_video.mp4';

                    // 设置视频预览
                    const videoPlayer = document.getElementById('douyin-video-preview');
                    if (videoPlayer && videoInfo.videoUrl) {
                        console.log('抖音视频URL:', videoInfo.videoUrl);
                        console.log('视频播放器元素:', videoPlayer);

                        videoPlayer.src = videoInfo.videoUrl;
                        videoPlayer.load(); // 重新加载视频元素

                        // 设置封面图片（如果有的话）
                        if (videoInfo.coverUrl) {
                            videoPlayer.poster = videoInfo.coverUrl;
                            console.log('设置视频封面:', videoInfo.coverUrl);
                        }

                        // 添加更详细的错误处理
                        videoPlayer.onerror = function(e) {
                            console.error('抖音视频加载失败:', e);
                            console.error('视频URL:', videoPlayer.src);
                            console.error('错误详情:', videoPlayer.error);
                            showStatus('douyin-status', '视频预览加载失败，请使用视频下载下方按钮进行下载', 'warning');

                            // 如果没有封面图片，显示默认提示
                            if (!videoInfo.coverUrl) {
                                videoPlayer.poster = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuinhumikeeEoeazleaSreaUvjwvdGV4dD48L3N2Zz4=';
                            }
                        };

                        // 视频加载成功
                        videoPlayer.onloadeddata = function() {
                            console.log('抖音视频预览加载成功');
                            console.log('视频时长:', videoPlayer.duration);
                            console.log('视频尺寸:', videoPlayer.videoWidth, 'x', videoPlayer.videoHeight);
                        };

                        // 添加更多事件监听
                        videoPlayer.onloadstart = function() {
                            console.log('开始加载抖音视频');
                        };

                        videoPlayer.oncanplay = function() {
                            console.log('抖音视频可以播放');
                        };

                        videoPlayer.onstalled = function() {
                            console.warn('抖音视频加载停滞');
                        };
                    } else {
                        console.error('视频播放器元素或视频URL不存在');
                        console.log('videoPlayer:', videoPlayer);
                        console.log('videoInfo.videoUrl:', videoInfo.videoUrl);
                    }

                    // 设置"在新窗口打开"按钮
                    const openVideoBtn = document.getElementById('douyin-open-video');
                    if (openVideoBtn && videoInfo.videoUrl) {
                        openVideoBtn.style.display = 'inline-block';
                        openVideoBtn.onclick = function() {
                            // 创建下载记录
                            createDownloadRecord('douyin', videoInfo);
                            // 在新窗口打开视频
                            window.open(videoInfo.videoUrl, '_blank');
                        };
                    }

                    // 打印建议的文件名
                    console.log('建议的视频文件名:', suggestedVideoFilename);

                    // 显示结果区域
                    document.getElementById('douyin-result').style.display = 'block';
                    showStatus('douyin-status', '解析成功!', 'success');

                    // 解析成功后立即创建搜索记录
                    if (window.isLoggedIn) {
                        createDownloadRecord('douyin', {
                            videoUrl: videoInfo.videoUrl,
                            title: videoInfo.title,
                            author: videoInfo.author,
                            duration: videoInfo.duration,
                            coverUrl: videoInfo.coverUrl
                        });
                    }
                } else {
                    // 检查是否是限制错误
                    if (data.error_type && (data.error_type === 'rate_limit' || data.error_type === 'daily_limit')) {
                        showLimitError(data, 'douyin');
                    } else {
                        showStatus('douyin-status', '解析失败: ' + (data.message || '无法获取视频信息'), 'error');
                    }
                    setProgress('douyin-progress', 0);
                }
            })
            .catch(error => {
                console.error('解析抖音视频时出错:', error);
                setProgress('douyin-progress', 0);

                // 检查是否是限制错误
                if (error.message.includes('操作太频繁') || error.message.includes('已用完') || error.message.includes('已达上限')) {
                    // 尝试解析错误信息中的限制数据
                    const errorData = {
                        message: error.message,
                        user_message: error.message,
                        error_type: error.message.includes('频繁') ? 'rate_limit' : 'daily_limit'
                    };
                    showLimitError(errorData, 'douyin');
                } else {
                    showStatus('douyin-status', '解析失败: ' + error.message, 'error');
                }
            })
            .finally(() => {
                // 恢复按钮状态
                douyinProcessing = false;
                submitBtn.innerHTML = '<i class="fas fa-search"></i> 解析视频';
                submitBtn.disabled = false;
            });
        });
    }

    // 哔哩哔哩视频解析
    const bilibiliForm = document.getElementById('bilibili-form');
    if (bilibiliForm) {
        let bilibiliProcessing = false; // 防重复提交标志

        bilibiliForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 防止重复提交
            if (bilibiliProcessing) {
                showStatus('bilibili-status', '正在处理中，请稍候...', 'warning');
                return;
            }

            const videoUrl = document.getElementById('bilibili-url').value;

            if (!isValidUrl(videoUrl)) {
                showStatus('bilibili-status', '请输入有效的视频链接', 'error');
                return;
            }

            // 设置处理中状态
            bilibiliProcessing = true;
            const submitBtn = bilibiliForm.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 解析中...';
            submitBtn.disabled = true;

            document.getElementById('bilibili-result').style.display = 'none';
            showStatus('bilibili-status', '正在解析视频信息...', 'info');
            setProgress('bilibili-progress', 30);

            // 使用 fetch 调用后端API
            fetch('/api/bilibili/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ video_url: videoUrl })
            })
            .then(response => {
                setProgress('bilibili-progress', 70);
                if (!response.ok) {
                    if (response.status === 429) {
                        // 特殊处理429错误，先尝试解析响应体
                        return response.json().then(data => {
                            throw new Error(data.user_message || data.message || '操作太频繁，请稍后再试');
                        }).catch(() => {
                            throw new Error('操作太频繁，请稍后再试');
                        });
                    }
                    throw new Error(`网络错误! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                setProgress('bilibili-progress', 100);

                if (data.success && data.data) {
                    const videoInfo = Array.isArray(data.data) ? data.data[0] : data.data;

                    // 更新结果区域
                    document.getElementById('bilibili-video-title').textContent = videoInfo.title || '未知标题';
                    document.getElementById('bilibili-video-author').textContent = videoInfo.author || '未知UP主';

                    // 设置下载提醒
                    const bilibiliVideoUrlLink = document.getElementById('bilibili-video-url');
                    if (bilibiliVideoUrlLink) {
                        bilibiliVideoUrlLink.innerHTML = '<i class="fas fa-info-circle"></i> 点击视频右下角三个点进行下载';
                    }

                    // 设置视频预览
                    const videoPlayer = document.getElementById('bilibili-video-preview');
                    if (videoPlayer && videoInfo.videoUrl) {
                        console.log('哔哩哔哩视频URL:', videoInfo.videoUrl);
                        console.log('视频播放器元素:', videoPlayer);

                        videoPlayer.src = videoInfo.videoUrl;
                        videoPlayer.load(); // 重新加载视频元素

                        // 设置封面图片（如果有的话）
                        if (videoInfo.coverUrl) {
                            videoPlayer.poster = videoInfo.coverUrl;
                            console.log('设置视频封面:', videoInfo.coverUrl);
                        }

                        // 添加错误处理
                        videoPlayer.onerror = function(e) {
                            console.error('哔哩哔哩视频加载失败:', e);
                            console.error('视频URL:', videoPlayer.src);
                            console.error('错误详情:', videoPlayer.error);
                            showStatus('bilibili-status', '视频预览加载失败，这是正常现象，B站视频需要特殊处理', 'warning');

                            // 显示默认提示
                            if (!videoInfo.coverUrl) {
                                videoPlayer.poster = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuinhumikeeEoeazleaSreaUvjwvdGV4dD48L3N2Zz4=';
                            }
                        };

                        // 视频加载成功
                        videoPlayer.onloadeddata = function() {
                            console.log('哔哩哔哩视频预览加载成功');
                            console.log('视频时长:', videoPlayer.duration);
                            console.log('视频尺寸:', videoPlayer.videoWidth, 'x', videoPlayer.videoHeight);
                        };
                    } else {
                        console.error('视频播放器元素或视频URL不存在');
                        console.log('videoPlayer:', videoPlayer);
                        console.log('videoInfo.videoUrl:', videoInfo.videoUrl);
                    }

                    // 使用 title 字段生成建议的文件名
                    const suggestedVideoFilename = videoInfo.title ? `${videoInfo.title}.mp4` : 'bilibili_video.mp4';

                    // 打印建议的文件名
                    console.log('建议的视频文件名:', suggestedVideoFilename);

                    // 显示结果区域
                    document.getElementById('bilibili-result').style.display = 'block';
                    showStatus('bilibili-status', '解析成功!', 'success');

                    // 解析成功后立即创建搜索记录
                    if (window.isLoggedIn) {
                        createDownloadRecord('bilibili', {
                            videoUrl: videoInfo.videoUrl,
                            title: videoInfo.title,
                            author: videoInfo.author,
                            duration: videoInfo.duration,
                            coverUrl: videoInfo.coverUrl
                        });
                    }
                } else {
                    // 检查是否是限制错误
                    if (data.error_type && (data.error_type === 'rate_limit' || data.error_type === 'daily_limit')) {
                        showLimitError(data, 'bilibili');
                    } else {
                        showStatus('bilibili-status', '解析失败: ' + (data.message || '无法获取视频信息'), 'error');
                    }
                    setProgress('bilibili-progress', 0);
                }
            })
            .catch(error => {
                console.error('解析哔哩哔哩视频时出错:', error);
                setProgress('bilibili-progress', 0);

                // 检查是否是限制错误
                if (error.message.includes('操作太频繁') || error.message.includes('已用完') || error.message.includes('已达上限')) {
                    // 尝试解析错误信息中的限制数据
                    const errorData = {
                        message: error.message,
                        user_message: error.message,
                        error_type: error.message.includes('频繁') ? 'rate_limit' : 'daily_limit'
                    };
                    showLimitError(errorData, 'bilibili');
                } else {
                    showStatus('bilibili-status', '解析失败: ' + error.message, 'error');
                }
            })
            .finally(() => {
                // 恢复按钮状态
                bilibiliProcessing = false;
                submitBtn.innerHTML = '<i class="fas fa-search"></i> 解析视频';
                submitBtn.disabled = false;
            });
        });
    }

    // CSDN表单处理
    const csdnForm = document.getElementById('csdn-form');
    if (csdnForm) {
        let csdnProcessing = false;

        // 格式选择变化时更新按钮文字
        const formatSelect = document.getElementById('csdn-format');
        const submitBtn = document.getElementById('csdn-submit-btn');

        if (formatSelect && submitBtn) {
            formatSelect.addEventListener('change', function() {
                const format = this.value;
                let buttonText = '';
                let iconClass = '';

                switch(format) {
                    case 'html':
                        buttonText = '生成HTML';
                        iconClass = 'fas fa-file-code';
                        break;
                    case 'pdf':
                        buttonText = '生成PDF';
                        iconClass = 'fas fa-file-pdf';
                        break;
                    case 'markdown':
                        buttonText = '生成Markdown';
                        iconClass = 'fab fa-markdown';
                        break;
                    default:
                        buttonText = '生成文件';
                        iconClass = 'fas fa-file';
                }

                submitBtn.innerHTML = `<i class="${iconClass}"></i> ${buttonText}`;
            });
        }

        csdnForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            if (csdnProcessing) {
                console.log('CSDN解析正在进行中，请稍候...');
                return;
            }

            const formData = new FormData(this);
            let articleUrl = formData.get('article_url');
            const email = formData.get('email');
            const format = formData.get('format') || 'html';

            // 链接预处理 - 借鉴csdn-test.py的逻辑
            if (articleUrl) {
                // 移除锚点文本
                const splits = articleUrl.split("#:~:text=");
                if (splits.length > 1) {
                    articleUrl = splits[0];
                }
                // 清理多余的查询参数
                articleUrl = articleUrl.split('&utm_')[0].split('?utm_')[0];
                // 移除末尾的查询参数噪音
                articleUrl = articleUrl.replace(/[?&](ops_request_misc|request_id|biz_id|utm_medium|utm_term|spm)=[^&]*/g, '');
                // 清理连续的&符号
                articleUrl = articleUrl.replace(/[&]{2,}/g, '&').replace(/[?&]$/, '');
            }

            if (!articleUrl || !email) {
                showStatus('csdn-status', '请填写完整的文章链接和邮箱地址', 'error');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showStatus('csdn-status', '请输入有效的邮箱地址', 'error');
                return;
            }

            // 验证CSDN链接格式 - 更严格的验证
            if (!articleUrl.includes('blog.csdn.net') && !articleUrl.includes('csdn.net')) {
                showStatus('csdn-status', '请输入有效的CSDN文章链接', 'error');
                return;
            }

            // 检查是否为文章链接而非下载链接
            if (articleUrl.includes('download.csdn.net')) {
                showStatus('csdn-status', '检测到下载链接，请输入文章链接而非资源下载链接', 'error');
                return;
            }

            csdnProcessing = true;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;

            try {
                // 更新按钮状态 - 类似抖音快手的动态效果
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 解析中...';
                submitBtn.disabled = true;

                // 显示详细的进度步骤
                showStatus('csdn-status', '🔍 正在分析文章链接...', 'info');
                setProgress('csdn-progress', 10);

                // 模拟链接分析过程
                await new Promise(resolve => setTimeout(resolve, 500));
                showStatus('csdn-status', '🌐 正在连接CSDN服务器...', 'info');
                setProgress('csdn-progress', 25);

                // 发送解析请求
                const response = await fetch('/api/csdn/parse', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        article_url: articleUrl,
                        email: email,
                        format: format
                    })
                });

                setProgress('csdn-progress', 50);
                showStatus('csdn-status', '📖 正在解析文章内容...', 'info');

                // 特殊处理429错误
                if (!response.ok && response.status === 429) {
                    const errorData = await response.json();
                    throw new Error(errorData.user_message || errorData.message || '操作太频繁，请稍后再试');
                }

                const data = await response.json();
                setProgress('csdn-progress', 75);

                if (data.success) {
                    setProgress('csdn-progress', 90);
                    showStatus('csdn-status', '🎯 解析成功，正在生成文件...', 'info');

                    // 显示详细的文章信息 - 借鉴抖音快手的展示方式
                    displayCSDNArticleInfo(data.data, format);

                    // 创建搜索记录
                    if (window.isLoggedIn && typeof createCsdnSearchRecord === 'function') {
                        createCsdnSearchRecord(data.data);
                    }

                    // 最终完成状态
                    setTimeout(() => {
                        setProgress('csdn-progress', 100);
                        const formatText = format === 'html' ? 'HTML文件' :
                                         format === 'pdf' ? 'PDF文件' :
                                         format === 'markdown' ? 'Markdown文件' : '文件';
                        showStatus('csdn-status', `✅ ${formatText}已生成并发送到您的邮箱`, 'success');
                    }, 1500);

                } else {
                    // 检查是否是限制错误
                    if (data.error_type && (data.error_type === 'rate_limit' || data.error_type === 'daily_limit')) {
                        showLimitError(data, 'csdn');
                    } else {
                        showStatus('csdn-status', '❌ 解析失败: ' + (data.message || '无法解析文章'), 'error');
                    }
                    setProgress('csdn-progress', 0);
                }

            } catch (error) {
                console.error('解析CSDN文章时出错:', error);
                setProgress('csdn-progress', 0);

                // 检查是否是限制错误
                if (error.message.includes('操作太频繁') || error.message.includes('已用完') || error.message.includes('已达上限')) {
                    // 尝试解析错误信息中的限制数据
                    const errorData = {
                        message: error.message,
                        user_message: error.message,
                        error_type: error.message.includes('频繁') ? 'rate_limit' : 'daily_limit'
                    };
                    showLimitError(errorData, 'csdn');
                } else {
                    showStatus('csdn-status', '解析失败: ' + error.message, 'error');
                }
            } finally {
                // 恢复按钮状态
                csdnProcessing = false;
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            }
        });
    }

}

/**
 * 显示CSDN文章信息 - 借鉴抖音快手的展示方式
 */
function displayCSDNArticleInfo(articleData, format) {
    const resultContainer = document.getElementById('csdn-result');
    if (!resultContainer) return;

    // 构建VIP状态信息
    let vipStatusHtml = '';
    if (articleData.is_vip_content) {
        const vipTypeMap = {
            'vip_member': { name: '🔥 VIP会员内容', color: '#ff6b35' },
            'column': { name: '📚 专栏内容', color: '#4ecdc4' },
            'paid': { name: '💰 付费内容', color: '#45b7d1' },
            'unsupported': { name: '❌ 不支持的专栏', color: '#96ceb4' }
        };

        const vipType = vipTypeMap[articleData.vip_type] || { name: '🔒 付费内容', color: '#feca57' };
        const usedVip = articleData.used_vip_account ? '✅ 已使用VIP账号解锁' : '⚠️ 未使用VIP账号';

        vipStatusHtml = `
            <div class="vip-status-card" style="
                background: linear-gradient(135deg, ${vipType.color}20 0%, ${vipType.color}10 100%);
                border-left: 4px solid ${vipType.color};
                padding: 12px;
                border-radius: 8px;
                margin: 12px 0;
            ">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <span style="font-weight: 600; color: ${vipType.color};">${vipType.name}</span>
                </div>
                <div style="font-size: 14px; color: #666; margin-bottom: 4px;">
                    状态: <span style="color: ${articleData.used_vip_account ? '#27ae60' : '#f39c12'};">${usedVip}</span>
                </div>
                ${articleData.price_info ? `<div style="font-size: 14px; color: #666; margin-bottom: 4px;">价格: ${articleData.price_info}</div>` : ''}
                ${articleData.content_warning ? `<div style="font-size: 14px; color: #e74c3c; margin-bottom: 4px;">⚠️ ${articleData.content_warning}</div>` : ''}
                ${articleData.content_status ? `<div style="font-size: 14px; color: #27ae60;">✅ ${articleData.content_status}</div>` : ''}
            </div>
        `;
    } else {
        vipStatusHtml = `
            <div class="free-content-card" style="
                background: linear-gradient(135deg, #27ae6020 0%, #27ae6010 100%);
                border-left: 4px solid #27ae60;
                padding: 10px;
                border-radius: 8px;
                margin: 12px 0;
            ">
                <span style="font-size: 14px; color: #27ae60; font-weight: 500;">🆓 免费内容，无需VIP</span>
            </div>
        `;
    }

    // 格式化内容长度
    const contentLength = articleData.content_text ? articleData.content_text.length : 0;
    const contentLengthText = contentLength > 1000 ? `${(contentLength / 1000).toFixed(1)}k字符` : `${contentLength}字符`;

    // 构建完整的结果HTML
    const resultHtml = `
        <div class="article-result-card" style="
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e1e8ed;
            margin-top: 20px;
        ">
            <div class="article-header" style="margin-bottom: 16px;">
                <h4 style="
                    margin: 0 0 8px 0;
                    color: #2c3e50;
                    font-size: 18px;
                    font-weight: 600;
                    line-height: 1.4;
                ">${articleData.title || '未知标题'}</h4>
                <div style="display: flex; align-items: center; gap: 16px; color: #666; font-size: 14px;">
                    <span>👤 ${articleData.author || '未知作者'}</span>
                    <span>📅 ${articleData.publish_time || '未知时间'}</span>
                    <span>👁️ ${articleData.read_count || '0'} 阅读</span>
                    <span>📝 ${contentLengthText}</span>
                </div>
            </div>

            ${vipStatusHtml}

            <div class="format-info" style="
                background: #f8f9fa;
                padding: 12px;
                border-radius: 8px;
                margin-top: 12px;
                border-left: 4px solid #007bff;
            ">
                <div style="font-weight: 600; color: #007bff; margin-bottom: 4px;">
                    📄 输出格式: ${format.toUpperCase()}
                </div>
                <div style="font-size: 14px; color: #666;">
                    ${getFormatDescription(format)}
                </div>
            </div>

            <div class="action-buttons" style="
                display: flex;
                gap: 12px;
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid #e1e8ed;
            ">
                <button onclick="window.open('${articleData.url}', '_blank')" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    transition: background 0.2s;
                " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                    🔗 查看原文
                </button>
                <button onclick="copyToClipboard('${articleData.url}')" style="
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    transition: background 0.2s;
                " onmouseover="this.style.background='#1e7e34'" onmouseout="this.style.background='#28a745'">
                    📋 复制链接
                </button>
            </div>
        </div>
    `;

    resultContainer.innerHTML = resultHtml;
    resultContainer.style.display = 'block';

    // 添加动画效果
    resultContainer.style.opacity = '0';
    resultContainer.style.transform = 'translateY(20px)';
    setTimeout(() => {
        resultContainer.style.transition = 'all 0.3s ease';
        resultContainer.style.opacity = '1';
        resultContainer.style.transform = 'translateY(0)';
    }, 100);
}

/**
 * 获取格式描述
 */
function getFormatDescription(format) {
    const descriptions = {
        'html': '保留完整格式和样式，支持代码高亮，可在浏览器中查看',
        'pdf': '适合打印和分享，格式固定，支持所有设备查看',
        'markdown': '纯文本格式，适合编辑和版本控制，兼容性强'
    };
    return descriptions[format] || '标准格式输出';
}

/**
 * 复制文本到剪贴板
 */
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器
        navigator.clipboard.writeText(text).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // 备用方法
        fallbackCopyTextToClipboard(text);
    }
}

/**
 * 备用复制方法
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('链接已复制到剪贴板', 'success');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }

    document.body.removeChild(textArea);
}

/**
 * 显示提示消息
 */
function showToast(message, type = 'info') {
    // 如果已经有toast，先移除
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(toast);

    // 动画显示
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * 初始化设置功能
 */
function initSettingsFeatures() {
    // 设置链接点击事件
    const settingsLink = document.getElementById('settings-link');
    if (settingsLink) {
        settingsLink.addEventListener('click', (e) => {
            e.preventDefault();
            // 使用全局 openModal 函数或本地函数
            if (typeof window.openModal === 'function') {
                window.openModal('settings-modal');
            } else {
                openModalLocal('settings-modal');
            }
            loadUserSettings();
        });
    }

    // 个人资料设置表单提交
    const profileSettingsForm = document.getElementById('profile-settings-form');
    if (profileSettingsForm) {
        profileSettingsForm.addEventListener('submit', handleProfileSettingsSubmit);
    }

    // 密码设置表单提交
    const passwordSettingsForm = document.getElementById('password-settings-form');
    if (passwordSettingsForm) {
        passwordSettingsForm.addEventListener('submit', handlePasswordSettingsSubmit);
    }

    // 密码强度检测已移除 - 使用简化的密码校验

    // 确认密码验证
    const confirmNewPasswordInput = document.getElementById('confirm-new-password');
    if (confirmNewPasswordInput && newPasswordInput) {
        confirmNewPasswordInput.addEventListener('input', () => {
            if (confirmNewPasswordInput.value && confirmNewPasswordInput.value !== newPasswordInput.value) {
                confirmNewPasswordInput.setCustomValidity('两次输入的密码不一致');
            } else {
                confirmNewPasswordInput.setCustomValidity('');
            }
        });
    }
}

/**
 * 加载用户设置
 */
async function loadUserSettings() {
    try {
        const response = await fetch('/api/auth/profile');
        const data = await response.json();

        if (data.success) {
            const profile = data.data;

            // 填充表单 - 用户名可编辑，手机号只读
            const usernameField = document.getElementById('settings-username');
            const phoneField = document.getElementById('settings-phone');

            if (usernameField) {
                usernameField.value = profile.username || '';
                // 存储原始用户名用于比较
                usernameField.dataset.originalValue = profile.username || '';
            }
            if (phoneField) {
                phoneField.value = profile.phone || '';
            }
        } else {
            console.error('获取用户资料失败:', data.message);
        }
    } catch (error) {
        console.error('获取用户资料请求失败:', error);
    }
}

/**
 * 处理个人资料设置表单提交
 */
async function handleProfileSettingsSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const statusContainer = document.getElementById('profile-settings-status');
    const usernameField = document.getElementById('settings-username');
    const validationContainer = document.getElementById('username-settings-validation');

    // 获取表单数据 - 只处理用户名（手机号为只读）
    const usernameValue = form.username.value.trim();
    const originalUsername = usernameField.dataset.originalValue || '';

    // 基本验证
    if (!usernameValue) {
        showStatus(statusContainer, '用户名不能为空', 'error');
        return;
    }

    if (usernameValue.length < 4 || usernameValue.length > 20) {
        showStatus(statusContainer, '用户名长度必须在4-20个字符之间', 'error');
        return;
    }

    // 用户名格式验证（必须以字母开头，只允许字母、数字、下划线）
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(usernameValue)) {
        showStatus(statusContainer, '用户名必须以字母开头，只能包含字母、数字和下划线', 'error');
        return;
    }

    // 如果用户名没有改变，不需要提交
    if (usernameValue === originalUsername) {
        showStatus(statusContainer, '用户名未发生变化', 'info');
        return;
    }

    // 先检查用户名是否已存在
    try {
        showStatus(statusContainer, '正在验证用户名...', 'info');

        const checkResponse = await fetch('/api/auth/check-username', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username: usernameValue })
        });

        const checkData = await checkResponse.json();

        if (!checkData.success || !checkData.available) {
            if (checkData.message.includes('已被占用') || checkData.message.includes('已存在')) {
                showValidationMessage(validationContainer, '用户名已存在，请选择其他用户名', 'error');
                showStatus(statusContainer, '用户名已存在', 'error');
            } else {
                showValidationMessage(validationContainer, checkData.message || '用户名验证失败', 'error');
                showStatus(statusContainer, checkData.message || '用户名验证失败', 'error');
            }
            return;
        }

        // 用户名可用，清除验证消息
        hideValidationMessage(validationContainer);

    } catch (error) {
        console.error('用户名验证请求失败:', error);
        showStatus(statusContainer, '用户名验证失败，请稍后再试', 'error');
        return;
    }

    // 提交用户名更新
    const formData = {
        username: usernameValue
    };

    try {
        showStatus(statusContainer, '正在保存...', 'info');

        const response = await fetch('/api/auth/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '保存成功', 'success');
            // 更新原始值
            usernameField.dataset.originalValue = usernameValue;

            // 更新页面上显示的用户名
            const usernameDisplay = document.getElementById('username-display');
            if (usernameDisplay) {
                usernameDisplay.textContent = usernameValue;
            }
        } else {
            showStatus(statusContainer, data.message || '保存失败', 'error');
        }
    } catch (error) {
        console.error('保存设置请求失败:', error);
        showStatus(statusContainer, '保存请求失败，请稍后再试', 'error');
    }
}

/**
 * 处理密码设置表单提交
 */
async function handlePasswordSettingsSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const statusContainer = document.getElementById('password-settings-status');

    const currentPassword = form.current_password.value;
    const newPassword = form.new_password.value;
    const confirmNewPassword = form.confirm_new_password.value;

    // 基本验证
    if (!currentPassword || !newPassword || !confirmNewPassword) {
        showStatus(statusContainer, '所有字段都是必填的', 'error');
        return;
    }

    if (newPassword !== confirmNewPassword) {
        showStatus(statusContainer, '两次输入的密码不一致', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showStatus(statusContainer, '密码长度至少为6个字符', 'error');
        return;
    }

    try {
        showStatus(statusContainer, '正在修改密码...', 'info');

        const response = await fetch('/api/auth/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword
            })
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '密码修改成功', 'success');

            // 清空表单
            form.reset();
        } else {
            showStatus(statusContainer, data.message || '密码修改失败', 'error');
        }
    } catch (error) {
        console.error('修改密码请求失败:', error);
        showStatus(statusContainer, '修改密码请求失败，请稍后再试', 'error');
    }
}

/**
 * 密码强度检测已移除 - 使用简化的密码校验
 * 现在只需要检查密码长度是否>=6个字符
 */

/**
 * 创建快手搜索记录（防抖版本）
 */
const createKuaishouDownloadRecord = debounceSearchRecord(async function(videoInfo) {
    // 检查用户是否已登录
    if (!window.isLoggedIn) {
        console.log('用户未登录，跳过创建快手搜索记录');
        return;
    }

    // 创建唯一标识符
    const recordKey = `kuaishou:${videoInfo.videoUrl}`;

    // 检查是否正在处理中
    if (window.processedSearchRecords.has(recordKey)) {
        console.log('该快手搜索记录正在处理中，跳过重复请求');
        return;
    }

    // 标记为正在处理
    window.processedSearchRecords.add(recordKey);

    try {
        console.log('准备创建快手搜索记录:', videoInfo);

        // 发送请求创建搜索记录
        const response = await fetch('/api/search/record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                platform: 'kuaishou',
                content_type: 'video',
                content_url: videoInfo.videoUrl,
                title: videoInfo.title,
                author: videoInfo.author,
                video_url: videoInfo.videoUrl,
                thumbnail_url: videoInfo.thumbnail
            })
        });

        const data = await response.json();
        if (data.success) {
            console.log('快手搜索记录创建成功:', data);

            // 只有在真正创建了新记录时才显示通知
            if (data.data && data.data.record_id) {
                showToast('已添加到搜索记录', 'success');
            }
        } else {
            console.error('创建快手搜索记录失败:', data.message);
        }
    } catch (error) {
        console.error('创建快手搜索记录请求失败:', error);
    } finally {
        // 延迟清除处理标记，防止快速重复请求
        setTimeout(() => {
            window.processedSearchRecords.delete(recordKey);
        }, 2000);
    }
}, 500); // 500ms 防抖延迟

// 快手搜索通知函数已移除，现在使用统一的 showToast 函数

/**
 * 初始化主页搜索功能
 */
function initHeroSearch() {
    const searchForm = document.getElementById('hero-search-form');
    const searchInput = document.getElementById('hero-search-input');
    const searchResult = document.getElementById('hero-search-result');
    const originalLinkEl = document.getElementById('original-link');
    const extractedLinkEl = document.getElementById('extracted-link');
    const copyLinkBtn = document.getElementById('copy-link-btn');
    const openLinkBtn = document.getElementById('open-link-btn');
    const closeResultBtn = document.getElementById('hero-result-close');

    if (!searchForm || !searchInput) return;

    // 处理搜索表单提交
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const inputText = searchInput.value.trim();
        if (!inputText) return;

        // 提取链接
        const extractedLink = extractLink(inputText);

        // 显示结果
        if (originalLinkEl) {
            originalLinkEl.textContent = inputText;
            originalLinkEl.style.color = 'var(--foreground)'; // 确保文本颜色为深色
        }
        if (extractedLinkEl) {
            extractedLinkEl.textContent = extractedLink || '未找到有效链接';
            extractedLinkEl.style.color = 'var(--foreground)'; // 确保文本颜色为深色
            extractedLinkEl.style.fontWeight = extractedLink ? '600' : '400'; // 如果找到链接，加粗显示
        }

        // 更新打开链接按钮状态
        if (openLinkBtn) {
            if (extractedLink) {
                openLinkBtn.disabled = false;
                openLinkBtn.onclick = function() {
                    window.open(extractedLink, '_blank');
                };
            } else {
                openLinkBtn.disabled = true;
                openLinkBtn.onclick = null;
            }
        }

        // 更新复制链接按钮状态
        if (copyLinkBtn) {
            if (extractedLink) {
                copyLinkBtn.disabled = false;
                copyLinkBtn.onclick = function() {
                    copyToClipboard(extractedLink);
                    alert('链接已复制到剪贴板');
                };
            } else {
                copyLinkBtn.disabled = true;
                copyLinkBtn.onclick = null;
            }
        }

        // 显示结果区域
        if (searchResult) {
            searchResult.style.display = 'block';
        }
    });

    // 关闭结果按钮
    if (closeResultBtn) {
        closeResultBtn.addEventListener('click', function() {
            if (searchResult) {
                searchResult.style.display = 'none';
            }
        });
    }
}

/**
 * 从文本中提取链接
 * @param {string} text - 包含链接的文本
 * @return {string|null} - 提取的链接或null
 */
function extractLink(text) {
    if (!text) return null;

    // 常见的URL模式
    const urlPatterns = [
        // 抖音链接
        /https?:\/\/v\.douyin\.com\/[a-zA-Z0-9]+\/?/,
        // 快手链接
        /https?:\/\/v\.kuaishou\.com\/[a-zA-Z0-9]+\/?/,
        // 哔哩哔哩链接
        /https?:\/\/(?:www\.)?bilibili\.com\/video\/[^\s]+/,
        // 微博链接
        /https?:\/\/weibo\.com\/[^\s]+/,
        // 通用HTTP/HTTPS链接
        /https?:\/\/[^\s]+/
    ];

    // 尝试匹配各种链接模式
    for (const pattern of urlPatterns) {
        const match = text.match(pattern);
        if (match && match[0]) {
            return match[0];
        }
    }

    return null;
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    // 创建临时textarea元素
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';  // 避免滚动到底部
    document.body.appendChild(textarea);
    textarea.select();

    try {
        // 执行复制命令
        document.execCommand('copy');
    } catch (err) {
        console.error('复制到剪贴板失败:', err);
    }

    // 移除临时元素
    document.body.removeChild(textarea);
}

/**
 * 显示状态消息
 */
function showStatus(container, message, type) {
    if (!container) return;

    container.style.display = 'block';

    const messageEl = container.querySelector('.status-message');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = 'status-message ' + (type || '');
    }
}

/**
 * 显示验证消息
 */
function showValidationMessage(container, message, type) {
    if (!container) return;

    container.style.display = 'block';
    container.textContent = message;
    container.className = 'validation-message ' + (type || '');
}

/**
 * 隐藏验证消息
 */
function hideValidationMessage(container) {
    if (!container) return;

    container.style.display = 'none';
    container.textContent = '';
    container.className = 'validation-message';
}

/**
 * 打开模态框
 * 注意：此函数可能与 auth.js 中的同名函数冲突
 * 我们使用 window.openModal 来确保使用全局函数
 */
function openModalLocal(modalId) {
    if (typeof window.openModal === 'function') {
        window.openModal(modalId);
    } else {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            document.body.classList.add('modal-open');
        }
    }
}

/**
 * 初始化反馈按钮功能
 */
function initFeedbackButton() {
    const feedbackBtn = document.getElementById('feedback-btn');
    if (feedbackBtn) {
        feedbackBtn.addEventListener('click', function() {
            // 使用全局 openModal 函数或本地函数
            if (typeof window.openModal === 'function') {
                window.openModal('wechat-modal');
            } else {
                openModalLocal('wechat-modal');
            }
        });
    }

    // 确保二维码模态框的关闭按钮正常工作
    const wechatModal = document.getElementById('wechat-modal');
    if (wechatModal) {
        const closeBtn = wechatModal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                if (typeof window.closeModal === 'function') {
                    window.closeModal('wechat-modal');
                } else {
                    closeModalLocal('wechat-modal');
                }
            });
        }

        // 点击遮罩层关闭模态框
        const overlay = wechatModal.querySelector('.modal-overlay');
        if (overlay) {
            overlay.addEventListener('click', function() {
                if (typeof window.closeModal === 'function') {
                    window.closeModal('wechat-modal');
                } else {
                    closeModalLocal('wechat-modal');
                }
            });
        }
    }
}

/**
 * 关闭模态框
 * 注意：此函数可能与 auth.js 中的同名函数冲突
 * 我们使用 window.closeModal 来确保使用全局函数
 */
function closeModalLocal(modalId) {
    if (typeof window.closeModal === 'function') {
        window.closeModal(modalId);
    } else {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        }
    }
}



/**
 * 初始化更多平台按钮功能
 */
function initMorePlatformsButton() {
    const morePlatformsBtn = document.getElementById('more-platforms-btn');

    if (morePlatformsBtn) {
        morePlatformsBtn.addEventListener('click', function() {
            // 直接打开模态框
            const modal = document.getElementById('platforms-roadmap-modal');
            if (modal) {
                modal.classList.add('show');
                document.body.classList.add('modal-open');
            }
        });
    }

    // 初始化模态框关闭功能
    initModalCloseHandlers();

    // 初始化平台投票功能
    initPlatformVoting();

    // 初始化平台建议表单
    initPlatformSuggestionForm();
}

/**
 * 初始化模态框关闭处理
 */
function initModalCloseHandlers() {
    // 为平台规划模态框添加关闭功能
    const modal = document.getElementById('platforms-roadmap-modal');
    if (modal) {
        // 关闭按钮
        const closeBtn = modal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
            });
        }

        // 点击遮罩层关闭
        const overlay = modal.querySelector('.modal-overlay');
        if (overlay) {
            overlay.addEventListener('click', function() {
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
            });
        }
    }
}

/**
 * 初始化平台投票功能
 */
function initPlatformVoting() {
    const voteButtons = document.querySelectorAll('.vote-btn');

    voteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.getAttribute('data-platform');
            const voteCountEl = this.querySelector('.vote-count');
            let voteCount = parseInt(voteCountEl.textContent);

            // 检查用户是否已经投票（使用localStorage存储）
            const votedPlatforms = JSON.parse(localStorage.getItem('votedPlatforms') || '[]');

            if (votedPlatforms.includes(platform)) {
                // 已经投票，取消投票
                voteCount--;
                voteCountEl.textContent = voteCount;

                // 从已投票列表中移除
                const index = votedPlatforms.indexOf(platform);
                votedPlatforms.splice(index, 1);

                // 移除活跃状态
                this.classList.remove('voted');
            } else {
                // 未投票，添加投票
                voteCount++;
                voteCountEl.textContent = voteCount;

                // 添加到已投票列表
                votedPlatforms.push(platform);

                // 添加活跃状态
                this.classList.add('voted');
            }

            // 保存投票状态
            localStorage.setItem('votedPlatforms', JSON.stringify(votedPlatforms));

            // 可以在这里添加向服务器发送投票的代码
            console.log(`用户为 ${platform} 平台投票，当前票数: ${voteCount}`);
        });
    });

    // 页面加载时恢复投票状态
    restoreVotingState();
}

/**
 * 恢复投票状态
 */
function restoreVotingState() {
    const votedPlatforms = JSON.parse(localStorage.getItem('votedPlatforms') || '[]');

    votedPlatforms.forEach(platform => {
        const button = document.querySelector(`.vote-btn[data-platform="${platform}"]`);
        if (button) {
            button.classList.add('voted');
        }
    });
}

/**
 * 初始化平台建议功能
 * 现在改为通过公众号提交建议，不再需要表单处理逻辑
 */
function initPlatformSuggestionForm() {
    // 检查二维码图片是否存在，如果不存在则显示加载错误提示
    const qrcodeImg = document.querySelector('.wechat-qrcode');
    if (qrcodeImg) {
        qrcodeImg.addEventListener('error', function() {
            this.src = '/static/images/logo.png';
            const qrcodeTip = document.querySelector('.qrcode-tip');
            if (qrcodeTip) {
                qrcodeTip.textContent = '扫码关注公众号';
            }
        });

        // 添加点击放大功能
        qrcodeImg.addEventListener('click', function() {
            // 创建一个模态框显示大图
            const modal = document.createElement('div');
            modal.className = 'qrcode-modal';
            modal.innerHTML = `
                <div class="qrcode-modal-overlay"></div>
                <div class="qrcode-modal-content">
                    <img src="${this.src}" alt="微信公众号二维码">
                    <p>关注公众号，提交您的平台建议</p>
                </div>
            `;

            document.body.appendChild(modal);

            // 点击关闭模态框
            modal.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        });
    }

    console.log('平台建议功能已初始化，用户将通过公众号提交建议');
}

/**
 * 初始化平台分类标签页
 */
function initPlatformTabs() {
    // 注意：标签页功能现在通过内联JavaScript实现
    // 这个函数保留用于其他可能的标签页相关初始化
    console.log('平台标签页初始化完成（使用内联JavaScript实现）');
}



/**
 * 初始化平台搜索功能
 */
function initPlatformSearch() {
    const searchInput = document.getElementById('platform-search');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        const platformCards = document.querySelectorAll('.platform-card');

        platformCards.forEach(card => {
            const platformName = card.querySelector('h3').textContent.toLowerCase();
            const platformDesc = card.querySelector('p').textContent.toLowerCase();

            // 如果搜索词为空或者平台名称/描述包含搜索词，则显示卡片
            if (searchTerm === '' || platformName.includes(searchTerm) || platformDesc.includes(searchTerm)) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        // 检查每个分类是否有可见的卡片
        const categories = document.querySelectorAll('.platform-category-content');
        categories.forEach(category => {
            const visibleCards = category.querySelectorAll('.platform-card[style="display: ;"], .platform-card:not([style*="display"])');

            // 如果当前分类没有可见的卡片，显示一个"无结果"消息
            const noResultsMsg = category.querySelector('.no-results-message');

            if (visibleCards.length === 0) {
                if (!noResultsMsg) {
                    const message = document.createElement('div');
                    message.className = 'no-results-message';
                    message.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-search"></i>
                            <p>没有找到匹配的平台</p>
                        </div>
                    `;
                    category.appendChild(message);
                }
            } else if (noResultsMsg) {
                category.removeChild(noResultsMsg);
            }
        });
    });

    // 添加清除搜索按钮功能
    const searchContainer = document.querySelector('.platform-search-container');
    if (searchContainer) {
        const clearButton = document.createElement('button');
        clearButton.className = 'search-clear-btn';
        clearButton.innerHTML = '<i class="fas fa-times"></i>';
        clearButton.style.display = 'none';
        searchContainer.appendChild(clearButton);

        searchInput.addEventListener('input', function() {
            clearButton.style.display = this.value ? 'block' : 'none';
        });

        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
            this.style.display = 'none';
        });
    }
}





// 全局变量，用于跟踪已处理的搜索记录
window.processedSearchRecords = window.processedSearchRecords || new Set();

/**
 * 创建搜索记录（防抖版本）
 * @param {string} platform - 平台名称
 * @param {object} videoInfo - 视频信息
 */
const createDownloadRecord = debounceSearchRecord(async function(platform, videoInfo) {
    // 检查用户是否已登录
    if (!window.isLoggedIn) {
        console.log('用户未登录，跳过创建搜索记录');
        return;
    }

    // 创建唯一标识符
    const recordKey = `${platform}:${videoInfo.videoUrl}`;

    // 检查是否正在处理中
    if (window.processedSearchRecords.has(recordKey)) {
        console.log('该搜索记录正在处理中，跳过重复请求');
        return;
    }

    // 标记为正在处理
    window.processedSearchRecords.add(recordKey);

    try {
        // 构建搜索记录数据
        const recordData = {
            platform: platform,
            content_type: 'video',
            content_url: videoInfo.videoUrl || '',
            title: videoInfo.title || '未知标题',
            author: videoInfo.author || '未知作者',
            video_url: videoInfo.videoUrl || '',
            duration: videoInfo.duration || 0,
            thumbnail_url: videoInfo.coverUrl || ''
        };

        console.log('准备创建搜索记录:', recordData);

        // 发送请求创建搜索记录
        const response = await fetch('/api/search/record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(recordData)
        });

        const data = await response.json();
        if (data.success) {
            console.log('搜索记录创建成功:', data.data);
            // 只在创建新记录时显示通知
            if (data.data && data.data.record_id) {
                showToast('已添加到搜索记录', 'success');
            }
        } else {
            console.error('创建搜索记录失败:', data.message);
        }
    } catch (error) {
        console.error('创建搜索记录请求失败:', error);
    } finally {
        // 延迟清除处理标记，防止快速重复请求
        setTimeout(() => {
            window.processedSearchRecords.delete(recordKey);
        }, 2000);
    }
}, 500); // 500ms 防抖延迟

/**
 * 防抖函数 - 专用于搜索记录创建
 */
function debounceSearchRecord(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}



/**
 * 显示CSDN解析结果
 */
function displayCsdnResult(articleData) {
    // 填充文章信息
    document.getElementById('csdn-article-title').textContent = articleData.title || '未知标题';
    document.getElementById('csdn-article-author').textContent = articleData.author || '未知作者';
    document.getElementById('csdn-article-time').textContent = articleData.publish_time || '未知时间';
    document.getElementById('csdn-article-reads').textContent = articleData.read_count || '0';

    // 显示文章摘要
    const summaryElement = document.getElementById('csdn-article-summary');
    if (articleData.content) {
        const summary = articleData.content.length > 200
            ? articleData.content.substring(0, 200) + '...'
            : articleData.content;
        summaryElement.textContent = summary;
    } else {
        summaryElement.textContent = '暂无摘要';
    }

    // 显示结果区域
    document.getElementById('csdn-result').style.display = 'block';
}

/**
 * 创建CSDN搜索记录
 */
const createCsdnSearchRecord = debounceSearchRecord(async function(articleData) {
    // 检查用户是否已登录
    if (!window.isLoggedIn) {
        console.log('用户未登录，跳过创建CSDN搜索记录');
        return;
    }

    // 创建唯一标识符
    const recordKey = `csdn:${articleData.url}`;

    // 检查是否正在处理中
    if (window.processedSearchRecords.has(recordKey)) {
        console.log('该CSDN搜索记录正在处理中，跳过重复请求');
        return;
    }

    // 标记为正在处理
    window.processedSearchRecords.add(recordKey);

    try {
        console.log('准备创建CSDN搜索记录:', articleData);

        // 发送请求创建搜索记录
        const response = await fetch('/api/search/record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                platform: 'csdn',
                content_type: 'article',
                content_url: articleData.url,
                title: articleData.title,
                author: articleData.author,
                thumbnail_url: articleData.thumbnail_url || ''
            })
        });

        const data = await response.json();
        if (data.success) {
            console.log('CSDN搜索记录创建成功:', data);

            // 只有在真正创建了新记录时才显示通知
            if (data.data && data.data.record_id) {
                showToast('已添加到搜索记录', 'success');
            }
        } else {
            console.error('创建CSDN搜索记录失败:', data.message);
        }
    } catch (error) {
        console.error('创建CSDN搜索记录请求失败:', error);
    } finally {
        // 延迟清除处理标记，防止快速重复请求
        setTimeout(() => {
            window.processedSearchRecords.delete(recordKey);
        }, 2000);
    }
}, 500); // 500ms 防抖延迟