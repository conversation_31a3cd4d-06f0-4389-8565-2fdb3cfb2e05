#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN爬虫模块 - 集成csdn-test.py优秀特性和抖音快手动态效果
用于解析CSDN文章链接，智能处理VIP内容，支持多格式输出
"""

import re
import time
import random
import requests
import logging
import html2text
import tempfile
import os
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
from datetime import datetime

from .base_spider import BaseSpider
from ..utils.vip_account_pool import vip_account_pool

# 创建日志记录器
logger = logging.getLogger(__name__)

# 用户代理列表 - 借鉴csdn-test.py
USER_AGENT_LIST = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/118.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.60"
]

# HTML模板 - 借鉴csdn-test.py的优秀设计
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }}
        .header {{
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .title {{
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.3;
        }}
        .meta {{
            color: #666;
            font-size: 14px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }}
        .meta-item {{
            display: flex;
            align-items: center;
        }}
        .meta-label {{
            font-weight: bold;
            margin-right: 5px;
        }}
        .content {{
            font-size: 16px;
            line-height: 1.8;
            color: #333;
        }}
        pre {{
            background-color: #f6f8fa;
            padding: 16px;
            overflow: auto;
            border-radius: 6px;
            border: 1px solid #e1e4e8;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
            line-height: 1.45;
            margin: 20px 0;
        }}
        code {{
            background-color: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
        }}
        img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
        }}
        h1, h2, h3, h4, h5, h6 {{
            color: #333;
            margin-top: 30px;
            margin-bottom: 15px;
            font-weight: bold;
        }}
        h1 {{ font-size: 24px; }}
        h2 {{ font-size: 22px; }}
        h3 {{ font-size: 20px; }}
        h4 {{ font-size: 18px; }}
        p {{
            margin-bottom: 15px;
        }}
        blockquote {{
            border-left: 4px solid #dfe2e5;
            padding-left: 16px;
            margin: 20px 0;
            color: #6a737d;
        }}
        ul, ol {{
            padding-left: 30px;
            margin-bottom: 15px;
        }}
        li {{
            margin-bottom: 5px;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        .footer {{
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e1e4e8;
            text-align: center;
            color: #666;
            font-size: 14px;
        }}
        .source-link {{
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #007acc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }}
        .source-link:hover {{
            background-color: #005a9e;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{title}</h1>
        <div class="meta">
            <div class="meta-item">
                <span class="meta-label">作者:</span>
                <span>{author}</span>
            </div>
            <div class="meta-item">
                <span class="meta-label">发布时间:</span>
                <span>{publish_time}</span>
            </div>
            <div class="meta-item">
                <span class="meta-label">阅读量:</span>
                <span>{read_count}</span>
            </div>
        </div>
    </div>

    <div class="content">
        {content}
    </div>

    <div class="footer">
        <p>文章来源: CSDN</p>
        <a href="{url}" target="_blank" class="source-link">查看原文</a>
        <p style="margin-top: 20px; font-size: 12px;">
            本文件由 SuperSpider 生成 | 生成时间: {generate_time}
        </p>
    </div>
</body>
</html>
"""


class CSDNSpider(BaseSpider):
    """
    CSDN爬虫类 - 集成csdn-test.py和抖音快手的优秀特性
    """

    def __init__(self):
        super().__init__("CSDN爬虫")

        # 设置请求头，模拟真实浏览器访问
        self.headers = {
            'User-Agent': random.choice(USER_AGENT_LIST),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }

        # 设置会话
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 添加反反爬虫措施
        self._setup_anti_detection()

    def _setup_anti_detection(self):
        """设置反反爬虫措施"""
        # 添加随机cookies模拟真实用户
        timestamp = str(int(time.time() * 1000))
        random_id = str(random.randint(100000000, 999999999))

        self.session.cookies.update({
            'uuid_tt_dd': f'10_{timestamp}_{random.randint(10000, 99999)}',
            'dc_session_id': random_id,
            'csrfToken': self._generate_csrf_token(),
            'Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
            'Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
        })

        # 设置请求间隔，避免请求过于频繁
        self.request_delay = random.uniform(1, 3)

    def _generate_csrf_token(self):
        """生成CSRF令牌"""
        return ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=32))

    def is_valid_url(self, url: str) -> bool:
        """
        验证URL是否为有效的CSDN文章链接

        Args:
            url: 待验证的URL

        Returns:
            bool: 是否为有效的CSDN文章链接
        """
        try:
            parsed = urlparse(url)

            # 检查域名
            valid_domains = ['blog.csdn.net', 'csdn.net']
            if not any(domain in parsed.netloc for domain in valid_domains):
                return False

            # 检查路径格式
            if 'blog.csdn.net' in parsed.netloc:
                # 标准文章格式: https://blog.csdn.net/username/article/details/123456
                return '/article/details/' in parsed.path

            return True

        except Exception as e:
            logger.error(f"URL验证失败: {e}")
            return False

    def clean_url(self, url: str) -> str:
        """
        清理URL，移除不必要的参数 - 借鉴csdn-test.py

        Args:
            url: 原始URL

        Returns:
            str: 清理后的URL
        """
        if not url:
            return url

        # 移除锚点文本
        splits = url.split("#:~:text=")
        if len(splits) > 1:
            url = splits[0]

        # 清理多余的查询参数
        url = url.split('&utm_')[0].split('?utm_')[0]

        # 移除特定的查询参数
        noise_params = [
            'ops_request_misc', 'request_id', 'biz_id',
            'utm_medium', 'utm_term', 'spm', 'utm_source',
            'utm_campaign', 'utm_content'
        ]

        for param in noise_params:
            url = re.sub(f'[?&]{param}=[^&]*', '', url)

        # 清理连续的&符号和末尾的?&
        url = re.sub(r'[&]{2,}', '&', url)
        url = re.sub(r'[?&]$', '', url)

        return url

    def extract_article_id(self, url: str) -> Optional[str]:
        """
        从URL中提取文章ID

        Args:
            url: CSDN文章URL

        Returns:
            Optional[str]: 文章ID，如果提取失败则返回None
        """
        try:
            # 匹配文章ID模式
            match = re.search(r'/article/details/(\d+)', url)
            if match:
                return match.group(1)
            return None
        except Exception as e:
            logger.error(f"提取文章ID失败: {e}")
            return None

    def send_request(self, url: str, use_vip: bool = False) -> Optional[requests.Response]:
        """
        发送HTTP请求获取页面内容 - 借鉴csdn-test.py的简洁性

        Args:
            url: 请求URL
            use_vip: 是否使用VIP账号

        Returns:
            Optional[requests.Response]: 响应对象，失败时返回None
        """
        try:
            if use_vip:
                # 使用VIP账号请求
                return self._request_with_vip_account(url)
            else:
                # 标准请求
                response = self.session.get(
                    url,
                    timeout=10,
                    verify=False,  # 绕过SSL验证
                    allow_redirects=True
                )
                response.encoding = "utf-8"

                if response.status_code == 200:
                    return response
                else:
                    logger.warning(f"请求失败: HTTP状态码 {response.status_code}")

        except Exception as e:
            logger.error(f"请求异常: {str(e)}")

        return None

    def _request_with_vip_account(self, url: str) -> Optional[requests.Response]:
        """使用VIP账号请求内容"""
        with vip_account_pool.use_account('csdn') as account:
            if not account:
                logger.warning("没有可用的CSDN VIP账号")
                return None

            try:
                # 创建新的session
                session = requests.Session()

                # 设置VIP账号的请求头
                headers = account.get_headers()
                if headers:
                    session.headers.update(headers)
                else:
                    session.headers.update(self.headers)

                # 设置VIP账号的cookies
                cookies = account.get_cookies()
                if cookies:
                    session.cookies.update(cookies)

                # 发送请求
                response = session.get(url, timeout=30, verify=False)
                response.raise_for_status()
                response.encoding = "utf-8"

                # 标记使用了VIP账号
                response._used_vip = True

                logger.info("VIP账号请求成功")
                return response

            except Exception as e:
                logger.error(f"VIP账号请求失败: {e}")
                return None

    def check_vip_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        检查是否为VIP内容并返回详细信息 - 增强版检测

        Args:
            soup: BeautifulSoup对象

        Returns:
            Dict: 包含VIP状态和类型的详细信息
        """
        result = {
            'is_vip_content': False,
            'vip_type': 'free',  # free, column, paid, vip_member, unsupported
            'unlock_methods': [],
            'price_info': '',
            'can_unlock': True,
            'content_length': 0
        }

        # 获取页面文本内容进行分析
        text_content = soup.get_text()
        text_lower = text_content.lower()

        # 计算内容长度
        result['content_length'] = len(text_content.strip())

        # 检查VIP内容的关键标识
        vip_indicators = [
            '.vip-article', '.member-article', '.pay-article',
            '[class*="vip"]', '[class*="member"]', '[class*="pay"]',
            '[class*="column"]', '[class*="subscribe"]'
        ]

        for indicator in vip_indicators:
            if soup.select(indicator):
                result['is_vip_content'] = True
                break

        # 检查具体的付费类型和解锁方式
        if '订阅专栏' in text_content and '解锁全文' in text_content:
            result['is_vip_content'] = True
            result['vip_type'] = 'column'
            result['unlock_methods'].append('订阅专栏')

        if '超级会员免费看' in text_content:
            result['is_vip_content'] = True
            result['vip_type'] = 'vip_member'
            result['unlock_methods'].append('超级会员')

        if '立即订阅' in text_content:
            result['is_vip_content'] = True
            if result['vip_type'] == 'free':
                result['vip_type'] = 'paid'
            result['unlock_methods'].append('付费订阅')

        # 检查价格信息
        price_patterns = [
            r'最低(\d+\.?\d*)元/天',
            r'(\d+\.?\d*)元/月',
            r'(\d+\.?\d*)元/年',
            r'￥(\d+\.?\d*)'
        ]

        for pattern in price_patterns:
            match = re.search(pattern, text_content)
            if match:
                result['price_info'] = match.group(0)
                result['is_vip_content'] = True
                if result['vip_type'] == 'free':
                    result['vip_type'] = 'paid'
                break

        # 检查是否为不支持的专栏内容
        if '了解本专栏' in text_content and '订阅专栏' in text_content:
            if '解锁全文' in text_content:
                result['can_unlock'] = False
                result['vip_type'] = 'unsupported'

        # 检查查看专栏按钮
        if '查看专栏' in text_content:
            result['is_vip_content'] = True
            if result['vip_type'] == 'free':
                result['vip_type'] = 'column'
            result['unlock_methods'].append('查看专栏')

        # 通用VIP关键词检查（作为补充）
        vip_keywords = ['vip', '会员', '付费', '专栏', '订阅', '解锁']
        trigger_words = ['需要', '开通', '购买', '升级']

        for keyword in vip_keywords:
            if keyword in text_lower:
                for trigger in trigger_words:
                    if trigger in text_content:
                        result['is_vip_content'] = True
                        if result['vip_type'] == 'free':
                            result['vip_type'] = 'paid'
                        break

        return result

    def extract_article_info(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        提取文章信息 - 借鉴csdn-test.py的提取逻辑

        Args:
            soup: BeautifulSoup对象
            url: 文章URL

        Returns:
            Dict: 文章信息字典
        """
        article_info = {
            'url': url,
            'title': '',
            'author': '',
            'publish_time': '',
            'read_count': '',
            'content': '',
            'content_text': '',
            'summary': ''
        }

        try:
            # 提取标题
            title_selectors = [
                '.title-article',
                'h1.title',
                '.article-title',
                'h1',
                'title'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem and title_elem.get_text().strip():
                    article_info['title'] = title_elem.get_text().strip()
                    break

            # 提取作者
            author_selectors = [
                '.follow-nickName',
                '.username',
                '.author-name',
                '[class*="author"]'
            ]

            for selector in author_selectors:
                author_elem = soup.select_one(selector)
                if author_elem and author_elem.get_text().strip():
                    article_info['author'] = author_elem.get_text().strip()
                    break

            # 提取发布时间
            time_selectors = [
                '.time',
                '.publish-time',
                '[class*="time"]',
                '.article-bar-top .time'
            ]

            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem and time_elem.get_text().strip():
                    article_info['publish_time'] = time_elem.get_text().strip()
                    break

            # 提取阅读量
            read_selectors = [
                '.read-count',
                '[class*="read"]',
                '.article-bar-top .read'
            ]

            for selector in read_selectors:
                read_elem = soup.select_one(selector)
                if read_elem and read_elem.get_text().strip():
                    read_text = read_elem.get_text().strip()
                    # 提取数字
                    read_match = re.search(r'(\d+)', read_text)
                    if read_match:
                        article_info['read_count'] = read_match.group(1)
                    break

            # 提取文章内容
            content_selectors = [
                '#content_views',
                '.article_content',
                '.blog-content-box',
                '.content',
                'article'
            ]

            content_elem = None
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    break

            if content_elem:
                # 清理内容
                self._clean_content_element(content_elem)

                # 获取HTML内容
                article_info['content'] = str(content_elem)

                # 获取纯文本内容
                article_info['content_text'] = content_elem.get_text().strip()

                # 生成摘要
                article_info['summary'] = self._generate_summary(article_info['content_text'])

        except Exception as e:
            logger.error(f"提取文章信息失败: {e}")

        return article_info

    def _clean_content_element(self, content_elem):
        """
        清理内容元素，移除不需要的标签和内容 - 借鉴csdn-test.py

        Args:
            content_elem: BeautifulSoup元素
        """
        # 移除的标签类型
        remove_tags = ['script', 'style', 'noscript', 'iframe']

        # 移除的CSS选择器
        remove_selectors = [
            '.hide-article-box',    # 隐藏的文章框
            '.article-copyright',   # 版权信息
            '.recommend-box',       # 推荐框
            '.ad', '.advertisement', '.recommend', '.related',
            '.comment', '.share', '.toolbar', '.sidebar',
            '.footer', '.header', '.nav', '.menu',
            '.csdn-tracking-statistics',  # CSDN统计
            '.tool-box',            # 工具箱
            '.article-bar-bottom',  # 底部工具栏
            '.comment-box',         # 评论框
            '.recommend-item-box',  # 推荐项目框
            '.aside-box',           # 侧边栏
            '.blog_container_aside' # 博客容器侧边栏
        ]

        # 移除标签
        for tag in remove_tags:
            for elem in content_elem.find_all(tag):
                elem.decompose()

        # 移除特定选择器的元素
        for selector in remove_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

        # 修复图片路径
        for img in content_elem.find_all('img'):
            # 处理懒加载图片
            if 'data-src' in img.attrs:
                img['src'] = img['data-src']
                del img['data-src']

            # 处理原始图片链接
            if 'data-original-src' in img.attrs:
                img['src'] = img['data-original-src']
                del img['data-original-src']

            # 移除没有src的图片
            if not img.get('src'):
                img.decompose()
                continue

            # 确保图片样式
            img['style'] = 'max-width: 100%; height: auto; display: block; margin: 0 auto;'

        # 移除空的段落和div
        for elem in content_elem.find_all(['p', 'div', 'span']):
            if not elem.get_text().strip() and not elem.find('img'):
                elem.decompose()

        # 清理属性，只保留必要的
        for elem in content_elem.find_all():
            # 保留的属性
            keep_attrs = ['href', 'src', 'alt', 'title', 'style', 'class']
            attrs_to_remove = []

            for attr in elem.attrs:
                if attr not in keep_attrs:
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                del elem.attrs[attr]

    def _generate_summary(self, content_text: str, max_length: int = 200) -> str:
        """
        生成文章摘要

        Args:
            content_text: 文章文本内容
            max_length: 摘要最大长度

        Returns:
            str: 文章摘要
        """
        if not content_text:
            return ""

        # 清理文本
        clean_text = re.sub(r'\s+', ' ', content_text).strip()

        if len(clean_text) <= max_length:
            return clean_text

        # 截取前max_length个字符，并在最后一个完整句子处截断
        summary = clean_text[:max_length]

        # 寻找最后一个句号、问号或感叹号
        last_sentence_end = max(
            summary.rfind('。'),
            summary.rfind('？'),
            summary.rfind('！'),
            summary.rfind('.')
        )

        if last_sentence_end > max_length // 2:
            summary = summary[:last_sentence_end + 1]
        else:
            summary = summary + '...'

        return summary

    def convert_to_markdown(self, article_info: Dict[str, Any]) -> str:
        """
        将文章内容转换为Markdown格式 - 借鉴csdn-test.py

        Args:
            article_info: 文章信息字典

        Returns:
            str: Markdown格式的内容
        """
        try:
            # 获取HTML内容
            html_content = article_info.get('content', '')
            if not html_content:
                return f"# {article_info.get('title', '未知标题')}\n\n无法获取文章内容"

            # 使用html2text转换
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.body_width = 0  # 禁用自动换行
            h.ignore_images = False
            h.ignore_emphasis = False
            h.ignore_tables = False

            # 转换内容
            markdown_content = h.handle(html_content)

            # 构建完整的Markdown文档
            full_markdown = f"""# {article_info.get('title', '未知标题')}

**作者**: {article_info.get('author', '未知作者')}
**发布时间**: {article_info.get('publish_time', '未知时间')}
**阅读量**: {article_info.get('read_count', '0')}
**原文链接**: [{article_info.get('url', '')}]({article_info.get('url', '')})

---

{markdown_content}

---

*本文档由 SuperSpider 自动生成*
"""

            return full_markdown

        except Exception as e:
            logger.error(f"转换Markdown失败: {e}")
            return f"# {article_info.get('title', '未知标题')}\n\n转换失败: {str(e)}"

    def convert_to_html(self, article_info: Dict[str, Any]) -> str:
        """
        将文章内容转换为HTML格式 - 使用优化的模板

        Args:
            article_info: 文章信息字典

        Returns:
            str: HTML格式的内容
        """
        try:
            return HTML_TEMPLATE.format(
                title=article_info.get('title', '未知标题'),
                author=article_info.get('author', '未知作者'),
                publish_time=article_info.get('publish_time', '未知时间'),
                read_count=article_info.get('read_count', '0'),
                content=article_info.get('content', '无内容'),
                url=article_info.get('url', ''),
                generate_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
        except Exception as e:
            logger.error(f"转换HTML失败: {e}")
            return f"<html><body><h1>{article_info.get('title', '未知标题')}</h1><p>转换失败: {str(e)}</p></body></html>"

    def parse_article(self, url: str) -> Dict[str, Any]:
        """
        解析CSDN文章 - 智能VIP处理，类似抖音快手的处理流程

        Args:
            url: CSDN文章URL

        Returns:
            Dict: 包含文章信息的字典
        """
        try:
            # 步骤1: 验证和清理URL
            if not self.is_valid_url(url):
                return {
                    'success': False,
                    'error': '无效的CSDN文章URL，请检查链接格式',
                    'data': None
                }

            clean_url = self.clean_url(url)
            logger.info(f"开始解析CSDN文章: {clean_url}")

            # 步骤2: 智能请求策略 - 先尝试普通请求
            response = None
            used_vip_account = False

            logger.info("🔍 正在分析文章链接...")
            time.sleep(self.request_delay)  # 防止请求过快

            # 尝试标准请求
            response = self.send_request(clean_url, use_vip=False)

            if not response:
                logger.warning("标准请求失败，尝试VIP账号...")
                response = self.send_request(clean_url, use_vip=True)
                used_vip_account = True

            if not response:
                return {
                    'success': False,
                    'error': '无法访问文章页面，请检查网络连接或稍后重试',
                    'data': None
                }

            # 步骤3: 解析HTML内容
            logger.info("📖 正在解析文章内容...")
            soup = BeautifulSoup(response.text, 'html.parser')

            # 步骤4: 检查VIP内容
            vip_info = self.check_vip_content(soup)

            # 步骤5: 智能VIP处理
            if vip_info['is_vip_content'] and not used_vip_account:
                # 检测到VIP内容但未使用VIP账号，且内容较短
                if vip_info['content_length'] < 1000:
                    logger.info(f"检测到VIP内容 ({vip_info['vip_type']})，尝试使用VIP账号...")
                    vip_response = self.send_request(clean_url, use_vip=True)

                    if vip_response and len(vip_response.text) > len(response.text):
                        logger.info("VIP账号获取到更多内容，使用VIP响应")
                        response = vip_response
                        used_vip_account = True
                        soup = BeautifulSoup(response.text, 'html.parser')
                        vip_info = self.check_vip_content(soup)  # 重新检查

            # 步骤6: 提取文章信息
            article_info = self.extract_article_info(soup, clean_url)

            # 步骤7: 添加VIP相关信息
            article_info.update({
                'is_vip_content': vip_info['is_vip_content'],
                'vip_type': vip_info['vip_type'],
                'unlock_methods': vip_info['unlock_methods'],
                'price_info': vip_info['price_info'],
                'can_unlock': vip_info['can_unlock'],
                'used_vip_account': used_vip_account
            })

            # 步骤8: 内容质量检查
            content_text_length = len(article_info.get('content_text', ''))

            if vip_info['is_vip_content']:
                if content_text_length < 500:
                    if used_vip_account:
                        article_info['content_warning'] = 'VIP解锁可能失败，内容不完整'
                        logger.warning(f"使用VIP账号但内容仍然较短 ({content_text_length}字符)")
                    else:
                        article_info['content_warning'] = '检测到VIP内容，但未使用VIP账号解锁'
                        logger.warning(f"VIP内容未使用VIP账号 ({content_text_length}字符)")
                else:
                    if used_vip_account:
                        article_info['content_status'] = 'VIP内容已成功解锁'
                        logger.info(f"VIP账号成功解锁内容 ({content_text_length}字符)")
                    else:
                        article_info['content_status'] = '成功获取内容'

            logger.info(f"✅ 成功解析CSDN文章: {article_info.get('title', '未知标题')} "
                       f"(VIP类型: {vip_info['vip_type']}, 使用VIP: {used_vip_account}, "
                       f"内容长度: {content_text_length}字符)")

            return {
                'success': True,
                'data': article_info
            }

        except Exception as e:
            logger.error(f"解析文章失败: {str(e)}")
            return {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'data': None
            }

    def generate_file(self, article_info: Dict[str, Any], format_type: str = 'html') -> str:
        """
        生成指定格式的文件 - 借鉴csdn-test.py的文件生成逻辑

        Args:
            article_info: 文章信息
            format_type: 文件格式 (html, markdown, pdf)

        Returns:
            str: 生成的文件路径
        """
        try:
            # 生成安全的文件名
            title = article_info.get('title', '未知标题')
            safe_title = re.sub(r'[\\/*?:"<>|]', '', title)[:50]
            timestamp = int(time.time())

            if format_type == 'html':
                filename = f"{safe_title}_{timestamp}.html"
                content = self.convert_to_html(article_info)

            elif format_type == 'markdown':
                filename = f"{safe_title}_{timestamp}.md"
                content = self.convert_to_markdown(article_info)

            elif format_type == 'pdf':
                # PDF生成需要额外的库支持
                filename = f"{safe_title}_{timestamp}.pdf"
                html_content = self.convert_to_html(article_info)
                return self._generate_pdf(html_content, filename)

            else:
                raise ValueError(f"不支持的格式: {format_type}")

            # 保存文件
            file_path = os.path.join(tempfile.gettempdir(), filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"文件生成成功: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成文件失败: {e}")
            raise

    def _generate_pdf(self, html_content: str, filename: str) -> str:
        """
        生成PDF文件

        Args:
            html_content: HTML内容
            filename: 文件名

        Returns:
            str: PDF文件路径
        """
        try:
            # 尝试使用weasyprint生成PDF
            import weasyprint

            file_path = os.path.join(tempfile.gettempdir(), filename)
            weasyprint.HTML(string=html_content).write_pdf(file_path)

            return file_path

        except ImportError:
            logger.error("PDF生成需要安装weasyprint库")
            raise Exception("PDF生成功能不可用，请安装weasyprint库")
        except Exception as e:
            logger.error(f"PDF生成失败: {e}")
            raise


# ==================== API接口部分 ====================

from flask import Blueprint, request, jsonify
import threading
from ..utils.email_sender import send_email

# 创建蓝图
csdn_bp = Blueprint('csdn', __name__)

@csdn_bp.route('/api/csdn/parse', methods=['POST'])
def parse_csdn_article():
    """
    解析CSDN文章API - 集成动态效果和智能VIP处理
    """
    try:
        data = request.json
        article_url = data.get('article_url')
        email = data.get('email')
        format_type = data.get('format', 'html')

        if not article_url or not email:
            return jsonify({
                'success': False,
                'message': '请提供文章链接和邮箱地址',
                'data': None
            }), 400

        # 创建爬虫实例
        spider = CSDNSpider()

        # 解析文章
        logger.info(f"开始解析CSDN文章: {article_url}")
        result = spider.parse_article(article_url)

        if not result['success']:
            return jsonify({
                'success': False,
                'message': result['error'],
                'data': None
            }), 400

        article_data = result['data']

        # 启动后台任务生成文件并发送邮件
        thread = threading.Thread(
            target=_process_csdn_article_background,
            args=(article_data, email, format_type, article_url)
        )
        thread.daemon = True
        thread.start()

        # 立即返回解析结果
        return jsonify({
            'success': True,
            'message': '文章解析成功，正在生成文件并发送到您的邮箱',
            'data': article_data
        })

    except Exception as e:
        logger.error(f"CSDN解析API错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}',
            'data': None
        }), 500


def _process_csdn_article_background(article_data, email, format_type, article_url):
    """
    后台处理CSDN文章 - 生成文件并发送邮件
    """
    try:
        logger.info(f"开始后台处理CSDN文章: {article_data.get('title', '未知标题')}")

        # 重新创建爬虫实例（因为在新线程中）
        spider = CSDNSpider()

        # 重新获取完整的文章内容（确保最新）
        full_result = spider.parse_article(article_url)

        if not full_result['success']:
            logger.error("无法获取完整文章内容")
            return

        full_article_data = full_result['data']

        # 生成文件
        timestamp = int(time.time())

        if format_type == 'html':
            filename = f"csdn_article_{timestamp}.html"
            file_path = spider.generate_file(full_article_data, 'html')

        elif format_type == 'markdown':
            filename = f"csdn_article_{timestamp}.md"
            file_path = spider.generate_file(full_article_data, 'markdown')

        elif format_type == 'pdf':
            filename = f"csdn_article_{timestamp}.pdf"
            file_path = spider.generate_file(full_article_data, 'pdf')

        else:
            logger.error(f"不支持的格式: {format_type}")
            return

        # 构建邮件内容
        subject = f"CSDN文章解析结果 - {full_article_data.get('title', '未知标题')}"

        # VIP状态信息
        vip_status_text = ""
        if full_article_data.get('is_vip_content'):
            vip_type_map = {
                'vip_member': 'VIP会员内容',
                'column': '专栏内容',
                'paid': '付费内容',
                'unsupported': '不支持的专栏'
            }
            vip_type_name = vip_type_map.get(full_article_data.get('vip_type'), '付费内容')
            used_vip = "已使用VIP账号解锁" if full_article_data.get('used_vip_account') else "未使用VIP账号"

            vip_status_text = f"""
VIP内容信息:
- 内容类型: {vip_type_name}
- 解锁状态: {used_vip}
"""
            if full_article_data.get('price_info'):
                vip_status_text += f"- 价格信息: {full_article_data.get('price_info')}\n"

            if full_article_data.get('content_warning'):
                vip_status_text += f"- 注意事项: {full_article_data.get('content_warning')}\n"

            if full_article_data.get('content_status'):
                vip_status_text += f"- 处理状态: {full_article_data.get('content_status')}\n"

        body = f"""
您好！

您请求的CSDN文章已成功解析并生成{format_type.upper()}文件。

文章信息:
- 标题: {full_article_data.get('title', '未知标题')}
- 作者: {full_article_data.get('author', '未知作者')}
- 发布时间: {full_article_data.get('publish_time', '未知时间')}
- 阅读量: {full_article_data.get('read_count', '0')}
- 原文链接: {article_url}

{vip_status_text}

文件格式: {format_type.upper()}
内容长度: {len(full_article_data.get('content_text', ''))} 字符

请查看附件中的文件。

---
SuperSpider 自动生成
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        # 发送邮件
        send_email(
            to_email=email,
            subject=subject,
            body=body,
            attachment_path=file_path
        )

        logger.info(f"CSDN文章处理完成，邮件已发送到: {email}")

        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时文件已清理: {file_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"后台处理CSDN文章失败: {e}")


# ==================== 工具函数 ====================

def get_csdn_spider():
    """获取CSDN爬虫实例"""
    return CSDNSpider()


def validate_csdn_url(url: str) -> bool:
    """验证CSDN URL"""
    spider = CSDNSpider()
    return spider.is_valid_url(url)


def clean_csdn_url(url: str) -> str:
    """清理CSDN URL"""
    spider = CSDNSpider()
    return spider.clean_url(url)


# ==================== 导出 ====================

__all__ = [
    'CSDNSpider',
    'csdn_bp',
    'get_csdn_spider',
    'validate_csdn_url',
    'clean_csdn_url'
]