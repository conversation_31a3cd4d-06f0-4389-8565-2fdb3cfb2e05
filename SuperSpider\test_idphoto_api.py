#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试证件照API的脚本
"""

import requests
import json

def test_hivision_direct():
    """直接测试HivisionIDPhotos API"""
    print("🧪 测试HivisionIDPhotos直接API调用...")
    
    # 测试服务状态
    try:
        response = requests.get('http://127.0.0.1:8080/docs', timeout=5)
        print(f"✅ HivisionIDPhotos服务状态: {response.status_code}")
    except Exception as e:
        print(f"❌ HivisionIDPhotos服务不可用: {e}")
        return False
    
    return True

def test_superspider_api():
    """测试SuperSpider证件照API"""
    print("🧪 测试SuperSpider证件照API...")
    
    # 测试状态API
    try:
        response = requests.get('http://127.0.0.1:5000/api/idphoto/status', timeout=5)
        print(f"✅ SuperSpider证件照API状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {data}")
        return True
    except Exception as e:
        print(f"❌ SuperSpider证件照API不可用: {e}")
        return False

def test_sizes_api():
    """测试尺寸API"""
    print("🧪 测试证件照尺寸API...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/idphoto/sizes', timeout=5)
        print(f"✅ 尺寸API状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   可用尺寸数量: {len(data.get('data', []))}")
            # 显示前3个尺寸
            for size in data.get('data', [])[:3]:
                print(f"   - {size['display']}")
        return True
    except Exception as e:
        print(f"❌ 尺寸API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🎯 证件照API测试")
    print("=" * 50)
    
    # 测试各个组件
    tests = [
        test_hivision_direct,
        test_superspider_api,
        test_sizes_api
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
            print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！证件照功能已就绪")
    else:
        print("⚠️  部分测试失败，请检查服务状态")

if __name__ == '__main__':
    main()
