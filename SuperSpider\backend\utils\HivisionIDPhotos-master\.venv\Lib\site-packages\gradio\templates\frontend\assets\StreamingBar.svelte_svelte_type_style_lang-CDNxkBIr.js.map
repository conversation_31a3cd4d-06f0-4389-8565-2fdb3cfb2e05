{"version": 3, "file": "StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js", "sources": ["../../../../node_modules/.pnpm/dompurify@3.0.3/node_modules/dompurify/dist/purify.es.js"], "sourcesContent": ["/*! @license DOMPurify 3.0.3 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\n\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\nfunction unapply(func) {\n  return function (thisArg) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return construct(func, args);\n  };\n}\n/* Add properties to a lookup table */\n\nfunction addToSet(set, array, transformCaseFunc) {\n  var _transformCaseFunc;\n\n  transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n\n  while (l--) {\n    let element = array[l];\n\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n/* Shallow clone an object */\n\nfunction clone(object) {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    newObject[property] = value;\n  }\n\n  return newObject;\n}\n/* This method automatically checks if the prop is function\n * or getter and behaves accordingly. */\n\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']); // SVG\n\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']); // List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\n\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']); // Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\n\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\n\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\n\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  ERB_EXPR: ERB_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR,\n  DATA_ATTR: DATA_ATTR,\n  ARIA_ATTR: ARIA_ATTR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  DOCTYPE_NAME: DOCTYPE_NAME\n});\n\nconst getGlobal = () => typeof window === 'undefined' ? null : window;\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\n\n\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  } // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n\n\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\n\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n\n  const DOMPurify = root => createDOMPurify(root);\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n\n\n  DOMPurify.version = '3.0.3';\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  const currentScript = originalDocument.currentScript;\n  let {\n    document\n  } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode'); // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = {};\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n\n  let SAFE_FOR_TEMPLATES = false;\n  /* Decide if document with <html>... should be returned */\n\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  /* Parsing of strict XHTML documents */\n\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n  /* Keep a reference to config to pass to hooks */\n\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n\n\n  const _parseConfig = function _parseConfig(cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n\n\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n\n\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE; // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n\n    ALLOWED_TAGS = 'ALLOWED_TAGS' in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = 'ALLOWED_ATTR' in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = 'ALLOWED_NAMESPACES' in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = 'ADD_URI_SAFE_ATTR' in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n    cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = 'ADD_DATA_URI_TAGS' in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n    cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n    transformCaseFunc // eslint-disable-line indent\n    ) // eslint-disable-line indent\n    : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = 'FORBID_CONTENTS' in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n\n\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...text]);\n      ALLOWED_ATTR = [];\n\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n\n\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n\n\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n\n\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n\n\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      } // Overwrite existing TrustedTypes policy.\n\n\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY; // Sign local variables required by `sanitize`.\n\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      } // If creating the internal policy succeeded sign internal variables.\n\n\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    } // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n\n\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['foreignobject', 'desc', 'title', 'annotation-xml']); // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n\n  const ALL_SVG_TAGS = addToSet({}, svg$1);\n  addToSet(ALL_SVG_TAGS, svgFilters);\n  addToSet(ALL_SVG_TAGS, svgDisallowed);\n  const ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n  addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element); // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      } // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n\n\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      } // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n\n\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      } // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n\n\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      } // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n\n\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      } // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n\n\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    } // For XHTML and XML documents that support custom namespaces\n\n\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    } // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n\n\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n\n\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      node.remove();\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n\n\n  const _removeAttribute = function _removeAttribute(name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node\n      });\n    }\n\n    node.removeAttribute(name); // We void attribute values for unremovable \"is\"\" attributes\n\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n\n\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n\n\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {// Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n\n\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n\n\n  const _createIterator = function _createIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root, // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT, null, false);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n\n\n  const _isClobbered = function _isClobbered(elm) {\n    return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n  };\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n\n\n  const _isNode = function _isNode(object) {\n    return typeof Node === 'object' ? object instanceof Node : object && typeof object === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string';\n  };\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n\n\n  const _executeHook = function _executeHook(entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n\n\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content;\n    /* Execute a hook if present */\n\n    _executeHook('beforeSanitizeElements', currentNode, null);\n    /* Check if element is clobbered or can clobber */\n\n\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Now let's check the element's type and name */\n\n\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n\n\n    if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n\n\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n      }\n      /* Keep content except for bad-listed elements */\n\n\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            parentNode.insertBefore(cloneNode(childNodes[i], true), getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n\n\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Make sure that older browsers don't get noscript mXSS */\n\n\n    if ((tagName === 'noscript' || tagName === 'noembed') && regExpTest(/<\\/no(script|embed)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n\n\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n\n\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n\n\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if ( // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if (value) {\n      return false;\n    } else ;\n\n    return true;\n  };\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n\n\n  const _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n    return tagName.indexOf('-') > 0;\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n\n\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR\n    };\n    l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n\n    while (l--) {\n      attr = attributes[l];\n      const {\n        name,\n        namespaceURI\n      } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n      /* Execute a hook if present */\n\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Remove attribute */\n\n\n      _removeAttribute(name, currentNode);\n      /* Did the hooks approve of the attribute? */\n\n\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n\n\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n\n\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n      /* Is `value` valid for this attribute? */\n\n\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n\n\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode); // Prefix the value and later re-create the attribute with the sanitized value\n\n\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Handle attributes that require Trusted Types */\n\n\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ; else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n\n\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n\n\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode;\n\n    const shadowIterator = _createIterator(fragment);\n    /* Execute a hook if present */\n\n\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n\n\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n      /* Deep shadow DOM detected */\n\n\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n      /* Check attributes, sanitize if necessary */\n\n\n      _sanitizeAttributes(shadowNode);\n    }\n    /* Execute a hook if present */\n\n\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n\n\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body;\n    let importedNode;\n    let currentNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n\n    IS_EMPTY_INPUT = !dirty;\n\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n\n\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n\n\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n\n\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n\n\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n\n\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n\n\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n\n\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n\n\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n      /* Shadow DOM detected, sanitize it */\n\n\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n      /* Check attributes, sanitize if necessary */\n\n\n      _sanitizeAttributes(currentNode);\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n\n\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n\n\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n\n\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n\n\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n\n    SET_CONFIG = true;\n  };\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n\n\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nvar purify = createDOMPurify();\n\nexport { purify as default };\n//# sourceMappingURL=purify.es.js.map\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "arrayForEach", "unapply", "arrayPop", "arrayPush", "stringToLowerCase", "stringToString", "stringMatch", "stringReplace", "stringIndexOf", "stringTrim", "regExpTest", "typeErrorCreate", "unconstruct", "func", "thisArg", "_len", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "value", "lookupGetter", "prop", "desc", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "EXPRESSIONS", "getGlobal", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "suffix", "ATTR_NAME", "policyName", "scriptUrl", "createDOMPurify", "window", "DOMPurify", "root", "originalDocument", "currentScript", "document", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "_parseConfig", "cfg", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "parentTagName", "_forceRemove", "node", "_removeAttribute", "name", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "body", "_createIterator", "_isClobbered", "elm", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "content", "_basicCustomElementTest", "parentNode", "childNodes", "childCount", "i", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "attributes", "hookEvent", "namespaceURI", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "importedNode", "returnNode", "nodeIterator", "serializedHTML", "tag", "hookFunction", "purify"], "mappings": "AAAA,2LAEA,KAAM,CACJ,QAAAA,GACA,eAAAC,GACA,SAAAC,GACA,eAAAC,GACA,yBAAAC,EACF,EAAI,OACJ,GAAI,CACF,OAAAC,EACA,KAAAC,EACA,OAAAC,EACF,EAAI,OAEA,CACF,MAAAC,GACA,UAAAC,EACF,EAAI,OAAO,QAAY,KAAe,QAEjCD,KACHA,GAAQ,SAAeE,EAAKC,EAAWC,EAAM,CAC3C,OAAOF,EAAI,MAAMC,EAAWC,CAAI,CACpC,GAGKP,IACHA,EAAS,SAAgBQ,EAAG,CAC1B,OAAOA,CACX,GAGKP,IACHA,EAAO,SAAcO,EAAG,CACtB,OAAOA,CACX,GAGKJ,KACHA,GAAY,SAAmBK,EAAMF,EAAM,CACzC,OAAO,IAAIE,EAAK,GAAGF,CAAI,CAC3B,GAGA,MAAMG,GAAeC,EAAQ,MAAM,UAAU,OAAO,EAC9CC,GAAWD,EAAQ,MAAM,UAAU,GAAG,EACtCE,EAAYF,EAAQ,MAAM,UAAU,IAAI,EACxCG,GAAoBH,EAAQ,OAAO,UAAU,WAAW,EACxDI,GAAiBJ,EAAQ,OAAO,UAAU,QAAQ,EAClDK,GAAcL,EAAQ,OAAO,UAAU,KAAK,EAC5CM,EAAgBN,EAAQ,OAAO,UAAU,OAAO,EAChDO,GAAgBP,EAAQ,OAAO,UAAU,OAAO,EAChDQ,GAAaR,EAAQ,OAAO,UAAU,IAAI,EAC1CS,EAAaT,EAAQ,OAAO,UAAU,IAAI,EAC1CU,EAAkBC,GAAY,SAAS,EAC7C,SAASX,EAAQY,EAAM,CACrB,OAAO,SAAUC,EAAS,CACxB,QAASC,EAAO,UAAU,OAAQlB,EAAO,IAAI,MAAMkB,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGC,EAAO,EAAGA,EAAOD,EAAMC,IAClGnB,EAAKmB,EAAO,CAAC,EAAI,UAAUA,CAAI,EAGjC,OAAOvB,GAAMoB,EAAMC,EAASjB,CAAI,CACpC,CACA,CACA,SAASe,GAAYC,EAAM,CACzB,OAAO,UAAY,CACjB,QAASI,EAAQ,UAAU,OAAQpB,EAAO,IAAI,MAAMoB,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFrB,EAAKqB,CAAK,EAAI,UAAUA,CAAK,EAG/B,OAAOxB,GAAUmB,EAAMhB,CAAI,CAC/B,CACA,CAGA,SAASsB,EAASC,EAAKC,EAAOC,EAAmB,CAC/C,IAAIC,EAEJD,GAAqBC,EAAqBD,KAAuB,MAAQC,IAAuB,OAASA,EAAqBnB,GAE1HlB,IAIFA,GAAekC,EAAK,IAAI,EAG1B,IAAII,EAAIH,EAAM,OAEd,KAAOG,KAAK,CACV,IAAIC,EAAUJ,EAAMG,CAAC,EAErB,GAAI,OAAOC,GAAY,SAAU,CAC/B,MAAMC,EAAYJ,EAAkBG,CAAO,EAEvCC,IAAcD,IAEXtC,GAASkC,CAAK,IACjBA,EAAMG,CAAC,EAAIE,GAGbD,EAAUC,EAEb,CAEDN,EAAIK,CAAO,EAAI,EAChB,CAED,OAAOL,CACT,CAGA,SAASO,EAAMC,EAAQ,CACrB,MAAMC,EAAYrC,GAAO,IAAI,EAE7B,SAAW,CAACsC,EAAUC,CAAK,IAAK9C,GAAQ2C,CAAM,EAC5CC,EAAUC,CAAQ,EAAIC,EAGxB,OAAOF,CACT,CAIA,SAASG,EAAaJ,EAAQK,EAAM,CAClC,KAAOL,IAAW,MAAM,CACtB,MAAMM,EAAO7C,GAAyBuC,EAAQK,CAAI,EAElD,GAAIC,EAAM,CACR,GAAIA,EAAK,IACP,OAAOjC,EAAQiC,EAAK,GAAG,EAGzB,GAAI,OAAOA,EAAK,OAAU,WACxB,OAAOjC,EAAQiC,EAAK,KAAK,CAE5B,CAEDN,EAASxC,GAAewC,CAAM,CAC/B,CAED,SAASO,EAAcV,EAAS,CAC9B,eAAQ,KAAK,qBAAsBA,CAAO,EACnC,IACR,CAED,OAAOU,CACT,CAEA,MAAMC,GAAS9C,EAAO,CAAC,IAAK,OAAQ,UAAW,UAAW,OAAQ,UAAW,QAAS,QAAS,IAAK,MAAO,MAAO,MAAO,QAAS,aAAc,OAAQ,KAAM,SAAU,SAAU,UAAW,SAAU,OAAQ,OAAQ,MAAO,WAAY,UAAW,OAAQ,WAAY,KAAM,YAAa,MAAO,UAAW,MAAO,SAAU,MAAO,MAAO,KAAM,KAAM,UAAW,KAAM,WAAY,aAAc,SAAU,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAAQ,SAAU,SAAU,KAAM,OAAQ,IAAK,MAAO,QAAS,MAAO,MAAO,QAAS,SAAU,KAAM,OAAQ,MAAO,OAAQ,UAAW,OAAQ,WAAY,QAAS,MAAO,OAAQ,KAAM,WAAY,SAAU,SAAU,IAAK,UAAW,MAAO,WAAY,IAAK,KAAM,KAAM,OAAQ,IAAK,OAAQ,UAAW,SAAU,SAAU,QAAS,SAAU,SAAU,OAAQ,SAAU,SAAU,QAAS,MAAO,UAAW,MAAO,QAAS,QAAS,KAAM,WAAY,WAAY,QAAS,KAAM,QAAS,OAAQ,KAAM,QAAS,KAAM,IAAK,KAAM,MAAO,QAAS,KAAK,CAAC,EAEz+B+C,GAAQ/C,EAAO,CAAC,MAAO,IAAK,WAAY,cAAe,eAAgB,eAAgB,gBAAiB,mBAAoB,SAAU,WAAY,OAAQ,OAAQ,UAAW,SAAU,OAAQ,IAAK,QAAS,WAAY,QAAS,QAAS,OAAQ,iBAAkB,SAAU,OAAQ,WAAY,QAAS,OAAQ,UAAW,UAAW,WAAY,iBAAkB,OAAQ,OAAQ,QAAS,SAAU,SAAU,OAAQ,WAAY,QAAS,OAAQ,QAAS,OAAQ,OAAO,CAAC,EACndgD,GAAahD,EAAO,CAAC,UAAW,gBAAiB,sBAAuB,cAAe,mBAAoB,oBAAqB,oBAAqB,iBAAkB,eAAgB,UAAW,UAAW,UAAW,UAAW,UAAW,iBAAkB,UAAW,UAAW,cAAe,eAAgB,WAAY,eAAgB,qBAAsB,cAAe,SAAU,cAAc,CAAC,EAK/YiD,GAAgBjD,EAAO,CAAC,UAAW,gBAAiB,SAAU,UAAW,YAAa,mBAAoB,iBAAkB,gBAAiB,gBAAiB,gBAAiB,QAAS,YAAa,OAAQ,eAAgB,YAAa,UAAW,gBAAiB,SAAU,MAAO,aAAc,UAAW,KAAK,CAAC,EACtTkD,GAAWlD,EAAO,CAAC,OAAQ,WAAY,SAAU,UAAW,QAAS,SAAU,KAAM,aAAc,gBAAiB,KAAM,KAAM,QAAS,UAAW,WAAY,QAAS,OAAQ,KAAM,SAAU,QAAS,SAAU,OAAQ,OAAQ,UAAW,SAAU,MAAO,QAAS,MAAO,SAAU,aAAc,aAAa,CAAC,EAGtTmD,GAAmBnD,EAAO,CAAC,UAAW,cAAe,aAAc,WAAY,YAAa,UAAW,UAAW,SAAU,SAAU,QAAS,YAAa,aAAc,iBAAkB,cAAe,MAAM,CAAC,EAClNoD,GAAOpD,EAAO,CAAC,OAAO,CAAC,EAEvBqD,GAAOrD,EAAO,CAAC,SAAU,SAAU,QAAS,MAAO,iBAAkB,eAAgB,uBAAwB,WAAY,aAAc,UAAW,SAAU,UAAW,cAAe,cAAe,UAAW,OAAQ,QAAS,QAAS,QAAS,OAAQ,UAAW,WAAY,eAAgB,SAAU,cAAe,WAAY,WAAY,UAAW,MAAO,WAAY,0BAA2B,wBAAyB,WAAY,YAAa,UAAW,eAAgB,OAAQ,MAAO,UAAW,SAAU,SAAU,OAAQ,OAAQ,WAAY,KAAM,YAAa,YAAa,QAAS,OAAQ,QAAS,OAAQ,OAAQ,UAAW,OAAQ,MAAO,MAAO,YAAa,QAAS,SAAU,MAAO,YAAa,WAAY,QAAS,OAAQ,QAAS,UAAW,aAAc,SAAU,OAAQ,UAAW,UAAW,cAAe,cAAe,SAAU,UAAW,UAAW,aAAc,WAAY,MAAO,WAAY,MAAO,WAAY,OAAQ,OAAQ,UAAW,aAAc,QAAS,WAAY,QAAS,OAAQ,QAAS,OAAQ,UAAW,QAAS,MAAO,SAAU,OAAQ,QAAS,UAAW,WAAY,QAAS,YAAa,OAAQ,SAAU,SAAU,QAAS,QAAS,QAAS,MAAM,CAAC,EACxqCsD,GAAMtD,EAAO,CAAC,gBAAiB,aAAc,WAAY,qBAAsB,SAAU,gBAAiB,gBAAiB,UAAW,gBAAiB,iBAAkB,QAAS,OAAQ,KAAM,QAAS,OAAQ,gBAAiB,YAAa,YAAa,QAAS,sBAAuB,8BAA+B,gBAAiB,kBAAmB,KAAM,KAAM,IAAK,KAAM,KAAM,kBAAmB,YAAa,UAAW,UAAW,MAAO,WAAY,YAAa,MAAO,OAAQ,eAAgB,YAAa,SAAU,cAAe,cAAe,gBAAiB,cAAe,YAAa,mBAAoB,eAAgB,aAAc,eAAgB,cAAe,KAAM,KAAM,KAAM,KAAM,aAAc,WAAY,gBAAiB,oBAAqB,SAAU,OAAQ,KAAM,kBAAmB,KAAM,MAAO,IAAK,KAAM,KAAM,KAAM,KAAM,UAAW,YAAa,aAAc,WAAY,OAAQ,eAAgB,iBAAkB,eAAgB,mBAAoB,iBAAkB,QAAS,aAAc,aAAc,eAAgB,eAAgB,cAAe,cAAe,mBAAoB,YAAa,MAAO,OAAQ,QAAS,SAAU,OAAQ,MAAO,OAAQ,aAAc,SAAU,WAAY,UAAW,QAAS,SAAU,cAAe,SAAU,WAAY,cAAe,OAAQ,aAAc,sBAAuB,mBAAoB,eAAgB,SAAU,gBAAiB,sBAAuB,iBAAkB,IAAK,KAAM,KAAM,SAAU,OAAQ,OAAQ,cAAe,YAAa,UAAW,SAAU,SAAU,QAAS,OAAQ,kBAAmB,mBAAoB,mBAAoB,eAAgB,cAAe,eAAgB,cAAe,aAAc,eAAgB,mBAAoB,oBAAqB,iBAAkB,kBAAmB,oBAAqB,iBAAkB,SAAU,eAAgB,QAAS,eAAgB,iBAAkB,WAAY,UAAW,UAAW,YAAa,mBAAoB,cAAe,kBAAmB,iBAAkB,aAAc,OAAQ,KAAM,KAAM,UAAW,SAAU,UAAW,aAAc,UAAW,aAAc,gBAAiB,gBAAiB,QAAS,eAAgB,OAAQ,eAAgB,mBAAoB,mBAAoB,IAAK,KAAM,KAAM,QAAS,IAAK,KAAM,KAAM,IAAK,YAAY,CAAC,EAC3wEuD,GAASvD,EAAO,CAAC,SAAU,cAAe,QAAS,WAAY,QAAS,eAAgB,cAAe,aAAc,aAAc,QAAS,MAAO,UAAW,eAAgB,WAAY,QAAS,QAAS,SAAU,OAAQ,KAAM,UAAW,SAAU,gBAAiB,SAAU,SAAU,iBAAkB,YAAa,WAAY,cAAe,UAAW,UAAW,gBAAiB,WAAY,WAAY,OAAQ,WAAY,WAAY,aAAc,UAAW,SAAU,SAAU,cAAe,gBAAiB,uBAAwB,YAAa,YAAa,aAAc,WAAY,iBAAkB,iBAAkB,YAAa,UAAW,QAAS,OAAO,CAAC,EAC7pBwD,GAAMxD,EAAO,CAAC,aAAc,SAAU,cAAe,YAAa,aAAa,CAAC,EAEhFyD,GAAgBxD,EAAK,2BAA2B,EAEhDyD,GAAWzD,EAAK,uBAAuB,EACvC0D,GAAc1D,EAAK,eAAe,EAClC2D,GAAY3D,EAAK,4BAA4B,EAE7C4D,GAAY5D,EAAK,gBAAgB,EAEjC6D,GAAiB7D,EAAK,2FAC5B,EACM8D,GAAoB9D,EAAK,uBAAuB,EAChD+D,GAAkB/D,EAAK,6DAC7B,EACMgE,GAAehE,EAAK,SAAS,EAEnC,IAAIiE,GAA2B,OAAO,OAAO,CAC3C,UAAW,KACX,cAAeT,GACf,SAAUC,GACV,YAAaC,GACb,UAAWC,GACX,UAAWC,GACX,eAAgBC,GAChB,kBAAmBC,GACnB,gBAAiBC,GACjB,aAAcC,EAChB,CAAC,EAED,MAAME,GAAY,IAAM,OAAO,OAAW,IAAc,KAAO,OAWzDC,GAA4B,SAAmCC,EAAcC,EAAmB,CACpG,GAAI,OAAOD,GAAiB,UAAY,OAAOA,EAAa,cAAiB,WAC3E,OAAO,KAMT,IAAIE,EAAS,KACb,MAAMC,EAAY,wBAEdF,GAAqBA,EAAkB,aAAaE,CAAS,IAC/DD,EAASD,EAAkB,aAAaE,CAAS,GAGnD,MAAMC,EAAa,aAAeF,EAAS,IAAMA,EAAS,IAE1D,GAAI,CACF,OAAOF,EAAa,aAAaI,EAAY,CAC3C,WAAWpB,EAAM,CACf,OAAOA,CACR,EAED,gBAAgBqB,EAAW,CACzB,OAAOA,CACR,CAEP,CAAK,CACF,MAAW,CAIV,eAAQ,KAAK,uBAAyBD,EAAa,wBAAwB,EACpE,IACR,CACH,EAEA,SAASE,IAAkB,CACzB,IAAIC,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAIT,GAAS,EAE1F,MAAMU,EAAYC,GAAQH,GAAgBG,CAAI,EAe9C,GARAD,EAAU,QAAU,QAMpBA,EAAU,QAAU,GAEhB,CAACD,GAAU,CAACA,EAAO,UAAYA,EAAO,SAAS,WAAa,EAG9D,OAAAC,EAAU,YAAc,GACjBA,EAGT,MAAME,EAAmBH,EAAO,SAC1BI,EAAgBD,EAAiB,cACvC,GAAI,CACF,SAAAE,CACD,EAAGL,EACJ,KAAM,CACJ,iBAAAM,EACA,oBAAAC,EACA,KAAAC,GACA,QAAAC,GACA,WAAAC,GACA,aAAAC,GAAeX,EAAO,cAAgBA,EAAO,gBAC7C,gBAAAY,GACA,UAAAC,GACA,aAAApB,CACD,EAAGO,EACEc,EAAmBL,GAAQ,UAC3BM,GAAYjD,EAAagD,EAAkB,WAAW,EACtDE,GAAiBlD,EAAagD,EAAkB,aAAa,EAC7DG,GAAgBnD,EAAagD,EAAkB,YAAY,EAC3DI,GAAgBpD,EAAagD,EAAkB,YAAY,EAOjE,GAAI,OAAOP,GAAwB,WAAY,CAC7C,MAAMY,EAAWd,EAAS,cAAc,UAAU,EAE9Cc,EAAS,SAAWA,EAAS,QAAQ,gBACvCd,EAAWc,EAAS,QAAQ,cAE/B,CAED,IAAIC,EACAC,EAAY,GAChB,KAAM,CACJ,eAAAC,GACA,mBAAAC,GACA,uBAAAC,GACA,qBAAAC,EACD,EAAGpB,EACE,CACJ,WAAAqB,EACD,EAAGvB,EACJ,IAAIwB,EAAQ,CAAA,EAKZ1B,EAAU,YAAc,OAAOlF,IAAY,YAAc,OAAOmG,IAAkB,YAAcI,IAAkBA,GAAe,qBAAuB,OACxJ,KAAM,CACJ,cAAAzC,GACA,SAAAC,GACA,YAAAC,GACA,UAAAC,GACA,UAAAC,GACA,kBAAAE,GACA,gBAAAC,EACD,EAAGE,GACJ,GAAI,CACF,eAAgBsC,EACjB,EAAGtC,GAQAuC,EAAe,KACnB,MAAMC,GAAuB7E,EAAS,GAAI,CAAC,GAAGiB,GAAQ,GAAGC,GAAO,GAAGC,GAAY,GAAGE,GAAU,GAAGE,EAAI,CAAC,EAGpG,IAAIuD,EAAe,KACnB,MAAMC,GAAuB/E,EAAS,CAAE,EAAE,CAAC,GAAGwB,GAAM,GAAGC,GAAK,GAAGC,GAAQ,GAAGC,EAAG,CAAC,EAQ9E,IAAIqD,EAA0B,OAAO,KAAK,OAAO,OAAO,KAAM,CAC5D,aAAc,CACZ,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,mBAAoB,CAClB,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,IACR,EACD,+BAAgC,CAC9B,SAAU,GACV,aAAc,GACd,WAAY,GACZ,MAAO,EACR,CACF,CAAA,CAAC,EAGEC,EAAc,KAGdC,GAAc,KAGdC,GAAkB,GAGlBC,GAAkB,GAGlBC,GAA0B,GAI1BC,GAA2B,GAK3BC,EAAqB,GAGrBC,EAAiB,GAGjBC,GAAa,GAIbC,GAAa,GAMbC,EAAa,GAIbC,EAAsB,GAItBC,EAAsB,GAKtBC,GAAe,GAefC,GAAuB,GAC3B,MAAMC,GAA8B,gBAGpC,IAAIC,GAAe,GAIfC,EAAW,GAGXC,EAAe,CAAA,EAGfC,EAAkB,KACtB,MAAMC,GAA0BrG,EAAS,CAAE,EAAE,CAAC,iBAAkB,QAAS,WAAY,OAAQ,gBAAiB,OAAQ,SAAU,OAAQ,KAAM,KAAM,KAAM,KAAM,QAAS,UAAW,WAAY,WAAY,YAAa,SAAU,QAAS,MAAO,WAAY,QAAS,QAAS,QAAS,KAAK,CAAC,EAGhS,IAAIsG,GAAgB,KACpB,MAAMC,GAAwBvG,EAAS,CAAE,EAAE,CAAC,QAAS,QAAS,MAAO,SAAU,QAAS,OAAO,CAAC,EAGhG,IAAIwG,GAAsB,KAC1B,MAAMC,GAA8BzG,EAAS,GAAI,CAAC,MAAO,QAAS,MAAO,KAAM,QAAS,OAAQ,UAAW,cAAe,OAAQ,UAAW,QAAS,QAAS,QAAS,OAAO,CAAC,EAC1K0G,EAAmB,qCACnBC,EAAgB,6BAChBC,EAAiB,+BAGvB,IAAIC,EAAYD,EACZE,GAAiB,GAGjBC,GAAqB,KACzB,MAAMC,GAA6BhH,EAAS,GAAI,CAAC0G,EAAkBC,EAAeC,CAAc,EAAG1H,EAAc,EAGjH,IAAI+H,EACJ,MAAMC,GAA+B,CAAC,wBAAyB,WAAW,EACpEC,GAA4B,YAClC,IAAIhH,EAGAiH,EAAS,KAKb,MAAMC,GAAcjE,EAAS,cAAc,MAAM,EAE3CkE,GAAoB,SAA2BC,EAAW,CAC9D,OAAOA,aAAqB,QAAUA,aAAqB,QAC/D,EASQC,GAAe,SAAsBC,EAAK,CAC9C,GAAI,EAAAL,GAAUA,IAAWK,GAuKzB,KAjKI,CAACA,GAAO,OAAOA,GAAQ,YACzBA,EAAM,CAAA,GAKRA,EAAMjH,EAAMiH,CAAG,EACfR,EACAC,GAA6B,QAAQO,EAAI,iBAAiB,IAAM,GAAKR,EAAoBE,GAA4BF,EAAoBQ,EAAI,kBAE7ItH,EAAoB8G,IAAsB,wBAA0B/H,GAAiBD,GAGrF2F,EAAe,iBAAkB6C,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,aAActH,CAAiB,EAAI0E,GAC3FC,EAAe,iBAAkB2C,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,aAActH,CAAiB,EAAI4E,GAC3FgC,GAAqB,uBAAwBU,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,mBAAoBvI,EAAc,EAAI8H,GAC1GR,GAAsB,sBAAuBiB,EAAMzH,EAASQ,EAAMiG,EAA2B,EAC7FgB,EAAI,kBACJtH,CACC,EACCsG,GACFH,GAAgB,sBAAuBmB,EAAMzH,EAASQ,EAAM+F,EAAqB,EACjFkB,EAAI,kBACJtH,CACC,EACCoG,GACFH,EAAkB,oBAAqBqB,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,gBAAiBtH,CAAiB,EAAIkG,GACpGpB,EAAc,gBAAiBwC,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,YAAatH,CAAiB,EAAI,GACxF+E,GAAc,gBAAiBuC,EAAMzH,EAAS,CAAA,EAAIyH,EAAI,YAAatH,CAAiB,EAAI,GACxFgG,EAAe,iBAAkBsB,EAAMA,EAAI,aAAe,GAC1DtC,GAAkBsC,EAAI,kBAAoB,GAE1CrC,GAAkBqC,EAAI,kBAAoB,GAE1CpC,GAA0BoC,EAAI,yBAA2B,GAEzDnC,GAA2BmC,EAAI,2BAA6B,GAE5DlC,EAAqBkC,EAAI,oBAAsB,GAE/CjC,EAAiBiC,EAAI,gBAAkB,GAEvC9B,EAAa8B,EAAI,YAAc,GAE/B7B,EAAsB6B,EAAI,qBAAuB,GAEjD5B,EAAsB4B,EAAI,qBAAuB,GAEjD/B,GAAa+B,EAAI,YAAc,GAE/B3B,GAAe2B,EAAI,eAAiB,GAEpC1B,GAAuB0B,EAAI,sBAAwB,GAEnDxB,GAAewB,EAAI,eAAiB,GAEpCvB,EAAWuB,EAAI,UAAY,GAE3B9C,GAAmB8C,EAAI,oBAAsBxF,GAC7C4E,EAAYY,EAAI,WAAab,EAC7B5B,EAA0ByC,EAAI,yBAA2B,GAErDA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,YAAY,IAC3FzC,EAAwB,aAAeyC,EAAI,wBAAwB,cAGjEA,EAAI,yBAA2BH,GAAkBG,EAAI,wBAAwB,kBAAkB,IACjGzC,EAAwB,mBAAqByC,EAAI,wBAAwB,oBAGvEA,EAAI,yBAA2B,OAAOA,EAAI,wBAAwB,gCAAmC,YACvGzC,EAAwB,+BAAiCyC,EAAI,wBAAwB,gCAGnFlC,IACFH,GAAkB,IAGhBQ,IACFD,EAAa,IAKXQ,IACFvB,EAAe5E,EAAS,CAAA,EAAI,CAAC,GAAGuB,EAAI,CAAC,EACrCuD,EAAe,CAAA,EAEXqB,EAAa,OAAS,KACxBnG,EAAS4E,EAAc3D,EAAM,EAC7BjB,EAAS8E,EAActD,EAAI,GAGzB2E,EAAa,MAAQ,KACvBnG,EAAS4E,EAAc1D,EAAK,EAC5BlB,EAAS8E,EAAcrD,EAAG,EAC1BzB,EAAS8E,EAAcnD,EAAG,GAGxBwE,EAAa,aAAe,KAC9BnG,EAAS4E,EAAczD,EAAU,EACjCnB,EAAS8E,EAAcrD,EAAG,EAC1BzB,EAAS8E,EAAcnD,EAAG,GAGxBwE,EAAa,SAAW,KAC1BnG,EAAS4E,EAAcvD,EAAQ,EAC/BrB,EAAS8E,EAAcpD,EAAM,EAC7B1B,EAAS8E,EAAcnD,EAAG,IAM1B8F,EAAI,WACF7C,IAAiBC,KACnBD,EAAepE,EAAMoE,CAAY,GAGnC5E,EAAS4E,EAAc6C,EAAI,SAAUtH,CAAiB,GAGpDsH,EAAI,WACF3C,IAAiBC,KACnBD,EAAetE,EAAMsE,CAAY,GAGnC9E,EAAS8E,EAAc2C,EAAI,SAAUtH,CAAiB,GAGpDsH,EAAI,mBACNzH,EAASwG,GAAqBiB,EAAI,kBAAmBtH,CAAiB,EAGpEsH,EAAI,kBACFrB,IAAoBC,KACtBD,EAAkB5F,EAAM4F,CAAe,GAGzCpG,EAASoG,EAAiBqB,EAAI,gBAAiBtH,CAAiB,GAK9D8F,KACFrB,EAAa,OAAO,EAAI,IAKtBY,GACFxF,EAAS4E,EAAc,CAAC,OAAQ,OAAQ,MAAM,CAAC,EAK7CA,EAAa,QACf5E,EAAS4E,EAAc,CAAC,OAAO,CAAC,EAChC,OAAOK,EAAY,OAGjBwC,EAAI,qBAAsB,CAC5B,GAAI,OAAOA,EAAI,qBAAqB,YAAe,WACjD,MAAMjI,EAAgB,6EAA6E,EAGrG,GAAI,OAAOiI,EAAI,qBAAqB,iBAAoB,WACtD,MAAMjI,EAAgB,kFAAkF,EAI1G2E,EAAqBsD,EAAI,qBAEzBrD,EAAYD,EAAmB,WAAW,EAAE,CAClD,MAEUA,IAAuB,SACzBA,EAAqB5B,GAA0BC,EAAcW,CAAa,GAIxEgB,IAAuB,MAAQ,OAAOC,GAAc,WACtDA,EAAYD,EAAmB,WAAW,EAAE,GAM5ChG,GACFA,EAAOsJ,CAAG,EAGZL,EAASK,EACb,EAEQC,GAAiC1H,EAAS,CAAA,EAAI,CAAC,KAAM,KAAM,KAAM,KAAM,OAAO,CAAC,EAC/E2H,GAA0B3H,EAAS,GAAI,CAAC,gBAAiB,OAAQ,QAAS,gBAAgB,CAAC,EAK3F4H,GAA+B5H,EAAS,CAAA,EAAI,CAAC,QAAS,QAAS,OAAQ,IAAK,QAAQ,CAAC,EAKrF6H,EAAe7H,EAAS,CAAE,EAAEkB,EAAK,EACvClB,EAAS6H,EAAc1G,EAAU,EACjCnB,EAAS6H,EAAczG,EAAa,EACpC,MAAM0G,GAAkB9H,EAAS,CAAE,EAAEqB,EAAQ,EAC7CrB,EAAS8H,GAAiBxG,EAAgB,EAU1C,MAAMyG,GAAuB,SAA8BzH,EAAS,CAClE,IAAI0H,EAAS/D,GAAc3D,CAAO,GAG9B,CAAC0H,GAAU,CAACA,EAAO,WACrBA,EAAS,CACP,aAAcnB,EACd,QAAS,UACjB,GAGI,MAAMoB,EAAUhJ,GAAkBqB,EAAQ,OAAO,EAC3C4H,EAAgBjJ,GAAkB+I,EAAO,OAAO,EAEtD,OAAKjB,GAAmBzG,EAAQ,YAAY,EAIxCA,EAAQ,eAAiBqG,EAIvBqB,EAAO,eAAiBpB,EACnBqB,IAAY,MAMjBD,EAAO,eAAiBtB,EACnBuB,IAAY,QAAUC,IAAkB,kBAAoBR,GAA+BQ,CAAa,GAK1G,EAAQL,EAAaI,CAAO,EAGjC3H,EAAQ,eAAiBoG,EAIvBsB,EAAO,eAAiBpB,EACnBqB,IAAY,OAKjBD,EAAO,eAAiBrB,EACnBsB,IAAY,QAAUN,GAAwBO,CAAa,EAK7D,EAAQJ,GAAgBG,CAAO,EAGpC3H,EAAQ,eAAiBsG,EAIvBoB,EAAO,eAAiBrB,GAAiB,CAACgB,GAAwBO,CAAa,GAI/EF,EAAO,eAAiBtB,GAAoB,CAACgB,GAA+BQ,CAAa,EACpF,GAKF,CAACJ,GAAgBG,CAAO,IAAML,GAA6BK,CAAO,GAAK,CAACJ,EAAaI,CAAO,GAIjG,GAAAhB,IAAsB,yBAA2BF,GAAmBzG,EAAQ,YAAY,GA5DnF,EAqEb,EAQQ6H,EAAe,SAAsBC,EAAM,CAC/CpJ,EAAUgE,EAAU,QAAS,CAC3B,QAASoF,CACf,CAAK,EAED,GAAI,CAEFA,EAAK,WAAW,YAAYA,CAAI,CACjC,MAAW,CACVA,EAAK,OAAM,CACZ,CACL,EASQC,GAAmB,SAA0BC,EAAMF,EAAM,CAC7D,GAAI,CACFpJ,EAAUgE,EAAU,QAAS,CAC3B,UAAWoF,EAAK,iBAAiBE,CAAI,EACrC,KAAMF,CACd,CAAO,CACF,MAAW,CACVpJ,EAAUgE,EAAU,QAAS,CAC3B,UAAW,KACX,KAAMoF,CACd,CAAO,CACF,CAID,GAFAA,EAAK,gBAAgBE,CAAI,EAErBA,IAAS,MAAQ,CAACxD,EAAawD,CAAI,EACrC,GAAI3C,GAAcC,EAChB,GAAI,CACFuC,EAAaC,CAAI,CAC3B,MAAoB,CAAE,KAEd,IAAI,CACFA,EAAK,aAAaE,EAAM,EAAE,CACpC,MAAoB,CAAE,CAGtB,EASQC,GAAgB,SAAuBC,EAAO,CAElD,IAAIC,EACAC,EAEJ,GAAIhD,GACF8C,EAAQ,oBAAsBA,MACzB,CAEL,MAAMG,EAAUxJ,GAAYqJ,EAAO,aAAa,EAChDE,EAAoBC,GAAWA,EAAQ,CAAC,CACzC,CAEG1B,IAAsB,yBAA2BJ,IAAcD,IAEjE4B,EAAQ,iEAAmEA,EAAQ,kBAGrF,MAAMI,EAAezE,EAAqBA,EAAmB,WAAWqE,CAAK,EAAIA,EAMjF,GAAI3B,IAAcD,EAChB,GAAI,CACF6B,EAAM,IAAI7E,GAAW,EAAC,gBAAgBgF,EAAc3B,CAAiB,CAC7E,MAAkB,CAAE,CAKhB,GAAI,CAACwB,GAAO,CAACA,EAAI,gBAAiB,CAChCA,EAAMpE,GAAe,eAAewC,EAAW,WAAY,IAAI,EAE/D,GAAI,CACF4B,EAAI,gBAAgB,UAAY3B,GAAiB1C,EAAYwE,CAC9D,MAAW,CACX,CACF,CAED,MAAMC,EAAOJ,EAAI,MAAQA,EAAI,gBAQ7B,OANID,GAASE,GACXG,EAAK,aAAazF,EAAS,eAAesF,CAAiB,EAAGG,EAAK,WAAW,CAAC,GAAK,IAAI,EAKtFhC,IAAcD,EACTpC,GAAqB,KAAKiE,EAAKjD,EAAiB,OAAS,MAAM,EAAE,CAAC,EAGpEA,EAAiBiD,EAAI,gBAAkBI,CAClD,EASQC,GAAkB,SAAyB7F,EAAM,CACrD,OAAOqB,GAAmB,KAAKrB,EAAK,eAAiBA,EAAMA,EAC3DQ,GAAW,aAAeA,GAAW,aAAeA,GAAW,UAAW,KAAM,EAAK,CACzF,EASQsF,GAAe,SAAsBC,EAAK,CAC9C,OAAOA,aAAerF,KAAoB,OAAOqF,EAAI,UAAa,UAAY,OAAOA,EAAI,aAAgB,UAAY,OAAOA,EAAI,aAAgB,YAAc,EAAEA,EAAI,sBAAsBtF,KAAiB,OAAOsF,EAAI,iBAAoB,YAAc,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,cAAiB,UAAY,OAAOA,EAAI,cAAiB,YAAc,OAAOA,EAAI,eAAkB,WACrZ,EASQC,EAAU,SAAiBxI,EAAQ,CACvC,OAAO,OAAO8C,IAAS,SAAW9C,aAAkB8C,GAAO9C,GAAU,OAAOA,GAAW,UAAY,OAAOA,EAAO,UAAa,UAAY,OAAOA,EAAO,UAAa,QACzK,EAWQyI,EAAe,SAAsBC,EAAYC,EAAaC,EAAM,CACnE3E,EAAMyE,CAAU,GAIrBtK,GAAa6F,EAAMyE,CAAU,EAAGG,GAAQ,CACtCA,EAAK,KAAKtG,EAAWoG,EAAaC,EAAMjC,CAAM,CACpD,CAAK,CACL,EAaQmC,GAAoB,SAA2BH,EAAa,CAChE,IAAII,EAOJ,GAJAN,EAAa,yBAA0BE,EAAa,IAAI,EAIpDL,GAAaK,CAAW,EAC1B,OAAAjB,EAAaiB,CAAW,EAEjB,GAKT,MAAMnB,EAAU9H,EAAkBiJ,EAAY,QAAQ,EAUtD,GAPAF,EAAa,sBAAuBE,EAAa,CAC/C,QAAAnB,EACA,YAAarD,CACnB,CAAK,EAIGwE,EAAY,iBAAmB,CAACH,EAAQG,EAAY,iBAAiB,IAAM,CAACH,EAAQG,EAAY,OAAO,GAAK,CAACH,EAAQG,EAAY,QAAQ,iBAAiB,IAAM7J,EAAW,UAAW6J,EAAY,SAAS,GAAK7J,EAAW,UAAW6J,EAAY,WAAW,EAC/P,OAAAjB,EAAaiB,CAAW,EAEjB,GAKT,GAAI,CAACxE,EAAaqD,CAAO,GAAKhD,EAAYgD,CAAO,EAAG,CAElD,GAAI,CAAChD,EAAYgD,CAAO,GAAKwB,GAAwBxB,CAAO,IACtDjD,EAAwB,wBAAwB,QAAUzF,EAAWyF,EAAwB,aAAciD,CAAO,GAClHjD,EAAwB,wBAAwB,UAAYA,EAAwB,aAAaiD,CAAO,GAAG,MAAO,GAKxH,GAAIhC,IAAgB,CAACG,EAAgB6B,CAAO,EAAG,CAC7C,MAAMyB,EAAazF,GAAcmF,CAAW,GAAKA,EAAY,WACvDO,EAAa3F,GAAcoF,CAAW,GAAKA,EAAY,WAE7D,GAAIO,GAAcD,EAAY,CAC5B,MAAME,EAAaD,EAAW,OAE9B,QAASE,EAAID,EAAa,EAAGC,GAAK,EAAG,EAAEA,EACrCH,EAAW,aAAa5F,GAAU6F,EAAWE,CAAC,EAAG,EAAI,EAAG9F,GAAeqF,CAAW,CAAC,CAEtF,CACF,CAED,OAAAjB,EAAaiB,CAAW,EAEjB,EACR,CAYD,OARIA,aAAuB5F,IAAW,CAACuE,GAAqBqB,CAAW,IAQlEnB,IAAY,YAAcA,IAAY,YAAc1I,EAAW,uBAAwB6J,EAAY,SAAS,GAC/GjB,EAAaiB,CAAW,EAEjB,KAKL7D,GAAsB6D,EAAY,WAAa,IAEjDI,EAAUJ,EAAY,YACtBI,EAAUpK,EAAcoK,EAAS5H,GAAe,GAAG,EACnD4H,EAAUpK,EAAcoK,EAAS3H,GAAU,GAAG,EAC9C2H,EAAUpK,EAAcoK,EAAS1H,GAAa,GAAG,EAE7CsH,EAAY,cAAgBI,IAC9BxK,EAAUgE,EAAU,QAAS,CAC3B,QAASoG,EAAY,UAAW,CAC1C,CAAS,EACDA,EAAY,YAAcI,IAM9BN,EAAa,wBAAyBE,EAAa,IAAI,EAEhD,GACX,EAYQU,GAAoB,SAA2BC,EAAOC,EAAQpJ,EAAO,CAEzE,GAAIkF,KAAiBkE,IAAW,MAAQA,IAAW,UAAYpJ,KAASwC,GAAYxC,KAASyG,IAC3F,MAAO,GAQT,GAAI,EAAAjC,IAAmB,CAACF,GAAY8E,CAAM,GAAKzK,EAAWwC,GAAWiI,CAAM,IAAU,GAAI,EAAA7E,IAAmB5F,EAAWyC,GAAWgI,CAAM,IAAU,GAAI,CAAClF,EAAakF,CAAM,GAAK9E,GAAY8E,CAAM,GAC/L,GAGA,EAAAP,GAAwBM,CAAK,IAAM/E,EAAwB,wBAAwB,QAAUzF,EAAWyF,EAAwB,aAAc+E,CAAK,GAAK/E,EAAwB,wBAAwB,UAAYA,EAAwB,aAAa+E,CAAK,KAAO/E,EAAwB,8BAA8B,QAAUzF,EAAWyF,EAAwB,mBAAoBgF,CAAM,GAAKhF,EAAwB,8BAA8B,UAAYA,EAAwB,mBAAmBgF,CAAM,IAE1fA,IAAW,MAAQhF,EAAwB,iCAAmCA,EAAwB,wBAAwB,QAAUzF,EAAWyF,EAAwB,aAAcpE,CAAK,GAAKoE,EAAwB,wBAAwB,UAAYA,EAAwB,aAAapE,CAAK,IACvS,MAAO,WAIA,CAAA4F,GAAoBwD,CAAM,GAAU,GAAI,CAAAzK,EAAWoF,GAAkBvF,EAAcwB,EAAOuB,GAAiB,EAAE,CAAC,GAAU,GAAK,GAAA6H,IAAW,OAASA,IAAW,cAAgBA,IAAW,SAAWD,IAAU,UAAY1K,GAAcuB,EAAO,OAAO,IAAM,GAAK0F,GAAcyD,CAAK,IAAU,GAAI,EAAA1E,IAA2B,CAAC9F,EAAW2C,GAAmB9C,EAAcwB,EAAOuB,GAAiB,EAAE,CAAC,IAAU,GAAIvB,EAC1Z,MAAO,QAGT,MAAO,EACX,EASQ6I,GAA0B,SAAiCxB,EAAS,CACxE,OAAOA,EAAQ,QAAQ,GAAG,EAAI,CAClC,EAaQgC,GAAsB,SAA6Bb,EAAa,CACpE,IAAIc,EACAtJ,EACAoJ,EACA3J,EAGJ6I,EAAa,2BAA4BE,EAAa,IAAI,EAE1D,KAAM,CACJ,WAAAe,CACD,EAAGf,EAGJ,GAAI,CAACe,EACH,OAGF,MAAMC,EAAY,CAChB,SAAU,GACV,UAAW,GACX,SAAU,GACV,kBAAmBtF,CACzB,EAII,IAHAzE,EAAI8J,EAAW,OAGR9J,KAAK,CACV6J,EAAOC,EAAW9J,CAAC,EACnB,KAAM,CACJ,KAAAiI,EACA,aAAA+B,EACD,EAAGH,EAyBJ,GAxBAtJ,EAAQ0H,IAAS,QAAU4B,EAAK,MAAQ5K,GAAW4K,EAAK,KAAK,EAC7DF,EAAS7J,EAAkBmI,CAAI,EAG/B8B,EAAU,SAAWJ,EACrBI,EAAU,UAAYxJ,EACtBwJ,EAAU,SAAW,GACrBA,EAAU,cAAgB,OAE1BlB,EAAa,wBAAyBE,EAAagB,CAAS,EAE5DxJ,EAAQwJ,EAAU,UAGdA,EAAU,gBAMd/B,GAAiBC,EAAMc,CAAW,EAI9B,CAACgB,EAAU,UACb,SAKF,GAAI,CAAC9E,IAA4B/F,EAAW,OAAQqB,CAAK,EAAG,CAC1DyH,GAAiBC,EAAMc,CAAW,EAElC,QACD,CAIG7D,IACF3E,EAAQxB,EAAcwB,EAAOgB,GAAe,GAAG,EAC/ChB,EAAQxB,EAAcwB,EAAOiB,GAAU,GAAG,EAC1CjB,EAAQxB,EAAcwB,EAAOkB,GAAa,GAAG,GAK/C,MAAMiI,GAAQ5J,EAAkBiJ,EAAY,QAAQ,EAEpD,GAAKU,GAAkBC,GAAOC,EAAQpJ,CAAK,EAkB3C,IAVImF,KAAyBiE,IAAW,MAAQA,IAAW,UAEzD3B,GAAiBC,EAAMc,CAAW,EAGlCxI,EAAQoF,GAA8BpF,GAKpCuD,GAAsB,OAAO3B,GAAiB,UAAY,OAAOA,EAAa,kBAAqB,YACjG,CAAA6H,GACF,OAAQ7H,EAAa,iBAAiBuH,GAAOC,CAAM,EAAC,CAClD,IAAK,cACH,CACEpJ,EAAQuD,EAAmB,WAAWvD,CAAK,EAC3C,KACD,CAEH,IAAK,mBACH,CACEA,EAAQuD,EAAmB,gBAAgBvD,CAAK,EAChD,KACD,CACJ,CAML,GAAI,CACEyJ,GACFjB,EAAY,eAAeiB,GAAc/B,EAAM1H,CAAK,EAGpDwI,EAAY,aAAad,EAAM1H,CAAK,EAGtC7B,GAASiE,EAAU,OAAO,CAClC,MAAkB,CAAE,EACf,CAIDkG,EAAa,0BAA2BE,EAAa,IAAI,CAC7D,EAQQkB,GAAqB,SAASA,EAAmBC,EAAU,CAC/D,IAAIC,EAEJ,MAAMC,EAAiB3B,GAAgByB,CAAQ,EAM/C,IAFArB,EAAa,0BAA2BqB,EAAU,IAAI,EAE/CC,EAAaC,EAAe,YAEjCvB,EAAa,yBAA0BsB,EAAY,IAAI,EAInD,CAAAjB,GAAkBiB,CAAU,IAM5BA,EAAW,mBAAmBnH,GAChCiH,EAAmBE,EAAW,OAAO,EAKvCP,GAAoBO,CAAU,GAKhCtB,EAAa,yBAA0BqB,EAAU,IAAI,CACzD,EAWE,OAAAvH,EAAU,SAAW,SAAUwF,EAAO,CACpC,IAAIf,EAAM,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAC1EoB,EACA6B,EACAtB,EACAuB,EAaJ,GARA7D,GAAiB,CAAC0B,EAEd1B,KACF0B,EAAQ,SAKN,OAAOA,GAAU,UAAY,CAACS,EAAQT,CAAK,EAC7C,GAAI,OAAOA,EAAM,UAAa,YAG5B,GAFAA,EAAQA,EAAM,WAEV,OAAOA,GAAU,SACnB,MAAMhJ,EAAgB,iCAAiC,MAGzD,OAAMA,EAAgB,4BAA4B,EAMtD,GAAI,CAACwD,EAAU,YACb,OAAOwF,EAkBT,GAbK/C,IACH+B,GAAaC,CAAG,EAKlBzE,EAAU,QAAU,GAGhB,OAAOwF,GAAU,WACnBtC,EAAW,IAGTA,GAEF,GAAIsC,EAAM,SAAU,CAClB,MAAMP,EAAU9H,EAAkBqI,EAAM,QAAQ,EAEhD,GAAI,CAAC5D,EAAaqD,CAAO,GAAKhD,EAAYgD,CAAO,EAC/C,MAAMzI,EAAgB,yDAAyD,CAElF,UACQgJ,aAAiBjF,GAG1BsF,EAAON,GAAc,SAAS,EAC9BmC,EAAe7B,EAAK,cAAc,WAAWL,EAAO,EAAI,EAEpDkC,EAAa,WAAa,GAAKA,EAAa,WAAa,QAGlDA,EAAa,WAAa,OADnC7B,EAAO6B,EAKP7B,EAAK,YAAY6B,CAAY,MAE1B,CAEL,GAAI,CAAC/E,GAAc,CAACJ,GAAsB,CAACC,GAC3CgD,EAAM,QAAQ,GAAG,IAAM,GACrB,OAAOrE,GAAsB0B,EAAsB1B,EAAmB,WAAWqE,CAAK,EAAIA,EAQ5F,GAHAK,EAAON,GAAcC,CAAK,EAGtB,CAACK,EACH,OAAOlD,EAAa,KAAOE,EAAsBzB,EAAY,EAEhE,CAIGyE,GAAQnD,IACVyC,EAAaU,EAAK,UAAU,EAK9B,MAAM+B,EAAe9B,GAAgB5C,EAAWsC,EAAQK,CAAI,EAI5D,KAAOO,EAAcwB,EAAa,YAE5BrB,GAAkBH,CAAW,IAM7BA,EAAY,mBAAmB/F,GACjCiH,GAAmBlB,EAAY,OAAO,EAKxCa,GAAoBb,CAAW,GAKjC,GAAIlD,EACF,OAAOsC,EAKT,GAAI7C,EAAY,CACd,GAAIC,EAGF,IAFA+E,EAAapG,GAAuB,KAAKsE,EAAK,aAAa,EAEpDA,EAAK,YAEV8B,EAAW,YAAY9B,EAAK,UAAU,OAGxC8B,EAAa9B,EAGf,OAAI/D,EAAa,YAAcA,EAAa,iBAQ1C6F,EAAalG,GAAW,KAAKvB,EAAkByH,EAAY,EAAI,GAG1DA,CACR,CAED,IAAIE,EAAiBrF,EAAiBqD,EAAK,UAAYA,EAAK,UAG5D,OAAIrD,GAAkBZ,EAAa,UAAU,GAAKiE,EAAK,eAAiBA,EAAK,cAAc,SAAWA,EAAK,cAAc,QAAQ,MAAQtJ,EAAW6C,GAAcyG,EAAK,cAAc,QAAQ,IAAI,IAC/LgC,EAAiB,aAAehC,EAAK,cAAc,QAAQ,KAAO;AAAA,EAAQgC,GAKxEtF,IACFsF,EAAiBzL,EAAcyL,EAAgBjJ,GAAe,GAAG,EACjEiJ,EAAiBzL,EAAcyL,EAAgBhJ,GAAU,GAAG,EAC5DgJ,EAAiBzL,EAAcyL,EAAgB/I,GAAa,GAAG,GAG1DqC,GAAsB0B,EAAsB1B,EAAmB,WAAW0G,CAAc,EAAIA,CACvG,EASE7H,EAAU,UAAY,SAAUyE,EAAK,CACnCD,GAAaC,CAAG,EAEhBhC,GAAa,EACjB,EAQEzC,EAAU,YAAc,UAAY,CAClCoE,EAAS,KACT3B,GAAa,EACjB,EAaEzC,EAAU,iBAAmB,SAAU8H,EAAKZ,EAAMtJ,EAAO,CAElDwG,GACHI,GAAa,CAAE,CAAA,EAGjB,MAAMuC,EAAQ5J,EAAkB2K,CAAG,EAC7Bd,EAAS7J,EAAkB+J,CAAI,EACrC,OAAOJ,GAAkBC,EAAOC,EAAQpJ,CAAK,CACjD,EAUEoC,EAAU,QAAU,SAAUmG,EAAY4B,EAAc,CAClD,OAAOA,GAAiB,aAI5BrG,EAAMyE,CAAU,EAAIzE,EAAMyE,CAAU,GAAK,CAAA,EACzCnK,EAAU0F,EAAMyE,CAAU,EAAG4B,CAAY,EAC7C,EAWE/H,EAAU,WAAa,SAAUmG,EAAY,CAC3C,GAAIzE,EAAMyE,CAAU,EAClB,OAAOpK,GAAS2F,EAAMyE,CAAU,CAAC,CAEvC,EASEnG,EAAU,YAAc,SAAUmG,EAAY,CACxCzE,EAAMyE,CAAU,IAClBzE,EAAMyE,CAAU,EAAI,GAE1B,EAQEnG,EAAU,eAAiB,UAAY,CACrC0B,EAAQ,CAAA,CACZ,EAES1B,CACT,CAEG,IAACgI,GAASlI,GAAe", "x_google_ignoreList": [0]}