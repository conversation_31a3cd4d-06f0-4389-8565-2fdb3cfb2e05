/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";import{B as Dt}from"./BlockLabel-3KxTaaiM.js";import{I as et}from"./IconButton-C_HS7fTi.js";import{E as qt}from"./Empty-ZqppqzTN.js";import{S as Ct}from"./ShareButton-q6iG5u0X.js";import{D as Mt}from"./Download-DVtk-Jv3.js";import{V as yt}from"./Video-fsmLZWjA.js";import{I as Rt}from"./IconButtonWrapper--EIOWuEM.js";import{n as Lt}from"./index-Ccc2t4AG.js";import{f as ce,u as Pt}from"./utils-BsGrhMNe.js";import{D as $t}from"./DownloadLink-QIttOhoR.js";import{T as Bt,P as It}from"./Trim-JQYgj7Jd.js";import{P as At}from"./Play-B0Q0U1Qz.js";import{U as Et}from"./Undo-DCjBnnSO.js";import{b as Ht,t as Nt,V as Xt}from"./Video-CW9C2EgQ.js";/* empty css                                             */import{M as Ut}from"./ModifyUpload-CkvPtjlQ.js";const{SvelteComponent:jt,append:Ft,attr:O,detach:Ot,init:zt,insert:Wt,noop:Ge,safe_not_equal:Gt,svg_element:nt}=window.__gradio__svelte__internal;function Jt(i){let e,n;return{c(){e=nt("svg"),n=nt("path"),O(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"fill","none"),O(e,"stroke","currentColor"),O(e,"stroke-width","1.5"),O(e,"stroke-linecap","round"),O(e,"stroke-linejoin","round")},m(t,l){Wt(t,e,l),Ft(e,n)},p:Ge,i:Ge,o:Ge,d(t){t&&Ot(e)}}}class Kt extends jt{constructor(e){super(),zt(this,e,null,Jt,Gt,{})}}const{SvelteComponent:Qt,append:ue,attr:B,destroy_block:Yt,detach:Ae,element:ae,ensure_array_like:it,flush:ke,init:Zt,insert:He,listen:_e,noop:Ye,run_all:xt,safe_not_equal:en,set_style:x,space:Je,src_url_equal:lt,update_keyed_each:tn}=window.__gradio__svelte__internal,{onMount:ot,onDestroy:nn}=window.__gradio__svelte__internal;function rt(i,e,n){const t=i.slice();return t[20]=e[n],t[22]=n,t}function ln(i){let e,n,t,l,o,r=[],a=new Map,f,g,_,u,d=it(i[1]);const c=w=>w[22];for(let w=0;w<d.length;w+=1){let v=rt(i,d,w),k=c(v);a.set(k,r[w]=at(k,v))}return{c(){e=ae("div"),n=ae("button"),t=Je(),l=ae("div"),o=Je();for(let w=0;w<r.length;w+=1)r[w].c();f=Je(),g=ae("button"),B(n,"aria-label","start drag handle for trimming video"),B(n,"class","handle left svelte-10c4beq"),x(n,"left",i[2]+"%"),B(l,"class","opaque-layer svelte-10c4beq"),x(l,"left",i[2]+"%"),x(l,"right",100-i[3]+"%"),B(g,"aria-label","end drag handle for trimming video"),B(g,"class","handle right svelte-10c4beq"),x(g,"left",i[3]+"%"),B(e,"id","timeline"),B(e,"class","thumbnail-wrapper svelte-10c4beq")},m(w,v){He(w,e,v),ue(e,n),ue(e,t),ue(e,l),ue(e,o);for(let k=0;k<r.length;k+=1)r[k]&&r[k].m(e,null);ue(e,f),ue(e,g),_||(u=[_e(n,"mousedown",i[10]),_e(n,"blur",i[5]),_e(n,"keydown",i[11]),_e(g,"mousedown",i[12]),_e(g,"blur",i[5]),_e(g,"keydown",i[13])],_=!0)},p(w,v){v&4&&x(n,"left",w[2]+"%"),v&4&&x(l,"left",w[2]+"%"),v&8&&x(l,"right",100-w[3]+"%"),v&2&&(d=it(w[1]),r=tn(r,v,c,1,w,d,a,e,Yt,at,f,rt)),v&8&&x(g,"left",w[3]+"%")},d(w){w&&Ae(e);for(let v=0;v<r.length;v+=1)r[v].d();_=!1,xt(u)}}}function on(i){let e;return{c(){e=ae("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',B(e,"class","load-wrap svelte-10c4beq")},m(n,t){He(n,e,t)},p:Ye,d(n){n&&Ae(e)}}}function at(i,e){let n,t,l;return{key:i,first:null,c(){n=ae("img"),lt(n.src,t=e[20])||B(n,"src",t),B(n,"alt",l=`frame-${e[22]}`),B(n,"draggable","false"),B(n,"class","svelte-10c4beq"),this.first=n},m(o,r){He(o,n,r)},p(o,r){e=o,r&2&&!lt(n.src,t=e[20])&&B(n,"src",t),r&2&&l!==(l=`frame-${e[22]}`)&&B(n,"alt",l)},d(o){o&&Ae(n)}}}function rn(i){let e;function n(o,r){return o[0]?on:ln}let t=n(i),l=t(i);return{c(){e=ae("div"),l.c(),B(e,"class","container svelte-10c4beq")},m(o,r){He(o,e,r),l.m(e,null)},p(o,[r]){t===(t=n(o))&&l?l.p(o,r):(l.d(1),l=t(o),l&&(l.c(),l.m(e,null)))},i:Ye,o:Ye,d(o){o&&Ae(e),l.d()}}}let Ke=10;function an(i,e,n){let{videoElement:t}=e,{trimmedDuration:l}=e,{dragStart:o}=e,{dragEnd:r}=e,{loadingTimeline:a}=e,f=[],g,_=0,u=100,d=null;const c=h=>{d=h},w=()=>{d=null},v=(h,T)=>{if(d){const y=document.getElementById("timeline");if(!y)return;const m=y.getBoundingClientRect();let V=(h.clientX-m.left)/m.width*100;if(T?V=d==="left"?_+T:u+T:V=(h.clientX-m.left)/m.width*100,V=Math.max(0,Math.min(V,100)),d==="left"){n(2,_=Math.min(V,u));const R=_/100*g;n(6,t.currentTime=R,t),n(8,o=R)}else if(d==="right"){n(3,u=Math.max(V,_));const R=u/100*g;n(6,t.currentTime=R,t),n(9,r=R)}const J=_/100*g,p=u/100*g;n(7,l=p-J),n(2,_),n(3,u)}},k=h=>{if(d){const T=1/g*100;h.key==="ArrowLeft"?v({clientX:0},-T):h.key==="ArrowRight"&&v({clientX:0},T)}},P=()=>{const h=document.createElement("canvas"),T=h.getContext("2d");if(!T)return;h.width=t.videoWidth,h.height=t.videoHeight,T.drawImage(t,0,0,h.width,h.height);const y=h.toDataURL("image/jpeg",.7);n(1,f=[...f,y])};ot(()=>{const h=()=>{g=t.duration;const T=g/Ke;let y=0;const m=()=>{P(),y++,y<Ke?n(6,t.currentTime+=T,t):t.removeEventListener("seeked",m)};t.addEventListener("seeked",m),n(6,t.currentTime=0,t)};t.readyState>=1?h():t.addEventListener("loadedmetadata",h)}),nn(()=>{window.removeEventListener("mousemove",v),window.removeEventListener("mouseup",w),window.removeEventListener("keydown",k)}),ot(()=>{window.addEventListener("mousemove",v),window.addEventListener("mouseup",w),window.addEventListener("keydown",k)});const I=()=>c("left"),$=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&c("left")},N=()=>c("right"),M=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&c("right")};return i.$$set=h=>{"videoElement"in h&&n(6,t=h.videoElement),"trimmedDuration"in h&&n(7,l=h.trimmedDuration),"dragStart"in h&&n(8,o=h.dragStart),"dragEnd"in h&&n(9,r=h.dragEnd),"loadingTimeline"in h&&n(0,a=h.loadingTimeline)},i.$$.update=()=>{i.$$.dirty&2&&n(0,a=f.length!==Ke)},[a,f,_,u,c,w,t,l,o,r,I,$,N,M]}class sn extends Qt{constructor(e){super(),Zt(this,e,an,rn,en,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}get videoElement(){return this.$$.ctx[6]}set videoElement(e){this.$$set({videoElement:e}),ke()}get trimmedDuration(){return this.$$.ctx[7]}set trimmedDuration(e){this.$$set({trimmedDuration:e}),ke()}get dragStart(){return this.$$.ctx[8]}set dragStart(e){this.$$set({dragStart:e}),ke()}get dragEnd(){return this.$$.ctx[9]}set dragEnd(e){this.$$set({dragEnd:e}),ke()}get loadingTimeline(){return this.$$.ctx[0]}set loadingTimeline(e){this.$$set({loadingTimeline:e}),ke()}}const{SvelteComponent:un,add_flush_callback:Me,append:me,attr:W,bind:Re,binding_callbacks:Le,check_outros:Ze,create_component:Ne,destroy_component:Xe,detach:Y,element:te,empty:_n,flush:X,group_outros:xe,init:dn,insert:Z,listen:st,mount_component:Ue,noop:fn,run_all:cn,safe_not_equal:mn,set_data:hn,space:Ve,text:gn,toggle_class:ee,transition_in:H,transition_out:G}=window.__gradio__svelte__internal,{onMount:bn}=window.__gradio__svelte__internal;function ut(i){let e,n,t,l,o,r,a;function f(c){i[18](c)}function g(c){i[19](c)}function _(c){i[20](c)}function u(c){i[21](c)}let d={videoElement:i[2]};return i[14]!==void 0&&(d.dragStart=i[14]),i[15]!==void 0&&(d.dragEnd=i[15]),i[12]!==void 0&&(d.trimmedDuration=i[12]),i[16]!==void 0&&(d.loadingTimeline=i[16]),n=new sn({props:d}),Le.push(()=>Re(n,"dragStart",f)),Le.push(()=>Re(n,"dragEnd",g)),Le.push(()=>Re(n,"trimmedDuration",_)),Le.push(()=>Re(n,"loadingTimeline",u)),{c(){e=te("div"),Ne(n.$$.fragment),W(e,"class","timeline-wrapper svelte-7yrr5f")},m(c,w){Z(c,e,w),Ue(n,e,null),a=!0},p(c,w){const v={};w&4&&(v.videoElement=c[2]),!t&&w&16384&&(t=!0,v.dragStart=c[14],Me(()=>t=!1)),!l&&w&32768&&(l=!0,v.dragEnd=c[15],Me(()=>l=!1)),!o&&w&4096&&(o=!0,v.trimmedDuration=c[12],Me(()=>o=!1)),!r&&w&65536&&(r=!0,v.loadingTimeline=c[16],Me(()=>r=!1)),n.$set(v)},i(c){a||(H(n.$$.fragment,c),a=!0)},o(c){G(n.$$.fragment,c),a=!1},d(c){c&&Y(e),Xe(n)}}}function wn(i){let e;return{c(){e=te("div"),W(e,"class","svelte-7yrr5f")},m(n,t){Z(n,e,t)},p:fn,d(n){n&&Y(e)}}}function vn(i){let e,n=ce(i[12])+"",t,l,o,r,a,f,g,_;return{c(){e=te("time"),t=gn(n),l=Ve(),o=te("div"),r=te("button"),r.textContent="Trim",a=Ve(),f=te("button"),f.textContent="Cancel",W(e,"aria-label","duration of selected region in seconds"),W(e,"class","svelte-7yrr5f"),ee(e,"hidden",i[16]),W(r,"class","text-button svelte-7yrr5f"),ee(r,"hidden",i[16]),W(f,"class","text-button svelte-7yrr5f"),ee(f,"hidden",i[16]),W(o,"class","edit-buttons svelte-7yrr5f")},m(u,d){Z(u,e,d),me(e,t),Z(u,l,d),Z(u,o,d),me(o,r),me(o,a),me(o,f),g||(_=[st(r,"click",i[22]),st(f,"click",i[17])],g=!0)},p(u,d){d&4096&&n!==(n=ce(u[12])+"")&&hn(t,n),d&65536&&ee(e,"hidden",u[16]),d&65536&&ee(r,"hidden",u[16]),d&65536&&ee(f,"hidden",u[16])},d(u){u&&(Y(e),Y(l),Y(o)),g=!1,cn(_)}}}function _t(i){let e,n;return e=new et({props:{Icon:Et,label:"Reset video to initial value",disabled:i[1]||!i[11]}}),e.$on("click",i[23]),{c(){Ne(e.$$.fragment)},m(t,l){Ue(e,t,l),n=!0},p(t,l){const o={};l&2050&&(o.disabled=t[1]||!t[11]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){G(e.$$.fragment,t),n=!1},d(t){Xe(e,t)}}}function dt(i){let e,n;return e=new et({props:{Icon:Bt,label:"Trim video to selection",disabled:i[1]}}),e.$on("click",i[17]),{c(){Ne(e.$$.fragment)},m(t,l){Ue(e,t,l),n=!0},p(t,l){const o={};l&2&&(o.disabled=t[1]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){G(e.$$.fragment,t),n=!1},d(t){Xe(e,t)}}}function pn(i){let e,n,t,l=i[3]&&i[0]===""&&_t(i),o=i[4]&&i[0]===""&&dt(i);return{c(){l&&l.c(),e=Ve(),o&&o.c(),n=_n()},m(r,a){l&&l.m(r,a),Z(r,e,a),o&&o.m(r,a),Z(r,n,a),t=!0},p(r,a){r[3]&&r[0]===""?l?(l.p(r,a),a&9&&H(l,1)):(l=_t(r),l.c(),H(l,1),l.m(e.parentNode,e)):l&&(xe(),G(l,1,1,()=>{l=null}),Ze()),r[4]&&r[0]===""?o?(o.p(r,a),a&17&&H(o,1)):(o=dt(r),o.c(),H(o,1),o.m(n.parentNode,n)):o&&(xe(),G(o,1,1,()=>{o=null}),Ze())},i(r){t||(H(l),H(o),t=!0)},o(r){G(l),G(o),t=!1},d(r){r&&(Y(e),Y(n)),l&&l.d(r),o&&o.d(r)}}}function kn(i){let e,n,t,l,o,r,a=i[0]==="edit"&&ut(i);function f(u,d){return u[0]==="edit"&&u[12]!==null?vn:wn}let g=f(i),_=g(i);return o=new Ut({props:{i18n:i[7],download:i[9]?i[8]?.url:null,$$slots:{default:[pn]},$$scope:{ctx:i}}}),o.$on("clear",i[24]),{c(){e=te("div"),a&&a.c(),n=Ve(),t=te("div"),_.c(),l=Ve(),Ne(o.$$.fragment),W(t,"class","controls svelte-7yrr5f"),W(t,"data-testid","waveform-controls"),W(e,"class","container svelte-7yrr5f"),ee(e,"hidden",i[0]!=="edit")},m(u,d){Z(u,e,d),a&&a.m(e,null),me(e,n),me(e,t),_.m(t,null),Z(u,l,d),Ue(o,u,d),r=!0},p(u,[d]){u[0]==="edit"?a?(a.p(u,d),d&1&&H(a,1)):(a=ut(u),a.c(),H(a,1),a.m(e,n)):a&&(xe(),G(a,1,1,()=>{a=null}),Ze()),g===(g=f(u))&&_?_.p(u,d):(_.d(1),_=g(u),_&&(_.c(),_.m(t,null))),(!r||d&1)&&ee(e,"hidden",u[0]!=="edit");const c={};d&128&&(c.i18n=u[7]),d&768&&(c.download=u[9]?u[8]?.url:null),d&33556539&&(c.$$scope={dirty:d,ctx:u}),o.$set(c)},i(u){r||(H(a),H(o.$$.fragment,u),r=!0)},o(u){G(a),G(o.$$.fragment,u),r=!1},d(u){u&&(Y(e),Y(l)),a&&a.d(),_.d(),Xe(o,u)}}}function yn(i,e,n){let{videoElement:t}=e,{showRedo:l=!1}=e,{interactive:o=!0}=e,{mode:r=""}=e,{handle_reset_value:a}=e,{handle_trim_video:f}=e,{processingVideo:g=!1}=e,{i18n:_}=e,{value:u=null}=e,{show_download_button:d=!1}=e,{handle_clear:c=()=>{}}=e,{has_change_history:w=!1}=e,v;bn(async()=>{n(13,v=await Ht())});let k=null,P=0,I=0,$=!1;const N=()=>{r==="edit"?(n(0,r=""),n(12,k=t.duration)):n(0,r="edit")};function M(p){P=p,n(14,P)}function h(p){I=p,n(15,I)}function T(p){k=p,n(12,k),n(0,r),n(2,t)}function y(p){$=p,n(16,$)}const m=()=>{n(0,r=""),n(1,g=!0),Nt(v,P,I,t).then(p=>{f(p)}).then(()=>{n(1,g=!1)})},V=()=>{a(),n(0,r="")},J=()=>c();return i.$$set=p=>{"videoElement"in p&&n(2,t=p.videoElement),"showRedo"in p&&n(3,l=p.showRedo),"interactive"in p&&n(4,o=p.interactive),"mode"in p&&n(0,r=p.mode),"handle_reset_value"in p&&n(5,a=p.handle_reset_value),"handle_trim_video"in p&&n(6,f=p.handle_trim_video),"processingVideo"in p&&n(1,g=p.processingVideo),"i18n"in p&&n(7,_=p.i18n),"value"in p&&n(8,u=p.value),"show_download_button"in p&&n(9,d=p.show_download_button),"handle_clear"in p&&n(10,c=p.handle_clear),"has_change_history"in p&&n(11,w=p.has_change_history)},i.$$.update=()=>{i.$$.dirty&4101&&r==="edit"&&k===null&&t&&n(12,k=t.duration)},[r,g,t,l,o,a,f,_,u,d,c,w,k,v,P,I,$,N,M,h,T,y,m,V,J]}class En extends un{constructor(e){super(),dn(this,e,yn,kn,mn,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1,i18n:7,value:8,show_download_button:9,handle_clear:10,has_change_history:11})}get videoElement(){return this.$$.ctx[2]}set videoElement(e){this.$$set({videoElement:e}),X()}get showRedo(){return this.$$.ctx[3]}set showRedo(e){this.$$set({showRedo:e}),X()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),X()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),X()}get handle_reset_value(){return this.$$.ctx[5]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),X()}get handle_trim_video(){return this.$$.ctx[6]}set handle_trim_video(e){this.$$set({handle_trim_video:e}),X()}get processingVideo(){return this.$$.ctx[1]}set processingVideo(e){this.$$set({processingVideo:e}),X()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),X()}get value(){return this.$$.ctx[8]}set value(e){this.$$set({value:e}),X()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),X()}get handle_clear(){return this.$$.ctx[10]}set handle_clear(e){this.$$set({handle_clear:e}),X()}get has_change_history(){return this.$$.ctx[11]}set has_change_history(e){this.$$set({has_change_history:e}),X()}}const{SvelteComponent:Tn,add_flush_callback:Ee,append:A,attr:C,bind:Te,binding_callbacks:Se,bubble:de,check_outros:ft,create_component:he,destroy_component:ge,detach:Pe,element:Q,empty:Sn,flush:q,group_outros:ct,init:Vn,insert:$e,listen:re,mount_component:be,prevent_default:mt,run_all:Dn,safe_not_equal:qn,set_data:ht,space:ye,src_url_equal:gt,stop_propagation:Cn,text:Qe,toggle_class:bt,transition_in:j,transition_out:z}=window.__gradio__svelte__internal,{createEventDispatcher:Mn}=window.__gradio__svelte__internal;function Rn(i){let e,n;return{c(){e=Q("track"),C(e,"kind","captions"),gt(e.src,n=i[1])||C(e,"src",n),e.default=!0},m(t,l){$e(t,e,l)},p(t,l){l[0]&2&&!gt(e.src,n=t[1])&&C(e,"src",n)},d(t){t&&Pe(e)}}}function Ln(i){let e,n;return e=new It({}),{c(){he(e.$$.fragment)},m(t,l){be(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function Pn(i){let e,n;return e=new At({}),{c(){he(e.$$.fragment)},m(t,l){be(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function $n(i){let e,n;return e=new Et({}),{c(){he(e.$$.fragment)},m(t,l){be(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){ge(e,t)}}}function wt(i){let e,n,t;function l(r){i[38](r)}let o={videoElement:i[17],showRedo:!0,handle_trim_video:i[23],handle_reset_value:i[7],value:i[11],i18n:i[9],show_download_button:i[10],handle_clear:i[12],has_change_history:i[13]};return i[18]!==void 0&&(o.processingVideo=i[18]),e=new En({props:o}),Se.push(()=>Te(e,"processingVideo",l)),{c(){he(e.$$.fragment)},m(r,a){be(e,r,a),t=!0},p(r,a){const f={};a[0]&131072&&(f.videoElement=r[17]),a[0]&128&&(f.handle_reset_value=r[7]),a[0]&2048&&(f.value=r[11]),a[0]&512&&(f.i18n=r[9]),a[0]&1024&&(f.show_download_button=r[10]),a[0]&4096&&(f.handle_clear=r[12]),a[0]&8192&&(f.has_change_history=r[13]),!n&&a[0]&262144&&(n=!0,f.processingVideo=r[18],Ee(()=>n=!1)),e.$set(f)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){z(e.$$.fragment,r),t=!1},d(r){ge(e,r)}}}function Bn(i){let e,n,t,l,o,r,a,f,g,_,u,d,c,w,v,k=ce(i[14])+"",P,I,$=ce(i[15])+"",N,M,h,T,y,m,V,J,p,R,pe,De;function je(b){i[28](b)}function Fe(b){i[29](b)}function Oe(b){i[30](b)}function ze(b){i[31](b)}let oe={src:i[0],preload:"auto",autoplay:i[3],loop:i[4],is_stream:i[8],"data-testid":`${i[5]}-player`,processingVideo:i[18],$$slots:{default:[Rn]},$$scope:{ctx:i}};i[14]!==void 0&&(oe.currentTime=i[14]),i[15]!==void 0&&(oe.duration=i[15]),i[16]!==void 0&&(oe.paused=i[16]),i[17]!==void 0&&(oe.node=i[17]),t=new Xt({props:oe}),Se.push(()=>Te(t,"currentTime",je)),Se.push(()=>Te(t,"duration",Fe)),Se.push(()=>Te(t,"paused",Oe)),Se.push(()=>Te(t,"node",ze)),t.$on("click",i[20]),t.$on("play",i[32]),t.$on("pause",i[33]),t.$on("error",i[34]),t.$on("ended",i[22]),t.$on("loadstart",i[35]),t.$on("loadeddata",i[36]),t.$on("loadedmetadata",i[37]);const qe=[$n,Pn,Ln],K=[];function Ce(b,E){return b[14]===b[15]?0:b[16]?1:2}d=Ce(i),c=K[d]=qe[d](i),V=new Kt({});let S=i[6]&&wt(i);return{c(){e=Q("div"),n=Q("div"),he(t.$$.fragment),f=ye(),g=Q("div"),_=Q("div"),u=Q("span"),c.c(),w=ye(),v=Q("span"),P=Qe(k),I=Qe(" / "),N=Qe($),M=ye(),h=Q("progress"),y=ye(),m=Q("div"),he(V.$$.fragment),J=ye(),S&&S.c(),p=Sn(),C(n,"class","mirror-wrap svelte-euo1cw"),bt(n,"mirror",i[2]),C(u,"role","button"),C(u,"tabindex","0"),C(u,"class","icon svelte-euo1cw"),C(u,"aria-label","play-pause-replay-button"),C(v,"class","time svelte-euo1cw"),h.value=T=i[14]/i[15]||0,C(h,"class","svelte-euo1cw"),C(m,"role","button"),C(m,"tabindex","0"),C(m,"class","icon svelte-euo1cw"),C(m,"aria-label","full-screen"),C(_,"class","inner svelte-euo1cw"),C(g,"class","controls svelte-euo1cw"),C(e,"class","wrap svelte-euo1cw")},m(b,E){$e(b,e,E),A(e,n),be(t,n,null),A(e,f),A(e,g),A(g,_),A(_,u),K[d].m(u,null),A(_,w),A(_,v),A(v,P),A(v,I),A(v,N),A(_,M),A(_,h),A(_,y),A(_,m),be(V,m,null),$e(b,J,E),S&&S.m(b,E),$e(b,p,E),R=!0,pe||(De=[re(u,"click",i[20]),re(u,"keydown",i[20]),re(h,"mousemove",i[19]),re(h,"touchmove",mt(i[19])),re(h,"click",Cn(mt(i[21]))),re(m,"click",i[24]),re(m,"keypress",i[24])],pe=!0)},p(b,E){const s={};E[0]&1&&(s.src=b[0]),E[0]&8&&(s.autoplay=b[3]),E[0]&16&&(s.loop=b[4]),E[0]&256&&(s.is_stream=b[8]),E[0]&32&&(s["data-testid"]=`${b[5]}-player`),E[0]&262144&&(s.processingVideo=b[18]),E[0]&2|E[1]&512&&(s.$$scope={dirty:E,ctx:b}),!l&&E[0]&16384&&(l=!0,s.currentTime=b[14],Ee(()=>l=!1)),!o&&E[0]&32768&&(o=!0,s.duration=b[15],Ee(()=>o=!1)),!r&&E[0]&65536&&(r=!0,s.paused=b[16],Ee(()=>r=!1)),!a&&E[0]&131072&&(a=!0,s.node=b[17],Ee(()=>a=!1)),t.$set(s),(!R||E[0]&4)&&bt(n,"mirror",b[2]);let F=d;d=Ce(b),d!==F&&(ct(),z(K[F],1,1,()=>{K[F]=null}),ft(),c=K[d],c||(c=K[d]=qe[d](b),c.c()),j(c,1),c.m(u,null)),(!R||E[0]&16384)&&k!==(k=ce(b[14])+"")&&ht(P,k),(!R||E[0]&32768)&&$!==($=ce(b[15])+"")&&ht(N,$),(!R||E[0]&49152&&T!==(T=b[14]/b[15]||0))&&(h.value=T),b[6]?S?(S.p(b,E),E[0]&64&&j(S,1)):(S=wt(b),S.c(),j(S,1),S.m(p.parentNode,p)):S&&(ct(),z(S,1,1,()=>{S=null}),ft())},i(b){R||(j(t.$$.fragment,b),j(c),j(V.$$.fragment,b),j(S),R=!0)},o(b){z(t.$$.fragment,b),z(c),z(V.$$.fragment,b),z(S),R=!1},d(b){b&&(Pe(e),Pe(J),Pe(p)),ge(t),K[d].d(),ge(V),S&&S.d(b),pe=!1,Dn(De)}}}function In(i,e,n){let{root:t=""}=e,{src:l}=e,{subtitle:o=null}=e,{mirror:r}=e,{autoplay:a}=e,{loop:f}=e,{label:g="test"}=e,{interactive:_=!1}=e,{handle_change:u=()=>{}}=e,{handle_reset_value:d=()=>{}}=e,{upload:c}=e,{is_stream:w}=e,{i18n:v}=e,{show_download_button:k=!1}=e,{value:P=null}=e,{handle_clear:I=()=>{}}=e,{has_change_history:$=!1}=e;const N=Mn();let M=0,h,T=!0,y,m=!1;function V(s){if(!h)return;if(s.type==="click"){p(s);return}if(s.type!=="touchmove"&&!(s.buttons&1))return;const F=s.type==="touchmove"?s.touches[0].clientX:s.clientX,{left:se,right:We}=s.currentTarget.getBoundingClientRect();n(14,M=h*(F-se)/(We-se))}async function J(){document.fullscreenElement!=y&&(y.currentTime>0&&!y.paused&&!y.ended&&y.readyState>y.HAVE_CURRENT_DATA?y.pause():await y.play())}function p(s){const{left:F,right:se}=s.currentTarget.getBoundingClientRect();n(14,M=h*(s.clientX-F)/(se-F))}function R(){N("stop"),N("end")}const pe=async s=>{let F=new File([s],"video.mp4");const se=await Lt([F]);let We=(await c(se,t))?.filter(Boolean)[0];u(We)};function De(){y.requestFullscreen()}function je(s){M=s,n(14,M)}function Fe(s){h=s,n(15,h)}function Oe(s){T=s,n(16,T)}function ze(s){y=s,n(17,y)}function oe(s){de.call(this,i,s)}function qe(s){de.call(this,i,s)}function K(s){de.call(this,i,s)}function Ce(s){de.call(this,i,s)}function S(s){de.call(this,i,s)}function b(s){de.call(this,i,s)}function E(s){m=s,n(18,m)}return i.$$set=s=>{"root"in s&&n(25,t=s.root),"src"in s&&n(0,l=s.src),"subtitle"in s&&n(1,o=s.subtitle),"mirror"in s&&n(2,r=s.mirror),"autoplay"in s&&n(3,a=s.autoplay),"loop"in s&&n(4,f=s.loop),"label"in s&&n(5,g=s.label),"interactive"in s&&n(6,_=s.interactive),"handle_change"in s&&n(26,u=s.handle_change),"handle_reset_value"in s&&n(7,d=s.handle_reset_value),"upload"in s&&n(27,c=s.upload),"is_stream"in s&&n(8,w=s.is_stream),"i18n"in s&&n(9,v=s.i18n),"show_download_button"in s&&n(10,k=s.show_download_button),"value"in s&&n(11,P=s.value),"handle_clear"in s&&n(12,I=s.handle_clear),"has_change_history"in s&&n(13,$=s.has_change_history)},i.$$.update=()=>{i.$$.dirty[0]&16384&&n(14,M=M||0),i.$$.dirty[0]&32768&&n(15,h=h||0)},[l,o,r,a,f,g,_,d,w,v,k,P,I,$,M,h,T,y,m,V,J,p,R,pe,De,t,u,c,je,Fe,Oe,ze,oe,qe,K,Ce,S,b,E]}class An extends Tn{constructor(e){super(),Vn(this,e,In,Bn,qn,{root:25,src:0,subtitle:1,mirror:2,autoplay:3,loop:4,label:5,interactive:6,handle_change:26,handle_reset_value:7,upload:27,is_stream:8,i18n:9,show_download_button:10,value:11,handle_clear:12,has_change_history:13},null,[-1,-1])}get root(){return this.$$.ctx[25]}set root(e){this.$$set({root:e}),q()}get src(){return this.$$.ctx[0]}set src(e){this.$$set({src:e}),q()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),q()}get mirror(){return this.$$.ctx[2]}set mirror(e){this.$$set({mirror:e}),q()}get autoplay(){return this.$$.ctx[3]}set autoplay(e){this.$$set({autoplay:e}),q()}get loop(){return this.$$.ctx[4]}set loop(e){this.$$set({loop:e}),q()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),q()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),q()}get handle_change(){return this.$$.ctx[26]}set handle_change(e){this.$$set({handle_change:e}),q()}get handle_reset_value(){return this.$$.ctx[7]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),q()}get upload(){return this.$$.ctx[27]}set upload(e){this.$$set({upload:e}),q()}get is_stream(){return this.$$.ctx[8]}set is_stream(e){this.$$set({is_stream:e}),q()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),q()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),q()}get value(){return this.$$.ctx[11]}set value(e){this.$$set({value:e}),q()}get handle_clear(){return this.$$.ctx[12]}set handle_clear(e){this.$$set({handle_clear:e}),q()}get has_change_history(){return this.$$.ctx[13]}set has_change_history(e){this.$$set({has_change_history:e}),q()}}const Hn=An,{SvelteComponent:Nn,attr:Xn,bubble:fe,check_outros:Be,create_component:ne,destroy_component:ie,detach:we,element:Un,empty:Tt,flush:U,group_outros:Ie,init:jn,insert:ve,mount_component:le,noop:St,safe_not_equal:Vt,space:tt,transition_in:D,transition_out:L}=window.__gradio__svelte__internal,{createEventDispatcher:Fn,afterUpdate:On,tick:zn}=window.__gradio__svelte__internal;function Wn(i){let e=i[0].url,n,t,l,o,r=vt(i);return l=new Rt({props:{display_top_corner:i[10],$$slots:{default:[Kn]},$$scope:{ctx:i}}}),{c(){r.c(),n=tt(),t=Un("div"),ne(l.$$.fragment),Xn(t,"data-testid","download-div")},m(a,f){r.m(a,f),ve(a,n,f),ve(a,t,f),le(l,t,null),o=!0},p(a,f){f&1&&Vt(e,e=a[0].url)?(Ie(),L(r,1,1,St),Be(),r=vt(a),r.c(),D(r,1),r.m(n.parentNode,n)):r.p(a,f);const g={};f&1024&&(g.display_top_corner=a[10]),f&4194657&&(g.$$scope={dirty:f,ctx:a}),l.$set(g)},i(a){o||(D(r),D(l.$$.fragment,a),o=!0)},o(a){L(r),L(l.$$.fragment,a),o=!1},d(a){a&&(we(n),we(t)),r.d(a),ie(l)}}}function Gn(i){let e,n;return e=new qt({props:{unpadded_box:!0,size:"large",$$slots:{default:[Qn]},$$scope:{ctx:i}}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&4194304&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function vt(i){let e,n;return e=new Hn({props:{src:i[0].url,subtitle:i[1]?.url,is_stream:i[0].is_stream,autoplay:i[4],mirror:!1,label:i[2],loop:i[7],interactive:!1,upload:i[9],i18n:i[8]}}),e.$on("play",i[12]),e.$on("pause",i[13]),e.$on("stop",i[14]),e.$on("end",i[15]),e.$on("loadedmetadata",i[16]),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&1&&(o.src=t[0].url),l&2&&(o.subtitle=t[1]?.url),l&1&&(o.is_stream=t[0].is_stream),l&16&&(o.autoplay=t[4]),l&4&&(o.label=t[2]),l&128&&(o.loop=t[7]),l&512&&(o.upload=t[9]),l&256&&(o.i18n=t[8]),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function pt(i){let e,n;return e=new $t({props:{href:i[0].is_stream?i[0].url?.replace("playlist.m3u8","playlist-file"):i[0].url,download:i[0].orig_name||i[0].path,$$slots:{default:[Jn]},$$scope:{ctx:i}}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&1&&(o.href=t[0].is_stream?t[0].url?.replace("playlist.m3u8","playlist-file"):t[0].url),l&1&&(o.download=t[0].orig_name||t[0].path),l&4194304&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Jn(i){let e,n;return e=new et({props:{Icon:Mt,label:"Download"}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p:St,i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function kt(i){let e,n;return e=new Ct({props:{i18n:i[8],value:i[0],formatter:i[17]}}),e.$on("error",i[18]),e.$on("share",i[19]),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&256&&(o.i18n=t[8]),l&1&&(o.value=t[0]),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Kn(i){let e,n,t,l=i[6]&&pt(i),o=i[5]&&kt(i);return{c(){l&&l.c(),e=tt(),o&&o.c(),n=Tt()},m(r,a){l&&l.m(r,a),ve(r,e,a),o&&o.m(r,a),ve(r,n,a),t=!0},p(r,a){r[6]?l?(l.p(r,a),a&64&&D(l,1)):(l=pt(r),l.c(),D(l,1),l.m(e.parentNode,e)):l&&(Ie(),L(l,1,1,()=>{l=null}),Be()),r[5]?o?(o.p(r,a),a&32&&D(o,1)):(o=kt(r),o.c(),D(o,1),o.m(n.parentNode,n)):o&&(Ie(),L(o,1,1,()=>{o=null}),Be())},i(r){t||(D(l),D(o),t=!0)},o(r){L(l),L(o),t=!1},d(r){r&&(we(e),we(n)),l&&l.d(r),o&&o.d(r)}}}function Qn(i){let e,n;return e=new yt({}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Yn(i){let e,n,t,l,o,r;e=new Dt({props:{show_label:i[3],Icon:yt,label:i[2]||"Video"}});const a=[Gn,Wn],f=[];function g(_,u){return!_[0]||_[0].url===void 0?0:1}return t=g(i),l=f[t]=a[t](i),{c(){ne(e.$$.fragment),n=tt(),l.c(),o=Tt()},m(_,u){le(e,_,u),ve(_,n,u),f[t].m(_,u),ve(_,o,u),r=!0},p(_,[u]){const d={};u&8&&(d.show_label=_[3]),u&4&&(d.label=_[2]||"Video"),e.$set(d);let c=t;t=g(_),t===c?f[t].p(_,u):(Ie(),L(f[c],1,1,()=>{f[c]=null}),Be(),l=f[t],l?l.p(_,u):(l=f[t]=a[t](_),l.c()),D(l,1),l.m(o.parentNode,o))},i(_){r||(D(e.$$.fragment,_),D(l),r=!0)},o(_){L(e.$$.fragment,_),L(l),r=!1},d(_){_&&(we(n),we(o)),ie(e,_),f[t].d(_)}}}function Zn(i,e,n){let{value:t=null}=e,{subtitle:l=null}=e,{label:o=void 0}=e,{show_label:r=!0}=e,{autoplay:a}=e,{show_share_button:f=!0}=e,{show_download_button:g=!0}=e,{loop:_}=e,{i18n:u}=e,{upload:d}=e,{display_icon_button_wrapper_top_corner:c=!1}=e,w=null,v=null;const k=Fn();On(async()=>{t!==w&&l!==v&&v!==null&&(w=t,n(0,t=null),await zn(),n(0,t=w)),w=t,v=l});function P(m){fe.call(this,i,m)}function I(m){fe.call(this,i,m)}function $(m){fe.call(this,i,m)}function N(m){fe.call(this,i,m)}const M=()=>{k("load")},h=async m=>m?await Pt(m.data):"";function T(m){fe.call(this,i,m)}function y(m){fe.call(this,i,m)}return i.$$set=m=>{"value"in m&&n(0,t=m.value),"subtitle"in m&&n(1,l=m.subtitle),"label"in m&&n(2,o=m.label),"show_label"in m&&n(3,r=m.show_label),"autoplay"in m&&n(4,a=m.autoplay),"show_share_button"in m&&n(5,f=m.show_share_button),"show_download_button"in m&&n(6,g=m.show_download_button),"loop"in m&&n(7,_=m.loop),"i18n"in m&&n(8,u=m.i18n),"upload"in m&&n(9,d=m.upload),"display_icon_button_wrapper_top_corner"in m&&n(10,c=m.display_icon_button_wrapper_top_corner)},i.$$.update=()=>{i.$$.dirty&1&&t&&k("change",t)},[t,l,o,r,a,f,g,_,u,d,c,k,P,I,$,N,M,h,T,y]}class xn extends Nn{constructor(e){super(),jn(this,e,Zn,Yn,Vt,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,loop:7,i18n:8,upload:9,display_icon_button_wrapper_top_corner:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),U()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),U()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),U()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),U()}get autoplay(){return this.$$.ctx[4]}set autoplay(e){this.$$set({autoplay:e}),U()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),U()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),U()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),U()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),U()}get upload(){return this.$$.ctx[9]}set upload(e){this.$$set({upload:e}),U()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),U()}}const wi=Object.freeze(Object.defineProperty({__proto__:null,default:xn},Symbol.toStringTag,{value:"Module"}));export{Hn as P,xn as V,wi as a};
//# sourceMappingURL=VideoPreview-DYkibY1b.js.map
