function ot(t,i,n){t.prototype=i.prototype=n,n.constructor=t}function Xt(t,i){var n=Object.create(t.prototype);for(var e in i)n[e]=i[e];return n}function H(){}var R=.7,O=1/R,M="\\s*([+-]?\\d+)\\s*",P="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",b="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",si=/^#([0-9a-f]{3,8})$/,hi=new RegExp(`^rgb\\(${M},${M},${M}\\)$`),oi=new RegExp(`^rgb\\(${b},${b},${b}\\)$`),ri=new RegExp(`^rgba\\(${M},${M},${M},${P}\\)$`),ai=new RegExp(`^rgba\\(${b},${b},${b},${P}\\)$`),_i=new RegExp(`^hsl\\(${P},${b},${b}\\)$`),li=new RegExp(`^hsla\\(${P},${b},${b},${P}\\)$`),gt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ot(H,rt,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:mt,formatHex:mt,formatHex8:ci,formatHsl:fi,formatRgb:wt,toString:wt});function mt(){return this.rgb().formatHex()}function ci(){return this.rgb().formatHex8()}function fi(){return At(this).formatHsl()}function wt(){return this.rgb().formatRgb()}function rt(t){var i,n;return t=(t+"").trim().toLowerCase(),(i=si.exec(t))?(n=i[1].length,i=parseInt(i[1],16),n===6?vt(i):n===3?new x(i>>8&15|i>>4&240,i>>4&15|i&240,(i&15)<<4|i&15,1):n===8?A(i>>24&255,i>>16&255,i>>8&255,(i&255)/255):n===4?A(i>>12&15|i>>8&240,i>>8&15|i>>4&240,i>>4&15|i&240,((i&15)<<4|i&15)/255):null):(i=hi.exec(t))?new x(i[1],i[2],i[3],1):(i=oi.exec(t))?new x(i[1]*255/100,i[2]*255/100,i[3]*255/100,1):(i=ri.exec(t))?A(i[1],i[2],i[3],i[4]):(i=ai.exec(t))?A(i[1]*255/100,i[2]*255/100,i[3]*255/100,i[4]):(i=_i.exec(t))?Nt(i[1],i[2]/100,i[3]/100,1):(i=li.exec(t))?Nt(i[1],i[2]/100,i[3]/100,i[4]):gt.hasOwnProperty(t)?vt(gt[t]):t==="transparent"?new x(NaN,NaN,NaN,0):null}function vt(t){return new x(t>>16&255,t>>8&255,t&255,1)}function A(t,i,n,e){return e<=0&&(t=i=n=NaN),new x(t,i,n,e)}function ui(t){return t instanceof H||(t=rt(t)),t?(t=t.rgb(),new x(t.r,t.g,t.b,t.opacity)):new x}function Y(t,i,n,e){return arguments.length===1?ui(t):new x(t,i,n,e??1)}function x(t,i,n,e){this.r=+t,this.g=+i,this.b=+n,this.opacity=+e}ot(x,Y,Xt(H,{brighter(t){return t=t==null?O:Math.pow(O,t),new x(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?R:Math.pow(R,t),new x(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new x(k(this.r),k(this.g),k(this.b),j(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:kt,formatHex:kt,formatHex8:xi,formatRgb:$t,toString:$t}));function kt(){return`#${v(this.r)}${v(this.g)}${v(this.b)}`}function xi(){return`#${v(this.r)}${v(this.g)}${v(this.b)}${v((isNaN(this.opacity)?1:this.opacity)*255)}`}function $t(){const t=j(this.opacity);return`${t===1?"rgb(":"rgba("}${k(this.r)}, ${k(this.g)}, ${k(this.b)}${t===1?")":`, ${t})`}`}function j(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function k(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function v(t){return t=k(t),(t<16?"0":"")+t.toString(16)}function Nt(t,i,n,e){return e<=0?t=i=n=NaN:n<=0||n>=1?t=i=NaN:i<=0&&(t=NaN),new p(t,i,n,e)}function At(t){if(t instanceof p)return new p(t.h,t.s,t.l,t.opacity);if(t instanceof H||(t=rt(t)),!t)return new p;if(t instanceof p)return t;t=t.rgb();var i=t.r/255,n=t.g/255,e=t.b/255,s=Math.min(i,n,e),h=Math.max(i,n,e),o=NaN,r=h-s,a=(h+s)/2;return r?(i===h?o=(n-e)/r+(n<e)*6:n===h?o=(e-i)/r+2:o=(i-n)/r+4,r/=a<.5?h+s:2-h-s,o*=60):r=a>0&&a<1?0:o,new p(o,r,a,t.opacity)}function pi(t,i,n,e){return arguments.length===1?At(t):new p(t,i,n,e??1)}function p(t,i,n,e){this.h=+t,this.s=+i,this.l=+n,this.opacity=+e}ot(p,pi,Xt(H,{brighter(t){return t=t==null?O:Math.pow(O,t),new p(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?R:Math.pow(R,t),new p(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,i=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,e=n+(n<.5?n:1-n)*i,s=2*n-e;return new x(W(t>=240?t-240:t+120,s,e),W(t,s,e),W(t<120?t+240:t-120,s,e),this.opacity)},clamp(){return new p(Mt(this.h),I(this.s),I(this.l),j(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=j(this.opacity);return`${t===1?"hsl(":"hsla("}${Mt(this.h)}, ${I(this.s)*100}%, ${I(this.l)*100}%${t===1?")":`, ${t})`}`}}));function Mt(t){return t=(t||0)%360,t<0?t+360:t}function I(t){return Math.max(0,Math.min(1,t||0))}function W(t,i,n){return(t<60?i+(n-i)*t/60:t<180?n:t<240?i+(n-i)*(240-t)/60:i)*255}function It(t,i,n,e,s){var h=t*t,o=h*t;return((1-3*t+3*h-o)*i+(4-6*h+3*o)*n+(1+3*t+3*h-3*o)*e+o*s)/6}function di(t){var i=t.length-1;return function(n){var e=n<=0?n=0:n>=1?(n=1,i-1):Math.floor(n*i),s=t[e],h=t[e+1],o=e>0?t[e-1]:2*s-h,r=e<i-1?t[e+2]:2*h-s;return It((n-e/i)*i,o,s,h,r)}}function yi(t){var i=t.length;return function(n){var e=Math.floor(((n%=1)<0?++n:n)*i),s=t[(e+i-1)%i],h=t[e%i],o=t[(e+1)%i],r=t[(e+2)%i];return It((n-e/i)*i,s,h,o,r)}}const at=t=>()=>t;function Bt(t,i){return function(n){return t+n*i}}function bi(t,i,n){return t=Math.pow(t,n),i=Math.pow(i,n)-t,n=1/n,function(e){return Math.pow(t+e*i,n)}}function Ii(t,i){var n=i-t;return n?Bt(t,n>180||n<-180?n-360*Math.round(n/360):n):at(isNaN(t)?i:t)}function gi(t){return(t=+t)==1?Ot:function(i,n){return n-i?bi(i,n,t):at(isNaN(i)?n:i)}}function Ot(t,i){var n=i-t;return n?Bt(t,n):at(isNaN(t)?i:t)}const Bi=function t(i){var n=gi(i);function e(s,h){var o=n((s=Y(s)).r,(h=Y(h)).r),r=n(s.g,h.g),a=n(s.b,h.b),l=Ot(s.opacity,h.opacity);return function(_){return s.r=o(_),s.g=r(_),s.b=a(_),s.opacity=l(_),s+""}}return e.gamma=t,e}(1);function Yt(t){return function(i){var n=i.length,e=new Array(n),s=new Array(n),h=new Array(n),o,r;for(o=0;o<n;++o)r=Y(i[o]),e[o]=r.r||0,s[o]=r.g||0,h[o]=r.b||0;return e=t(e),s=t(s),h=t(h),r.opacity=1,function(a){return r.r=e(a),r.g=s(a),r.b=h(a),r+""}}}var Oi=Yt(di),Yi=Yt(yi);function m(t,i){return t=+t,i=+i,function(n){return t*(1-n)+i*n}}var tt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,J=new RegExp(tt.source,"g");function mi(t){return function(){return t}}function wi(t){return function(i){return t(i)+""}}function ji(t,i){var n=tt.lastIndex=J.lastIndex=0,e,s,h,o=-1,r=[],a=[];for(t=t+"",i=i+"";(e=tt.exec(t))&&(s=J.exec(i));)(h=s.index)>n&&(h=i.slice(n,h),r[o]?r[o]+=h:r[++o]=h),(e=e[0])===(s=s[0])?r[o]?r[o]+=s:r[++o]=s:(r[++o]=null,a.push({i:o,x:m(e,s)})),n=J.lastIndex;return n<i.length&&(h=i.slice(n),r[o]?r[o]+=h:r[++o]=h),r.length<2?a[0]?wi(a[0].x):mi(i):(i=a.length,function(l){for(var _=0,c;_<i;++_)r[(c=a[_]).i]=c.x(l);return r.join("")})}var Tt=180/Math.PI,it={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function jt(t,i,n,e,s,h){var o,r,a;return(o=Math.sqrt(t*t+i*i))&&(t/=o,i/=o),(a=t*n+i*e)&&(n-=t*a,e-=i*a),(r=Math.sqrt(n*n+e*e))&&(n/=r,e/=r,a/=r),t*e<i*n&&(t=-t,i=-i,a=-a,o=-o),{translateX:s,translateY:h,rotate:Math.atan2(i,t)*Tt,skewX:Math.atan(a)*Tt,scaleX:o,scaleY:r}}var B;function vi(t){const i=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return i.isIdentity?it:jt(i.a,i.b,i.c,i.d,i.e,i.f)}function ki(t){return t==null||(B||(B=document.createElementNS("http://www.w3.org/2000/svg","g")),B.setAttribute("transform",t),!(t=B.transform.baseVal.consolidate()))?it:(t=t.matrix,jt(t.a,t.b,t.c,t.d,t.e,t.f))}function zt(t,i,n,e){function s(l){return l.length?l.pop()+" ":""}function h(l,_,c,f,u,d){if(l!==c||_!==f){var y=u.push("translate(",null,i,null,n);d.push({i:y-4,x:m(l,c)},{i:y-2,x:m(_,f)})}else(c||f)&&u.push("translate("+c+i+f+n)}function o(l,_,c,f){l!==_?(l-_>180?_+=360:_-l>180&&(l+=360),f.push({i:c.push(s(c)+"rotate(",null,e)-2,x:m(l,_)})):_&&c.push(s(c)+"rotate("+_+e)}function r(l,_,c,f){l!==_?f.push({i:c.push(s(c)+"skewX(",null,e)-2,x:m(l,_)}):_&&c.push(s(c)+"skewX("+_+e)}function a(l,_,c,f,u,d){if(l!==c||_!==f){var y=u.push(s(u)+"scale(",null,",",null,")");d.push({i:y-4,x:m(l,c)},{i:y-2,x:m(_,f)})}else(c!==1||f!==1)&&u.push(s(u)+"scale("+c+","+f+")")}return function(l,_){var c=[],f=[];return l=t(l),_=t(_),h(l.translateX,l.translateY,_.translateX,_.translateY,c,f),o(l.rotate,_.rotate,c,f),r(l.skewX,_.skewX,c,f),a(l.scaleX,l.scaleY,_.scaleX,_.scaleY,c,f),l=_=null,function(u){for(var d=-1,y=f.length,X;++d<y;)c[(X=f[d]).i]=X.x(u);return c.join("")}}}var zi=zt(vi,"px, ","px)","deg)"),Li=zt(ki,", ",")",")"),T=0,S=0,E=0,Lt=1e3,z,C,L=0,$=0,G=0,q=typeof performance=="object"&&performance.now?performance:Date,Dt=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Ft(){return $||(Dt($i),$=q.now()+G)}function $i(){$=0}function nt(){this._call=this._time=this._next=null}nt.prototype=Ni.prototype={constructor:nt,restart:function(t,i,n){if(typeof t!="function")throw new TypeError("callback is not a function");n=(n==null?Ft():+n)+(i==null?0:+i),!this._next&&C!==this&&(C?C._next=this:z=this,C=this),this._call=t,this._time=n,et()},stop:function(){this._call&&(this._call=null,this._time=1/0,et())}};function Ni(t,i,n){var e=new nt;return e.restart(t,i,n),e}function Mi(){Ft(),++T;for(var t=z,i;t;)(i=$-t._time)>=0&&t._call.call(void 0,i),t=t._next;--T}function Et(){$=(L=q.now())+G,T=S=0;try{Mi()}finally{T=0,Ei(),$=0}}function Ti(){var t=q.now(),i=t-L;i>Lt&&(G-=i,L=t)}function Ei(){for(var t,i=z,n,e=1/0;i;)i._call?(e>i._time&&(e=i._time),t=i,i=i._next):(n=i._next,i._next=null,i=t?t._next=n:z=n);C=t,et(e)}function et(t){if(!T){S&&(S=clearTimeout(S));var i=t-$;i>24?(t<1/0&&(S=setTimeout(Et,t-q.now()-G)),E&&(E=clearInterval(E))):(E||(L=q.now(),E=setInterval(Ti,Lt)),T=1,Dt(Et))}}const st=Math.PI,ht=2*st,w=1e-6,Si=ht-w;function Zt(t){this._+=t[0];for(let i=1,n=t.length;i<n;++i)this._+=arguments[i]+t[i]}function Ci(t){let i=Math.floor(t);if(!(i>=0))throw new Error(`invalid digits: ${t}`);if(i>15)return Zt;const n=10**i;return function(e){this._+=e[0];for(let s=1,h=e.length;s<h;++s)this._+=Math.round(arguments[s]*n)/n+e[s]}}class _t{constructor(i){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=i==null?Zt:Ci(i)}moveTo(i,n){this._append`M${this._x0=this._x1=+i},${this._y0=this._y1=+n}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(i,n){this._append`L${this._x1=+i},${this._y1=+n}`}quadraticCurveTo(i,n,e,s){this._append`Q${+i},${+n},${this._x1=+e},${this._y1=+s}`}bezierCurveTo(i,n,e,s,h,o){this._append`C${+i},${+n},${+e},${+s},${this._x1=+h},${this._y1=+o}`}arcTo(i,n,e,s,h){if(i=+i,n=+n,e=+e,s=+s,h=+h,h<0)throw new Error(`negative radius: ${h}`);let o=this._x1,r=this._y1,a=e-i,l=s-n,_=o-i,c=r-n,f=_*_+c*c;if(this._x1===null)this._append`M${this._x1=i},${this._y1=n}`;else if(f>w)if(!(Math.abs(c*a-l*_)>w)||!h)this._append`L${this._x1=i},${this._y1=n}`;else{let u=e-o,d=s-r,y=a*a+l*l,X=u*u+d*d,pt=Math.sqrt(y),dt=Math.sqrt(f),yt=h*Math.tan((st-Math.acos((y+f-X)/(2*pt*dt)))/2),V=yt/dt,bt=yt/pt;Math.abs(V-1)>w&&this._append`L${i+V*_},${n+V*c}`,this._append`A${h},${h},0,0,${+(c*u>_*d)},${this._x1=i+bt*a},${this._y1=n+bt*l}`}}arc(i,n,e,s,h,o){if(i=+i,n=+n,e=+e,o=!!o,e<0)throw new Error(`negative radius: ${e}`);let r=e*Math.cos(s),a=e*Math.sin(s),l=i+r,_=n+a,c=1^o,f=o?s-h:h-s;this._x1===null?this._append`M${l},${_}`:(Math.abs(this._x1-l)>w||Math.abs(this._y1-_)>w)&&this._append`L${l},${_}`,e&&(f<0&&(f=f%ht+ht),f>Si?this._append`A${e},${e},0,1,${c},${i-r},${n-a}A${e},${e},0,1,${c},${this._x1=l},${this._y1=_}`:f>w&&this._append`A${e},${e},0,${+(f>=st)},${c},${this._x1=i+e*Math.cos(h)},${this._y1=n+e*Math.sin(h)}`)}rect(i,n,e,s){this._append`M${this._x0=this._x1=+i},${this._y0=this._y1=+n}h${e=+e}v${+s}h${-e}Z`}toString(){return this._}}function Ri(){return new _t}Ri.prototype=_t.prototype;function N(t){return function(){return t}}const Di=Math.abs,Fi=Math.atan2,Zi=Math.cos,Gi=Math.max,Ki=Math.min,Qi=Math.sin,Vi=Math.sqrt,St=1e-12,lt=Math.PI,Ct=lt/2,Wi=2*lt;function Ji(t){return t>1?0:t<-1?lt:Math.acos(t)}function Ui(t){return t>=1?Ct:t<=-1?-Ct:Math.asin(t)}function Pi(t){let i=3;return t.digits=function(n){if(!arguments.length)return i;if(n==null)i=null;else{const e=Math.floor(n);if(!(e>=0))throw new RangeError(`invalid digits: ${n}`);i=e}return t},()=>new _t(i)}function qi(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Gt(t){this._context=t}Gt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2;default:this._context.lineTo(t,i);break}}};function Hi(t){return new Gt(t)}function Xi(t){return t[0]}function Ai(t){return t[1]}function tn(t,i){var n=N(!0),e=null,s=Hi,h=null,o=Pi(r);t=typeof t=="function"?t:t===void 0?Xi:N(t),i=typeof i=="function"?i:i===void 0?Ai:N(i);function r(a){var l,_=(a=qi(a)).length,c,f=!1,u;for(e==null&&(h=s(u=o())),l=0;l<=_;++l)!(l<_&&n(c=a[l],l,a))===f&&((f=!f)?h.lineStart():h.lineEnd()),f&&h.point(+t(c,l,a),+i(c,l,a));if(u)return h=null,u+""||null}return r.x=function(a){return arguments.length?(t=typeof a=="function"?a:N(+a),r):t},r.y=function(a){return arguments.length?(i=typeof a=="function"?a:N(+a),r):i},r.defined=function(a){return arguments.length?(n=typeof a=="function"?a:N(!!a),r):n},r.curve=function(a){return arguments.length?(s=a,e!=null&&(h=s(e)),r):s},r.context=function(a){return arguments.length?(a==null?e=h=null:h=s(e=a),r):e},r}function g(){}function D(t,i,n){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+i)/6,(t._y0+4*t._y1+n)/6)}function K(t){this._context=t}K.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:D(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:D(this,t,i);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=i}};function nn(t){return new K(t)}function Kt(t){this._context=t}Kt.prototype={areaStart:g,areaEnd:g,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._x2=t,this._y2=i;break;case 1:this._point=2,this._x3=t,this._y3=i;break;case 2:this._point=3,this._x4=t,this._y4=i,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+i)/6);break;default:D(this,t,i);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=i}};function en(t){return new Kt(t)}function Qt(t){this._context=t}Qt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+t)/6,e=(this._y0+4*this._y1+i)/6;this._line?this._context.lineTo(n,e):this._context.moveTo(n,e);break;case 3:this._point=4;default:D(this,t,i);break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=i}};function sn(t){return new Qt(t)}function Vt(t,i){this._basis=new K(t),this._beta=i}Vt.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,i=this._y,n=t.length-1;if(n>0)for(var e=t[0],s=i[0],h=t[n]-e,o=i[n]-s,r=-1,a;++r<=n;)a=r/n,this._basis.point(this._beta*t[r]+(1-this._beta)*(e+a*h),this._beta*i[r]+(1-this._beta)*(s+a*o));this._x=this._y=null,this._basis.lineEnd()},point:function(t,i){this._x.push(+t),this._y.push(+i)}};const hn=function t(i){function n(e){return i===1?new K(e):new Vt(e,i)}return n.beta=function(e){return t(+e)},n}(.85);function F(t,i,n){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-i),t._y2+t._k*(t._y1-n),t._x2,t._y2)}function ct(t,i){this._context=t,this._k=(1-i)/6}ct.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:F(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2,this._x1=t,this._y1=i;break;case 2:this._point=3;default:F(this,t,i);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const on=function t(i){function n(e){return new ct(e,i)}return n.tension=function(e){return t(+e)},n}(0);function ft(t,i){this._context=t,this._k=(1-i)/6}ft.prototype={areaStart:g,areaEnd:g,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._x3=t,this._y3=i;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=i);break;case 2:this._point=3,this._x5=t,this._y5=i;break;default:F(this,t,i);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const rn=function t(i){function n(e){return new ft(e,i)}return n.tension=function(e){return t(+e)},n}(0);function ut(t,i){this._context=t,this._k=(1-i)/6}ut.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:F(this,t,i);break}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const an=function t(i){function n(e){return new ut(e,i)}return n.tension=function(e){return t(+e)},n}(0);function xt(t,i,n){var e=t._x1,s=t._y1,h=t._x2,o=t._y2;if(t._l01_a>St){var r=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,a=3*t._l01_a*(t._l01_a+t._l12_a);e=(e*r-t._x0*t._l12_2a+t._x2*t._l01_2a)/a,s=(s*r-t._y0*t._l12_2a+t._y2*t._l01_2a)/a}if(t._l23_a>St){var l=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,_=3*t._l23_a*(t._l23_a+t._l12_a);h=(h*l+t._x1*t._l23_2a-i*t._l12_2a)/_,o=(o*l+t._y1*t._l23_2a-n*t._l12_2a)/_}t._context.bezierCurveTo(e,s,h,o,t._x2,t._y2)}function Wt(t,i){this._context=t,this._alpha=i}Wt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){if(t=+t,i=+i,this._point){var n=this._x2-t,e=this._y2-i;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2;break;case 2:this._point=3;default:xt(this,t,i);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const _n=function t(i){function n(e){return i?new Wt(e,i):new ct(e,0)}return n.alpha=function(e){return t(+e)},n}(.5);function Jt(t,i){this._context=t,this._alpha=i}Jt.prototype={areaStart:g,areaEnd:g,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(t,i){if(t=+t,i=+i,this._point){var n=this._x2-t,e=this._y2-i;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=i;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=i);break;case 2:this._point=3,this._x5=t,this._y5=i;break;default:xt(this,t,i);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const ln=function t(i){function n(e){return i?new Jt(e,i):new ft(e,0)}return n.alpha=function(e){return t(+e)},n}(.5);function Ut(t,i){this._context=t,this._alpha=i}Ut.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){if(t=+t,i=+i,this._point){var n=this._x2-t,e=this._y2-i;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+e*e,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:xt(this,t,i);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=i}};const cn=function t(i){function n(e){return i?new Ut(e,i):new ut(e,0)}return n.alpha=function(e){return t(+e)},n}(.5);function ti(t){this._context=t}ti.prototype={areaStart:g,areaEnd:g,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,i){t=+t,i=+i,this._point?this._context.lineTo(t,i):(this._point=1,this._context.moveTo(t,i))}};function fn(t){return new ti(t)}function Rt(t){return t<0?-1:1}function Pt(t,i,n){var e=t._x1-t._x0,s=i-t._x1,h=(t._y1-t._y0)/(e||s<0&&-0),o=(n-t._y1)/(s||e<0&&-0),r=(h*s+o*e)/(e+s);return(Rt(h)+Rt(o))*Math.min(Math.abs(h),Math.abs(o),.5*Math.abs(r))||0}function qt(t,i){var n=t._x1-t._x0;return n?(3*(t._y1-t._y0)/n-i)/2:i}function U(t,i,n){var e=t._x0,s=t._y0,h=t._x1,o=t._y1,r=(h-e)/3;t._context.bezierCurveTo(e+r,s+r*i,h-r,o-r*n,h,o)}function Z(t){this._context=t}Z.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:U(this,this._t0,qt(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,i){var n=NaN;if(t=+t,i=+i,!(t===this._x1&&i===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2;break;case 2:this._point=3,U(this,qt(this,n=Pt(this,t,i)),n);break;default:U(this,this._t0,n=Pt(this,t,i));break}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=i,this._t0=n}}};function ii(t){this._context=new ni(t)}(ii.prototype=Object.create(Z.prototype)).point=function(t,i){Z.prototype.point.call(this,i,t)};function ni(t){this._context=t}ni.prototype={moveTo:function(t,i){this._context.moveTo(i,t)},closePath:function(){this._context.closePath()},lineTo:function(t,i){this._context.lineTo(i,t)},bezierCurveTo:function(t,i,n,e,s,h){this._context.bezierCurveTo(i,t,e,n,h,s)}};function un(t){return new Z(t)}function xn(t){return new ii(t)}function ei(t){this._context=t}ei.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,i=this._y,n=t.length;if(n)if(this._line?this._context.lineTo(t[0],i[0]):this._context.moveTo(t[0],i[0]),n===2)this._context.lineTo(t[1],i[1]);else for(var e=Ht(t),s=Ht(i),h=0,o=1;o<n;++h,++o)this._context.bezierCurveTo(e[0][h],s[0][h],e[1][h],s[1][h],t[o],i[o]);(this._line||this._line!==0&&n===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,i){this._x.push(+t),this._y.push(+i)}};function Ht(t){var i,n=t.length-1,e,s=new Array(n),h=new Array(n),o=new Array(n);for(s[0]=0,h[0]=2,o[0]=t[0]+2*t[1],i=1;i<n-1;++i)s[i]=1,h[i]=4,o[i]=4*t[i]+2*t[i+1];for(s[n-1]=2,h[n-1]=7,o[n-1]=8*t[n-1]+t[n],i=1;i<n;++i)e=s[i]/h[i-1],h[i]-=e,o[i]-=e*o[i-1];for(s[n-1]=o[n-1]/h[n-1],i=n-2;i>=0;--i)s[i]=(o[i]-s[i+1])/h[i];for(h[n-1]=(t[n]+s[n-1])/2,i=0;i<n-1;++i)h[i]=2*t[i+1]-s[i+1];return[s,h]}function pn(t){return new ei(t)}function Q(t,i){this._context=t,this._t=i}Q.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,i){switch(t=+t,i=+i,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,i):this._context.moveTo(t,i);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,i),this._context.lineTo(t,i);else{var n=this._x*(1-this._t)+t*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,i)}break}}this._x=t,this._y=i}};function dn(t){return new Q(t,.5)}function yn(t){return new Q(t,0)}function bn(t){return new Q(t,1)}export{cn as $,Bi as A,ji as B,H as C,O as D,R as E,pi as F,di as G,yi as H,Oi as I,Yi as J,zi as K,Li as L,Ft as M,Ni as N,Hi as O,Xi as P,Ai as Q,x as R,en as S,nt as T,sn as U,hn as V,on as W,an as X,rn as Y,_n as Z,ln as _,N as a,fn as a0,xn as a1,un as a2,pn as a3,dn as a4,bn as a5,yn as a6,Ri as a7,Y as a8,Vi as b,Zi as c,Di as d,St as e,Fi as f,Ui as g,Ct as h,Ji as i,Gi as j,nn as k,tn as l,Ki as m,qi as n,ot as o,lt as p,Xt as q,ui as r,Qi as s,Wi as t,Ot as u,Ii as v,Pi as w,at as x,m as y,rt as z};
//# sourceMappingURL=step-Ce-xBr2D.js.map
