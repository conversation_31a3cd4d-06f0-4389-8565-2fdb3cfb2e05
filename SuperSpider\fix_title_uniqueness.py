#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复搜索记录唯一性约束
基于标题+平台+用户来确保唯一性，清理重复记录
"""

import os
import sys
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def fix_title_uniqueness():
    """修复基于标题的唯一性约束"""
    # MySQL 连接配置
    db_config = {
        'host': os.environ.get('MYSQL_HOST', 'localhost'),
        'port': int(os.environ.get('MYSQL_PORT', '3306')),
        'user': os.environ.get('MYSQL_USER', 'root'),
        'password': os.environ.get('MYSQL_PASSWORD', '123456'),
        'database': os.environ.get('MYSQL_DATABASE', 'superspider'),
        'charset': 'utf8mb4'
    }
    
    print("开始修复搜索记录唯一性约束...")
    print(f"连接到 MySQL: {db_config['host']}:{db_config['port']}/{db_config['database']}")

    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 1. 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'search_records'")
        if not cursor.fetchone():
            print("search_records 表不存在，跳过迁移")
            return
        
        # 2. 删除旧的URL哈希约束（如果存在）
        print("删除旧的URL哈希约束...")
        try:
            cursor.execute("ALTER TABLE search_records DROP CONSTRAINT uq_user_content_url_hash")
            print("✅ 删除旧的URL哈希约束成功")
        except Exception as e:
            if "doesn't exist" in str(e) or "check that column/key exists" in str(e):
                print("✅ 旧的URL哈希约束不存在")
            else:
                print(f"⚠️  删除旧约束失败: {e}")
        
        # 3. 查找基于标题的重复记录
        print("查找基于标题的重复记录...")
        cursor.execute("""
            SELECT user_id, platform, title, COUNT(*) as count
            FROM search_records 
            WHERE title IS NOT NULL AND title != ''
            GROUP BY user_id, platform, title 
            HAVING COUNT(*) > 1
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"发现 {len(duplicates)} 组重复记录")
            
            for user_id, platform, title, count in duplicates:
                print(f"处理用户 {user_id} 在 {platform} 平台的重复记录: {title} ({count} 条)")
                
                # 获取所有重复记录，按创建时间排序
                cursor.execute("""
                    SELECT id, created_at, search_count 
                    FROM search_records 
                    WHERE user_id = %s AND platform = %s AND title = %s
                    ORDER BY created_at DESC
                """, (user_id, platform, title))
                
                records = cursor.fetchall()
                
                if len(records) > 1:
                    # 保留最新的记录，合并搜索次数
                    keep_id = records[0][0]
                    total_search_count = sum(record[2] or 1 for record in records)
                    
                    # 更新保留记录的搜索次数
                    cursor.execute("""
                        UPDATE search_records 
                        SET search_count = %s, last_searched_at = %s
                        WHERE id = %s
                    """, (total_search_count, datetime.now(), keep_id))
                    
                    # 删除其他重复记录
                    delete_ids = [record[0] for record in records[1:]]
                    for delete_id in delete_ids:
                        cursor.execute("DELETE FROM search_records WHERE id = %s", (delete_id,))
                        print(f"  删除重复记录 ID: {delete_id}")
        else:
            print("没有发现基于标题的重复记录")
        
        # 4. 添加新的唯一性约束
        print("添加基于标题的唯一性约束...")
        try:
            cursor.execute("""
                ALTER TABLE search_records 
                ADD CONSTRAINT uq_user_platform_title UNIQUE (user_id, platform, title)
            """)
            print("✅ 基于标题的唯一性约束添加成功")
        except Exception as e:
            if "already exists" in str(e) or "Duplicate key name" in str(e):
                print("✅ 基于标题的唯一性约束已存在")
            elif "Duplicate entry" in str(e):
                print("⚠️  仍有重复记录，需要手动清理")
                # 再次查找重复记录
                cursor.execute("""
                    SELECT user_id, platform, title, COUNT(*) as count
                    FROM search_records 
                    WHERE title IS NOT NULL AND title != ''
                    GROUP BY user_id, platform, title 
                    HAVING COUNT(*) > 1
                    LIMIT 5
                """)
                remaining_duplicates = cursor.fetchall()
                for dup in remaining_duplicates:
                    print(f"  重复记录: 用户{dup[0]}, {dup[1]}, {dup[2]} ({dup[3]}条)")
            else:
                print(f"⚠️  添加唯一性约束失败: {e}")
        
        # 5. 删除不需要的URL哈希字段和索引
        print("清理URL哈希相关字段...")
        try:
            cursor.execute("ALTER TABLE search_records DROP INDEX idx_content_url_hash")
            print("✅ 删除URL哈希索引成功")
        except Exception as e:
            if "doesn't exist" in str(e) or "check that column/key exists" in str(e):
                print("✅ URL哈希索引不存在")
            else:
                print(f"⚠️  删除URL哈希索引失败: {e}")
        
        try:
            cursor.execute("ALTER TABLE search_records DROP COLUMN content_url_hash")
            print("✅ 删除URL哈希字段成功")
        except Exception as e:
            if "doesn't exist" in str(e) or "check that column/key exists" in str(e):
                print("✅ URL哈希字段不存在")
            else:
                print(f"⚠️  删除URL哈希字段失败: {e}")
        
        conn.commit()
        print("✅ 基于标题的唯一性约束修复完成！")
        
        # 6. 验证结果
        cursor.execute("SELECT COUNT(*) FROM search_records")
        total_count = cursor.fetchone()[0]
        print(f"修复后总记录数: {total_count}")
        
        # 7. 再次检查是否还有重复记录
        cursor.execute("""
            SELECT user_id, platform, title, COUNT(*) as count
            FROM search_records 
            WHERE title IS NOT NULL AND title != ''
            GROUP BY user_id, platform, title 
            HAVING COUNT(*) > 1
        """)
        remaining_duplicates = cursor.fetchall()
        if remaining_duplicates:
            print(f"⚠️  仍有 {len(remaining_duplicates)} 组重复记录需要处理")
        else:
            print("✅ 没有基于标题的重复记录")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        raise
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_title_uniqueness()
