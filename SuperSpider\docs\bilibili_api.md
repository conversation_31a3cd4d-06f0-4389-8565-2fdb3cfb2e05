# 哔哩哔哩 API 文档

## 概述

哔哩哔哩模块提供了解析哔哩哔哩视频信息的功能，支持BV号和AV号的视频链接解析。

## 文件结构

```
backend/
├── api/
│   └── bilibili_api.py          # 哔哩哔哩API接口
├── spiders/
│   └── bilibili_spider.py       # 哔哩哔哩爬虫核心逻辑
├── utils/
│   └── bilibili_utils.py        # 哔哩哔哩工具函数
└── tests/
    └── test_bilibili.py         # 测试文件
```

## API 接口

### 基础URL
```
/api/bilibili
```

### 1. 解析视频信息

**接口地址：** `POST /api/bilibili/parse`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer <token>  # 需要登录
```

**请求体：**
```json
{
    "video_url": "https://www.bilibili.com/video/BV1xx411c7mD"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "解析成功",
    "data": {
        "title": "视频标题",
        "author": "UP主名称",
        "duration": 300,
        "videoUrl": "视频下载地址",
        "coverUrl": "封面图片地址",
        "description": "视频描述",
        "tags": ["标签1", "标签2"],
        "view_count": 10000,
        "like_count": 500,
        "coin_count": 100,
        "share_count": 50,
        "upload_time": 1640995200,
        "bvid": "BV1xx411c7mD",
        "aid": 12345
    }
}
```

### 2. 下载视频

**接口地址：** `POST /api/bilibili/download`

**请求体：**
```json
{
    "video_url": "视频下载地址",
    "filename": "文件名(可选)"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "下载成功",
    "data": {
        "download_url": "下载地址",
        "filename": "文件名",
        "file_size": 1024000
    }
}
```

### 3. 获取UP主信息

**接口地址：** `GET /api/bilibili/user/<user_id>`

**响应示例：**
```json
{
    "success": true,
    "message": "获取成功",
    "data": {
        "uid": "用户ID",
        "name": "用户名",
        "avatar": "头像地址",
        "sign": "个性签名",
        "level": 6,
        "follower_count": 10000,
        "following_count": 100,
        "video_count": 50
    }
}
```

### 4. 搜索视频

**接口地址：** `GET /api/bilibili/search`

**查询参数：**
- `keyword`: 搜索关键词
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `order`: 排序方式，默认为综合排序

**响应示例：**
```json
{
    "success": true,
    "message": "搜索成功",
    "data": {
        "videos": [
            {
                "title": "视频标题",
                "author": "UP主名称",
                "bvid": "BV1xx411c7mD",
                "view_count": 10000
            }
        ],
        "total": 100,
        "page": 1,
        "page_size": 20
    }
}
```

### 5. 检查服务状态

**接口地址：** `GET /api/bilibili/status`

**响应示例：**
```json
{
    "success": true,
    "message": "服务正常",
    "data": {
        "spider": {
            "name": "哔哩哔哩爬虫",
            "platform": "bilibili",
            "status": "ready",
            "version": "1.0.0"
        }
    }
}
```

## 支持的URL格式

- `https://www.bilibili.com/video/BV1xx411c7mD`
- `https://bilibili.com/video/BV1xx411c7mD`
- `https://m.bilibili.com/video/BV1xx411c7mD`
- `https://b23.tv/abc123` (短链接)
- `https://www.bilibili.com/video/av12345`

## 工具函数

### BilibiliUtils 类

#### 主要方法：

1. **extract_video_id(url)** - 从URL提取视频ID
2. **bv_to_av(bvid)** - BV号转AV号
3. **av_to_bv(aid)** - AV号转BV号
4. **parse_duration(duration_str)** - 解析时长字符串
5. **format_count(count)** - 格式化数字显示
6. **get_video_quality_desc(qn)** - 获取清晰度描述
7. **validate_url(url)** - 验证URL有效性
8. **clean_url(url)** - 清理URL参数

#### 使用示例：

```python
from backend.utils.bilibili_utils import BilibiliUtils

utils = BilibiliUtils()

# BV号转AV号
aid = utils.bv_to_av("BV1xx411c7mD")
print(f"AV号: {aid}")

# AV号转BV号
bvid = utils.av_to_bv(12345)
print(f"BV号: {bvid}")

# 验证URL
is_valid = utils.validate_url("https://www.bilibili.com/video/BV1xx411c7mD")
print(f"URL有效: {is_valid}")

# 格式化数字
formatted = utils.format_count(12345)
print(f"格式化数字: {formatted}")  # 输出: 1.2万
```

## 错误处理

### 常见错误码：

- `400` - 请求参数错误
- `401` - 未登录或登录过期
- `404` - 接口不存在
- `500` - 服务器内部错误
- `501` - 功能未实现

### 错误响应格式：

```json
{
    "success": false,
    "message": "错误描述",
    "data": null
}
```

## 开发状态

### 已完成：
- ✅ 基础文件结构
- ✅ API接口定义
- ✅ 工具函数实现
- ✅ URL验证功能
- ✅ BV/AV号转换
- ✅ 测试文件

### 待实现：
- ⏳ 视频信息解析
- ⏳ 视频流地址获取
- ⏳ 下载功能
- ⏳ UP主信息获取
- ⏳ 视频搜索功能
- ⏳ 短链接解析

## 测试

运行测试：
```bash
cd SuperSpider/backend
python tests/test_bilibili.py
```

测试覆盖：
- 爬虫类初始化
- URL验证
- 工具函数
- BV/AV号转换
- 数字格式化
- 时长解析

## 注意事项

1. **登录要求**: 所有API接口都需要用户登录
2. **速率限制**: 建议添加请求频率限制
3. **版权问题**: 下载功能需要注意版权法规
4. **反爬虫**: 哔哩哔哩有反爬虫机制，需要合理设置请求头和频率
5. **API变化**: 哔哩哔哩API可能会变化，需要定期更新

## 后续开发计划

1. 实现核心解析功能
2. 添加视频质量选择
3. 支持分P视频
4. 添加弹幕下载（可选）
5. 实现批量下载
6. 添加进度显示
7. 优化错误处理
