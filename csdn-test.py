import requests
import pdfkit
import re
import os
from bs4 import BeautifulSoup
import html2text
import random
import time

# 创建输出目录
os.makedirs("HTML", exist_ok=True)
os.makedirs("PDF", exist_ok=True)
os.makedirs("MD", exist_ok=True)

# 定义HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{title}</title>
    <style>
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif; 
            line-height: 1.6; 
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }}
        pre {{ 
            background-color: #f6f8fa; 
            padding: 16px; 
            overflow: auto; 
            border-radius: 4px;
        }}
        code {{ 
            background-color: #f6f8fa; 
            padding: 0.2em 0.4em; 
            border-radius: 4px;
        }}
        img {{ 
            max-width: 100%; 
            height: auto; 
            display: block;
            margin: 0 auto;
        }}
        h1 {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    {content}
</body>
</html>
"""

USER_AGENT_LIST = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.60"
]


class CSDNSpider():
    def __init__(self):
        self.headers = {'user-agent': random.choice(USER_AGENT_LIST)}

    def send_request(self, url):
        """发送HTTP请求获取页面内容"""
        try:
            response = requests.get(
                url,
                headers=self.headers,
                timeout=10,
                verify=False  # 绕过SSL验证
            )
            response.encoding = "utf-8"
            if response.status_code == 200:
                return response
            else:
                print(f"请求失败: HTTP状态码 {response.status_code}")
        except Exception as e:
            print(f"请求异常: {str(e)}")
        return None

    def parse_article(self, html):
        """解析文章内容"""
        soup = BeautifulSoup(html, 'html.parser')

        # 获取标题
        title_element = soup.find('h1', class_='title-article') or soup.find('h1', id='articleContentId')
        if title_element:
            title = title_element.get_text().strip()
        else:
            title = f"未命名文章_{int(time.time())}"
            print("未找到标题元素，使用默认标题")

        # 获取内容区域
        content_div = soup.find('div', id='content_views')
        if not content_div:
            content_div = soup.find('article', class_='baidu_pl')
        if not content_div:
            content_div = soup.find('div', class_='article_content')
        if not content_div:
            print("未找到内容区域")
            return None, None

        # 移除不需要的元素
        for element in content_div.select('.hide-article-box, .article-copyright, .recommend-box'):
            element.decompose()

        # 修复图片路径
        for img in content_div.find_all('img'):
            if 'data-src' in img.attrs:
                img['src'] = img['data-src']
                del img['data-src']
            if not img.get('src'):
                img.decompose()

        return title, str(content_div)

    def save_content(self, html_content, title):
        """保存文章内容到HTML、PDF和MD文件"""
        # 清理文件名
        clean_title = re.sub(r'[\\/*?:"<>|]', '', title)[:100]
        if not clean_title:
            clean_title = f"untitled_{int(time.time())}"

        # 文件路径
        html_path = f"HTML/{clean_title}.html"
        pdf_path = f"PDF/{clean_title}.pdf"
        md_path = f"MD/{clean_title}.md"

        # 保存HTML
        try:
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                print(f"HTML文件已保存至: {os.path.abspath(html_path)}")
        except Exception as e:
            print(f"保存HTML失败: {str(e)}")
            return

        # 转换PDF
        if os.path.exists(html_path):
            try:
                # 确保wkhtmltopdf路径正确
                config = pdfkit.configuration(wkhtmltopdf=r'D:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')
                pdfkit.from_file(html_path, pdf_path, configuration=config)
                print(f"PDF文件已保存至: {os.path.abspath(pdf_path)}")
            except Exception as e:
                print(f"PDF转换失败: {str(e)}")

        # 转换Markdown
        try:
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.body_width = 0  # 禁用自动换行
            markdown = h.handle(html_content)

            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown)
                print(f"Markdown文件已保存至: {os.path.abspath(md_path)}")
        except Exception as e:
            print(f"Markdown转换失败: {str(e)}")

    def download_article(self, article_url):
        """下载并保存单个CSDN文章"""
        print(f"开始下载文章: {article_url}")
        start_time = time.time()

        response = self.send_request(article_url)
        if not response:
            print("下载失败")
            return

        title, content = self.parse_article(response.text)
        if not title or not content:
            print("解析文章内容失败")
            return

        # 生成HTML文件
        formatted_html = HTML_TEMPLATE.format(title=title, content=content)
        self.save_content(formatted_html, title)

        elapsed_time = time.time() - start_time
        print(f"文章下载完成: {title} (耗时: {elapsed_time:.2f}秒)")


if __name__ == '__main__':
    spider = CSDNSpider()

    # 在这里指定要下载的CSDN文章链接
    article_url = "https://blog.csdn.net/fengbin2005/article/details/145213069?ops_request_misc=%257B%2522request%255Fid%2522%253A%252207935327e48551fe94a6adbfb1a5f717%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=07935327e48551fe94a6adbfb1a5f717&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~baidu_landing_v2~default-5-145213069-null-null.142^v102^pc_search_result_base8&utm_term=nacos%E5%BC%80%E5%90%AF%E5%AF%86%E7%A0%81&spm=1018.2226.3001.4187"

    print("=" * 60)
    print("CSDN文章下载器")
    print("=" * 60)
    print(f"目标文章: {article_url}")

    spider.download_article(article_url)

    print("=" * 60)
    print("下载完成！文件已保存到以下目录:")
    print(f"HTML文件: {os.path.abspath('HTML')}")
    print(f"PDF文件: {os.path.abspath('PDF')}")
    print(f"Markdown文件: {os.path.abspath('MD')}")
    print("=" * 60)