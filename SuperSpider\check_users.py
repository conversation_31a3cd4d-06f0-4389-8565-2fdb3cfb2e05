#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库中的用户
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.main import app, db
from backend.models.user import User
from backend.models.user_usage import UserUsage

def check_users():
    """检查数据库中的用户"""
    
    print("🔍 检查数据库中的用户...")
    
    try:
        with app.app_context():
            # 获取所有用户
            users = User.query.all()
            
            if not users:
                print("❌ 数据库中没有用户")
                print("💡 创建一个测试用户...")
                
                # 创建测试用户
                test_user = User(
                    username='testuser',
                    phone='13800138000',
                    role='normal_user'
                )
                test_user.set_password('test123456')
                
                db.session.add(test_user)
                db.session.commit()
                
                print(f"✅ 创建测试用户成功: {test_user.username}")
                
                # 重新获取用户列表
                users = User.query.all()
            
            print(f"\n📊 数据库中共有 {len(users)} 个用户:")
            for user in users:
                print(f"   👤 {user.username} ({user.role}) - ID: {user.id}")
                
                # 检查用户权限
                permissions = user.get_permissions()
                print(f"      📥 下载限制: {permissions.get('download_limit', 'N/A')}")
                print(f"      🔄 API限制: {permissions.get('api_rate_limit', 'N/A')}")
                
                # 检查使用统计
                usage = UserUsage.get_or_create_today_usage(user.id)
                print(f"      📊 今日使用: 下载{usage.download_count}次, API{usage.api_call_count}次")
                print()
            
            return users[0] if users else None
            
    except Exception as e:
        print(f"❌ 检查用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_login_with_user(user):
    """测试用指定用户登录"""
    
    print(f"🔑 测试用户 {user.username} 登录...")
    
    import requests
    
    login_data = {
        "username": user.username,
        "password": "test123456"
    }
    
    try:
        response = requests.post("http://127.0.0.1:5000/api/auth/login", json=login_data)
        print(f"登录响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功!")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            try:
                result = response.json()
                print(f"错误信息: {result.get('message', response.text[:200])}")
            except:
                print(f"错误信息: {response.text[:200]}")
                
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 检查用户和测试登录...")
    
    user = check_users()
    
    if user:
        success = test_login_with_user(user)
        
        if success:
            print("✅ 用户系统正常工作")
        else:
            print("❌ 登录系统有问题")
    else:
        print("❌ 无法创建或获取用户")
    
    print("\n🎉 检查完成!")
