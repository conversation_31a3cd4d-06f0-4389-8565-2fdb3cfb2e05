const{SvelteComponent:a,append:c,attr:e,detach:p,init:d,insert:g,noop:r,safe_not_equal:u,svg_element:s}=window.__gradio__svelte__internal;function _(l){let t,o;return{c(){t=s("svg"),o=s("polygon"),e(o,"points","5 3 19 12 5 21 5 3"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","currentColor"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(n,i){g(n,t,i),c(t,o)},p:r,i:r,o:r,d(n){n&&p(t)}}}class w extends a{constructor(t){super(),d(this,t,null,_,u,{})}}export{w as P};
//# sourceMappingURL=Play-B0Q0U1Qz.js.map
