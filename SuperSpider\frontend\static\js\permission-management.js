/**
 * 权限管理模块
 * 处理用户权限升级、降级和显示
 */

class PermissionManagement {
    constructor() {
        this.currentUserData = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUserPermissions();
    }

    bindEvents() {
        // 激活码激活按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.id === 'activate-code-btn') {
                this.handleActivateCode();
            }
        });

        // 降级按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('downgrade-btn')) {
                const targetRole = e.target.dataset.target;
                this.handleDowngrade(targetRole);
            }
        });

        // 管理员生成激活码按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.id === 'generate-codes-btn') {
                this.handleGenerateCodes();
            }
        });

        // 查看激活码按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.id === 'view-codes-btn') {
                this.showActivationCodesList();
            }
        });

        // 复制激活码按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('copy-code-btn')) {
                const code = e.target.dataset.code;
                this.copyToClipboard(code);
            }
        });

        // 激活码输入格式化
        const activationCodeInput = document.getElementById('activation-code');
        if (activationCodeInput) {
            activationCodeInput.addEventListener('input', (e) => {
                this.formatActivationCode(e.target);
            });
        }

        // 权限设置标签页切换事件
        document.addEventListener('click', (e) => {
            if (e.target.dataset.tab === 'permission-settings') {
                setTimeout(() => {
                    this.loadUserPermissions();
                }, 100);
            }
        });
    }

    async loadUserPermissions() {
        try {
            const response = await fetch('/api/permission/check');
            const data = await response.json();

            if (data.success) {
                const oldRole = this.currentUserData ? this.currentUserData.role : null;
                this.currentUserData = data.data;
                this.updatePermissionDisplay();

                // 如果用户从Pro降级为普通用户，显示提示
                if (oldRole === 'pro_user' && this.currentUserData.role === 'normal_user') {
                    this.showRoleChangeNotice('您的Pro会员已过期，已自动降级为普通用户');
                }
            } else {
                console.error('获取权限信息失败:', data.message);
            }
        } catch (error) {
            console.error('获取权限信息请求失败:', error);
        }
    }

    /**
     * 显示角色变化提示
     */
    showRoleChangeNotice(message) {
        const notice = document.createElement('div');
        notice.className = 'role-change-notice';
        notice.innerHTML = `
            <div class="notice-content">
                <i class="fas fa-info-circle"></i>
                <span>${message}</span>
                <button class="notice-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        notice.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ffa726;
            color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notice);

        setTimeout(() => {
            if (notice.parentElement) {
                notice.remove();
            }
        }, 5000);
    }

    updatePermissionDisplay() {
        if (!this.currentUserData) return;

        const { role, role_display, permissions, is_pro_valid, vip_expire_date } = this.currentUserData;

        // 更新当前角色显示
        this.updateRoleDisplay(role, role_display, is_pro_valid, vip_expire_date);

        // 更新升级/降级选项
        this.updateUpgradeOptions(role, is_pro_valid);

        // 更新管理员界面
        this.updateAdminSection(role);

        // 更新权限详情
        this.updatePermissionDetails(permissions);
    }

    updateRoleDisplay(role, roleDisplay, isProValid, vipExpireDate) {
        const roleBadge = document.getElementById('current-role-badge');
        const roleDescription = document.getElementById('current-role-description');
        const roleExpireInfo = document.getElementById('role-expire-info');
        const expireDate = document.getElementById('expire-date');

        if (roleBadge) {
            roleBadge.textContent = roleDisplay;
            roleBadge.className = `role-badge ${role}`;
        }

        if (roleDescription) {
            const descriptions = {
                'normal_user': '可使用视频平台功能',
                'pro_user': '可使用所有平台功能',
                'super_admin': '拥有所有管理权限'
            };
            roleDescription.textContent = descriptions[role] || '未知角色';
        }

        // 显示Pro用户到期时间
        if (role === 'pro_user' && vipExpireDate && roleExpireInfo && expireDate) {
            const expireDateTime = new Date(vipExpireDate);
            expireDate.textContent = expireDateTime.toLocaleDateString('zh-CN');
            roleExpireInfo.style.display = 'block';

            // 检查是否即将过期
            const daysUntilExpire = Math.ceil((expireDateTime - new Date()) / (1000 * 60 * 60 * 24));
            if (daysUntilExpire <= 7) {
                expireDate.style.color = '#e53e3e';
                expireDate.textContent += ` (${daysUntilExpire}天后过期)`;
            }
        } else if (roleExpireInfo) {
            roleExpireInfo.style.display = 'none';
        }
    }

    updateUpgradeOptions(role, isProValid) {
        const proUpgradeCard = document.getElementById('pro-upgrade-card');
        const downgradeCard = document.getElementById('downgrade-card');
        const upgradeOptionsSection = document.querySelector('.upgrade-options');

        if (role === 'normal_user') {
            // 普通用户只显示升级选项，不显示降级
            if (upgradeOptionsSection) upgradeOptionsSection.style.display = 'block';
            if (proUpgradeCard) proUpgradeCard.style.display = 'block';
            if (downgradeCard) downgradeCard.style.display = 'none';
        } else if (role === 'pro_user') {
            // Pro用户显示续费选项，降级功能通过到期自动处理
            if (upgradeOptionsSection) upgradeOptionsSection.style.display = 'block';
            if (proUpgradeCard) {
                proUpgradeCard.style.display = 'block';
                const upgradeHeader = proUpgradeCard.querySelector('.upgrade-header h4');
                if (upgradeHeader) {
                    upgradeHeader.textContent = isProValid ? '续费 Pro 用户' : '重新激活 Pro 用户';
                }
            }
            // Pro用户也不显示手动降级选项，到期后自动降级
            if (downgradeCard) downgradeCard.style.display = 'none';
        } else {
            // 超级管理员隐藏整个升级选项区域
            if (upgradeOptionsSection) upgradeOptionsSection.style.display = 'none';
            if (proUpgradeCard) proUpgradeCard.style.display = 'none';
            if (downgradeCard) downgradeCard.style.display = 'none';
        }
    }

    updatePermissionDetails(permissions) {
        const permissionList = document.getElementById('permission-list');
        if (!permissionList) return;

        const permissionItems = [
            {
                name: '视频平台',
                value: permissions.video_platforms?.length || 0,
                total: 3,
                type: 'platforms'
            },
            {
                name: '文章平台',
                value: permissions.article_platforms?.length || 0,
                total: 2,
                type: 'platforms'
            },
            {
                name: '每日下载限制',
                value: permissions.download_limit === -1 ? '无限制' : permissions.download_limit,
                type: 'limit'
            },
            {
                name: 'API调用限制',
                value: permissions.api_rate_limit === -1 ? '无限制' : `${permissions.api_rate_limit}/分钟`,
                type: 'limit'
            }
        ];

        permissionList.innerHTML = permissionItems.map(item => {
            let statusClass, statusText;

            if (item.type === 'platforms') {
                if (item.value === item.total) {
                    statusClass = 'enabled';
                    statusText = '全部可用';
                } else if (item.value > 0) {
                    statusClass = 'limited';
                    statusText = `${item.value}/${item.total}`;
                } else {
                    statusClass = 'disabled';
                    statusText = '不可用';
                }
            } else {
                statusClass = item.value === '无限制' ? 'enabled' : 'limited';
                statusText = item.value;
            }

            return `
                <div class="permission-item">
                    <span class="permission-name">${item.name}</span>
                    <span class="permission-status ${statusClass}">${statusText}</span>
                </div>
            `;
        }).join('');
    }

    updateAdminSection(role) {
        const adminSection = document.getElementById('admin-section');

        if (role === 'super_admin') {
            if (adminSection) {
                adminSection.style.display = 'block';
                this.loadActivationStats();
            }
        } else {
            if (adminSection) {
                adminSection.style.display = 'none';
            }
        }
    }

    formatActivationCode(input) {
        let value = input.value.replace(/[^A-Z0-9]/g, '');

        // 限制长度为16个字符
        if (value.length > 16) {
            value = value.substring(0, 16);
        }

        // 格式化为 XXXX-XXXX-XXXX-XXXX
        const formatted = value.replace(/(.{4})/g, '$1-').replace(/-$/, '');
        input.value = formatted;
    }

    async handleActivateCode() {
        const codeInput = document.getElementById('activation-code');
        const statusContainer = document.getElementById('permission-settings-status');

        if (!codeInput) return;

        const code = codeInput.value.trim();

        if (!code) {
            this.showStatus(statusContainer, '请输入激活码', 'error');
            return;
        }

        if (code.length !== 19) { // XXXX-XXXX-XXXX-XXXX
            this.showStatus(statusContainer, '激活码格式不正确', 'error');
            return;
        }

        try {
            this.showStatus(statusContainer, '正在激活...', 'info');

            const response = await fetch('/api/activation/use', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: code
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showStatus(statusContainer, data.message, 'success');
                codeInput.value = '';

                // 重新加载权限信息
                setTimeout(() => {
                    this.loadUserPermissions();
                }, 1000);
            } else {
                this.showStatus(statusContainer, data.message || '激活失败', 'error');
            }
        } catch (error) {
            console.error('激活码使用失败:', error);
            this.showStatus(statusContainer, '激活失败，请稍后再试', 'error');
        }
    }

    async handleGenerateCodes() {
        const daysSelect = document.getElementById('code-days');
        const countInput = document.getElementById('code-count');
        const descriptionInput = document.getElementById('code-description');
        const statusContainer = document.getElementById('permission-settings-status');

        if (!daysSelect || !countInput) return;

        const days = parseInt(daysSelect.value);
        const count = parseInt(countInput.value);
        const description = descriptionInput ? descriptionInput.value.trim() : '';

        if (count < 1 || count > 100) {
            this.showStatus(statusContainer, '生成数量必须在1-100之间', 'error');
            return;
        }

        try {
            this.showStatus(statusContainer, '正在生成激活码...', 'info');

            const response = await fetch('/api/activation/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    days: days,
                    count: count,
                    description: description
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showStatus(statusContainer, data.message, 'success');
                this.displayGeneratedCodes(data.data.codes);
                this.loadActivationStats();

                // 清空表单
                if (countInput) countInput.value = '1';
                if (descriptionInput) descriptionInput.value = '';
            } else {
                this.showStatus(statusContainer, data.message || '生成失败', 'error');
            }
        } catch (error) {
            console.error('生成激活码失败:', error);
            this.showStatus(statusContainer, '生成失败，请稍后再试', 'error');
        }
    }

    async handleDowngrade(targetRole) {
        if (!confirm('确认降级到普通用户吗？您将失去Pro用户的所有特权功能。')) {
            return;
        }

        const statusContainer = document.getElementById('permission-settings-status');

        try {
            this.showStatus(statusContainer, '正在处理降级请求...', 'info');

            const response = await fetch('/api/permission/upgrade', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.currentUserData.user_id || 'current',
                    target_role: targetRole
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showStatus(statusContainer, '已降级到普通用户', 'success');
                // 重新加载权限信息
                setTimeout(() => {
                    this.loadUserPermissions();
                }, 1000);
            } else {
                this.showStatus(statusContainer, data.message || '降级失败', 'error');
            }
        } catch (error) {
            console.error('降级请求失败:', error);
            this.showStatus(statusContainer, '降级请求失败，请稍后再试', 'error');
        }
    }

    showStatus(container, message, type) {
        if (!container) return;

        container.style.display = 'block';
        const messageEl = container.querySelector('.status-message');
        if (messageEl) {
            messageEl.textContent = message;
            messageEl.className = 'status-message ' + (type || '');
        }

        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 3000);
        }
    }

    displayGeneratedCodes(codes) {
        const generatedCodesDiv = document.getElementById('generated-codes');
        const codesList = document.getElementById('codes-list');

        if (!generatedCodesDiv || !codesList) return;

        codesList.innerHTML = codes.map(code => `
            <div class="code-item">
                <div>
                    <span class="code-value">${code.code}</span>
                    <div class="code-info">${code.days}天 - ${code.description || '无描述'}</div>
                </div>
                <button class="copy-code-btn" data-code="${code.code}">复制</button>
            </div>
        `).join('');

        generatedCodesDiv.style.display = 'block';
    }

    async loadActivationStats() {
        try {
            const response = await fetch('/api/activation/stats');
            const data = await response.json();

            if (data.success) {
                this.displayActivationStats(data.data);
            }
        } catch (error) {
            console.error('加载激活码统计失败:', error);
        }
    }

    displayActivationStats(stats) {
        const statsGrid = document.getElementById('stats-grid');

        if (!statsGrid) return;

        const statsItems = [
            { label: '总计', value: stats.total, color: '#7c3aed' },
            { label: '已使用', value: stats.used, color: '#38a169' },
            { label: '未使用', value: stats.unused, color: '#f59e0b' },
            { label: '使用率', value: stats.usage_rate + '%', color: '#e53e3e' }
        ];

        statsGrid.innerHTML = statsItems.map(item => `
            <div class="stat-item">
                <span class="stat-value" style="color: ${item.color}">${item.value}</span>
                <div class="stat-label">${item.label}</div>
            </div>
        `).join('');

        // 显示按天数统计
        if (stats.by_days && Object.keys(stats.by_days).length > 0) {
            const dayStatsHtml = Object.entries(stats.by_days).map(([days, dayStats]) => `
                <div class="stat-item">
                    <span class="stat-value">${dayStats.total}</span>
                    <div class="stat-label">${days}天激活码</div>
                    <div class="code-info">已用: ${dayStats.used}</div>
                </div>
            `).join('');

            statsGrid.innerHTML += dayStatsHtml;
        }
    }

    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功提示
                const statusContainer = document.getElementById('permission-settings-status');
                this.showStatus(statusContainer, '激活码已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                const statusContainer = document.getElementById('permission-settings-status');
                this.showStatus(statusContainer, '激活码已复制到剪贴板', 'success');
            } catch (err) {
                console.error('复制失败:', err);
            }
            document.body.removeChild(textArea);
        }
    }

    async showActivationCodesList() {
        try {
            const response = await fetch('/api/activation/list?per_page=50');
            const data = await response.json();

            if (data.success) {
                this.displayActivationCodesList(data.data.codes);
            } else {
                const statusContainer = document.getElementById('permission-settings-status');
                this.showStatus(statusContainer, data.message || '获取激活码列表失败', 'error');
            }
        } catch (error) {
            console.error('获取激活码列表失败:', error);
            const statusContainer = document.getElementById('permission-settings-status');
            this.showStatus(statusContainer, '获取激活码列表失败，请稍后再试', 'error');
        }
    }

    displayActivationCodesList(codes) {
        const generatedCodesDiv = document.getElementById('generated-codes');
        const codesList = document.getElementById('codes-list');

        if (!generatedCodesDiv || !codesList) return;

        if (codes.length === 0) {
            codesList.innerHTML = '<div class="no-codes">暂无激活码</div>';
        } else {
            codesList.innerHTML = codes.map(code => {
                const statusClass = code.is_used ? 'used' : 'unused';
                const statusText = code.is_used ? '已使用' : '未使用';
                const createdDate = new Date(code.created_at).toLocaleDateString('zh-CN');

                return `
                    <div class="code-item ${statusClass}">
                        <div class="code-main">
                            <span class="code-value">${code.code}</span>
                            <div class="code-info">
                                ${code.days}天 - ${code.description || '无描述'}
                                <span class="code-date">(${createdDate})</span>
                            </div>
                        </div>
                        <div class="code-actions">
                            <span class="code-status ${statusClass}">${statusText}</span>
                            ${!code.is_used ? `<button class="copy-code-btn" data-code="${code.code}">复制</button>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新标题
        const codesTitle = generatedCodesDiv.querySelector('h4');
        if (codesTitle) {
            codesTitle.textContent = `激活码列表 (共${codes.length}个)`;
        }

        generatedCodesDiv.style.display = 'block';
    }
}

// 页面加载完成后初始化权限管理
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.permissionManagement = new PermissionManagement();
    }, 500);
});
