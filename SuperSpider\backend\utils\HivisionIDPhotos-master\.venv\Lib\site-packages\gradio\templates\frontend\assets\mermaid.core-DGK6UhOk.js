const __vite__fileDeps=["./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js","./prism-python-D8O99YiR.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./MarkdownCode-Dw1-H2nG.css","./dagre-QXRM2OYR-B_pfLEzv.js","./graph-w0q23cSo.js","./_baseUniq-CIsjNB2G.js","./layout-D4ctKTfm.js","./_basePickBy-CxKuy9eP.js","./clone-Bxj9bjXK.js","./dispatch-kxCwF96_.js","./step-Ce-xBr2D.js","./select-BigU4G0v.js","./c4Diagram-7JAJQR3Y-Dhf491tz.js","./chunk-PLTTB2RT-4f9nCmH_.js","./flowDiagram-27HWSH3H-Dn6SZx5b.js","./chunk-2O5F6CEG-BJ0hfvv0.js","./channel-BVcFyT3i.js","./erDiagram-MVNNDQJ5-DhoSfK7x.js","./gitGraphDiagram-ISGV4O2Y-kryu9vhB.js","./chunk-IUKPXING-Kf55odCW.js","./chunk-66XRIAFR-Cs7RKspm.js","./mermaid-parser.core-4AQHUFmR.js","./ganttDiagram-ZCE2YOAT-9KrQO0Hw.js","./time-Bgyi_H-V.js","./linear-CV3SENcB.js","./init-Dmth1JHB.js","./infoDiagram-SDLB2J7W-DHVrZ69o.js","./pieDiagram-OC6WZ2SS-Co4CCibQ.js","./arc-Ctxh2KTd.js","./ordinal-BeghXfj9.js","./quadrantDiagram-OT6RYTWY-GH8KmUhM.js","./xychartDiagram-NJOKMNIP-DChUQIgb.js","./range-OtVwhkKS.js","./requirementDiagram-BKGUWIPO-D3YJMkOf.js","./sequenceDiagram-C4VUPXDP-DbrsMme5.js","./classDiagram-L266QK7U-C6d8YZyD.js","./chunk-5V4FS25O-DlbCtaIl.js","./classDiagram-v2-JRWBCVM4-C6d8YZyD.js","./stateDiagram-BVO7J4UH-BS86ek3y.js","./chunk-4IRHCMPZ-DXOgct9V.js","./stateDiagram-v2-WR7QG3WR-BSCKtWyM.js","./journeyDiagram-D7A75E63-CfbQA05t.js","./timeline-definition-WOTUTIAU-DW9lAhIx.js","./mindmap-definition-7EJRZJGK-pbSPzA9j.js","./cytoscape.esm-BPoQaFli.js","./kanban-definition-4GR4SRK3-j3vcolHs.js","./sankeyDiagram-3MH5UGAL-B4rSfPq8.js","./diagram-DHSB7DV3-DzQPZow3.js","./blockDiagram-5JUZGEFE-XsfnSuGn.js","./architectureDiagram-PQUH6ZAG-BOwpk7xQ.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{k as yp,f as xp,_ as ft}from"./index-Ccc2t4AG.js";import{d as bp}from"./dispatch-kxCwF96_.js";import{T as Cp,N as kp,y as wp,z as bo,A as Co,B as vp,L as Sp,K as Tp,M as _p,k as Pi,S as Bp,U as Lp,V as Ap,Y as Mp,X as Ep,W as Ol,_ as Fp,$ as $p,Z as Op,O as bn,a0 as Dp,a2 as Rp,a1 as Ip,a3 as Pp,a4 as Np,a5 as zp,a6 as Wp,l as qp}from"./step-Ce-xBr2D.js";import{n as Dl,m as Hp,a as jp,b as Up,c as va,d as si,s as lt}from"./select-BigU4G0v.js";var Rl={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(yp,function(){var r=1e3,i=6e4,a=36e5,n="millisecond",o="second",s="minute",l="hour",c="day",h="week",u="month",p="quarter",d="year",g="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(E){var M=["th","st","nd","rd"],_=E%100;return"["+E+(M[(_-20)%10]||M[_]||M[0])+"]"}},k=function(E,M,_){var F=String(E);return!F||F.length>=M?E:""+Array(M+1-F.length).join(_)+E},v={s:k,z:function(E){var M=-E.utcOffset(),_=Math.abs(M),F=Math.floor(_/60),A=_%60;return(M<=0?"+":"-")+k(F,2,"0")+":"+k(A,2,"0")},m:function E(M,_){if(M.date()<_.date())return-E(_,M);var F=12*(_.year()-M.year())+(_.month()-M.month()),A=M.clone().add(F,u),N=_-A<0,q=M.clone().add(F+(N?-1:1),u);return+(-(F+(_-A)/(N?A-q:q-A))||0)},a:function(E){return E<0?Math.ceil(E)||0:Math.floor(E)},p:function(E){return{M:u,y:d,w:h,d:c,D:g,h:l,m:s,s:o,ms:n,Q:p}[E]||String(E||"").toLowerCase().replace(/s$/,"")},u:function(E){return E===void 0}},S="en",L={};L[S]=b;var T="$isDayjsObject",D=function(E){return E instanceof z||!(!E||!E[T])},R=function E(M,_,F){var A;if(!M)return S;if(typeof M=="string"){var N=M.toLowerCase();L[N]&&(A=N),_&&(L[N]=_,A=N);var q=M.split("-");if(!A&&q.length>1)return E(q[0])}else{var Z=M.name;L[Z]=M,A=Z}return!F&&A&&(S=A),A||!F&&S},O=function(E,M){if(D(E))return E.clone();var _=typeof M=="object"?M:{};return _.date=E,_.args=arguments,new z(_)},B=v;B.l=R,B.i=D,B.w=function(E,M){return O(E,{locale:M.$L,utc:M.$u,x:M.$x,$offset:M.$offset})};var z=function(){function E(_){this.$L=R(_.locale,null,!0),this.parse(_),this.$x=this.$x||_.x||{},this[T]=!0}var M=E.prototype;return M.parse=function(_){this.$d=function(F){var A=F.date,N=F.utc;if(A===null)return new Date(NaN);if(B.u(A))return new Date;if(A instanceof Date)return new Date(A);if(typeof A=="string"&&!/Z$/i.test(A)){var q=A.match(y);if(q){var Z=q[2]-1||0,nt=(q[7]||"0").substring(0,3);return N?new Date(Date.UTC(q[1],Z,q[3]||1,q[4]||0,q[5]||0,q[6]||0,nt)):new Date(q[1],Z,q[3]||1,q[4]||0,q[5]||0,q[6]||0,nt)}}return new Date(A)}(_),this.init()},M.init=function(){var _=this.$d;this.$y=_.getFullYear(),this.$M=_.getMonth(),this.$D=_.getDate(),this.$W=_.getDay(),this.$H=_.getHours(),this.$m=_.getMinutes(),this.$s=_.getSeconds(),this.$ms=_.getMilliseconds()},M.$utils=function(){return B},M.isValid=function(){return this.$d.toString()!==m},M.isSame=function(_,F){var A=O(_);return this.startOf(F)<=A&&A<=this.endOf(F)},M.isAfter=function(_,F){return O(_)<this.startOf(F)},M.isBefore=function(_,F){return this.endOf(F)<O(_)},M.$g=function(_,F,A){return B.u(_)?this[F]:this.set(A,_)},M.unix=function(){return Math.floor(this.valueOf()/1e3)},M.valueOf=function(){return this.$d.getTime()},M.startOf=function(_,F){var A=this,N=!!B.u(F)||F,q=B.p(_),Z=function(ie,St){var ae=B.w(A.$u?Date.UTC(A.$y,St,ie):new Date(A.$y,St,ie),A);return N?ae:ae.endOf(c)},nt=function(ie,St){return B.w(A.toDate()[ie].apply(A.toDate("s"),(N?[0,0,0,0]:[23,59,59,999]).slice(St)),A)},at=this.$W,pt=this.$M,st=this.$D,Vt="set"+(this.$u?"UTC":"");switch(q){case d:return N?Z(1,0):Z(31,11);case u:return N?Z(1,pt):Z(0,pt+1);case h:var Ot=this.$locale().weekStart||0,$e=(at<Ot?at+7:at)-Ot;return Z(N?st-$e:st+(6-$e),pt);case c:case g:return nt(Vt+"Hours",0);case l:return nt(Vt+"Minutes",1);case s:return nt(Vt+"Seconds",2);case o:return nt(Vt+"Milliseconds",3);default:return this.clone()}},M.endOf=function(_){return this.startOf(_,!1)},M.$set=function(_,F){var A,N=B.p(_),q="set"+(this.$u?"UTC":""),Z=(A={},A[c]=q+"Date",A[g]=q+"Date",A[u]=q+"Month",A[d]=q+"FullYear",A[l]=q+"Hours",A[s]=q+"Minutes",A[o]=q+"Seconds",A[n]=q+"Milliseconds",A)[N],nt=N===c?this.$D+(F-this.$W):F;if(N===u||N===d){var at=this.clone().set(g,1);at.$d[Z](nt),at.init(),this.$d=at.set(g,Math.min(this.$D,at.daysInMonth())).$d}else Z&&this.$d[Z](nt);return this.init(),this},M.set=function(_,F){return this.clone().$set(_,F)},M.get=function(_){return this[B.p(_)]()},M.add=function(_,F){var A,N=this;_=Number(_);var q=B.p(F),Z=function(pt){var st=O(N);return B.w(st.date(st.date()+Math.round(pt*_)),N)};if(q===u)return this.set(u,this.$M+_);if(q===d)return this.set(d,this.$y+_);if(q===c)return Z(1);if(q===h)return Z(7);var nt=(A={},A[s]=i,A[l]=a,A[o]=r,A)[q]||1,at=this.$d.getTime()+_*nt;return B.w(at,this)},M.subtract=function(_,F){return this.add(-1*_,F)},M.format=function(_){var F=this,A=this.$locale();if(!this.isValid())return A.invalidDate||m;var N=_||"YYYY-MM-DDTHH:mm:ssZ",q=B.z(this),Z=this.$H,nt=this.$m,at=this.$M,pt=A.weekdays,st=A.months,Vt=A.meridiem,Ot=function(St,ae,Zt,Oe){return St&&(St[ae]||St(F,N))||Zt[ae].slice(0,Oe)},$e=function(St){return B.s(Z%12||12,St,"0")},ie=Vt||function(St,ae,Zt){var Oe=St<12?"AM":"PM";return Zt?Oe.toLowerCase():Oe};return N.replace(x,function(St,ae){return ae||function(Zt){switch(Zt){case"YY":return String(F.$y).slice(-2);case"YYYY":return B.s(F.$y,4,"0");case"M":return at+1;case"MM":return B.s(at+1,2,"0");case"MMM":return Ot(A.monthsShort,at,st,3);case"MMMM":return Ot(st,at);case"D":return F.$D;case"DD":return B.s(F.$D,2,"0");case"d":return String(F.$W);case"dd":return Ot(A.weekdaysMin,F.$W,pt,2);case"ddd":return Ot(A.weekdaysShort,F.$W,pt,3);case"dddd":return pt[F.$W];case"H":return String(Z);case"HH":return B.s(Z,2,"0");case"h":return $e(1);case"hh":return $e(2);case"a":return ie(Z,nt,!0);case"A":return ie(Z,nt,!1);case"m":return String(nt);case"mm":return B.s(nt,2,"0");case"s":return String(F.$s);case"ss":return B.s(F.$s,2,"0");case"SSS":return B.s(F.$ms,3,"0");case"Z":return q}return null}(St)||q.replace(":","")})},M.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},M.diff=function(_,F,A){var N,q=this,Z=B.p(F),nt=O(_),at=(nt.utcOffset()-this.utcOffset())*i,pt=this-nt,st=function(){return B.m(q,nt)};switch(Z){case d:N=st()/12;break;case u:N=st();break;case p:N=st()/3;break;case h:N=(pt-at)/6048e5;break;case c:N=(pt-at)/864e5;break;case l:N=pt/a;break;case s:N=pt/i;break;case o:N=pt/r;break;default:N=pt}return A?N:B.a(N)},M.daysInMonth=function(){return this.endOf(u).$D},M.$locale=function(){return L[this.$L]},M.locale=function(_,F){if(!_)return this.$L;var A=this.clone(),N=R(_,F,!0);return N&&(A.$L=N),A},M.clone=function(){return B.w(this.$d,this)},M.toDate=function(){return new Date(this.valueOf())},M.toJSON=function(){return this.isValid()?this.toISOString():null},M.toISOString=function(){return this.$d.toISOString()},M.toString=function(){return this.$d.toUTCString()},E}(),P=z.prototype;return O.prototype=P,[["$ms",n],["$s",o],["$m",s],["$H",l],["$W",c],["$M",u],["$y",d],["$D",g]].forEach(function(E){P[E[1]]=function(M){return this.$g(M,E[0],E[1])}}),O.extend=function(E,M){return E.$i||(E(M,z,O),E.$i=!0),O},O.locale=R,O.isDayjs=D,O.unix=function(E){return O(1e3*E)},O.en=L[S],O.Ls=L,O.p={},O})})(Rl);var Yp=Rl.exports;const Gp=xp(Yp),Ni={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const a=r<.5?r*(1+t):r+t-r*t,n=2*r-a;switch(i){case"r":return Ni.hue2rgb(n,a,e+1/3)*255;case"g":return Ni.hue2rgb(n,a,e)*255;case"b":return Ni.hue2rgb(n,a,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const a=Math.max(e,t,r),n=Math.min(e,t,r),o=(a+n)/2;if(i==="l")return o*100;if(a===n)return 0;const s=a-n,l=o>.5?s/(2-a-n):s/(a+n);if(i==="s")return l*100;switch(a){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},Vp={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},Xp={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},rt={channel:Ni,lang:Vp,unit:Xp},Re={};for(let e=0;e<=255;e++)Re[e]=rt.unit.dec2hex(e);const Mt={ALL:0,RGB:1,HSL:2};class Zp{constructor(){this.type=Mt.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=Mt.ALL}is(t){return this.type===t}}class Kp{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new Zp}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=Mt.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:a}=t;r===void 0&&(t.h=rt.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=rt.channel.rgb2hsl(t,"s")),a===void 0&&(t.l=rt.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:a}=t;r===void 0&&(t.r=rt.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=rt.channel.hsl2rgb(t,"g")),a===void 0&&(t.b=rt.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is(Mt.HSL)&&r!==void 0?r:(this._ensureHSL(),rt.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is(Mt.HSL)&&r!==void 0?r:(this._ensureHSL(),rt.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is(Mt.HSL)&&r!==void 0?r:(this._ensureHSL(),rt.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is(Mt.RGB)&&r!==void 0?r:(this._ensureRGB(),rt.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is(Mt.RGB)&&r!==void 0?r:(this._ensureRGB(),rt.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is(Mt.RGB)&&r!==void 0?r:(this._ensureRGB(),rt.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set(Mt.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(Mt.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(Mt.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(Mt.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(Mt.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(Mt.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const Sa=new Kp({r:0,g:0,b:0,a:0},"transparent"),xr={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(xr.re);if(!t)return;const r=t[1],i=parseInt(r,16),a=r.length,n=a%4===0,o=a>4,s=o?1:17,l=o?8:4,c=n?0:-1,h=o?255:15;return Sa.set({r:(i>>l*(c+3)&h)*s,g:(i>>l*(c+2)&h)*s,b:(i>>l*(c+1)&h)*s,a:n?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`#${Re[Math.round(t)]}${Re[Math.round(r)]}${Re[Math.round(i)]}${Re[Math.round(a*255)]}`:`#${Re[Math.round(t)]}${Re[Math.round(r)]}${Re[Math.round(i)]}`}},Xe={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Xe.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return rt.channel.clamp.h(parseFloat(r)*.9);case"rad":return rt.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return rt.channel.clamp.h(parseFloat(r)*360)}}return rt.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Xe.re);if(!r)return;const[,i,a,n,o,s]=r;return Sa.set({h:Xe._hue2deg(i),s:rt.channel.clamp.s(parseFloat(a)),l:rt.channel.clamp.l(parseFloat(n)),a:o?rt.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a}=e;return a<1?`hsla(${rt.lang.round(t)}, ${rt.lang.round(r)}%, ${rt.lang.round(i)}%, ${a})`:`hsl(${rt.lang.round(t)}, ${rt.lang.round(r)}%, ${rt.lang.round(i)}%)`}},ti={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=ti.colors[e];if(t)return xr.parse(t)},stringify:e=>{const t=xr.stringify(e);for(const r in ti.colors)if(ti.colors[r]===t)return r}},Xr={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(Xr.re);if(!r)return;const[,i,a,n,o,s,l,c,h]=r;return Sa.set({r:rt.channel.clamp.r(a?parseFloat(i)*2.55:parseFloat(i)),g:rt.channel.clamp.g(o?parseFloat(n)*2.55:parseFloat(n)),b:rt.channel.clamp.b(l?parseFloat(s)*2.55:parseFloat(s)),a:c?rt.channel.clamp.a(h?parseFloat(c)/100:parseFloat(c)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`rgba(${rt.lang.round(t)}, ${rt.lang.round(r)}, ${rt.lang.round(i)}, ${rt.lang.round(a)})`:`rgb(${rt.lang.round(t)}, ${rt.lang.round(r)}, ${rt.lang.round(i)})`}},ge={format:{keyword:ti,hex:xr,rgb:Xr,rgba:Xr,hsl:Xe,hsla:Xe},parse:e=>{if(typeof e!="string")return e;const t=xr.parse(e)||Xr.parse(e)||Xe.parse(e)||ti.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is(Mt.HSL)||e.data.r===void 0?Xe.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?Xr.stringify(e):xr.stringify(e)},Il=(e,t)=>{const r=ge.parse(e);for(const i in t)r[i]=rt.channel.clamp[i](t[i]);return ge.stringify(r)},ei=(e,t,r=0,i=1)=>{if(typeof e!="number")return Il(e,{a:t});const a=Sa.set({r:rt.channel.clamp.r(e),g:rt.channel.clamp.g(t),b:rt.channel.clamp.b(r),a:rt.channel.clamp.a(i)});return ge.stringify(a)},Qp=e=>{const{r:t,g:r,b:i}=ge.parse(e),a=.2126*rt.channel.toLinear(t)+.7152*rt.channel.toLinear(r)+.0722*rt.channel.toLinear(i);return rt.lang.round(a)},Jp=e=>Qp(e)>=.5,gi=e=>!Jp(e),Pl=(e,t,r)=>{const i=ge.parse(e),a=i[t],n=rt.channel.clamp[t](a+r);return a!==n&&(i[t]=n),ge.stringify(i)},H=(e,t)=>Pl(e,"l",t),J=(e,t)=>Pl(e,"l",-t),w=(e,t)=>{const r=ge.parse(e),i={};for(const a in t)t[a]&&(i[a]=r[a]+t[a]);return Il(e,i)},tg=(e,t,r=50)=>{const{r:i,g:a,b:n,a:o}=ge.parse(e),{r:s,g:l,b:c,a:h}=ge.parse(t),u=r/100,p=u*2-1,d=o-h,m=((p*d===-1?p:(p+d)/(1+p*d))+1)/2,y=1-m,x=i*m+s*y,b=a*m+l*y,k=n*m+c*y,v=o*u+h*(1-u);return ei(x,b,k,v)},W=(e,t=100)=>{const r=ge.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,tg(r,e,t)};/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */const{entries:Nl,setPrototypeOf:ko,isFrozen:eg,getPrototypeOf:rg,getOwnPropertyDescriptor:ig}=Object;let{freeze:Wt,seal:ee,create:zl}=Object,{apply:Cn,construct:kn}=typeof Reflect<"u"&&Reflect;Wt||(Wt=function(t){return t});ee||(ee=function(t){return t});Cn||(Cn=function(t,r,i){return t.apply(r,i)});kn||(kn=function(t,r){return new t(...r)});const Mi=qt(Array.prototype.forEach),ag=qt(Array.prototype.lastIndexOf),wo=qt(Array.prototype.pop),Pr=qt(Array.prototype.push),ng=qt(Array.prototype.splice),zi=qt(String.prototype.toLowerCase),en=qt(String.prototype.toString),vo=qt(String.prototype.match),Nr=qt(String.prototype.replace),sg=qt(String.prototype.indexOf),og=qt(String.prototype.trim),ne=qt(Object.prototype.hasOwnProperty),Rt=qt(RegExp.prototype.test),zr=lg(TypeError);function qt(e){return function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return Cn(e,t,i)}}function lg(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return kn(e,r)}}function ot(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:zi;ko&&ko(e,null);let i=t.length;for(;i--;){let a=t[i];if(typeof a=="string"){const n=r(a);n!==a&&(eg(t)||(t[i]=n),a=n)}e[a]=!0}return e}function cg(e){for(let t=0;t<e.length;t++)ne(e,t)||(e[t]=null);return e}function Ye(e){const t=zl(null);for(const[r,i]of Nl(e))ne(e,r)&&(Array.isArray(i)?t[r]=cg(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=Ye(i):t[r]=i);return t}function Wr(e,t){for(;e!==null;){const i=ig(e,t);if(i){if(i.get)return qt(i.get);if(typeof i.value=="function")return qt(i.value)}e=rg(e)}function r(){return null}return r}const So=Wt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),rn=Wt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),an=Wt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),hg=Wt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),nn=Wt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),ug=Wt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),To=Wt(["#text"]),_o=Wt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),sn=Wt(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Bo=Wt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ei=Wt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),dg=ee(/\{\{[\w\W]*|[\w\W]*\}\}/gm),fg=ee(/<%[\w\W]*|[\w\W]*%>/gm),pg=ee(/\$\{[\w\W]*/gm),gg=ee(/^data-[\-\w.\u00B7-\uFFFF]+$/),mg=ee(/^aria-[\-\w]+$/),Wl=ee(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),yg=ee(/^(?:\w+script|data):/i),xg=ee(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ql=ee(/^html$/i),bg=ee(/^[a-z][.\w]*(-[.\w]+)+$/i);var Lo=Object.freeze({__proto__:null,ARIA_ATTR:mg,ATTR_WHITESPACE:xg,CUSTOM_ELEMENT:bg,DATA_ATTR:gg,DOCTYPE_NAME:ql,ERB_EXPR:fg,IS_ALLOWED_URI:Wl,IS_SCRIPT_OR_DATA:yg,MUSTACHE_EXPR:dg,TMPLIT_EXPR:pg});const qr={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Cg=function(){return typeof window>"u"?null:window},kg=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const a="data-tt-policy-suffix";r&&r.hasAttribute(a)&&(i=r.getAttribute(a));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+n+" could not be created."),null}},Ao=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Hl(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Cg();const t=Q=>Hl(Q);if(t.version="3.2.4",t.removed=[],!e||!e.document||e.document.nodeType!==qr.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,a=i.currentScript,{DocumentFragment:n,HTMLTemplateElement:o,Node:s,Element:l,NodeFilter:c,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:p,trustedTypes:d}=e,g=l.prototype,m=Wr(g,"cloneNode"),y=Wr(g,"remove"),x=Wr(g,"nextSibling"),b=Wr(g,"childNodes"),k=Wr(g,"parentNode");if(typeof o=="function"){const Q=r.createElement("template");Q.content&&Q.content.ownerDocument&&(r=Q.content.ownerDocument)}let v,S="";const{implementation:L,createNodeIterator:T,createDocumentFragment:D,getElementsByTagName:R}=r,{importNode:O}=i;let B=Ao();t.isSupported=typeof Nl=="function"&&typeof k=="function"&&L&&L.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:z,ERB_EXPR:P,TMPLIT_EXPR:E,DATA_ATTR:M,ARIA_ATTR:_,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:A,CUSTOM_ELEMENT:N}=Lo;let{IS_ALLOWED_URI:q}=Lo,Z=null;const nt=ot({},[...So,...rn,...an,...nn,...To]);let at=null;const pt=ot({},[..._o,...sn,...Bo,...Ei]);let st=Object.seal(zl(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Vt=null,Ot=null,$e=!0,ie=!0,St=!1,ae=!0,Zt=!1,Oe=!0,je=!1,Ga=!1,Va=!1,cr=!1,vi=!1,Si=!1,to=!0,eo=!1;const cp="user-content-";let Xa=!0,Dr=!1,hr={},ur=null;const ro=ot({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let io=null;const ao=ot({},["audio","video","img","source","image","track"]);let Za=null;const no=ot({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ti="http://www.w3.org/1998/Math/MathML",_i="http://www.w3.org/2000/svg",be="http://www.w3.org/1999/xhtml";let dr=be,Ka=!1,Qa=null;const hp=ot({},[Ti,_i,be],en);let Bi=ot({},["mi","mo","mn","ms","mtext"]),Li=ot({},["annotation-xml"]);const up=ot({},["title","style","font","a","script"]);let Rr=null;const dp=["application/xhtml+xml","text/html"],fp="text/html";let wt=null,fr=null;const pp=r.createElement("form"),so=function(C){return C instanceof RegExp||C instanceof Function},Ja=function(){let C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(fr&&fr===C)){if((!C||typeof C!="object")&&(C={}),C=Ye(C),Rr=dp.indexOf(C.PARSER_MEDIA_TYPE)===-1?fp:C.PARSER_MEDIA_TYPE,wt=Rr==="application/xhtml+xml"?en:zi,Z=ne(C,"ALLOWED_TAGS")?ot({},C.ALLOWED_TAGS,wt):nt,at=ne(C,"ALLOWED_ATTR")?ot({},C.ALLOWED_ATTR,wt):pt,Qa=ne(C,"ALLOWED_NAMESPACES")?ot({},C.ALLOWED_NAMESPACES,en):hp,Za=ne(C,"ADD_URI_SAFE_ATTR")?ot(Ye(no),C.ADD_URI_SAFE_ATTR,wt):no,io=ne(C,"ADD_DATA_URI_TAGS")?ot(Ye(ao),C.ADD_DATA_URI_TAGS,wt):ao,ur=ne(C,"FORBID_CONTENTS")?ot({},C.FORBID_CONTENTS,wt):ro,Vt=ne(C,"FORBID_TAGS")?ot({},C.FORBID_TAGS,wt):{},Ot=ne(C,"FORBID_ATTR")?ot({},C.FORBID_ATTR,wt):{},hr=ne(C,"USE_PROFILES")?C.USE_PROFILES:!1,$e=C.ALLOW_ARIA_ATTR!==!1,ie=C.ALLOW_DATA_ATTR!==!1,St=C.ALLOW_UNKNOWN_PROTOCOLS||!1,ae=C.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Zt=C.SAFE_FOR_TEMPLATES||!1,Oe=C.SAFE_FOR_XML!==!1,je=C.WHOLE_DOCUMENT||!1,cr=C.RETURN_DOM||!1,vi=C.RETURN_DOM_FRAGMENT||!1,Si=C.RETURN_TRUSTED_TYPE||!1,Va=C.FORCE_BODY||!1,to=C.SANITIZE_DOM!==!1,eo=C.SANITIZE_NAMED_PROPS||!1,Xa=C.KEEP_CONTENT!==!1,Dr=C.IN_PLACE||!1,q=C.ALLOWED_URI_REGEXP||Wl,dr=C.NAMESPACE||be,Bi=C.MATHML_TEXT_INTEGRATION_POINTS||Bi,Li=C.HTML_INTEGRATION_POINTS||Li,st=C.CUSTOM_ELEMENT_HANDLING||{},C.CUSTOM_ELEMENT_HANDLING&&so(C.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(st.tagNameCheck=C.CUSTOM_ELEMENT_HANDLING.tagNameCheck),C.CUSTOM_ELEMENT_HANDLING&&so(C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(st.attributeNameCheck=C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),C.CUSTOM_ELEMENT_HANDLING&&typeof C.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(st.allowCustomizedBuiltInElements=C.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Zt&&(ie=!1),vi&&(cr=!0),hr&&(Z=ot({},To),at=[],hr.html===!0&&(ot(Z,So),ot(at,_o)),hr.svg===!0&&(ot(Z,rn),ot(at,sn),ot(at,Ei)),hr.svgFilters===!0&&(ot(Z,an),ot(at,sn),ot(at,Ei)),hr.mathMl===!0&&(ot(Z,nn),ot(at,Bo),ot(at,Ei))),C.ADD_TAGS&&(Z===nt&&(Z=Ye(Z)),ot(Z,C.ADD_TAGS,wt)),C.ADD_ATTR&&(at===pt&&(at=Ye(at)),ot(at,C.ADD_ATTR,wt)),C.ADD_URI_SAFE_ATTR&&ot(Za,C.ADD_URI_SAFE_ATTR,wt),C.FORBID_CONTENTS&&(ur===ro&&(ur=Ye(ur)),ot(ur,C.FORBID_CONTENTS,wt)),Xa&&(Z["#text"]=!0),je&&ot(Z,["html","head","body"]),Z.table&&(ot(Z,["tbody"]),delete Vt.tbody),C.TRUSTED_TYPES_POLICY){if(typeof C.TRUSTED_TYPES_POLICY.createHTML!="function")throw zr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof C.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw zr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');v=C.TRUSTED_TYPES_POLICY,S=v.createHTML("")}else v===void 0&&(v=kg(d,a)),v!==null&&typeof S=="string"&&(S=v.createHTML(""));Wt&&Wt(C),fr=C}},oo=ot({},[...rn,...an,...hg]),lo=ot({},[...nn,...ug]),gp=function(C){let I=k(C);(!I||!I.tagName)&&(I={namespaceURI:dr,tagName:"template"});const Y=zi(C.tagName),mt=zi(I.tagName);return Qa[C.namespaceURI]?C.namespaceURI===_i?I.namespaceURI===be?Y==="svg":I.namespaceURI===Ti?Y==="svg"&&(mt==="annotation-xml"||Bi[mt]):!!oo[Y]:C.namespaceURI===Ti?I.namespaceURI===be?Y==="math":I.namespaceURI===_i?Y==="math"&&Li[mt]:!!lo[Y]:C.namespaceURI===be?I.namespaceURI===_i&&!Li[mt]||I.namespaceURI===Ti&&!Bi[mt]?!1:!lo[Y]&&(up[Y]||!oo[Y]):!!(Rr==="application/xhtml+xml"&&Qa[C.namespaceURI]):!1},ce=function(C){Pr(t.removed,{element:C});try{k(C).removeChild(C)}catch{y(C)}},Ai=function(C,I){try{Pr(t.removed,{attribute:I.getAttributeNode(C),from:I})}catch{Pr(t.removed,{attribute:null,from:I})}if(I.removeAttribute(C),C==="is")if(cr||vi)try{ce(I)}catch{}else try{I.setAttribute(C,"")}catch{}},co=function(C){let I=null,Y=null;if(Va)C="<remove></remove>"+C;else{const Tt=vo(C,/^[\r\n\t ]+/);Y=Tt&&Tt[0]}Rr==="application/xhtml+xml"&&dr===be&&(C='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+C+"</body></html>");const mt=v?v.createHTML(C):C;if(dr===be)try{I=new p().parseFromString(mt,Rr)}catch{}if(!I||!I.documentElement){I=L.createDocument(dr,"template",null);try{I.documentElement.innerHTML=Ka?S:mt}catch{}}const At=I.body||I.documentElement;return C&&Y&&At.insertBefore(r.createTextNode(Y),At.childNodes[0]||null),dr===be?R.call(I,je?"html":"body")[0]:je?I.documentElement:At},ho=function(C){return T.call(C.ownerDocument||C,C,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},tn=function(C){return C instanceof u&&(typeof C.nodeName!="string"||typeof C.textContent!="string"||typeof C.removeChild!="function"||!(C.attributes instanceof h)||typeof C.removeAttribute!="function"||typeof C.setAttribute!="function"||typeof C.namespaceURI!="string"||typeof C.insertBefore!="function"||typeof C.hasChildNodes!="function")},uo=function(C){return typeof s=="function"&&C instanceof s};function Ce(Q,C,I){Mi(Q,Y=>{Y.call(t,C,I,fr)})}const fo=function(C){let I=null;if(Ce(B.beforeSanitizeElements,C,null),tn(C))return ce(C),!0;const Y=wt(C.nodeName);if(Ce(B.uponSanitizeElement,C,{tagName:Y,allowedTags:Z}),C.hasChildNodes()&&!uo(C.firstElementChild)&&Rt(/<[/\w]/g,C.innerHTML)&&Rt(/<[/\w]/g,C.textContent)||C.nodeType===qr.progressingInstruction||Oe&&C.nodeType===qr.comment&&Rt(/<[/\w]/g,C.data))return ce(C),!0;if(!Z[Y]||Vt[Y]){if(!Vt[Y]&&go(Y)&&(st.tagNameCheck instanceof RegExp&&Rt(st.tagNameCheck,Y)||st.tagNameCheck instanceof Function&&st.tagNameCheck(Y)))return!1;if(Xa&&!ur[Y]){const mt=k(C)||C.parentNode,At=b(C)||C.childNodes;if(At&&mt){const Tt=At.length;for(let Ht=Tt-1;Ht>=0;--Ht){const he=m(At[Ht],!0);he.__removalCount=(C.__removalCount||0)+1,mt.insertBefore(he,x(C))}}}return ce(C),!0}return C instanceof l&&!gp(C)||(Y==="noscript"||Y==="noembed"||Y==="noframes")&&Rt(/<\/no(script|embed|frames)/i,C.innerHTML)?(ce(C),!0):(Zt&&C.nodeType===qr.text&&(I=C.textContent,Mi([z,P,E],mt=>{I=Nr(I,mt," ")}),C.textContent!==I&&(Pr(t.removed,{element:C.cloneNode()}),C.textContent=I)),Ce(B.afterSanitizeElements,C,null),!1)},po=function(C,I,Y){if(to&&(I==="id"||I==="name")&&(Y in r||Y in pp))return!1;if(!(ie&&!Ot[I]&&Rt(M,I))){if(!($e&&Rt(_,I))){if(!at[I]||Ot[I]){if(!(go(C)&&(st.tagNameCheck instanceof RegExp&&Rt(st.tagNameCheck,C)||st.tagNameCheck instanceof Function&&st.tagNameCheck(C))&&(st.attributeNameCheck instanceof RegExp&&Rt(st.attributeNameCheck,I)||st.attributeNameCheck instanceof Function&&st.attributeNameCheck(I))||I==="is"&&st.allowCustomizedBuiltInElements&&(st.tagNameCheck instanceof RegExp&&Rt(st.tagNameCheck,Y)||st.tagNameCheck instanceof Function&&st.tagNameCheck(Y))))return!1}else if(!Za[I]){if(!Rt(q,Nr(Y,A,""))){if(!((I==="src"||I==="xlink:href"||I==="href")&&C!=="script"&&sg(Y,"data:")===0&&io[C])){if(!(St&&!Rt(F,Nr(Y,A,"")))){if(Y)return!1}}}}}}return!0},go=function(C){return C!=="annotation-xml"&&vo(C,N)},mo=function(C){Ce(B.beforeSanitizeAttributes,C,null);const{attributes:I}=C;if(!I||tn(C))return;const Y={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:at,forceKeepAttr:void 0};let mt=I.length;for(;mt--;){const At=I[mt],{name:Tt,namespaceURI:Ht,value:he}=At,Ir=wt(Tt);let Dt=Tt==="value"?he:og(he);if(Y.attrName=Ir,Y.attrValue=Dt,Y.keepAttr=!0,Y.forceKeepAttr=void 0,Ce(B.uponSanitizeAttribute,C,Y),Dt=Y.attrValue,eo&&(Ir==="id"||Ir==="name")&&(Ai(Tt,C),Dt=cp+Dt),Oe&&Rt(/((--!?|])>)|<\/(style|title)/i,Dt)){Ai(Tt,C);continue}if(Y.forceKeepAttr||(Ai(Tt,C),!Y.keepAttr))continue;if(!ae&&Rt(/\/>/i,Dt)){Ai(Tt,C);continue}Zt&&Mi([z,P,E],xo=>{Dt=Nr(Dt,xo," ")});const yo=wt(C.nodeName);if(po(yo,Ir,Dt)){if(v&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!Ht)switch(d.getAttributeType(yo,Ir)){case"TrustedHTML":{Dt=v.createHTML(Dt);break}case"TrustedScriptURL":{Dt=v.createScriptURL(Dt);break}}try{Ht?C.setAttributeNS(Ht,Tt,Dt):C.setAttribute(Tt,Dt),tn(C)?ce(C):wo(t.removed)}catch{}}}Ce(B.afterSanitizeAttributes,C,null)},mp=function Q(C){let I=null;const Y=ho(C);for(Ce(B.beforeSanitizeShadowDOM,C,null);I=Y.nextNode();)Ce(B.uponSanitizeShadowNode,I,null),fo(I),mo(I),I.content instanceof n&&Q(I.content);Ce(B.afterSanitizeShadowDOM,C,null)};return t.sanitize=function(Q){let C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},I=null,Y=null,mt=null,At=null;if(Ka=!Q,Ka&&(Q="<!-->"),typeof Q!="string"&&!uo(Q))if(typeof Q.toString=="function"){if(Q=Q.toString(),typeof Q!="string")throw zr("dirty is not a string, aborting")}else throw zr("toString is not a function");if(!t.isSupported)return Q;if(Ga||Ja(C),t.removed=[],typeof Q=="string"&&(Dr=!1),Dr){if(Q.nodeName){const he=wt(Q.nodeName);if(!Z[he]||Vt[he])throw zr("root node is forbidden and cannot be sanitized in-place")}}else if(Q instanceof s)I=co("<!---->"),Y=I.ownerDocument.importNode(Q,!0),Y.nodeType===qr.element&&Y.nodeName==="BODY"||Y.nodeName==="HTML"?I=Y:I.appendChild(Y);else{if(!cr&&!Zt&&!je&&Q.indexOf("<")===-1)return v&&Si?v.createHTML(Q):Q;if(I=co(Q),!I)return cr?null:Si?S:""}I&&Va&&ce(I.firstChild);const Tt=ho(Dr?Q:I);for(;mt=Tt.nextNode();)fo(mt),mo(mt),mt.content instanceof n&&mp(mt.content);if(Dr)return Q;if(cr){if(vi)for(At=D.call(I.ownerDocument);I.firstChild;)At.appendChild(I.firstChild);else At=I;return(at.shadowroot||at.shadowrootmode)&&(At=O.call(i,At,!0)),At}let Ht=je?I.outerHTML:I.innerHTML;return je&&Z["!doctype"]&&I.ownerDocument&&I.ownerDocument.doctype&&I.ownerDocument.doctype.name&&Rt(ql,I.ownerDocument.doctype.name)&&(Ht="<!DOCTYPE "+I.ownerDocument.doctype.name+`>
`+Ht),Zt&&Mi([z,P,E],he=>{Ht=Nr(Ht,he," ")}),v&&Si?v.createHTML(Ht):Ht},t.setConfig=function(){let Q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Ja(Q),Ga=!0},t.clearConfig=function(){fr=null,Ga=!1},t.isValidAttribute=function(Q,C,I){fr||Ja({});const Y=wt(Q),mt=wt(C);return po(Y,mt,I)},t.addHook=function(Q,C){typeof C=="function"&&Pr(B[Q],C)},t.removeHook=function(Q,C){if(C!==void 0){const I=ag(B[Q],C);return I===-1?void 0:ng(B[Q],I,1)[0]}return wo(B[Q])},t.removeHooks=function(Q){B[Q]=[]},t.removeAllHooks=function(){B=Ao()},t}var Cr=Hl(),jl=Object.defineProperty,f=(e,t)=>jl(e,"name",{value:t,configurable:!0}),wg=(e,t)=>{for(var r in t)jl(e,r,{get:t[r],enumerable:!0})},ke={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},$={trace:f((...e)=>{},"trace"),debug:f((...e)=>{},"debug"),info:f((...e)=>{},"info"),warn:f((...e)=>{},"warn"),error:f((...e)=>{},"error"),fatal:f((...e)=>{},"fatal")},os=f(function(e="fatal"){let t=ke.fatal;typeof e=="string"?e.toLowerCase()in ke&&(t=ke[e]):typeof e=="number"&&(t=e),$.trace=()=>{},$.debug=()=>{},$.info=()=>{},$.warn=()=>{},$.error=()=>{},$.fatal=()=>{},t<=ke.fatal&&($.fatal=console.error?console.error.bind(console,Kt("FATAL"),"color: orange"):console.log.bind(console,"\x1B[35m",Kt("FATAL"))),t<=ke.error&&($.error=console.error?console.error.bind(console,Kt("ERROR"),"color: orange"):console.log.bind(console,"\x1B[31m",Kt("ERROR"))),t<=ke.warn&&($.warn=console.warn?console.warn.bind(console,Kt("WARN"),"color: orange"):console.log.bind(console,"\x1B[33m",Kt("WARN"))),t<=ke.info&&($.info=console.info?console.info.bind(console,Kt("INFO"),"color: lightblue"):console.log.bind(console,"\x1B[34m",Kt("INFO"))),t<=ke.debug&&($.debug=console.debug?console.debug.bind(console,Kt("DEBUG"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",Kt("DEBUG"))),t<=ke.trace&&($.trace=console.debug?console.debug.bind(console,Kt("TRACE"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",Kt("TRACE")))},"setLogLevel"),Kt=f(e=>`%c${Gp().format("ss.SSS")} : ${e} : `,"format"),Ul=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,ri=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,vg=/\s*%%.*\n/gm,Yl=class extends Error{static{f(this,"UnknownDiagramError")}constructor(e){super(e),this.name="UnknownDiagramError"}},kr={},ls=f(function(e,t){e=e.replace(Ul,"").replace(ri,"").replace(vg,`
`);for(const[r,{detector:i}]of Object.entries(kr))if(i(e,t))return r;throw new Yl(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),Gl=f((...e)=>{for(const{id:t,detector:r,loader:i}of e)Vl(t,r,i)},"registerLazyLoadedDiagrams"),Vl=f((e,t,r)=>{kr[e]&&$.warn(`Detector with key ${e} already exists. Overwriting.`),kr[e]={detector:t,loader:r},$.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),Sg=f(e=>kr[e].loader,"getDiagramLoader"),wn=f((e,t,{depth:r=2,clobber:i=!1}={})=>{const a={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(n=>wn(e,n,a)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(n=>{e.includes(n)||e.push(n)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(n=>{typeof t[n]=="object"&&(e[n]===void 0||typeof e[n]=="object")?(e[n]===void 0&&(e[n]=Array.isArray(t[n])?[]:{}),e[n]=wn(e[n],t[n],{depth:r-1,clobber:i})):(i||typeof e[n]!="object"&&typeof t[n]!="object")&&(e[n]=t[n])}),e)},"assignWithDepth"),Lt=wn,Ta="#ffffff",_a="#f2f2f2",It=f((e,t)=>t?w(e,{s:-40,l:10}):w(e,{s:-40,l:-10}),"mkBorder"),Tg=class{static{f(this,"Theme")}constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||w(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||w(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||It(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||It(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||It(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||It(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||W(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||W(this.tertiaryColor),this.lineColor=this.lineColor||W(this.background),this.arrowheadColor=this.arrowheadColor||W(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||J(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||W(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||H(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||J(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||J(this.mainBkg,10)):(this.rowOdd=this.rowOdd||H(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||H(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.darkMode)for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=J(this["cScale"+t],75);else for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=J(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||W(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this.darkMode?this["cScalePeer"+t]=this["cScalePeer"+t]||H(this["cScale"+t],10):this["cScalePeer"+t]=this["cScalePeer"+t]||J(this["cScale"+t],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;const e=this.darkMode?-4:-1;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||w(this.mainBkg,{h:180,s:-15,l:e*(5+t*3)}),this["surfacePeer"+t]=this["surfacePeer"+t]||w(this.mainBkg,{h:180,s:-15,l:e*(8+t*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||w(this.primaryColor,{h:64}),this.fillType3=this.fillType3||w(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||w(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||w(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||w(this.primaryColor,{h:128}),this.fillType7=this.fillType7||w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||w(this.primaryColor,{l:-10}),this.pie5=this.pie5||w(this.secondaryColor,{l:-10}),this.pie6=this.pie6||w(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||w(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||gi(this.quadrant1Fill)?H(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=H(this.git0,25),this.git1=H(this.git1,25),this.git2=H(this.git2,25),this.git3=H(this.git3,25),this.git4=H(this.git4,25),this.git5=H(this.git5,25),this.git6=H(this.git6,25),this.git7=H(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||W(this.git0),this.gitInv1=this.gitInv1||W(this.git1),this.gitInv2=this.gitInv2||W(this.git2),this.gitInv3=this.gitInv3||W(this.git3),this.gitInv4=this.gitInv4||W(this.git4),this.gitInv5=this.gitInv5||W(this.git5),this.gitInv6=this.gitInv6||W(this.git6),this.gitInv7=this.gitInv7||W(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ta,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||_a}calculate(e){if(typeof e!="object"){this.updateColors();return}const t=Object.keys(e);t.forEach(r=>{this[r]=e[r]}),this.updateColors(),t.forEach(r=>{this[r]=e[r]})}},_g=f(e=>{const t=new Tg;return t.calculate(e),t},"getThemeVariables"),Bg=class{static{f(this,"Theme")}constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=H(this.primaryColor,16),this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=W(this.background),this.secondaryBorderColor=It(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=It(this.tertiaryColor,this.darkMode),this.primaryTextColor=W(this.primaryColor),this.secondaryTextColor=W(this.secondaryColor),this.tertiaryTextColor=W(this.tertiaryColor),this.lineColor=W(this.background),this.textColor=W(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=H(W("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=ei(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=J("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=J(this.sectionBkgColor,10),this.taskBorderColor=ei(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=ei(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||H(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||J(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){this.secondBkg=H(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=H(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=H(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330});for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleInv"+e]=this["cScaleInv"+e]||W(this["cScale"+e]);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScalePeer"+e]=this["cScalePeer"+e]||H(this["cScale"+e],10);for(let e=0;e<5;e++)this["surface"+e]=this["surface"+e]||w(this.mainBkg,{h:30,s:-30,l:-(-10+e*4)}),this["surfacePeer"+e]=this["surfacePeer"+e]||w(this.mainBkg,{h:30,s:-30,l:-(-7+e*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleLabel"+e]=this["cScaleLabel"+e]||this.scaleLabelColor;for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["pie"+e]=this["cScale"+e];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||gi(this.quadrant1Fill)?H(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=H(this.secondaryColor,20),this.git1=H(this.pie2||this.secondaryColor,20),this.git2=H(this.pie3||this.tertiaryColor,20),this.git3=H(this.pie4||w(this.primaryColor,{h:-30}),20),this.git4=H(this.pie5||w(this.primaryColor,{h:-60}),20),this.git5=H(this.pie6||w(this.primaryColor,{h:-90}),10),this.git6=H(this.pie7||w(this.primaryColor,{h:60}),10),this.git7=H(this.pie8||w(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||W(this.git0),this.gitInv1=this.gitInv1||W(this.git1),this.gitInv2=this.gitInv2||W(this.git2),this.gitInv3=this.gitInv3||W(this.git3),this.gitInv4=this.gitInv4||W(this.git4),this.gitInv5=this.gitInv5||W(this.git5),this.gitInv6=this.gitInv6||W(this.git6),this.gitInv7=this.gitInv7||W(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||W(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||W(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||H(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||H(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(e){if(typeof e!="object"){this.updateColors();return}const t=Object.keys(e);t.forEach(r=>{this[r]=e[r]}),this.updateColors(),t.forEach(r=>{this[r]=e[r]})}},Lg=f(e=>{const t=new Bg;return t.calculate(e),t},"getThemeVariables"),Ag=class{static{f(this,"Theme")}constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=w(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=It(this.primaryColor,this.darkMode),this.secondaryBorderColor=It(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=It(this.tertiaryColor,this.darkMode),this.primaryTextColor=W(this.primaryColor),this.secondaryTextColor=W(this.secondaryColor),this.tertiaryTextColor=W(this.tertiaryColor),this.lineColor=W(this.background),this.textColor=W(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=ei(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||J(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||J(this.tertiaryColor,40);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScale"+e]=J(this["cScale"+e],10),this["cScalePeer"+e]=this["cScalePeer"+e]||J(this["cScale"+e],25);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleInv"+e]=this["cScaleInv"+e]||w(this["cScale"+e],{h:180});for(let e=0;e<5;e++)this["surface"+e]=this["surface"+e]||w(this.mainBkg,{h:30,l:-(5+e*5)}),this["surfacePeer"+e]=this["surfacePeer"+e]||w(this.mainBkg,{h:30,l:-(7+e*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||W(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||W(this.labelTextColor);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleLabel"+e]=this["cScaleLabel"+e]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=H(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||H(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||H(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||w(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||w(this.primaryColor,{l:-10}),this.pie5=this.pie5||w(this.secondaryColor,{l:-30}),this.pie6=this.pie6||w(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||w(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||gi(this.quadrant1Fill)?H(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=H(this.git0,25),this.git1=H(this.git1,25),this.git2=H(this.git2,25),this.git3=H(this.git3,25),this.git4=H(this.git4,25),this.git5=H(this.git5,25),this.git6=H(this.git6,25),this.git7=H(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||J(W(this.git0),25),this.gitInv1=this.gitInv1||W(this.git1),this.gitInv2=this.gitInv2||W(this.git2),this.gitInv3=this.gitInv3||W(this.git3),this.gitInv4=this.gitInv4||W(this.git4),this.gitInv5=this.gitInv5||W(this.git5),this.gitInv6=this.gitInv6||W(this.git6),this.gitInv7=this.gitInv7||W(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||W(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||W(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ta,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||_a}calculate(e){if(Object.keys(this).forEach(r=>{this[r]==="calculated"&&(this[r]=void 0)}),typeof e!="object"){this.updateColors();return}const t=Object.keys(e);t.forEach(r=>{this[r]=e[r]}),this.updateColors(),t.forEach(r=>{this[r]=e[r]})}},Mg=f(e=>{const t=new Ag;return t.calculate(e),t},"getThemeVariables"),Eg=class{static{f(this,"Theme")}constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=H("#cde498",10),this.primaryBorderColor=It(this.primaryColor,this.darkMode),this.secondaryBorderColor=It(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=It(this.tertiaryColor,this.darkMode),this.primaryTextColor=W(this.primaryColor),this.secondaryTextColor=W(this.secondaryColor),this.tertiaryTextColor=W(this.primaryColor),this.lineColor=W(this.background),this.textColor=W(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){this.actorBorder=J(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||w(this.primaryColor,{h:30}),this.cScale4=this.cScale4||w(this.primaryColor,{h:60}),this.cScale5=this.cScale5||w(this.primaryColor,{h:90}),this.cScale6=this.cScale6||w(this.primaryColor,{h:120}),this.cScale7=this.cScale7||w(this.primaryColor,{h:150}),this.cScale8=this.cScale8||w(this.primaryColor,{h:210}),this.cScale9=this.cScale9||w(this.primaryColor,{h:270}),this.cScale10=this.cScale10||w(this.primaryColor,{h:300}),this.cScale11=this.cScale11||w(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||J(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||J(this.tertiaryColor,40);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScale"+e]=J(this["cScale"+e],10),this["cScalePeer"+e]=this["cScalePeer"+e]||J(this["cScale"+e],25);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleInv"+e]=this["cScaleInv"+e]||w(this["cScale"+e],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleLabel"+e]=this["cScaleLabel"+e]||this.scaleLabelColor;for(let e=0;e<5;e++)this["surface"+e]=this["surface"+e]||w(this.mainBkg,{h:30,s:-30,l:-(5+e*5)}),this["surfacePeer"+e]=this["surfacePeer"+e]||w(this.mainBkg,{h:30,s:-30,l:-(8+e*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||H(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||H(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||w(this.primaryColor,{l:-30}),this.pie5=this.pie5||w(this.secondaryColor,{l:-30}),this.pie6=this.pie6||w(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||w(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||w(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||w(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||w(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||w(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||w(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||gi(this.quadrant1Fill)?H(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||w(this.primaryColor,{h:-30}),this.git4=this.git4||w(this.primaryColor,{h:-60}),this.git5=this.git5||w(this.primaryColor,{h:-90}),this.git6=this.git6||w(this.primaryColor,{h:60}),this.git7=this.git7||w(this.primaryColor,{h:120}),this.darkMode?(this.git0=H(this.git0,25),this.git1=H(this.git1,25),this.git2=H(this.git2,25),this.git3=H(this.git3,25),this.git4=H(this.git4,25),this.git5=H(this.git5,25),this.git6=H(this.git6,25),this.git7=H(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||W(this.git0),this.gitInv1=this.gitInv1||W(this.git1),this.gitInv2=this.gitInv2||W(this.git2),this.gitInv3=this.gitInv3||W(this.git3),this.gitInv4=this.gitInv4||W(this.git4),this.gitInv5=this.gitInv5||W(this.git5),this.gitInv6=this.gitInv6||W(this.git6),this.gitInv7=this.gitInv7||W(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||W(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||W(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ta,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||_a}calculate(e){if(typeof e!="object"){this.updateColors();return}const t=Object.keys(e);t.forEach(r=>{this[r]=e[r]}),this.updateColors(),t.forEach(r=>{this[r]=e[r]})}},Fg=f(e=>{const t=new Eg;return t.calculate(e),t},"getThemeVariables"),$g=class{static{f(this,"Theme")}constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=H(this.contrast,55),this.background="#ffffff",this.tertiaryColor=w(this.primaryColor,{h:-160}),this.primaryBorderColor=It(this.primaryColor,this.darkMode),this.secondaryBorderColor=It(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=It(this.tertiaryColor,this.darkMode),this.primaryTextColor=W(this.primaryColor),this.secondaryTextColor=W(this.secondaryColor),this.tertiaryTextColor=W(this.tertiaryColor),this.lineColor=W(this.background),this.textColor=W(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||H(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){this.secondBkg=H(this.contrast,55),this.border2=this.contrast,this.actorBorder=H(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleInv"+e]=this["cScaleInv"+e]||W(this["cScale"+e]);for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this.darkMode?this["cScalePeer"+e]=this["cScalePeer"+e]||H(this["cScale"+e],10):this["cScalePeer"+e]=this["cScalePeer"+e]||J(this["cScale"+e],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["cScaleLabel"+e]=this["cScaleLabel"+e]||this.scaleLabelColor;for(let e=0;e<5;e++)this["surface"+e]=this["surface"+e]||w(this.mainBkg,{l:-(5+e*5)}),this["surfacePeer"+e]=this["surfacePeer"+e]||w(this.mainBkg,{l:-(8+e*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=H(this.contrast,30),this.sectionBkgColor2=H(this.contrast,30),this.taskBorderColor=J(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=H(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=J(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=w(this.primaryColor,{h:64}),this.fillType3=w(this.secondaryColor,{h:64}),this.fillType4=w(this.primaryColor,{h:-64}),this.fillType5=w(this.secondaryColor,{h:-64}),this.fillType6=w(this.primaryColor,{h:128}),this.fillType7=w(this.secondaryColor,{h:128});for(let e=0;e<this.THEME_COLOR_LIMIT;e++)this["pie"+e]=this["cScale"+e];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||w(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||w(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||w(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||w(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||w(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||w(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||gi(this.quadrant1Fill)?H(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=J(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||w(this.primaryColor,{h:-30}),this.git4=this.pie5||w(this.primaryColor,{h:-60}),this.git5=this.pie6||w(this.primaryColor,{h:-90}),this.git6=this.pie7||w(this.primaryColor,{h:60}),this.git7=this.pie8||w(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||W(this.git0),this.gitInv1=this.gitInv1||W(this.git1),this.gitInv2=this.gitInv2||W(this.git2),this.gitInv3=this.gitInv3||W(this.git3),this.gitInv4=this.gitInv4||W(this.git4),this.gitInv5=this.gitInv5||W(this.git5),this.gitInv6=this.gitInv6||W(this.git6),this.gitInv7=this.gitInv7||W(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ta,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||_a}calculate(e){if(typeof e!="object"){this.updateColors();return}const t=Object.keys(e);t.forEach(r=>{this[r]=e[r]}),this.updateColors(),t.forEach(r=>{this[r]=e[r]})}},Og=f(e=>{const t=new $g;return t.calculate(e),t},"getThemeVariables"),Te={base:{getThemeVariables:_g},dark:{getThemeVariables:Lg},default:{getThemeVariables:Mg},forest:{getThemeVariables:Fg},neutral:{getThemeVariables:Og}},De={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},Xl={...De,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:Te.default.getThemeVariables(),sequence:{...De.sequence,messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:f(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:f(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{...De.gantt,tickInterval:void 0,useWidth:void 0},c4:{...De.c4,useWidth:void 0,personFont:f(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),external_personFont:f(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:f(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:f(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:f(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:f(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:f(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:f(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:f(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:f(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:f(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:f(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:f(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:f(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:f(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:f(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:f(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:f(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:f(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:f(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:f(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{...De.pie,useWidth:984},xyChart:{...De.xyChart,useWidth:void 0},requirement:{...De.requirement,useWidth:void 0},packet:{...De.packet}},Zl=f((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...Zl(e[i],"")]:[...r,t+i],[]),"keyify"),Dg=new Set(Zl(Xl,"")),Kl=Xl,Qi=f(e=>{if($.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>Qi(t));return}for(const t of Object.keys(e)){if($.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!Dg.has(t)||e[t]==null){$.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){$.debug("sanitizing object",t),Qi(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&($.debug("sanitizing css option",t),e[t]=Rg(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r?.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}$.debug("After sanitization",e)}},"sanitizeDirective"),Rg=f(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),wr=Object.freeze(Kl),Ut=Lt({},wr),Ql,vr=[],ii=Lt({},wr),Ba=f((e,t)=>{let r=Lt({},e),i={};for(const a of t)ec(a),i=Lt(i,a);if(r=Lt(r,i),i.theme&&i.theme in Te){const a=Lt({},Ql),n=Lt(a.themeVariables||{},i.themeVariables);r.theme&&r.theme in Te&&(r.themeVariables=Te[r.theme].getThemeVariables(n))}return ii=r,rc(ii),ii},"updateCurrentConfig"),Ig=f(e=>(Ut=Lt({},wr),Ut=Lt(Ut,e),e.theme&&Te[e.theme]&&(Ut.themeVariables=Te[e.theme].getThemeVariables(e.themeVariables)),Ba(Ut,vr),Ut),"setSiteConfig"),Pg=f(e=>{Ql=Lt({},e)},"saveConfigFromInitialize"),Ng=f(e=>(Ut=Lt(Ut,e),Ba(Ut,vr),Ut),"updateSiteConfig"),Jl=f(()=>Lt({},Ut),"getSiteConfig"),tc=f(e=>(rc(e),Lt(ii,e),Gt()),"setConfig"),Gt=f(()=>Lt({},ii),"getConfig"),ec=f(e=>{e&&(["secure",...Ut.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&($.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&ec(e[t])}))},"sanitize"),zg=f(e=>{Qi(e),e.fontFamily&&!e.themeVariables?.fontFamily&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),vr.push(e),Ba(Ut,vr)},"addDirective"),Ji=f((e=Ut)=>{vr=[],Ba(e,vr)},"reset"),Wg={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},Mo={},qg=f(e=>{Mo[e]||($.warn(Wg[e]),Mo[e]=!0)},"issueWarning"),rc=f(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&qg("LAZY_LOAD_DEPRECATED")},"checkConfig"),mi=/<br\s*\/?>/gi,Hg=f(e=>e?nc(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),jg=(()=>{let e=!1;return()=>{e||(ic(),e=!0)}})();function ic(){const e="data-temp-href-target";Cr.addHook("beforeSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),Cr.addHook("afterSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}f(ic,"setupDompurifyHooks");var ac=f(e=>(jg(),Cr.sanitize(e)),"removeScript"),Eo=f((e,t)=>{if(t.flowchart?.htmlLabels!==!1){const r=t.securityLevel;r==="antiscript"||r==="strict"?e=ac(e):r!=="loose"&&(e=nc(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=Vg(e))}return e},"sanitizeMore"),Je=f((e,t)=>e&&(t.dompurifyConfig?e=Cr.sanitize(Eo(e,t),t.dompurifyConfig).toString():e=Cr.sanitize(Eo(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),Ug=f((e,t)=>typeof e=="string"?Je(e,t):e.flat().map(r=>Je(r,t)),"sanitizeTextOrArray"),Yg=f(e=>mi.test(e),"hasBreaks"),Gg=f(e=>e.split(mi),"splitBreaks"),Vg=f(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),nc=f(e=>e.replace(mi,"#br#"),"breakToPlaceholder"),Xg=f(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=t.replaceAll(/\(/g,"\\("),t=t.replaceAll(/\)/g,"\\)")),t},"getUrl"),kt=f(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),Zg=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),Kg=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),Fo=f(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let a=t[i];if(a===","&&i>0&&i+1<t.length){const n=t[i-1],o=t[i+1];Qg(n,o)&&(a=n+","+o,i++,r.pop())}r.push(Jg(a))}return r.join("")},"parseGenericTypes"),vn=f((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),Qg=f((e,t)=>{const r=vn(e,"~"),i=vn(t,"~");return r===1&&i===1},"shouldCombineSets"),Jg=f(e=>{const t=vn(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let a=i.indexOf("~"),n=i.lastIndexOf("~");for(;a!==-1&&n!==-1&&a!==n;)i[a]="<",i[n]=">",a=i.indexOf("~"),n=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),$o=f(()=>window.MathMLElement!==void 0,"isMathMLSupported"),Sn=/\$\$(.*)\$\$/g,Sr=f(e=>(e.match(Sn)?.length??0)>0,"hasKatex"),fS=f(async(e,t)=>{e=await cs(e,t);const r=document.createElement("div");r.innerHTML=e,r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0",document.querySelector("body")?.insertAdjacentElement("beforeend",r);const a={width:r.clientWidth,height:r.clientHeight};return r.remove(),a},"calculateMathMLDimensions"),cs=f(async(e,t)=>{if(!Sr(e))return e;if(!($o()||t.legacyMathML||t.forceLegacyMathML))return e.replace(Sn,"MathML is unsupported in this environment.");const{default:r}=await ft(()=>import("./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js").then(a=>a.a),__vite__mapDeps([0,1,2,3,4]),import.meta.url),i=t.forceLegacyMathML||!$o()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(mi).map(a=>Sr(a)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${a}</div>`:`<div>${a}</div>`).join("").replace(Sn,(a,n)=>r.renderToString(n,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))},"renderKatex"),Ar={getRows:Hg,sanitizeText:Je,sanitizeTextOrArray:Ug,hasBreaks:Yg,splitBreaks:Gg,lineBreakRegex:mi,removeScript:ac,getUrl:Xg,evaluate:kt,getMax:Zg,getMin:Kg},tm=f(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),em=f(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),sc=f(function(e,t,r,i){const a=em(t,r,i);tm(e,a)},"configureSvgSize"),rm=f(function(e,t,r,i){const a=t.node().getBBox(),n=a.width,o=a.height;$.info(`SVG bounds: ${n}x${o}`,a);let s=0,l=0;$.info(`Graph bounds: ${s}x${l}`,e),s=n+r*2,l=o+r*2,$.info(`Calculated bounds: ${s}x${l}`),sc(t,l,s,i);const c=`${a.x-r} ${a.y-r} ${a.width+2*r} ${a.height+2*r}`;t.attr("viewBox",c)},"setupGraphViewbox"),Wi={},im=f((e,t,r)=>{let i="";return e in Wi&&Wi[e]?i=Wi[e](r):$.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),am=f((e,t)=>{t!==void 0&&(Wi[e]=t)},"addStylesForDiagram"),nm=im,oc={};wg(oc,{clear:()=>sm,getAccDescription:()=>hm,getAccTitle:()=>lm,getDiagramTitle:()=>dm,setAccDescription:()=>cm,setAccTitle:()=>om,setDiagramTitle:()=>um});var hs="",us="",ds="",fs=f(e=>Je(e,Gt()),"sanitizeText"),sm=f(()=>{hs="",ds="",us=""},"clear"),om=f(e=>{hs=fs(e).replace(/^\s+/g,"")},"setAccTitle"),lm=f(()=>hs,"getAccTitle"),cm=f(e=>{ds=fs(e).replace(/\n\s+/g,`
`)},"setAccDescription"),hm=f(()=>ds,"getAccDescription"),um=f(e=>{us=fs(e)},"setDiagramTitle"),dm=f(()=>us,"getDiagramTitle"),Oo=$,fm=os,ht=Gt,pS=tc,gS=wr,ps=f(e=>Je(e,ht()),"sanitizeText"),pm=rm,gm=f(()=>oc,"getCommonDb"),ta={},ea=f((e,t,r)=>{ta[e]&&Oo.warn(`Diagram with id ${e} already registered. Overwriting.`),ta[e]=t,r&&Vl(e,r),am(e,t.styles),t.injectUtils?.(Oo,fm,ht,ps,pm,gm(),()=>{})},"registerDiagram"),Tn=f(e=>{if(e in ta)return ta[e];throw new mm(e)},"getDiagram"),mm=class extends Error{static{f(this,"DiagramNotFoundError")}constructor(e){super(`Diagram ${e} not found.`)}};function gs(e){return typeof e>"u"||e===null}f(gs,"isNothing");function lc(e){return typeof e=="object"&&e!==null}f(lc,"isObject");function cc(e){return Array.isArray(e)?e:gs(e)?[]:[e]}f(cc,"toArray");function hc(e,t){var r,i,a,n;if(t)for(n=Object.keys(t),r=0,i=n.length;r<i;r+=1)a=n[r],e[a]=t[a];return e}f(hc,"extend");function uc(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}f(uc,"repeat");function dc(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}f(dc,"isNegativeZero");var ym=gs,xm=lc,bm=cc,Cm=uc,km=dc,wm=hc,Ct={isNothing:ym,isObject:xm,toArray:bm,repeat:Cm,isNegativeZero:km,extend:wm};function ms(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}f(ms,"formatError");function Tr(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=ms(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}f(Tr,"YAMLException$1");Tr.prototype=Object.create(Error.prototype);Tr.prototype.constructor=Tr;Tr.prototype.toString=f(function(t){return this.name+": "+ms(this,t)},"toString");var Yt=Tr;function qi(e,t,r,i,a){var n="",o="",s=Math.floor(a/2)-1;return i-t>s&&(n=" ... ",t=i-s+n.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:n+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+n.length}}f(qi,"getLine");function Hi(e,t){return Ct.repeat(" ",t-e.length)+e}f(Hi,"padStart");function fc(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],a=[],n,o=-1;n=r.exec(e.buffer);)a.push(n.index),i.push(n.index+n[0].length),e.position<=n.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",l,c,h=Math.min(e.line+t.linesAfter,a.length).toString().length,u=t.maxLength-(t.indent+h+3);for(l=1;l<=t.linesBefore&&!(o-l<0);l++)c=qi(e.buffer,i[o-l],a[o-l],e.position-(i[o]-i[o-l]),u),s=Ct.repeat(" ",t.indent)+Hi((e.line-l+1).toString(),h)+" | "+c.str+`
`+s;for(c=qi(e.buffer,i[o],a[o],e.position,u),s+=Ct.repeat(" ",t.indent)+Hi((e.line+1).toString(),h)+" | "+c.str+`
`,s+=Ct.repeat("-",t.indent+h+3+c.pos)+`^
`,l=1;l<=t.linesAfter&&!(o+l>=a.length);l++)c=qi(e.buffer,i[o+l],a[o+l],e.position-(i[o]-i[o+l]),u),s+=Ct.repeat(" ",t.indent)+Hi((e.line+l+1).toString(),h)+" | "+c.str+`
`;return s.replace(/\n$/,"")}f(fc,"makeSnippet");var vm=fc,Sm=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],Tm=["scalar","sequence","mapping"];function pc(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}f(pc,"compileStyleAliases");function gc(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(Sm.indexOf(r)===-1)throw new Yt('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=pc(t.styleAliases||null),Tm.indexOf(this.kind)===-1)throw new Yt('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}f(gc,"Type$1");var Ft=gc;function _n(e,t){var r=[];return e[t].forEach(function(i){var a=r.length;r.forEach(function(n,o){n.tag===i.tag&&n.kind===i.kind&&n.multi===i.multi&&(a=o)}),r[a]=i}),r}f(_n,"compileList");function mc(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(a){a.multi?(e.multi[a.kind].push(a),e.multi.fallback.push(a)):e[a.kind][a.tag]=e.fallback[a.tag]=a}for(f(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}f(mc,"compileMap");function ra(e){return this.extend(e)}f(ra,"Schema$1");ra.prototype.extend=f(function(t){var r=[],i=[];if(t instanceof Ft)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new Yt("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(n){if(!(n instanceof Ft))throw new Yt("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(n.loadKind&&n.loadKind!=="scalar")throw new Yt("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(n.multi)throw new Yt("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(n){if(!(n instanceof Ft))throw new Yt("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var a=Object.create(ra.prototype);return a.implicit=(this.implicit||[]).concat(r),a.explicit=(this.explicit||[]).concat(i),a.compiledImplicit=_n(a,"implicit"),a.compiledExplicit=_n(a,"explicit"),a.compiledTypeMap=mc(a.compiledImplicit,a.compiledExplicit),a},"extend");var _m=ra,Bm=new Ft("tag:yaml.org,2002:str",{kind:"scalar",construct:f(function(e){return e!==null?e:""},"construct")}),Lm=new Ft("tag:yaml.org,2002:seq",{kind:"sequence",construct:f(function(e){return e!==null?e:[]},"construct")}),Am=new Ft("tag:yaml.org,2002:map",{kind:"mapping",construct:f(function(e){return e!==null?e:{}},"construct")}),Mm=new _m({explicit:[Bm,Lm,Am]});function yc(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}f(yc,"resolveYamlNull");function xc(){return null}f(xc,"constructYamlNull");function bc(e){return e===null}f(bc,"isNull");var Em=new Ft("tag:yaml.org,2002:null",{kind:"scalar",resolve:yc,construct:xc,predicate:bc,represent:{canonical:f(function(){return"~"},"canonical"),lowercase:f(function(){return"null"},"lowercase"),uppercase:f(function(){return"NULL"},"uppercase"),camelcase:f(function(){return"Null"},"camelcase"),empty:f(function(){return""},"empty")},defaultStyle:"lowercase"});function Cc(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}f(Cc,"resolveYamlBoolean");function kc(e){return e==="true"||e==="True"||e==="TRUE"}f(kc,"constructYamlBoolean");function wc(e){return Object.prototype.toString.call(e)==="[object Boolean]"}f(wc,"isBoolean");var Fm=new Ft("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Cc,construct:kc,predicate:wc,represent:{lowercase:f(function(e){return e?"true":"false"},"lowercase"),uppercase:f(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:f(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function vc(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}f(vc,"isHexCode");function Sc(e){return 48<=e&&e<=55}f(Sc,"isOctCode");function Tc(e){return 48<=e&&e<=57}f(Tc,"isDecCode");function _c(e){if(e===null)return!1;var t=e.length,r=0,i=!1,a;if(!t)return!1;if(a=e[r],(a==="-"||a==="+")&&(a=e[++r]),a==="0"){if(r+1===t)return!0;if(a=e[++r],a==="b"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(a!=="0"&&a!=="1")return!1;i=!0}return i&&a!=="_"}if(a==="x"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!vc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}if(a==="o"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Sc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}}if(a==="_")return!1;for(;r<t;r++)if(a=e[r],a!=="_"){if(!Tc(e.charCodeAt(r)))return!1;i=!0}return!(!i||a==="_")}f(_c,"resolveYamlInteger");function Bc(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}f(Bc,"constructYamlInteger");function Lc(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!Ct.isNegativeZero(e)}f(Lc,"isInteger");var $m=new Ft("tag:yaml.org,2002:int",{kind:"scalar",resolve:_c,construct:Bc,predicate:Lc,represent:{binary:f(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:f(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:f(function(e){return e.toString(10)},"decimal"),hexadecimal:f(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Om=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Ac(e){return!(e===null||!Om.test(e)||e[e.length-1]==="_")}f(Ac,"resolveYamlFloat");function Mc(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}f(Mc,"constructYamlFloat");var Dm=/^[-+]?[0-9]+e/;function Ec(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Ct.isNegativeZero(e))return"-0.0";return r=e.toString(10),Dm.test(r)?r.replace("e",".e"):r}f(Ec,"representYamlFloat");function Fc(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||Ct.isNegativeZero(e))}f(Fc,"isFloat");var Rm=new Ft("tag:yaml.org,2002:float",{kind:"scalar",resolve:Ac,construct:Mc,predicate:Fc,represent:Ec,defaultStyle:"lowercase"}),$c=Mm.extend({implicit:[Em,Fm,$m,Rm]}),Im=$c,Oc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),Dc=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Rc(e){return e===null?!1:Oc.exec(e)!==null||Dc.exec(e)!==null}f(Rc,"resolveYamlTimestamp");function Ic(e){var t,r,i,a,n,o,s,l=0,c=null,h,u,p;if(t=Oc.exec(e),t===null&&(t=Dc.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(r,i,a));if(n=+t[4],o=+t[5],s=+t[6],t[7]){for(l=t[7].slice(0,3);l.length<3;)l+="0";l=+l}return t[9]&&(h=+t[10],u=+(t[11]||0),c=(h*60+u)*6e4,t[9]==="-"&&(c=-c)),p=new Date(Date.UTC(r,i,a,n,o,s,l)),c&&p.setTime(p.getTime()-c),p}f(Ic,"constructYamlTimestamp");function Pc(e){return e.toISOString()}f(Pc,"representYamlTimestamp");var Pm=new Ft("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Rc,construct:Ic,instanceOf:Date,represent:Pc});function Nc(e){return e==="<<"||e===null}f(Nc,"resolveYamlMerge");var Nm=new Ft("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Nc}),ys=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function zc(e){if(e===null)return!1;var t,r,i=0,a=e.length,n=ys;for(r=0;r<a;r++)if(t=n.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}f(zc,"resolveYamlBinary");function Wc(e){var t,r,i=e.replace(/[\r\n=]/g,""),a=i.length,n=ys,o=0,s=[];for(t=0;t<a;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|n.indexOf(i.charAt(t));return r=a%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}f(Wc,"constructYamlBinary");function qc(e){var t="",r=0,i,a,n=e.length,o=ys;for(i=0;i<n;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return a=n%3,a===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):a===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):a===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}f(qc,"representYamlBinary");function Hc(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}f(Hc,"isBinary");var zm=new Ft("tag:yaml.org,2002:binary",{kind:"scalar",resolve:zc,construct:Wc,predicate:Hc,represent:qc}),Wm=Object.prototype.hasOwnProperty,qm=Object.prototype.toString;function jc(e){if(e===null)return!0;var t=[],r,i,a,n,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(a=s[r],o=!1,qm.call(a)!=="[object Object]")return!1;for(n in a)if(Wm.call(a,n))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(n)===-1)t.push(n);else return!1}return!0}f(jc,"resolveYamlOmap");function Uc(e){return e!==null?e:[]}f(Uc,"constructYamlOmap");var Hm=new Ft("tag:yaml.org,2002:omap",{kind:"sequence",resolve:jc,construct:Uc}),jm=Object.prototype.toString;function Yc(e){if(e===null)return!0;var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],jm.call(i)!=="[object Object]"||(a=Object.keys(i),a.length!==1))return!1;n[t]=[a[0],i[a[0]]]}return!0}f(Yc,"resolveYamlPairs");function Gc(e){if(e===null)return[];var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],a=Object.keys(i),n[t]=[a[0],i[a[0]]];return n}f(Gc,"constructYamlPairs");var Um=new Ft("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:Yc,construct:Gc}),Ym=Object.prototype.hasOwnProperty;function Vc(e){if(e===null)return!0;var t,r=e;for(t in r)if(Ym.call(r,t)&&r[t]!==null)return!1;return!0}f(Vc,"resolveYamlSet");function Xc(e){return e!==null?e:{}}f(Xc,"constructYamlSet");var Gm=new Ft("tag:yaml.org,2002:set",{kind:"mapping",resolve:Vc,construct:Xc}),Zc=Im.extend({implicit:[Pm,Nm],explicit:[zm,Hm,Um,Gm]}),Ne=Object.prototype.hasOwnProperty,ia=1,Kc=2,Qc=3,aa=4,on=1,Vm=2,Do=3,Xm=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Zm=/[\x85\u2028\u2029]/,Km=/[,\[\]\{\}]/,Jc=/^(?:!|!!|![a-z\-]+!)$/i,th=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Bn(e){return Object.prototype.toString.call(e)}f(Bn,"_class");function se(e){return e===10||e===13}f(se,"is_EOL");function Pe(e){return e===9||e===32}f(Pe,"is_WHITE_SPACE");function Pt(e){return e===9||e===32||e===10||e===13}f(Pt,"is_WS_OR_EOL");function Ze(e){return e===44||e===91||e===93||e===123||e===125}f(Ze,"is_FLOW_INDICATOR");function eh(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}f(eh,"fromHexCode");function rh(e){return e===120?2:e===117?4:e===85?8:0}f(rh,"escapedHexLen");function ih(e){return 48<=e&&e<=57?e-48:-1}f(ih,"fromDecimalCode");function Ln(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}f(Ln,"simpleEscapeSequence");function ah(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}f(ah,"charFromCodepoint");var nh=new Array(256),sh=new Array(256);for(Ue=0;Ue<256;Ue++)nh[Ue]=Ln(Ue)?1:0,sh[Ue]=Ln(Ue);var Ue;function oh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||Zc,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}f(oh,"State$1");function xs(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=vm(r),new Yt(t,r)}f(xs,"generateError");function K(e,t){throw xs(e,t)}f(K,"throwError");function oi(e,t){e.onWarning&&e.onWarning.call(null,xs(e,t))}f(oi,"throwWarning");var Ro={YAML:f(function(t,r,i){var a,n,o;t.version!==null&&K(t,"duplication of %YAML directive"),i.length!==1&&K(t,"YAML directive accepts exactly one argument"),a=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),a===null&&K(t,"ill-formed argument of the YAML directive"),n=parseInt(a[1],10),o=parseInt(a[2],10),n!==1&&K(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&oi(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:f(function(t,r,i){var a,n;i.length!==2&&K(t,"TAG directive accepts exactly two arguments"),a=i[0],n=i[1],Jc.test(a)||K(t,"ill-formed tag handle (first argument) of the TAG directive"),Ne.call(t.tagMap,a)&&K(t,'there is a previously declared suffix for "'+a+'" tag handle'),th.test(n)||K(t,"ill-formed tag prefix (second argument) of the TAG directive");try{n=decodeURIComponent(n)}catch{K(t,"tag prefix is malformed: "+n)}t.tagMap[a]=n},"handleTagDirective")};function _e(e,t,r,i){var a,n,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(a=0,n=s.length;a<n;a+=1)o=s.charCodeAt(a),o===9||32<=o&&o<=1114111||K(e,"expected valid JSON character");else Xm.test(s)&&K(e,"the stream contains non-printable characters");e.result+=s}}f(_e,"captureSegment");function An(e,t,r,i){var a,n,o,s;for(Ct.isObject(r)||K(e,"cannot merge mappings; the provided source object is unacceptable"),a=Object.keys(r),o=0,s=a.length;o<s;o+=1)n=a[o],Ne.call(t,n)||(t[n]=r[n],i[n]=!0)}f(An,"mergeMappings");function Ke(e,t,r,i,a,n,o,s,l){var c,h;if(Array.isArray(a))for(a=Array.prototype.slice.call(a),c=0,h=a.length;c<h;c+=1)Array.isArray(a[c])&&K(e,"nested arrays are not supported inside keys"),typeof a=="object"&&Bn(a[c])==="[object Object]"&&(a[c]="[object Object]");if(typeof a=="object"&&Bn(a)==="[object Object]"&&(a="[object Object]"),a=String(a),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(n))for(c=0,h=n.length;c<h;c+=1)An(e,t,n[c],r);else An(e,t,n,r);else!e.json&&!Ne.call(r,a)&&Ne.call(t,a)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=l||e.position,K(e,"duplicated mapping key")),a==="__proto__"?Object.defineProperty(t,a,{configurable:!0,enumerable:!0,writable:!0,value:n}):t[a]=n,delete r[a];return t}f(Ke,"storeMappingPair");function La(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):K(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}f(La,"readLineBreak");function xt(e,t,r){for(var i=0,a=e.input.charCodeAt(e.position);a!==0;){for(;Pe(a);)a===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),a=e.input.charCodeAt(++e.position);if(t&&a===35)do a=e.input.charCodeAt(++e.position);while(a!==10&&a!==13&&a!==0);if(se(a))for(La(e),a=e.input.charCodeAt(e.position),i++,e.lineIndent=0;a===32;)e.lineIndent++,a=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&oi(e,"deficient indentation"),i}f(xt,"skipSeparationSpace");function yi(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||Pt(r)))}f(yi,"testDocumentSeparator");function Aa(e,t){t===1?e.result+=" ":t>1&&(e.result+=Ct.repeat(`
`,t-1))}f(Aa,"writeFoldedLines");function lh(e,t,r){var i,a,n,o,s,l,c,h,u=e.kind,p=e.result,d;if(d=e.input.charCodeAt(e.position),Pt(d)||Ze(d)||d===35||d===38||d===42||d===33||d===124||d===62||d===39||d===34||d===37||d===64||d===96||(d===63||d===45)&&(a=e.input.charCodeAt(e.position+1),Pt(a)||r&&Ze(a)))return!1;for(e.kind="scalar",e.result="",n=o=e.position,s=!1;d!==0;){if(d===58){if(a=e.input.charCodeAt(e.position+1),Pt(a)||r&&Ze(a))break}else if(d===35){if(i=e.input.charCodeAt(e.position-1),Pt(i))break}else{if(e.position===e.lineStart&&yi(e)||r&&Ze(d))break;if(se(d))if(l=e.line,c=e.lineStart,h=e.lineIndent,xt(e,!1,-1),e.lineIndent>=t){s=!0,d=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=l,e.lineStart=c,e.lineIndent=h;break}}s&&(_e(e,n,o,!1),Aa(e,e.line-l),n=o=e.position,s=!1),Pe(d)||(o=e.position+1),d=e.input.charCodeAt(++e.position)}return _e(e,n,o,!1),e.result?!0:(e.kind=u,e.result=p,!1)}f(lh,"readPlainScalar");function ch(e,t){var r,i,a;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=a=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(_e(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,a=e.position;else return!0;else se(r)?(_e(e,i,a,!0),Aa(e,xt(e,!1,t)),i=a=e.position):e.position===e.lineStart&&yi(e)?K(e,"unexpected end of the document within a single quoted scalar"):(e.position++,a=e.position);K(e,"unexpected end of the stream within a single quoted scalar")}f(ch,"readSingleQuotedScalar");function hh(e,t){var r,i,a,n,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return _e(e,r,e.position,!0),e.position++,!0;if(s===92){if(_e(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),se(s))xt(e,!1,t);else if(s<256&&nh[s])e.result+=sh[s],e.position++;else if((o=rh(s))>0){for(a=o,n=0;a>0;a--)s=e.input.charCodeAt(++e.position),(o=eh(s))>=0?n=(n<<4)+o:K(e,"expected hexadecimal character");e.result+=ah(n),e.position++}else K(e,"unknown escape sequence");r=i=e.position}else se(s)?(_e(e,r,i,!0),Aa(e,xt(e,!1,t)),r=i=e.position):e.position===e.lineStart&&yi(e)?K(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}K(e,"unexpected end of the stream within a double quoted scalar")}f(hh,"readDoubleQuotedScalar");function uh(e,t){var r=!0,i,a,n,o=e.tag,s,l=e.anchor,c,h,u,p,d,g=Object.create(null),m,y,x,b;if(b=e.input.charCodeAt(e.position),b===91)h=93,d=!1,s=[];else if(b===123)h=125,d=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),b=e.input.charCodeAt(++e.position);b!==0;){if(xt(e,!0,t),b=e.input.charCodeAt(e.position),b===h)return e.position++,e.tag=o,e.anchor=l,e.kind=d?"mapping":"sequence",e.result=s,!0;r?b===44&&K(e,"expected the node content, but found ','"):K(e,"missed comma between flow collection entries"),y=m=x=null,u=p=!1,b===63&&(c=e.input.charCodeAt(e.position+1),Pt(c)&&(u=p=!0,e.position++,xt(e,!0,t))),i=e.line,a=e.lineStart,n=e.position,tr(e,t,ia,!1,!0),y=e.tag,m=e.result,xt(e,!0,t),b=e.input.charCodeAt(e.position),(p||e.line===i)&&b===58&&(u=!0,b=e.input.charCodeAt(++e.position),xt(e,!0,t),tr(e,t,ia,!1,!0),x=e.result),d?Ke(e,s,g,y,m,x,i,a,n):u?s.push(Ke(e,null,g,y,m,x,i,a,n)):s.push(m),xt(e,!0,t),b=e.input.charCodeAt(e.position),b===44?(r=!0,b=e.input.charCodeAt(++e.position)):r=!1}K(e,"unexpected end of the stream within a flow collection")}f(uh,"readFlowCollection");function dh(e,t){var r,i,a=on,n=!1,o=!1,s=t,l=0,c=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)on===a?a=u===43?Do:Vm:K(e,"repeat of a chomping mode identifier");else if((h=ih(u))>=0)h===0?K(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?K(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(Pe(u)){do u=e.input.charCodeAt(++e.position);while(Pe(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!se(u)&&u!==0)}for(;u!==0;){for(La(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),se(u)){l++;continue}if(e.lineIndent<s){a===Do?e.result+=Ct.repeat(`
`,n?1+l:l):a===on&&n&&(e.result+=`
`);break}for(i?Pe(u)?(c=!0,e.result+=Ct.repeat(`
`,n?1+l:l)):c?(c=!1,e.result+=Ct.repeat(`
`,l+1)):l===0?n&&(e.result+=" "):e.result+=Ct.repeat(`
`,l):e.result+=Ct.repeat(`
`,n?1+l:l),n=!0,o=!0,l=0,r=e.position;!se(u)&&u!==0;)u=e.input.charCodeAt(++e.position);_e(e,r,e.position,!1)}return!0}f(dh,"readBlockScalar");function Mn(e,t){var r,i=e.tag,a=e.anchor,n=[],o,s=!1,l;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=n),l=e.input.charCodeAt(e.position);l!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,K(e,"tab characters must not be used in indentation")),!(l!==45||(o=e.input.charCodeAt(e.position+1),!Pt(o))));){if(s=!0,e.position++,xt(e,!0,-1)&&e.lineIndent<=t){n.push(null),l=e.input.charCodeAt(e.position);continue}if(r=e.line,tr(e,t,Qc,!1,!0),n.push(e.result),xt(e,!0,-1),l=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&l!==0)K(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=a,e.kind="sequence",e.result=n,!0):!1}f(Mn,"readBlockSequence");function fh(e,t,r){var i,a,n,o,s,l,c=e.tag,h=e.anchor,u={},p=Object.create(null),d=null,g=null,m=null,y=!1,x=!1,b;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),b=e.input.charCodeAt(e.position);b!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,K(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),n=e.line,(b===63||b===58)&&Pt(i))b===63?(y&&(Ke(e,u,p,d,g,null,o,s,l),d=g=m=null),x=!0,y=!0,a=!0):y?(y=!1,a=!0):K(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,b=i;else{if(o=e.line,s=e.lineStart,l=e.position,!tr(e,r,Kc,!1,!0))break;if(e.line===n){for(b=e.input.charCodeAt(e.position);Pe(b);)b=e.input.charCodeAt(++e.position);if(b===58)b=e.input.charCodeAt(++e.position),Pt(b)||K(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Ke(e,u,p,d,g,null,o,s,l),d=g=m=null),x=!0,y=!1,a=!1,d=e.tag,g=e.result;else if(x)K(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=c,e.anchor=h,!0}else if(x)K(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=c,e.anchor=h,!0}if((e.line===n||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,l=e.position),tr(e,t,aa,!0,a)&&(y?g=e.result:m=e.result),y||(Ke(e,u,p,d,g,m,o,s,l),d=g=m=null),xt(e,!0,-1),b=e.input.charCodeAt(e.position)),(e.line===n||e.lineIndent>t)&&b!==0)K(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&Ke(e,u,p,d,g,null,o,s,l),x&&(e.tag=c,e.anchor=h,e.kind="mapping",e.result=u),x}f(fh,"readBlockMapping");function ph(e){var t,r=!1,i=!1,a,n,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&K(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,a="!!",o=e.input.charCodeAt(++e.position)):a="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(n=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):K(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!Pt(o);)o===33&&(i?K(e,"tag suffix cannot contain exclamation marks"):(a=e.input.slice(t-1,e.position+1),Jc.test(a)||K(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),Km.test(n)&&K(e,"tag suffix cannot contain flow indicator characters")}n&&!th.test(n)&&K(e,"tag name cannot contain such characters: "+n);try{n=decodeURIComponent(n)}catch{K(e,"tag name is malformed: "+n)}return r?e.tag=n:Ne.call(e.tagMap,a)?e.tag=e.tagMap[a]+n:a==="!"?e.tag="!"+n:a==="!!"?e.tag="tag:yaml.org,2002:"+n:K(e,'undeclared tag handle "'+a+'"'),!0}f(ph,"readTagProperty");function gh(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&K(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!Pt(r)&&!Ze(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&K(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}f(gh,"readAnchorProperty");function mh(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!Pt(i)&&!Ze(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&K(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),Ne.call(e.anchorMap,r)||K(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],xt(e,!0,-1),!0}f(mh,"readAlias");function tr(e,t,r,i,a){var n,o,s,l=1,c=!1,h=!1,u,p,d,g,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,n=o=s=aa===r||Qc===r,i&&xt(e,!0,-1)&&(c=!0,e.lineIndent>t?l=1:e.lineIndent===t?l=0:e.lineIndent<t&&(l=-1)),l===1)for(;ph(e)||gh(e);)xt(e,!0,-1)?(c=!0,s=n,e.lineIndent>t?l=1:e.lineIndent===t?l=0:e.lineIndent<t&&(l=-1)):s=!1;if(s&&(s=c||a),(l===1||aa===r)&&(ia===r||Kc===r?m=t:m=t+1,y=e.position-e.lineStart,l===1?s&&(Mn(e,y)||fh(e,y,m))||uh(e,m)?h=!0:(o&&dh(e,m)||ch(e,m)||hh(e,m)?h=!0:mh(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&K(e,"alias node should not have any properties")):lh(e,m,ia===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):l===0&&(h=s&&Mn(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&K(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,p=e.implicitTypes.length;u<p;u+=1)if(g=e.implicitTypes[u],g.resolve(e.result)){e.result=g.construct(e.result),e.tag=g.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(Ne.call(e.typeMap[e.kind||"fallback"],e.tag))g=e.typeMap[e.kind||"fallback"][e.tag];else for(g=null,d=e.typeMap.multi[e.kind||"fallback"],u=0,p=d.length;u<p;u+=1)if(e.tag.slice(0,d[u].tag.length)===d[u].tag){g=d[u];break}g||K(e,"unknown tag !<"+e.tag+">"),e.result!==null&&g.kind!==e.kind&&K(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+g.kind+'", not "'+e.kind+'"'),g.resolve(e.result,e.tag)?(e.result=g.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):K(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}f(tr,"composeNode");function yh(e){var t=e.position,r,i,a,n=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(xt(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(n=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!Pt(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),a=[],i.length<1&&K(e,"directive name must not be less than one character in length");o!==0;){for(;Pe(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!se(o));break}if(se(o))break;for(r=e.position;o!==0&&!Pt(o);)o=e.input.charCodeAt(++e.position);a.push(e.input.slice(r,e.position))}o!==0&&La(e),Ne.call(Ro,i)?Ro[i](e,i,a):oi(e,'unknown document directive "'+i+'"')}if(xt(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,xt(e,!0,-1)):n&&K(e,"directives end mark is expected"),tr(e,e.lineIndent-1,aa,!1,!0),xt(e,!0,-1),e.checkLineBreaks&&Zm.test(e.input.slice(t,e.position))&&oi(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&yi(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,xt(e,!0,-1));return}if(e.position<e.length-1)K(e,"end of the stream or a document separator is expected");else return}f(yh,"readDocument");function bs(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new oh(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,K(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)yh(r);return r.documents}f(bs,"loadDocuments");function xh(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=bs(e,r);if(typeof t!="function")return i;for(var a=0,n=i.length;a<n;a+=1)t(i[a])}f(xh,"loadAll$1");function bh(e,t){var r=bs(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Yt("expected a single document in the stream, but found more")}}f(bh,"load$1");var Qm=xh,Jm=bh,ty={loadAll:Qm,load:Jm},Ch=Object.prototype.toString,kh=Object.prototype.hasOwnProperty,Cs=65279,ey=9,li=10,ry=13,iy=32,ay=33,ny=34,En=35,sy=37,oy=38,ly=39,cy=42,wh=44,hy=45,na=58,uy=61,dy=62,fy=63,py=64,vh=91,Sh=93,gy=96,Th=123,my=124,_h=125,$t={};$t[0]="\\0";$t[7]="\\a";$t[8]="\\b";$t[9]="\\t";$t[10]="\\n";$t[11]="\\v";$t[12]="\\f";$t[13]="\\r";$t[27]="\\e";$t[34]='\\"';$t[92]="\\\\";$t[133]="\\N";$t[160]="\\_";$t[8232]="\\L";$t[8233]="\\P";var yy=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],xy=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Bh(e,t){var r,i,a,n,o,s,l;if(t===null)return{};for(r={},i=Object.keys(t),a=0,n=i.length;a<n;a+=1)o=i[a],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),l=e.compiledTypeMap.fallback[o],l&&kh.call(l.styleAliases,s)&&(s=l.styleAliases[s]),r[o]=s;return r}f(Bh,"compileStyleMap");function Lh(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new Yt("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Ct.repeat("0",i-t.length)+t}f(Lh,"encodeHex");var by=1,ci=2;function Ah(e){this.schema=e.schema||Zc,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Ct.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Bh(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?ci:by,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}f(Ah,"State");function Fn(e,t){for(var r=Ct.repeat(" ",t),i=0,a=-1,n="",o,s=e.length;i<s;)a=e.indexOf(`
`,i),a===-1?(o=e.slice(i),i=s):(o=e.slice(i,a+1),i=a+1),o.length&&o!==`
`&&(n+=r),n+=o;return n}f(Fn,"indentString");function sa(e,t){return`
`+Ct.repeat(" ",e.indent*t)}f(sa,"generateNextLine");function Mh(e,t){var r,i,a;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(a=e.implicitTypes[r],a.resolve(t))return!0;return!1}f(Mh,"testImplicitResolving");function hi(e){return e===iy||e===ey}f(hi,"isWhitespace");function _r(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Cs||65536<=e&&e<=1114111}f(_r,"isPrintable");function $n(e){return _r(e)&&e!==Cs&&e!==ry&&e!==li}f($n,"isNsCharOrWhitespace");function On(e,t,r){var i=$n(e),a=i&&!hi(e);return(r?i:i&&e!==wh&&e!==vh&&e!==Sh&&e!==Th&&e!==_h)&&e!==En&&!(t===na&&!a)||$n(t)&&!hi(t)&&e===En||t===na&&a}f(On,"isPlainSafe");function Eh(e){return _r(e)&&e!==Cs&&!hi(e)&&e!==hy&&e!==fy&&e!==na&&e!==wh&&e!==vh&&e!==Sh&&e!==Th&&e!==_h&&e!==En&&e!==oy&&e!==cy&&e!==ay&&e!==my&&e!==uy&&e!==dy&&e!==ly&&e!==ny&&e!==sy&&e!==py&&e!==gy}f(Eh,"isPlainSafeFirst");function Fh(e){return!hi(e)&&e!==na}f(Fh,"isPlainSafeLast");function yr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}f(yr,"codePointAt");function ks(e){var t=/^\n* /;return t.test(e)}f(ks,"needIndentIndicator");var $h=1,Dn=2,Oh=3,Dh=4,gr=5;function Rh(e,t,r,i,a,n,o,s){var l,c=0,h=null,u=!1,p=!1,d=i!==-1,g=-1,m=Eh(yr(e,0))&&Fh(yr(e,e.length-1));if(t||o)for(l=0;l<e.length;c>=65536?l+=2:l++){if(c=yr(e,l),!_r(c))return gr;m=m&&On(c,h,s),h=c}else{for(l=0;l<e.length;c>=65536?l+=2:l++){if(c=yr(e,l),c===li)u=!0,d&&(p=p||l-g-1>i&&e[g+1]!==" ",g=l);else if(!_r(c))return gr;m=m&&On(c,h,s),h=c}p=p||d&&l-g-1>i&&e[g+1]!==" "}return!u&&!p?m&&!o&&!a(e)?$h:n===ci?gr:Dn:r>9&&ks(e)?gr:o?n===ci?gr:Dn:p?Dh:Oh}f(Rh,"chooseScalarStyle");function Ih(e,t,r,i,a){e.dump=function(){if(t.length===0)return e.quotingType===ci?'""':"''";if(!e.noCompatMode&&(yy.indexOf(t)!==-1||xy.test(t)))return e.quotingType===ci?'"'+t+'"':"'"+t+"'";var n=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-n),s=i||e.flowLevel>-1&&r>=e.flowLevel;function l(c){return Mh(e,c)}switch(f(l,"testAmbiguity"),Rh(t,s,e.indent,o,l,e.quotingType,e.forceQuotes&&!i,a)){case $h:return t;case Dn:return"'"+t.replace(/'/g,"''")+"'";case Oh:return"|"+Rn(t,e.indent)+In(Fn(t,n));case Dh:return">"+Rn(t,e.indent)+In(Fn(Ph(t,o),n));case gr:return'"'+Nh(t)+'"';default:throw new Yt("impossible error: invalid scalar style")}}()}f(Ih,"writeScalar");function Rn(e,t){var r=ks(e)?String(t):"",i=e[e.length-1]===`
`,a=i&&(e[e.length-2]===`
`||e===`
`),n=a?"+":i?"":"-";return r+n+`
`}f(Rn,"blockHeader");function In(e){return e[e.length-1]===`
`?e.slice(0,-1):e}f(In,"dropEndingNewline");function Ph(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var c=e.indexOf(`
`);return c=c!==-1?c:e.length,r.lastIndex=c,Pn(e.slice(0,c),t)}(),a=e[0]===`
`||e[0]===" ",n,o;o=r.exec(e);){var s=o[1],l=o[2];n=l[0]===" ",i+=s+(!a&&!n&&l!==""?`
`:"")+Pn(l,t),a=n}return i}f(Ph,"foldString");function Pn(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,a=0,n,o=0,s=0,l="";i=r.exec(e);)s=i.index,s-a>t&&(n=o>a?o:s,l+=`
`+e.slice(a,n),a=n+1),o=s;return l+=`
`,e.length-a>t&&o>a?l+=e.slice(a,o)+`
`+e.slice(o+1):l+=e.slice(a),l.slice(1)}f(Pn,"foldLine");function Nh(e){for(var t="",r=0,i,a=0;a<e.length;r>=65536?a+=2:a++)r=yr(e,a),i=$t[r],!i&&_r(r)?(t+=e[a],r>=65536&&(t+=e[a+1])):t+=i||Lh(r);return t}f(Nh,"escapeString");function zh(e,t,r){var i="",a=e.tag,n,o,s;for(n=0,o=r.length;n<o;n+=1)s=r[n],e.replacer&&(s=e.replacer.call(r,String(n),s)),(me(e,t,s,!1,!1)||typeof s>"u"&&me(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=a,e.dump="["+i+"]"}f(zh,"writeFlowSequence");function Nn(e,t,r,i){var a="",n=e.tag,o,s,l;for(o=0,s=r.length;o<s;o+=1)l=r[o],e.replacer&&(l=e.replacer.call(r,String(o),l)),(me(e,t+1,l,!0,!0,!1,!0)||typeof l>"u"&&me(e,t+1,null,!0,!0,!1,!0))&&((!i||a!=="")&&(a+=sa(e,t)),e.dump&&li===e.dump.charCodeAt(0)?a+="-":a+="- ",a+=e.dump);e.tag=n,e.dump=a||"[]"}f(Nn,"writeBlockSequence");function Wh(e,t,r){var i="",a=e.tag,n=Object.keys(r),o,s,l,c,h;for(o=0,s=n.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),l=n[o],c=r[l],e.replacer&&(c=e.replacer.call(r,l,c)),me(e,t,l,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),me(e,t,c,!1,!1)&&(h+=e.dump,i+=h));e.tag=a,e.dump="{"+i+"}"}f(Wh,"writeFlowMapping");function qh(e,t,r,i){var a="",n=e.tag,o=Object.keys(r),s,l,c,h,u,p;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new Yt("sortKeys must be a boolean or a function");for(s=0,l=o.length;s<l;s+=1)p="",(!i||a!=="")&&(p+=sa(e,t)),c=o[s],h=r[c],e.replacer&&(h=e.replacer.call(r,c,h)),me(e,t+1,c,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&li===e.dump.charCodeAt(0)?p+="?":p+="? "),p+=e.dump,u&&(p+=sa(e,t)),me(e,t+1,h,!0,u)&&(e.dump&&li===e.dump.charCodeAt(0)?p+=":":p+=": ",p+=e.dump,a+=p));e.tag=n,e.dump=a||"{}"}f(qh,"writeBlockMapping");function zn(e,t,r){var i,a,n,o,s,l;for(a=r?e.explicitTypes:e.implicitTypes,n=0,o=a.length;n<o;n+=1)if(s=a[n],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(l=e.styleMap[s.tag]||s.defaultStyle,Ch.call(s.represent)==="[object Function]")i=s.represent(t,l);else if(kh.call(s.represent,l))i=s.represent[l](t,l);else throw new Yt("!<"+s.tag+'> tag resolver accepts not "'+l+'" style');e.dump=i}return!0}return!1}f(zn,"detectType");function me(e,t,r,i,a,n,o){e.tag=null,e.dump=r,zn(e,r,!1)||zn(e,r,!0);var s=Ch.call(e.dump),l=i,c;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,p;if(h&&(u=e.duplicates.indexOf(r),p=u!==-1),(e.tag!==null&&e.tag!=="?"||p||e.indent!==2&&t>0)&&(a=!1),p&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&p&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(qh(e,t,e.dump,a),p&&(e.dump="&ref_"+u+e.dump)):(Wh(e,t,e.dump),p&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?Nn(e,t-1,e.dump,a):Nn(e,t,e.dump,a),p&&(e.dump="&ref_"+u+e.dump)):(zh(e,t,e.dump),p&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&Ih(e,e.dump,t,n,l);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Yt("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(c=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?c="!"+c:c.slice(0,18)==="tag:yaml.org,2002:"?c="!!"+c.slice(18):c="!<"+c+">",e.dump=c+" "+e.dump)}return!0}f(me,"writeNode");function Hh(e,t){var r=[],i=[],a,n;for(oa(e,r,i),a=0,n=i.length;a<n;a+=1)t.duplicates.push(r[i[a]]);t.usedDuplicates=new Array(n)}f(Hh,"getDuplicateReferences");function oa(e,t,r){var i,a,n;if(e!==null&&typeof e=="object")if(a=t.indexOf(e),a!==-1)r.indexOf(a)===-1&&r.push(a);else if(t.push(e),Array.isArray(e))for(a=0,n=e.length;a<n;a+=1)oa(e[a],t,r);else for(i=Object.keys(e),a=0,n=i.length;a<n;a+=1)oa(e[i[a]],t,r)}f(oa,"inspectNode");function Cy(e,t){t=t||{};var r=new Ah(t);r.noRefs||Hh(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),me(r,0,i,!0,!0)?r.dump+`
`:""}f(Cy,"dump$1");function ky(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}f(ky,"renamed");var wy=$c,vy=ty.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/const Sy=Object.freeze({left:0,top:0,width:16,height:16}),la=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),jh=Object.freeze({...Sy,...la}),Ty=Object.freeze({...jh,body:"",hidden:!1}),_y=Object.freeze({width:null,height:null}),By=Object.freeze({..._y,...la}),Ly=(e,t,r,i="")=>{const a=e.split(":");if(e.slice(0,1)==="@"){if(a.length<2||a.length>3)return null;i=a.shift().slice(1)}if(a.length>3||!a.length)return null;if(a.length>1){const s=a.pop(),l=a.pop(),c={provider:a.length>0?a[0]:i,prefix:l,name:s};return ln(c)?c:null}const n=a[0],o=n.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return ln(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:n};return ln(s,r)?s:null}return null},ln=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function Ay(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function Io(e,t){const r=Ay(e,t);for(const i in Ty)i in la?i in e&&!(i in r)&&(r[i]=la[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function My(e,t){const r=e.icons,i=e.aliases||Object.create(null),a=Object.create(null);function n(o){if(r[o])return a[o]=[];if(!(o in a)){a[o]=null;const s=i[o]&&i[o].parent,l=s&&n(s);l&&(a[o]=[s].concat(l))}return a[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(n),a}function Po(e,t,r){const i=e.icons,a=e.aliases||Object.create(null);let n={};function o(s){n=Io(i[s]||a[s],n)}return o(t),r.forEach(o),Io(e,n)}function Ey(e,t){if(e.icons[t])return Po(e,t,[]);const r=My(e,[t])[t];return r?Po(e,t,r):null}const Fy=/(-?[0-9.]*[0-9]+[0-9.]*)/g,$y=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function No(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(Fy);if(i===null||!i.length)return e;const a=[];let n=i.shift(),o=$y.test(n);for(;;){if(o){const s=parseFloat(n);isNaN(s)?a.push(n):a.push(Math.ceil(s*t*r)/r)}else a.push(n);if(n=i.shift(),n===void 0)return a.join("");o=!o}}function Oy(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const a=e.indexOf(">",i),n=e.indexOf("</"+t);if(a===-1||n===-1)break;const o=e.indexOf(">",n);if(o===-1)break;r+=e.slice(a+1,n).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function Dy(e,t){return e?"<defs>"+e+"</defs>"+t:t}function Ry(e,t,r){const i=Oy(e);return Dy(i.defs,t+i.content+r)}const Iy=e=>e==="unset"||e==="undefined"||e==="none";function Py(e,t){const r={...jh,...e},i={...By,...t},a={left:r.left,top:r.top,width:r.width,height:r.height};let n=r.body;[r,i].forEach(m=>{const y=[],x=m.hFlip,b=m.vFlip;let k=m.rotate;x?b?k+=2:(y.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")"),y.push("scale(-1 1)"),a.top=a.left=0):b&&(y.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")"),y.push("scale(1 -1)"),a.top=a.left=0);let v;switch(k<0&&(k-=Math.floor(k/4)*4),k=k%4,k){case 1:v=a.height/2+a.top,y.unshift("rotate(90 "+v.toString()+" "+v.toString()+")");break;case 2:y.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:v=a.width/2+a.left,y.unshift("rotate(-90 "+v.toString()+" "+v.toString()+")");break}k%2===1&&(a.left!==a.top&&(v=a.left,a.left=a.top,a.top=v),a.width!==a.height&&(v=a.width,a.width=a.height,a.height=v)),y.length&&(n=Ry(n,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,l=a.width,c=a.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?c:s,h=No(u,l/c)):(h=o==="auto"?l:o,u=s===null?No(h,c/l):s==="auto"?c:s);const p={},d=(m,y)=>{Iy(y)||(p[m]=y.toString())};d("width",h),d("height",u);const g=[a.left,a.top,l,c];return p.viewBox=g.join(" "),{attributes:p,viewBox:g,body:n}}const Ny=/\sid="(\S+)"/g,zy="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Wy=0;function qy(e,t=zy){const r=[];let i;for(;i=Ny.exec(e);)r.push(i[1]);if(!r.length)return e;const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(n=>{const o=typeof t=="function"?t(n):t+(Wy++).toString(),s=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+a+"$3")}),e=e.replace(new RegExp(a,"g"),""),e}function Hy(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}var jy={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},Wn=new Map,Uh=new Map,Uy=f(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if($.debug("Registering icon pack:",t.name),"loader"in t)Uh.set(t.name,t.loader);else if("icons"in t)Wn.set(t.name,t.icons);else throw $.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),Yy=f(async(e,t)=>{const r=Ly(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let a=Wn.get(i);if(!a){const o=Uh.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{a={...await o(),prefix:i},Wn.set(i,a)}catch(s){throw $.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const n=Ey(a,r.name);if(!n)throw new Error(`Icon not found: ${e}`);return n},"getRegisteredIconData"),Ma=f(async(e,t)=>{let r;try{r=await Yy(e,t?.fallbackPrefix)}catch(n){$.error(n),r=jy}const i=Py(r,t);return Hy(qy(i.body),i.attributes)},"getIconSVG"),ws=f(({flowchart:e})=>{const t=e?.subGraphTitleMargin?.top??0,r=e?.subGraphTitleMargin?.bottom??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins"),vs={},vt={};Object.defineProperty(vt,"__esModule",{value:!0});vt.BLANK_URL=vt.relativeFirstCharacters=vt.whitespaceEscapeCharsRegex=vt.urlSchemeRegex=vt.ctrlCharactersRegex=vt.htmlCtrlEntityRegex=vt.htmlEntitiesRegex=vt.invalidProtocolRegex=void 0;vt.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;vt.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;vt.htmlCtrlEntityRegex=/&(newline|tab);/gi;vt.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;vt.urlSchemeRegex=/^.+(:|&colon;)/gim;vt.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;vt.relativeFirstCharacters=[".","/"];vt.BLANK_URL="about:blank";Object.defineProperty(vs,"__esModule",{value:!0});var Yh=vs.sanitizeUrl=void 0,Et=vt;function Gy(e){return Et.relativeFirstCharacters.indexOf(e[0])>-1}function Vy(e){var t=e.replace(Et.ctrlCharactersRegex,"");return t.replace(Et.htmlEntitiesRegex,function(r,i){return String.fromCharCode(i)})}function Xy(e){return URL.canParse(e)}function zo(e){try{return decodeURIComponent(e)}catch{return e}}function Zy(e){if(!e)return Et.BLANK_URL;var t,r=zo(e.trim());do r=Vy(r).replace(Et.htmlCtrlEntityRegex,"").replace(Et.ctrlCharactersRegex,"").replace(Et.whitespaceEscapeCharsRegex,"").trim(),r=zo(r),t=r.match(Et.ctrlCharactersRegex)||r.match(Et.htmlEntitiesRegex)||r.match(Et.htmlCtrlEntityRegex)||r.match(Et.whitespaceEscapeCharsRegex);while(t&&t.length>0);var i=r;if(!i)return Et.BLANK_URL;if(Gy(i))return i;var a=i.trimStart(),n=a.match(Et.urlSchemeRegex);if(!n)return i;var o=n[0].toLowerCase().trim();if(Et.invalidProtocolRegex.test(o))return Et.BLANK_URL;var s=a.replace(/\\/g,"/");if(o==="mailto:"||o.includes("://"))return s;if(o==="http:"||o==="https:"){if(!Xy(s))return Et.BLANK_URL;var l=new URL(s);return l.protocol=l.protocol.toLowerCase(),l.hostname=l.hostname.toLowerCase(),l.toString()}return s}Yh=vs.sanitizeUrl=Zy;function Wo(e,t,r){var i=new Cp;return t=t==null?0:+t,i.restart(a=>{i.stop(),e(a+t)},t,r),i}var Ky=bp("start","end","cancel","interrupt"),Qy=[],Gh=0,qo=1,qn=2,ji=3,Ho=4,Hn=5,Ui=6;function Ea(e,t,r,i,a,n){var o=e.__transition;if(!o)e.__transition={};else if(r in o)return;Jy(e,r,{name:t,index:i,group:a,on:Ky,tween:Qy,time:n.time,delay:n.delay,duration:n.duration,ease:n.ease,timer:null,state:Gh})}function Ss(e,t){var r=le(e,t);if(r.state>Gh)throw new Error("too late; already scheduled");return r}function ye(e,t){var r=le(e,t);if(r.state>ji)throw new Error("too late; already running");return r}function le(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function Jy(e,t,r){var i=e.__transition,a;i[t]=r,r.timer=kp(n,0,r.time);function n(c){r.state=qo,r.timer.restart(o,r.delay,r.time),r.delay<=c&&o(c-r.delay)}function o(c){var h,u,p,d;if(r.state!==qo)return l();for(h in i)if(d=i[h],d.name===r.name){if(d.state===ji)return Wo(o);d.state===Ho?(d.state=Ui,d.timer.stop(),d.on.call("interrupt",e,e.__data__,d.index,d.group),delete i[h]):+h<t&&(d.state=Ui,d.timer.stop(),d.on.call("cancel",e,e.__data__,d.index,d.group),delete i[h])}if(Wo(function(){r.state===ji&&(r.state=Ho,r.timer.restart(s,r.delay,r.time),s(c))}),r.state=qn,r.on.call("start",e,e.__data__,r.index,r.group),r.state===qn){for(r.state=ji,a=new Array(p=r.tween.length),h=0,u=-1;h<p;++h)(d=r.tween[h].value.call(e,e.__data__,r.index,r.group))&&(a[++u]=d);a.length=u+1}}function s(c){for(var h=c<r.duration?r.ease.call(null,c/r.duration):(r.timer.restart(l),r.state=Hn,1),u=-1,p=a.length;++u<p;)a[u].call(e,h);r.state===Hn&&(r.on.call("end",e,e.__data__,r.index,r.group),l())}function l(){r.state=Ui,r.timer.stop(),delete i[t];for(var c in i)return;delete e.__transition}}function t0(e,t){var r=e.__transition,i,a,n=!0,o;if(r){t=t==null?null:t+"";for(o in r){if((i=r[o]).name!==t){n=!1;continue}a=i.state>qn&&i.state<Hn,i.state=Ui,i.timer.stop(),i.on.call(a?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[o]}n&&delete e.__transition}}function e0(e){return this.each(function(){t0(this,e)})}function r0(e,t){var r,i;return function(){var a=ye(this,e),n=a.tween;if(n!==r){i=r=n;for(var o=0,s=i.length;o<s;++o)if(i[o].name===t){i=i.slice(),i.splice(o,1);break}}a.tween=i}}function i0(e,t,r){var i,a;if(typeof r!="function")throw new Error;return function(){var n=ye(this,e),o=n.tween;if(o!==i){a=(i=o).slice();for(var s={name:t,value:r},l=0,c=a.length;l<c;++l)if(a[l].name===t){a[l]=s;break}l===c&&a.push(s)}n.tween=a}}function a0(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=le(this.node(),r).tween,a=0,n=i.length,o;a<n;++a)if((o=i[a]).name===e)return o.value;return null}return this.each((t==null?r0:i0)(r,e,t))}function Ts(e,t,r){var i=e._id;return e.each(function(){var a=ye(this,i);(a.value||(a.value={}))[t]=r.apply(this,arguments)}),function(a){return le(a,i).value[t]}}function Vh(e,t){var r;return(typeof t=="number"?wp:t instanceof bo?Co:(r=bo(t))?(t=r,Co):vp)(e,t)}function n0(e){return function(){this.removeAttribute(e)}}function s0(e){return function(){this.removeAttributeNS(e.space,e.local)}}function o0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttribute(e);return o===a?null:o===i?n:n=t(i=o,r)}}function l0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttributeNS(e.space,e.local);return o===a?null:o===i?n:n=t(i=o,r)}}function c0(e,t,r){var i,a,n;return function(){var o,s=r(this),l;return s==null?void this.removeAttribute(e):(o=this.getAttribute(e),l=s+"",o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s)))}}function h0(e,t,r){var i,a,n;return function(){var o,s=r(this),l;return s==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),l=s+"",o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s)))}}function u0(e,t){var r=Dl(e),i=r==="transform"?Sp:Vh;return this.attrTween(e,typeof t=="function"?(r.local?h0:c0)(r,i,Ts(this,"attr."+e,t)):t==null?(r.local?s0:n0)(r):(r.local?l0:o0)(r,i,t))}function d0(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function f0(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function p0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&f0(e,n)),r}return a._value=t,a}function g0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&d0(e,n)),r}return a._value=t,a}function m0(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=Dl(e);return this.tween(r,(i.local?p0:g0)(i,t))}function y0(e,t){return function(){Ss(this,e).delay=+t.apply(this,arguments)}}function x0(e,t){return t=+t,function(){Ss(this,e).delay=t}}function b0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?y0:x0)(t,e)):le(this.node(),t).delay}function C0(e,t){return function(){ye(this,e).duration=+t.apply(this,arguments)}}function k0(e,t){return t=+t,function(){ye(this,e).duration=t}}function w0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?C0:k0)(t,e)):le(this.node(),t).duration}function v0(e,t){if(typeof t!="function")throw new Error;return function(){ye(this,e).ease=t}}function S0(e){var t=this._id;return arguments.length?this.each(v0(t,e)):le(this.node(),t).ease}function T0(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;ye(this,e).ease=r}}function _0(e){if(typeof e!="function")throw new Error;return this.each(T0(this._id,e))}function B0(e){typeof e!="function"&&(e=Hp(e));for(var t=this._groups,r=t.length,i=new Array(r),a=0;a<r;++a)for(var n=t[a],o=n.length,s=i[a]=[],l,c=0;c<o;++c)(l=n[c])&&e.call(l,l.__data__,c,n)&&s.push(l);return new Be(i,this._parents,this._name,this._id)}function L0(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,a=r.length,n=Math.min(i,a),o=new Array(i),s=0;s<n;++s)for(var l=t[s],c=r[s],h=l.length,u=o[s]=new Array(h),p,d=0;d<h;++d)(p=l[d]||c[d])&&(u[d]=p);for(;s<i;++s)o[s]=t[s];return new Be(o,this._parents,this._name,this._id)}function A0(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function M0(e,t,r){var i,a,n=A0(t)?Ss:ye;return function(){var o=n(this,e),s=o.on;s!==i&&(a=(i=s).copy()).on(t,r),o.on=a}}function E0(e,t){var r=this._id;return arguments.length<2?le(this.node(),r).on.on(e):this.each(M0(r,e,t))}function F0(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function $0(){return this.on("end.remove",F0(this._id))}function O0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=jp(e));for(var i=this._groups,a=i.length,n=new Array(a),o=0;o<a;++o)for(var s=i[o],l=s.length,c=n[o]=new Array(l),h,u,p=0;p<l;++p)(h=s[p])&&(u=e.call(h,h.__data__,p,s))&&("__data__"in h&&(u.__data__=h.__data__),c[p]=u,Ea(c[p],t,r,p,c,le(h,r)));return new Be(n,this._parents,t,r)}function D0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=Up(e));for(var i=this._groups,a=i.length,n=[],o=[],s=0;s<a;++s)for(var l=i[s],c=l.length,h,u=0;u<c;++u)if(h=l[u]){for(var p=e.call(h,h.__data__,u,l),d,g=le(h,r),m=0,y=p.length;m<y;++m)(d=p[m])&&Ea(d,t,r,m,p,g);n.push(p),o.push(h)}return new Be(n,o,t,r)}var R0=va.prototype.constructor;function I0(){return new R0(this._groups,this._parents)}function P0(e,t){var r,i,a;return function(){var n=si(this,e),o=(this.style.removeProperty(e),si(this,e));return n===o?null:n===r&&o===i?a:a=t(r=n,i=o)}}function Xh(e){return function(){this.style.removeProperty(e)}}function N0(e,t,r){var i,a=r+"",n;return function(){var o=si(this,e);return o===a?null:o===i?n:n=t(i=o,r)}}function z0(e,t,r){var i,a,n;return function(){var o=si(this,e),s=r(this),l=s+"";return s==null&&(l=s=(this.style.removeProperty(e),si(this,e))),o===l?null:o===i&&l===a?n:(a=l,n=t(i=o,s))}}function W0(e,t){var r,i,a,n="style."+t,o="end."+n,s;return function(){var l=ye(this,e),c=l.on,h=l.value[n]==null?s||(s=Xh(t)):void 0;(c!==r||a!==h)&&(i=(r=c).copy()).on(o,a=h),l.on=i}}function q0(e,t,r){var i=(e+="")=="transform"?Tp:Vh;return t==null?this.styleTween(e,P0(e,i)).on("end.style."+e,Xh(e)):typeof t=="function"?this.styleTween(e,z0(e,i,Ts(this,"style."+e,t))).each(W0(this._id,e)):this.styleTween(e,N0(e,i,t),r).on("end.style."+e,null)}function H0(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function j0(e,t,r){var i,a;function n(){var o=t.apply(this,arguments);return o!==a&&(i=(a=o)&&H0(e,o,r)),i}return n._value=t,n}function U0(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,j0(e,t,r??""))}function Y0(e){return function(){this.textContent=e}}function G0(e){return function(){var t=e(this);this.textContent=t??""}}function V0(e){return this.tween("text",typeof e=="function"?G0(Ts(this,"text",e)):Y0(e==null?"":e+""))}function X0(e){return function(t){this.textContent=e.call(this,t)}}function Z0(e){var t,r;function i(){var a=e.apply(this,arguments);return a!==r&&(t=(r=a)&&X0(a)),t}return i._value=e,i}function K0(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Z0(e))}function Q0(){for(var e=this._name,t=this._id,r=Zh(),i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,l,c=0;c<s;++c)if(l=o[c]){var h=le(l,t);Ea(l,e,r,c,o,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new Be(i,this._parents,e,r)}function J0(){var e,t,r=this,i=r._id,a=r.size();return new Promise(function(n,o){var s={value:o},l={value:function(){--a===0&&n()}};r.each(function(){var c=ye(this,i),h=c.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),c.on=t}),a===0&&n()})}var tx=0;function Be(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function Zh(){return++tx}var we=va.prototype;Be.prototype={constructor:Be,select:O0,selectAll:D0,selectChild:we.selectChild,selectChildren:we.selectChildren,filter:B0,merge:L0,selection:I0,transition:Q0,call:we.call,nodes:we.nodes,node:we.node,size:we.size,empty:we.empty,each:we.each,on:E0,attr:u0,attrTween:m0,style:q0,styleTween:U0,text:V0,textTween:K0,remove:$0,tween:a0,delay:b0,duration:w0,ease:S0,easeVarying:_0,end:J0,[Symbol.iterator]:we[Symbol.iterator]};function ex(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var rx={time:null,delay:0,duration:250,ease:ex};function ix(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function ax(e){var t,r;e instanceof Be?(t=e._id,e=e._name):(t=Zh(),(r=rx).time=_p(),e=e==null?null:e+"");for(var i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,l,c=0;c<s;++c)(l=o[c])&&Ea(l,e,t,c,o,r||ix(l,t));return new Be(i,this._parents,e,t)}va.prototype.interrupt=e0;va.prototype.transition=ax;class Kh{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function nx(e){return new Kh(e,!0)}function sx(e){return new Kh(e,!1)}function Zr(e,t,r){this.k=e,this.x=t,this.y=r}Zr.prototype={constructor:Zr,scale:function(e){return e===1?this:new Zr(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Zr(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};Zr.prototype;var Qh=typeof global=="object"&&global&&global.Object===Object&&global,ox=typeof self=="object"&&self&&self.Object===Object&&self,xe=Qh||ox||Function("return this")(),ca=xe.Symbol,Jh=Object.prototype,lx=Jh.hasOwnProperty,cx=Jh.toString,Hr=ca?ca.toStringTag:void 0;function hx(e){var t=lx.call(e,Hr),r=e[Hr];try{e[Hr]=void 0;var i=!0}catch{}var a=cx.call(e);return i&&(t?e[Hr]=r:delete e[Hr]),a}var ux=Object.prototype,dx=ux.toString;function fx(e){return dx.call(e)}var px="[object Null]",gx="[object Undefined]",jo=ca?ca.toStringTag:void 0;function Mr(e){return e==null?e===void 0?gx:px:jo&&jo in Object(e)?hx(e):fx(e)}function ar(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var mx="[object AsyncFunction]",yx="[object Function]",xx="[object GeneratorFunction]",bx="[object Proxy]";function _s(e){if(!ar(e))return!1;var t=Mr(e);return t==yx||t==xx||t==mx||t==bx}var cn=xe["__core-js_shared__"],Uo=function(){var e=/[^.]+$/.exec(cn&&cn.keys&&cn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Cx(e){return!!Uo&&Uo in e}var kx=Function.prototype,wx=kx.toString;function nr(e){if(e!=null){try{return wx.call(e)}catch{}try{return e+""}catch{}}return""}var vx=/[\\^$.*+?()[\]{}|]/g,Sx=/^\[object .+?Constructor\]$/,Tx=Function.prototype,_x=Object.prototype,Bx=Tx.toString,Lx=_x.hasOwnProperty,Ax=RegExp("^"+Bx.call(Lx).replace(vx,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Mx(e){if(!ar(e)||Cx(e))return!1;var t=_s(e)?Ax:Sx;return t.test(nr(e))}function Ex(e,t){return e?.[t]}function sr(e,t){var r=Ex(e,t);return Mx(r)?r:void 0}var ui=sr(Object,"create");function Fx(){this.__data__=ui?ui(null):{},this.size=0}function $x(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Ox="__lodash_hash_undefined__",Dx=Object.prototype,Rx=Dx.hasOwnProperty;function Ix(e){var t=this.__data__;if(ui){var r=t[e];return r===Ox?void 0:r}return Rx.call(t,e)?t[e]:void 0}var Px=Object.prototype,Nx=Px.hasOwnProperty;function zx(e){var t=this.__data__;return ui?t[e]!==void 0:Nx.call(t,e)}var Wx="__lodash_hash_undefined__";function qx(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=ui&&t===void 0?Wx:t,this}function er(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}er.prototype.clear=Fx;er.prototype.delete=$x;er.prototype.get=Ix;er.prototype.has=zx;er.prototype.set=qx;function Hx(){this.__data__=[],this.size=0}function Fa(e,t){return e===t||e!==e&&t!==t}function $a(e,t){for(var r=e.length;r--;)if(Fa(e[r][0],t))return r;return-1}var jx=Array.prototype,Ux=jx.splice;function Yx(e){var t=this.__data__,r=$a(t,e);if(r<0)return!1;var i=t.length-1;return r==i?t.pop():Ux.call(t,r,1),--this.size,!0}function Gx(e){var t=this.__data__,r=$a(t,e);return r<0?void 0:t[r][1]}function Vx(e){return $a(this.__data__,e)>-1}function Xx(e,t){var r=this.__data__,i=$a(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this}function Me(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}Me.prototype.clear=Hx;Me.prototype.delete=Yx;Me.prototype.get=Gx;Me.prototype.has=Vx;Me.prototype.set=Xx;var di=sr(xe,"Map");function Zx(){this.size=0,this.__data__={hash:new er,map:new(di||Me),string:new er}}function Kx(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Oa(e,t){var r=e.__data__;return Kx(t)?r[typeof t=="string"?"string":"hash"]:r.map}function Qx(e){var t=Oa(this,e).delete(e);return this.size-=t?1:0,t}function Jx(e){return Oa(this,e).get(e)}function tb(e){return Oa(this,e).has(e)}function eb(e,t){var r=Oa(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this}function qe(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}qe.prototype.clear=Zx;qe.prototype.delete=Qx;qe.prototype.get=Jx;qe.prototype.has=tb;qe.prototype.set=eb;var rb="Expected a function";function xi(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(rb);var r=function(){var i=arguments,a=t?t.apply(this,i):i[0],n=r.cache;if(n.has(a))return n.get(a);var o=e.apply(this,i);return r.cache=n.set(a,o)||n,o};return r.cache=new(xi.Cache||qe),r}xi.Cache=qe;function ib(){this.__data__=new Me,this.size=0}function ab(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function nb(e){return this.__data__.get(e)}function sb(e){return this.__data__.has(e)}var ob=200;function lb(e,t){var r=this.__data__;if(r instanceof Me){var i=r.__data__;if(!di||i.length<ob-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new qe(i)}return r.set(e,t),this.size=r.size,this}function Er(e){var t=this.__data__=new Me(e);this.size=t.size}Er.prototype.clear=ib;Er.prototype.delete=ab;Er.prototype.get=nb;Er.prototype.has=sb;Er.prototype.set=lb;var ha=function(){try{var e=sr(Object,"defineProperty");return e({},"",{}),e}catch{}}();function Bs(e,t,r){t=="__proto__"&&ha?ha(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function jn(e,t,r){(r!==void 0&&!Fa(e[t],r)||r===void 0&&!(t in e))&&Bs(e,t,r)}function cb(e){return function(t,r,i){for(var a=-1,n=Object(t),o=i(t),s=o.length;s--;){var l=o[++a];if(r(n[l],l,n)===!1)break}return t}}var hb=cb(),tu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Yo=tu&&typeof module=="object"&&module&&!module.nodeType&&module,ub=Yo&&Yo.exports===tu,Go=ub?xe.Buffer:void 0,Vo=Go?Go.allocUnsafe:void 0;function db(e,t){if(t)return e.slice();var r=e.length,i=Vo?Vo(r):new e.constructor(r);return e.copy(i),i}var Xo=xe.Uint8Array;function fb(e){var t=new e.constructor(e.byteLength);return new Xo(t).set(new Xo(e)),t}function pb(e,t){var r=t?fb(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function gb(e,t){var r=-1,i=e.length;for(t||(t=Array(i));++r<i;)t[r]=e[r];return t}var Zo=Object.create,mb=function(){function e(){}return function(t){if(!ar(t))return{};if(Zo)return Zo(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function eu(e,t){return function(r){return e(t(r))}}var ru=eu(Object.getPrototypeOf,Object),yb=Object.prototype;function Da(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||yb;return e===r}function xb(e){return typeof e.constructor=="function"&&!Da(e)?mb(ru(e)):{}}function bi(e){return e!=null&&typeof e=="object"}var bb="[object Arguments]";function Ko(e){return bi(e)&&Mr(e)==bb}var iu=Object.prototype,Cb=iu.hasOwnProperty,kb=iu.propertyIsEnumerable,ua=Ko(function(){return arguments}())?Ko:function(e){return bi(e)&&Cb.call(e,"callee")&&!kb.call(e,"callee")},da=Array.isArray,wb=9007199254740991;function au(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=wb}function Ra(e){return e!=null&&au(e.length)&&!_s(e)}function vb(e){return bi(e)&&Ra(e)}function Sb(){return!1}var nu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Qo=nu&&typeof module=="object"&&module&&!module.nodeType&&module,Tb=Qo&&Qo.exports===nu,Jo=Tb?xe.Buffer:void 0,_b=Jo?Jo.isBuffer:void 0,Ls=_b||Sb,Bb="[object Object]",Lb=Function.prototype,Ab=Object.prototype,su=Lb.toString,Mb=Ab.hasOwnProperty,Eb=su.call(Object);function Fb(e){if(!bi(e)||Mr(e)!=Bb)return!1;var t=ru(e);if(t===null)return!0;var r=Mb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&su.call(r)==Eb}var $b="[object Arguments]",Ob="[object Array]",Db="[object Boolean]",Rb="[object Date]",Ib="[object Error]",Pb="[object Function]",Nb="[object Map]",zb="[object Number]",Wb="[object Object]",qb="[object RegExp]",Hb="[object Set]",jb="[object String]",Ub="[object WeakMap]",Yb="[object ArrayBuffer]",Gb="[object DataView]",Vb="[object Float32Array]",Xb="[object Float64Array]",Zb="[object Int8Array]",Kb="[object Int16Array]",Qb="[object Int32Array]",Jb="[object Uint8Array]",tC="[object Uint8ClampedArray]",eC="[object Uint16Array]",rC="[object Uint32Array]",gt={};gt[Vb]=gt[Xb]=gt[Zb]=gt[Kb]=gt[Qb]=gt[Jb]=gt[tC]=gt[eC]=gt[rC]=!0;gt[$b]=gt[Ob]=gt[Yb]=gt[Db]=gt[Gb]=gt[Rb]=gt[Ib]=gt[Pb]=gt[Nb]=gt[zb]=gt[Wb]=gt[qb]=gt[Hb]=gt[jb]=gt[Ub]=!1;function iC(e){return bi(e)&&au(e.length)&&!!gt[Mr(e)]}function aC(e){return function(t){return e(t)}}var ou=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ai=ou&&typeof module=="object"&&module&&!module.nodeType&&module,nC=ai&&ai.exports===ou,hn=nC&&Qh.process,tl=function(){try{var e=ai&&ai.require&&ai.require("util").types;return e||hn&&hn.binding&&hn.binding("util")}catch{}}(),el=tl&&tl.isTypedArray,As=el?aC(el):iC;function Un(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var sC=Object.prototype,oC=sC.hasOwnProperty;function lC(e,t,r){var i=e[t];(!(oC.call(e,t)&&Fa(i,r))||r===void 0&&!(t in e))&&Bs(e,t,r)}function cC(e,t,r,i){var a=!r;r||(r={});for(var n=-1,o=t.length;++n<o;){var s=t[n],l=void 0;l===void 0&&(l=e[s]),a?Bs(r,s,l):lC(r,s,l)}return r}function hC(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}var uC=9007199254740991,dC=/^(?:0|[1-9]\d*)$/;function lu(e,t){var r=typeof e;return t=t??uC,!!t&&(r=="number"||r!="symbol"&&dC.test(e))&&e>-1&&e%1==0&&e<t}var fC=Object.prototype,pC=fC.hasOwnProperty;function gC(e,t){var r=da(e),i=!r&&ua(e),a=!r&&!i&&Ls(e),n=!r&&!i&&!a&&As(e),o=r||i||a||n,s=o?hC(e.length,String):[],l=s.length;for(var c in e)(t||pC.call(e,c))&&!(o&&(c=="length"||a&&(c=="offset"||c=="parent")||n&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||lu(c,l)))&&s.push(c);return s}function mC(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var yC=Object.prototype,xC=yC.hasOwnProperty;function bC(e){if(!ar(e))return mC(e);var t=Da(e),r=[];for(var i in e)i=="constructor"&&(t||!xC.call(e,i))||r.push(i);return r}function cu(e){return Ra(e)?gC(e,!0):bC(e)}function CC(e){return cC(e,cu(e))}function kC(e,t,r,i,a,n,o){var s=Un(e,r),l=Un(t,r),c=o.get(l);if(c){jn(e,r,c);return}var h=n?n(s,l,r+"",e,t,o):void 0,u=h===void 0;if(u){var p=da(l),d=!p&&Ls(l),g=!p&&!d&&As(l);h=l,p||d||g?da(s)?h=s:vb(s)?h=gb(s):d?(u=!1,h=db(l,!0)):g?(u=!1,h=pb(l,!0)):h=[]:Fb(l)||ua(l)?(h=s,ua(s)?h=CC(s):(!ar(s)||_s(s))&&(h=xb(l))):u=!1}u&&(o.set(l,h),a(h,l,i,n,o),o.delete(l)),jn(e,r,h)}function hu(e,t,r,i,a){e!==t&&hb(t,function(n,o){if(a||(a=new Er),ar(n))kC(e,t,o,r,hu,i,a);else{var s=i?i(Un(e,o),n,o+"",e,t,a):void 0;s===void 0&&(s=n),jn(e,o,s)}},cu)}function uu(e){return e}function wC(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var rl=Math.max;function vC(e,t,r){return t=rl(t===void 0?e.length-1:t,0),function(){for(var i=arguments,a=-1,n=rl(i.length-t,0),o=Array(n);++a<n;)o[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(o),wC(e,this,s)}}function SC(e){return function(){return e}}var TC=ha?function(e,t){return ha(e,"toString",{configurable:!0,enumerable:!1,value:SC(t),writable:!0})}:uu,_C=800,BC=16,LC=Date.now;function AC(e){var t=0,r=0;return function(){var i=LC(),a=BC-(i-r);if(r=i,a>0){if(++t>=_C)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var MC=AC(TC);function EC(e,t){return MC(vC(e,t,uu),e+"")}function FC(e,t,r){if(!ar(r))return!1;var i=typeof t;return(i=="number"?Ra(r)&&lu(t,r.length):i=="string"&&t in r)?Fa(r[t],e):!1}function $C(e){return EC(function(t,r){var i=-1,a=r.length,n=a>1?r[a-1]:void 0,o=a>2?r[2]:void 0;for(n=e.length>3&&typeof n=="function"?(a--,n):void 0,o&&FC(r[0],r[1],o)&&(n=a<3?void 0:n,a=1),t=Object(t);++i<a;){var s=r[i];s&&e(t,s,i,n)}return t})}var OC=$C(function(e,t,r){hu(e,t,r)}),DC="​",RC={curveBasis:Pi,curveBasisClosed:Bp,curveBasisOpen:Lp,curveBumpX:nx,curveBumpY:sx,curveBundle:Ap,curveCardinalClosed:Mp,curveCardinalOpen:Ep,curveCardinal:Ol,curveCatmullRomClosed:Fp,curveCatmullRomOpen:$p,curveCatmullRom:Op,curveLinear:bn,curveLinearClosed:Dp,curveMonotoneX:Rp,curveMonotoneY:Ip,curveNatural:Pp,curveStep:Np,curveStepAfter:zp,curveStepBefore:Wp},IC=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,PC=f(function(e,t){const r=du(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);Qi(o),i=Lt(i,[...o])}else i=r.args;if(!i)return;let a=ls(e,t);const n="config";return i[n]!==void 0&&(a==="flowchart-v2"&&(a="flowchart"),i[a]=i[n],delete i[n]),i},"detectInit"),du=f(function(e,t=null){try{const r=new RegExp(`[%]{2}(?![{]${IC.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(r,"").replace(/'/gm,'"'),$.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let i;const a=[];for(;(i=ri.exec(e))!==null;)if(i.index===ri.lastIndex&&ri.lastIndex++,i&&!t||t&&i[1]?.match(t)||t&&i[2]?.match(t)){const n=i[1]?i[1]:i[2],o=i[3]?i[3].trim():i[4]?JSON.parse(i[4].trim()):null;a.push({type:n,args:o})}return a.length===0?{type:e,args:null}:a.length===1?a[0]:a}catch(r){return $.error(`ERROR: ${r.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),NC=f(function(e){return e.replace(ri,"")},"removeDirectives"),zC=f(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function Ms(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return RC[r]??t}f(Ms,"interpolateToCurve");function fu(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?Yh(r):r}f(fu,"formatUrl");var WC=f((e,...t)=>{const r=e.split("."),i=r.length-1,a=r[i];let n=window;for(let o=0;o<i;o++)if(n=n[r[o]],!n){$.error(`Function name: ${e} not found in window`);return}n[a](...t)},"runFunc");function Es(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}f(Es,"distance");function pu(e){let t,r=0;e.forEach(a=>{r+=Es(a,t),t=a});const i=r/2;return Fs(e,i)}f(pu,"traverseEdge");function gu(e){return e.length===1?e[0]:pu(e)}f(gu,"calcLabelPosition");var il=f((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),Fs=f((e,t)=>{let r,i=t;for(const a of e){if(r){const n=Es(a,r);if(n===0)return r;if(n<i)i-=n;else{const o=i/n;if(o<=0)return r;if(o>=1)return{x:a.x,y:a.y};if(o>0&&o<1)return{x:il((1-o)*r.x+o*a.x,5),y:il((1-o)*r.y+o*a.y,5)}}}r=a}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),qC=f((e,t,r)=>{$.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const a=Fs(t,25),n=e?10:5,o=Math.atan2(t[0].y-a.y,t[0].x-a.x),s={x:0,y:0};return s.x=Math.sin(o)*n+(t[0].x+a.x)/2,s.y=-Math.cos(o)*n+(t[0].y+a.y)/2,s},"calcCardinalityPosition");function mu(e,t,r){const i=structuredClone(r);$.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const a=25+e,n=Fs(i,a),o=10+e*.5,s=Math.atan2(i[0].y-n.y,i[0].x-n.x),l={x:0,y:0};return t==="start_left"?(l.x=Math.sin(s+Math.PI)*o+(i[0].x+n.x)/2,l.y=-Math.cos(s+Math.PI)*o+(i[0].y+n.y)/2):t==="end_right"?(l.x=Math.sin(s-Math.PI)*o+(i[0].x+n.x)/2-5,l.y=-Math.cos(s-Math.PI)*o+(i[0].y+n.y)/2-5):t==="end_left"?(l.x=Math.sin(s)*o+(i[0].x+n.x)/2-5,l.y=-Math.cos(s)*o+(i[0].y+n.y)/2-5):(l.x=Math.sin(s)*o+(i[0].x+n.x)/2,l.y=-Math.cos(s)*o+(i[0].y+n.y)/2),l}f(mu,"calcTerminalLabelPosition");function yu(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}f(yu,"getStylesFromArray");var al=0,HC=f(()=>(al++,"id-"+Math.random().toString(36).substr(2,12)+"-"+al),"generateId");function xu(e){let t="";const r="0123456789abcdef",i=r.length;for(let a=0;a<e;a++)t+=r.charAt(Math.floor(Math.random()*i));return t}f(xu,"makeRandomHex");var jC=f(e=>xu(e.length),"random"),UC=f(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),YC=f(function(e,t){const r=t.text.replace(Ar.lineBreakRegex," "),[,i]=Ia(t.fontSize),a=e.append("text");a.attr("x",t.x),a.attr("y",t.y),a.style("text-anchor",t.anchor),a.style("font-family",t.fontFamily),a.style("font-size",i),a.style("font-weight",t.fontWeight),a.attr("fill",t.fill),t.class!==void 0&&a.attr("class",t.class);const n=a.append("tspan");return n.attr("x",t.x+t.textMargin*2),n.attr("fill",t.fill),n.text(r),a},"drawSimpleText"),GC=xi((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Ar.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),a=[];let n="";return i.forEach((o,s)=>{const l=Le(`${o} `,r),c=Le(n,r);if(l>t){const{hyphenatedStrings:p,remainingWord:d}=VC(o,t,"-",r);a.push(n,...p),n=d}else c+l>=t?(a.push(n),n=o):n=[n,o].filter(Boolean).join(" ");s+1===i.length&&a.push(n)}),a.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),VC=xi((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const a=[...e],n=[];let o="";return a.forEach((s,l)=>{const c=`${o}${s}`;if(Le(c,i)>=t){const u=l+1,p=a.length===u,d=`${c}${r}`;n.push(p?c:d),o=""}else o=c}),{hyphenatedStrings:n,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function bu(e,t){return $s(e,t).height}f(bu,"calculateTextHeight");function Le(e,t){return $s(e,t).width}f(Le,"calculateTextWidth");var $s=xi((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:a=400}=t;if(!e)return{width:0,height:0};const[,n]=Ia(r),o=["sans-serif",i],s=e.split(Ar.lineBreakRegex),l=[],c=lt("body");if(!c.remove)return{width:0,height:0,lineHeight:0};const h=c.append("svg");for(const p of o){let d=0;const g={width:0,height:0,lineHeight:0};for(const m of s){const y=UC();y.text=m||DC;const x=YC(h,y).style("font-size",n).style("font-weight",a).style("font-family",p),b=(x._groups||x)[0][0].getBBox();if(b.width===0&&b.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,b.width)),d=Math.round(b.height),g.height+=d,g.lineHeight=Math.round(Math.max(g.lineHeight,d))}l.push(g)}h.remove();const u=isNaN(l[1].height)||isNaN(l[1].width)||isNaN(l[1].lineHeight)||l[0].height>l[1].height&&l[0].width>l[1].width&&l[0].lineHeight>l[1].lineHeight?0:1;return l[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),XC=class{constructor(e=!1,t){this.count=0,this.count=t?t.length:0,this.next=e?()=>this.count++:()=>Date.now()}static{f(this,"InitIDGenerator")}},Fi,ZC=f(function(e){return Fi=Fi||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),Fi.innerHTML=e,unescape(Fi.textContent)},"entityDecode");function Os(e){return"str"in e}f(Os,"isDetailedError");var KC=f((e,t,r,i)=>{if(!i)return;const a=e.node()?.getBBox();a&&e.append("text").text(i).attr("text-anchor","middle").attr("x",a.x+a.width/2).attr("y",-r).attr("class",t)},"insertTitle"),Ia=f(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function Ds(e,t){return OC({},e,t)}f(Ds,"cleanAndMerge");var pe={assignWithDepth:Lt,wrapLabel:GC,calculateTextHeight:bu,calculateTextWidth:Le,calculateTextDimensions:$s,cleanAndMerge:Ds,detectInit:PC,detectDirective:du,isSubstringInArray:zC,interpolateToCurve:Ms,calcLabelPosition:gu,calcCardinalityPosition:qC,calcTerminalLabelPosition:mu,formatUrl:fu,getStylesFromArray:yu,generateId:HC,random:jC,runFunc:WC,entityDecode:ZC,insertTitle:KC,parseFontSize:Ia,InitIDGenerator:XC},QC=f(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),or=f(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),mS=f((e,t,{counter:r=0,prefix:i,suffix:a},n)=>n||`${i?`${i}_`:""}${e}_${t}_${r}${a?`_${a}`:""}`,"getEdgeId");function zt(e){return e??null}f(zt,"handleUndefinedAttr");function Rs(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let lr=Rs();function Cu(e){lr=e}const ni={exec:()=>null};function dt(e,t=""){let r=typeof e=="string"?e:e.source;const i={replace:(a,n)=>{let o=typeof n=="string"?n:n.source;return o=o.replace(Nt.caret,"$1"),r=r.replace(a,o),i},getRegex:()=>new RegExp(r,t)};return i}const Nt={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},JC=/^(?:[ \t]*(?:\n|$))+/,t1=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,e1=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Ci=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,r1=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Is=/(?:[*+-]|\d{1,9}[.)])/,ku=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,wu=dt(ku).replace(/bull/g,Is).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),i1=dt(ku).replace(/bull/g,Is).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Ps=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,a1=/^[^\n]+/,Ns=/(?!\s*\])(?:\\.|[^\[\]\\])+/,n1=dt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Ns).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),s1=dt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Is).getRegex(),Pa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",zs=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,o1=dt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",zs).replace("tag",Pa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),vu=dt(Ps).replace("hr",Ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pa).getRegex(),l1=dt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",vu).getRegex(),Ws={blockquote:l1,code:t1,def:n1,fences:e1,heading:r1,hr:Ci,html:o1,lheading:wu,list:s1,newline:JC,paragraph:vu,table:ni,text:a1},nl=dt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pa).getRegex(),c1={...Ws,lheading:i1,table:nl,paragraph:dt(Ps).replace("hr",Ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",nl).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pa).getRegex()},h1={...Ws,html:dt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",zs).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ni,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:dt(Ps).replace("hr",Ci).replace("heading",` *#{1,6} *[^
]`).replace("lheading",wu).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},u1=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,d1=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Su=/^( {2,}|\\)\n(?!\s*$)/,f1=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Na=/[\p{P}\p{S}]/u,qs=/[\s\p{P}\p{S}]/u,Tu=/[^\s\p{P}\p{S}]/u,p1=dt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,qs).getRegex(),_u=/(?!~)[\p{P}\p{S}]/u,g1=/(?!~)[\s\p{P}\p{S}]/u,m1=/(?:[^\s\p{P}\p{S}]|~)/u,y1=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Bu=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,x1=dt(Bu,"u").replace(/punct/g,Na).getRegex(),b1=dt(Bu,"u").replace(/punct/g,_u).getRegex(),Lu="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",C1=dt(Lu,"gu").replace(/notPunctSpace/g,Tu).replace(/punctSpace/g,qs).replace(/punct/g,Na).getRegex(),k1=dt(Lu,"gu").replace(/notPunctSpace/g,m1).replace(/punctSpace/g,g1).replace(/punct/g,_u).getRegex(),w1=dt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Tu).replace(/punctSpace/g,qs).replace(/punct/g,Na).getRegex(),v1=dt(/\\(punct)/,"gu").replace(/punct/g,Na).getRegex(),S1=dt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),T1=dt(zs).replace("(?:-->|$)","-->").getRegex(),_1=dt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",T1).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),fa=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,B1=dt(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",fa).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Au=dt(/^!?\[(label)\]\[(ref)\]/).replace("label",fa).replace("ref",Ns).getRegex(),Mu=dt(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ns).getRegex(),L1=dt("reflink|nolink(?!\\()","g").replace("reflink",Au).replace("nolink",Mu).getRegex(),Hs={_backpedal:ni,anyPunctuation:v1,autolink:S1,blockSkip:y1,br:Su,code:d1,del:ni,emStrongLDelim:x1,emStrongRDelimAst:C1,emStrongRDelimUnd:w1,escape:u1,link:B1,nolink:Mu,punctuation:p1,reflink:Au,reflinkSearch:L1,tag:_1,text:f1,url:ni},A1={...Hs,link:dt(/^!?\[(label)\]\((.*?)\)/).replace("label",fa).getRegex(),reflink:dt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",fa).getRegex()},Yn={...Hs,emStrongRDelimAst:k1,emStrongLDelim:b1,url:dt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},M1={...Yn,br:dt(Su).replace("{2,}","*").getRegex(),text:dt(Yn.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},$i={normal:Ws,gfm:c1,pedantic:h1},jr={normal:Hs,gfm:Yn,breaks:M1,pedantic:A1},E1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},sl=e=>E1[e];function ue(e,t){if(t){if(Nt.escapeTest.test(e))return e.replace(Nt.escapeReplace,sl)}else if(Nt.escapeTestNoEncode.test(e))return e.replace(Nt.escapeReplaceNoEncode,sl);return e}function ol(e){try{e=encodeURI(e).replace(Nt.percentDecode,"%")}catch{return null}return e}function ll(e,t){const r=e.replace(Nt.findPipe,(n,o,s)=>{let l=!1,c=o;for(;--c>=0&&s[c]==="\\";)l=!l;return l?"|":" |"}),i=r.split(Nt.splitPipe);let a=0;if(i[0].trim()||i.shift(),i.length>0&&!i.at(-1)?.trim()&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;a<i.length;a++)i[a]=i[a].trim().replace(Nt.slashPipe,"|");return i}function Ur(e,t,r){const i=e.length;if(i===0)return"";let a=0;for(;a<i&&e.charAt(i-a-1)===t;)a++;return e.slice(0,i-a)}function F1(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function cl(e,t,r,i,a){const n=t.href,o=t.title||null,s=e[1].replace(a.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){i.state.inLink=!0;const l={type:"link",raw:r,href:n,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,l}return{type:"image",raw:r,href:n,title:o,text:s}}function $1(e,t,r){const i=e.match(r.other.indentCodeCompensation);if(i===null)return t;const a=i[1];return t.split(`
`).map(n=>{const o=n.match(r.other.beginningSpace);if(o===null)return n;const[s]=o;return s.length>=a.length?n.slice(a.length):n}).join(`
`)}class pa{options;rules;lexer;constructor(t){this.options=t||lr}space(t){const r=this.rules.block.newline.exec(t);if(r&&r[0].length>0)return{type:"space",raw:r[0]}}code(t){const r=this.rules.block.code.exec(t);if(r){const i=r[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:r[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Ur(i,`
`)}}}fences(t){const r=this.rules.block.fences.exec(t);if(r){const i=r[0],a=$1(i,r[3]||"",this.rules);return{type:"code",raw:i,lang:r[2]?r[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):r[2],text:a}}}heading(t){const r=this.rules.block.heading.exec(t);if(r){let i=r[2].trim();if(this.rules.other.endingHash.test(i)){const a=Ur(i,"#");(this.options.pedantic||!a||this.rules.other.endingSpaceChar.test(a))&&(i=a.trim())}return{type:"heading",raw:r[0],depth:r[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(t){const r=this.rules.block.hr.exec(t);if(r)return{type:"hr",raw:Ur(r[0],`
`)}}blockquote(t){const r=this.rules.block.blockquote.exec(t);if(r){let i=Ur(r[0],`
`).split(`
`),a="",n="";const o=[];for(;i.length>0;){let s=!1;const l=[];let c;for(c=0;c<i.length;c++)if(this.rules.other.blockquoteStart.test(i[c]))l.push(i[c]),s=!0;else if(!s)l.push(i[c]);else break;i=i.slice(c);const h=l.join(`
`),u=h.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");a=a?`${a}
${h}`:h,n=n?`${n}
${u}`:u;const p=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,o,!0),this.lexer.state.top=p,i.length===0)break;const d=o.at(-1);if(d?.type==="code")break;if(d?.type==="blockquote"){const g=d,m=g.raw+`
`+i.join(`
`),y=this.blockquote(m);o[o.length-1]=y,a=a.substring(0,a.length-g.raw.length)+y.raw,n=n.substring(0,n.length-g.text.length)+y.text;break}else if(d?.type==="list"){const g=d,m=g.raw+`
`+i.join(`
`),y=this.list(m);o[o.length-1]=y,a=a.substring(0,a.length-d.raw.length)+y.raw,n=n.substring(0,n.length-g.raw.length)+y.raw,i=m.substring(o.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:a,tokens:o,text:n}}}list(t){let r=this.rules.block.list.exec(t);if(r){let i=r[1].trim();const a=i.length>1,n={type:"list",raw:"",ordered:a,start:a?+i.slice(0,-1):"",loose:!1,items:[]};i=a?`\\d{1,9}\\${i.slice(-1)}`:`\\${i}`,this.options.pedantic&&(i=a?i:"[*+-]");const o=this.rules.other.listItemRegex(i);let s=!1;for(;t;){let c=!1,h="",u="";if(!(r=o.exec(t))||this.rules.block.hr.test(t))break;h=r[0],t=t.substring(h.length);let p=r[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,b=>" ".repeat(3*b.length)),d=t.split(`
`,1)[0],g=!p.trim(),m=0;if(this.options.pedantic?(m=2,u=p.trimStart()):g?m=r[1].length+1:(m=r[2].search(this.rules.other.nonSpaceChar),m=m>4?1:m,u=p.slice(m),m+=r[1].length),g&&this.rules.other.blankLine.test(d)&&(h+=d+`
`,t=t.substring(d.length+1),c=!0),!c){const b=this.rules.other.nextBulletRegex(m),k=this.rules.other.hrRegex(m),v=this.rules.other.fencesBeginRegex(m),S=this.rules.other.headingBeginRegex(m),L=this.rules.other.htmlBeginRegex(m);for(;t;){const T=t.split(`
`,1)[0];let D;if(d=T,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),D=d):D=d.replace(this.rules.other.tabCharGlobal,"    "),v.test(d)||S.test(d)||L.test(d)||b.test(d)||k.test(d))break;if(D.search(this.rules.other.nonSpaceChar)>=m||!d.trim())u+=`
`+D.slice(m);else{if(g||p.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||v.test(p)||S.test(p)||k.test(p))break;u+=`
`+d}!g&&!d.trim()&&(g=!0),h+=T+`
`,t=t.substring(T.length+1),p=D.slice(m)}}n.loose||(s?n.loose=!0:this.rules.other.doubleBlankLine.test(h)&&(s=!0));let y=null,x;this.options.gfm&&(y=this.rules.other.listIsTask.exec(u),y&&(x=y[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),n.items.push({type:"list_item",raw:h,task:!!y,checked:x,loose:!1,text:u,tokens:[]}),n.raw+=h}const l=n.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;n.raw=n.raw.trimEnd();for(let c=0;c<n.items.length;c++)if(this.lexer.state.top=!1,n.items[c].tokens=this.lexer.blockTokens(n.items[c].text,[]),!n.loose){const h=n.items[c].tokens.filter(p=>p.type==="space"),u=h.length>0&&h.some(p=>this.rules.other.anyLine.test(p.raw));n.loose=u}if(n.loose)for(let c=0;c<n.items.length;c++)n.items[c].loose=!0;return n}}html(t){const r=this.rules.block.html.exec(t);if(r)return{type:"html",block:!0,raw:r[0],pre:r[1]==="pre"||r[1]==="script"||r[1]==="style",text:r[0]}}def(t){const r=this.rules.block.def.exec(t);if(r){const i=r[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),a=r[2]?r[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=r[3]?r[3].substring(1,r[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):r[3];return{type:"def",tag:i,raw:r[0],href:a,title:n}}}table(t){const r=this.rules.block.table.exec(t);if(!r||!this.rules.other.tableDelimiter.test(r[2]))return;const i=ll(r[1]),a=r[2].replace(this.rules.other.tableAlignChars,"").split("|"),n=r[3]?.trim()?r[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],o={type:"table",raw:r[0],header:[],align:[],rows:[]};if(i.length===a.length){for(const s of a)this.rules.other.tableAlignRight.test(s)?o.align.push("right"):this.rules.other.tableAlignCenter.test(s)?o.align.push("center"):this.rules.other.tableAlignLeft.test(s)?o.align.push("left"):o.align.push(null);for(let s=0;s<i.length;s++)o.header.push({text:i[s],tokens:this.lexer.inline(i[s]),header:!0,align:o.align[s]});for(const s of n)o.rows.push(ll(s,o.header.length).map((l,c)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:o.align[c]})));return o}}lheading(t){const r=this.rules.block.lheading.exec(t);if(r)return{type:"heading",raw:r[0],depth:r[2].charAt(0)==="="?1:2,text:r[1],tokens:this.lexer.inline(r[1])}}paragraph(t){const r=this.rules.block.paragraph.exec(t);if(r){const i=r[1].charAt(r[1].length-1)===`
`?r[1].slice(0,-1):r[1];return{type:"paragraph",raw:r[0],text:i,tokens:this.lexer.inline(i)}}}text(t){const r=this.rules.block.text.exec(t);if(r)return{type:"text",raw:r[0],text:r[0],tokens:this.lexer.inline(r[0])}}escape(t){const r=this.rules.inline.escape.exec(t);if(r)return{type:"escape",raw:r[0],text:r[1]}}tag(t){const r=this.rules.inline.tag.exec(t);if(r)return!this.lexer.state.inLink&&this.rules.other.startATag.test(r[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(r[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(r[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(r[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:r[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:r[0]}}link(t){const r=this.rules.inline.link.exec(t);if(r){const i=r[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(i)){if(!this.rules.other.endAngleBracket.test(i))return;const o=Ur(i.slice(0,-1),"\\");if((i.length-o.length)%2===0)return}else{const o=F1(r[2],"()");if(o>-1){const l=(r[0].indexOf("!")===0?5:4)+r[1].length+o;r[2]=r[2].substring(0,o),r[0]=r[0].substring(0,l).trim(),r[3]=""}}let a=r[2],n="";if(this.options.pedantic){const o=this.rules.other.pedanticHrefTitle.exec(a);o&&(a=o[1],n=o[3])}else n=r[3]?r[3].slice(1,-1):"";return a=a.trim(),this.rules.other.startAngleBracket.test(a)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(i)?a=a.slice(1):a=a.slice(1,-1)),cl(r,{href:a&&a.replace(this.rules.inline.anyPunctuation,"$1"),title:n&&n.replace(this.rules.inline.anyPunctuation,"$1")},r[0],this.lexer,this.rules)}}reflink(t,r){let i;if((i=this.rules.inline.reflink.exec(t))||(i=this.rules.inline.nolink.exec(t))){const a=(i[2]||i[1]).replace(this.rules.other.multipleSpaceGlobal," "),n=r[a.toLowerCase()];if(!n){const o=i[0].charAt(0);return{type:"text",raw:o,text:o}}return cl(i,n,i[0],this.lexer,this.rules)}}emStrong(t,r,i=""){let a=this.rules.inline.emStrongLDelim.exec(t);if(!a||a[3]&&i.match(this.rules.other.unicodeAlphaNumeric))return;if(!(a[1]||a[2]||"")||!i||this.rules.inline.punctuation.exec(i)){const o=[...a[0]].length-1;let s,l,c=o,h=0;const u=a[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,r=r.slice(-1*t.length+o);(a=u.exec(r))!=null;){if(s=a[1]||a[2]||a[3]||a[4]||a[5]||a[6],!s)continue;if(l=[...s].length,a[3]||a[4]){c+=l;continue}else if((a[5]||a[6])&&o%3&&!((o+l)%3)){h+=l;continue}if(c-=l,c>0)continue;l=Math.min(l,l+c+h);const p=[...a[0]][0].length,d=t.slice(0,o+a.index+p+l);if(Math.min(o,l)%2){const m=d.slice(1,-1);return{type:"em",raw:d,text:m,tokens:this.lexer.inlineTokens(m)}}const g=d.slice(2,-2);return{type:"strong",raw:d,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const r=this.rules.inline.code.exec(t);if(r){let i=r[2].replace(this.rules.other.newLineCharGlobal," ");const a=this.rules.other.nonSpaceChar.test(i),n=this.rules.other.startingSpaceChar.test(i)&&this.rules.other.endingSpaceChar.test(i);return a&&n&&(i=i.substring(1,i.length-1)),{type:"codespan",raw:r[0],text:i}}}br(t){const r=this.rules.inline.br.exec(t);if(r)return{type:"br",raw:r[0]}}del(t){const r=this.rules.inline.del.exec(t);if(r)return{type:"del",raw:r[0],text:r[2],tokens:this.lexer.inlineTokens(r[2])}}autolink(t){const r=this.rules.inline.autolink.exec(t);if(r){let i,a;return r[2]==="@"?(i=r[1],a="mailto:"+i):(i=r[1],a=i),{type:"link",raw:r[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}}url(t){let r;if(r=this.rules.inline.url.exec(t)){let i,a;if(r[2]==="@")i=r[0],a="mailto:"+i;else{let n;do n=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])?.[0]??"";while(n!==r[0]);i=r[0],r[1]==="www."?a="http://"+r[0]:a=r[0]}return{type:"link",raw:r[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t){const r=this.rules.inline.text.exec(t);if(r){const i=this.lexer.state.inRawBlock;return{type:"text",raw:r[0],text:r[0],escaped:i}}}}class Jt{tokens;options;state;tokenizer;inlineQueue;constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||lr,this.options.tokenizer=this.options.tokenizer||new pa,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const r={other:Nt,block:$i.normal,inline:jr.normal};this.options.pedantic?(r.block=$i.pedantic,r.inline=jr.pedantic):this.options.gfm&&(r.block=$i.gfm,this.options.breaks?r.inline=jr.breaks:r.inline=jr.gfm),this.tokenizer.rules=r}static get rules(){return{block:$i,inline:jr}}static lex(t,r){return new Jt(r).lex(t)}static lexInline(t,r){return new Jt(r).inlineTokens(t)}lex(t){t=t.replace(Nt.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){const i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){for(this.options.pedantic&&(t=t.replace(Nt.tabCharGlobal,"    ").replace(Nt.spaceLine,""));t;){let a;if(this.options.extensions?.block?.some(o=>(a=o.call({lexer:this},t,r))?(t=t.substring(a.raw.length),r.push(a),!0):!1))continue;if(a=this.tokenizer.space(t)){t=t.substring(a.raw.length);const o=r.at(-1);a.raw.length===1&&o!==void 0?o.raw+=`
`:r.push(a);continue}if(a=this.tokenizer.code(t)){t=t.substring(a.raw.length);const o=r.at(-1);o?.type==="paragraph"||o?.type==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.at(-1).src=o.text):r.push(a);continue}if(a=this.tokenizer.fences(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.heading(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.hr(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.blockquote(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.list(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.html(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.def(t)){t=t.substring(a.raw.length);const o=r.at(-1);o?.type==="paragraph"||o?.type==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.raw,this.inlineQueue.at(-1).src=o.text):this.tokens.links[a.tag]||(this.tokens.links[a.tag]={href:a.href,title:a.title});continue}if(a=this.tokenizer.table(t)){t=t.substring(a.raw.length),r.push(a);continue}if(a=this.tokenizer.lheading(t)){t=t.substring(a.raw.length),r.push(a);continue}let n=t;if(this.options.extensions?.startBlock){let o=1/0;const s=t.slice(1);let l;this.options.extensions.startBlock.forEach(c=>{l=c.call({lexer:this},s),typeof l=="number"&&l>=0&&(o=Math.min(o,l))}),o<1/0&&o>=0&&(n=t.substring(0,o+1))}if(this.state.top&&(a=this.tokenizer.paragraph(n))){const o=r.at(-1);i&&o?.type==="paragraph"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):r.push(a),i=n.length!==t.length,t=t.substring(a.raw.length);continue}if(a=this.tokenizer.text(t)){t=t.substring(a.raw.length);const o=r.at(-1);o?.type==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):r.push(a);continue}if(t){const o="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){let i=t,a=null;if(this.tokens.links){const s=Object.keys(this.tokens.links);if(s.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)s.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,a.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let n=!1,o="";for(;t;){n||(o=""),n=!1;let s;if(this.options.extensions?.inline?.some(c=>(s=c.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.escape(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.tag(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.link(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(s.raw.length);const c=r.at(-1);s.type==="text"&&c?.type==="text"?(c.raw+=s.raw,c.text+=s.text):r.push(s);continue}if(s=this.tokenizer.emStrong(t,i,o)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.codespan(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.br(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.del(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.autolink(t)){t=t.substring(s.raw.length),r.push(s);continue}if(!this.state.inLink&&(s=this.tokenizer.url(t))){t=t.substring(s.raw.length),r.push(s);continue}let l=t;if(this.options.extensions?.startInline){let c=1/0;const h=t.slice(1);let u;this.options.extensions.startInline.forEach(p=>{u=p.call({lexer:this},h),typeof u=="number"&&u>=0&&(c=Math.min(c,u))}),c<1/0&&c>=0&&(l=t.substring(0,c+1))}if(s=this.tokenizer.inlineText(l)){t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(o=s.raw.slice(-1)),n=!0;const c=r.at(-1);c?.type==="text"?(c.raw+=s.raw,c.text+=s.text):r.push(s);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return r}}class ga{options;parser;constructor(t){this.options=t||lr}space(t){return""}code({text:t,lang:r,escaped:i}){const a=(r||"").match(Nt.notSpaceStart)?.[0],n=t.replace(Nt.endingNewline,"")+`
`;return a?'<pre><code class="language-'+ue(a)+'">'+(i?n:ue(n,!0))+`</code></pre>
`:"<pre><code>"+(i?n:ue(n,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:r}){return`<h${r}>${this.parser.parseInline(t)}</h${r}>
`}hr(t){return`<hr>
`}list(t){const r=t.ordered,i=t.start;let a="";for(let s=0;s<t.items.length;s++){const l=t.items[s];a+=this.listitem(l)}const n=r?"ol":"ul",o=r&&i!==1?' start="'+i+'"':"";return"<"+n+o+`>
`+a+"</"+n+`>
`}listitem(t){let r="";if(t.task){const i=this.checkbox({checked:!!t.checked});t.loose?t.tokens[0]?.type==="paragraph"?(t.tokens[0].text=i+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=i+" "+ue(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:i+" ",text:i+" ",escaped:!0}):r+=i+" "}return r+=this.parser.parse(t.tokens,!!t.loose),`<li>${r}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let r="",i="";for(let n=0;n<t.header.length;n++)i+=this.tablecell(t.header[n]);r+=this.tablerow({text:i});let a="";for(let n=0;n<t.rows.length;n++){const o=t.rows[n];i="";for(let s=0;s<o.length;s++)i+=this.tablecell(o[s]);a+=this.tablerow({text:i})}return a&&(a=`<tbody>${a}</tbody>`),`<table>
<thead>
`+r+`</thead>
`+a+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){const r=this.parser.parseInline(t.tokens),i=t.header?"th":"td";return(t.align?`<${i} align="${t.align}">`:`<${i}>`)+r+`</${i}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${ue(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:r,tokens:i}){const a=this.parser.parseInline(i),n=ol(t);if(n===null)return a;t=n;let o='<a href="'+t+'"';return r&&(o+=' title="'+ue(r)+'"'),o+=">"+a+"</a>",o}image({href:t,title:r,text:i}){const a=ol(t);if(a===null)return ue(i);t=a;let n=`<img src="${t}" alt="${i}"`;return r&&(n+=` title="${ue(r)}"`),n+=">",n}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:ue(t.text)}}class js{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}}class te{options;renderer;textRenderer;constructor(t){this.options=t||lr,this.options.renderer=this.options.renderer||new ga,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new js}static parse(t,r){return new te(r).parse(t)}static parseInline(t,r){return new te(r).parseInline(t)}parse(t,r=!0){let i="";for(let a=0;a<t.length;a++){const n=t[a];if(this.options.extensions?.renderers?.[n.type]){const s=n,l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(s.type)){i+=l||"";continue}}const o=n;switch(o.type){case"space":{i+=this.renderer.space(o);continue}case"hr":{i+=this.renderer.hr(o);continue}case"heading":{i+=this.renderer.heading(o);continue}case"code":{i+=this.renderer.code(o);continue}case"table":{i+=this.renderer.table(o);continue}case"blockquote":{i+=this.renderer.blockquote(o);continue}case"list":{i+=this.renderer.list(o);continue}case"html":{i+=this.renderer.html(o);continue}case"paragraph":{i+=this.renderer.paragraph(o);continue}case"text":{let s=o,l=this.renderer.text(s);for(;a+1<t.length&&t[a+1].type==="text";)s=t[++a],l+=`
`+this.renderer.text(s);r?i+=this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:!0}]}):i+=l;continue}default:{const s='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return i}parseInline(t,r=this.renderer){let i="";for(let a=0;a<t.length;a++){const n=t[a];if(this.options.extensions?.renderers?.[n.type]){const s=this.options.extensions.renderers[n.type].call({parser:this},n);if(s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(n.type)){i+=s||"";continue}}const o=n;switch(o.type){case"escape":{i+=r.text(o);break}case"html":{i+=r.html(o);break}case"link":{i+=r.link(o);break}case"image":{i+=r.image(o);break}case"strong":{i+=r.strong(o);break}case"em":{i+=r.em(o);break}case"codespan":{i+=r.codespan(o);break}case"br":{i+=r.br(o);break}case"del":{i+=r.del(o);break}case"text":{i+=r.text(o);break}default:{const s='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return i}}class Yi{options;block;constructor(t){this.options=t||lr}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?Jt.lex:Jt.lexInline}provideParser(){return this.block?te.parse:te.parseInline}}class O1{defaults=Rs();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=te;Renderer=ga;TextRenderer=js;Lexer=Jt;Tokenizer=pa;Hooks=Yi;constructor(...t){this.use(...t)}walkTokens(t,r){let i=[];for(const a of t)switch(i=i.concat(r.call(this,a)),a.type){case"table":{const n=a;for(const o of n.header)i=i.concat(this.walkTokens(o.tokens,r));for(const o of n.rows)for(const s of o)i=i.concat(this.walkTokens(s.tokens,r));break}case"list":{const n=a;i=i.concat(this.walkTokens(n.items,r));break}default:{const n=a;this.defaults.extensions?.childTokens?.[n.type]?this.defaults.extensions.childTokens[n.type].forEach(o=>{const s=n[o].flat(1/0);i=i.concat(this.walkTokens(s,r))}):n.tokens&&(i=i.concat(this.walkTokens(n.tokens,r)))}}return i}use(...t){const r=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(i=>{const a={...i};if(a.async=this.defaults.async||a.async||!1,i.extensions&&(i.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const o=r.renderers[n.name];o?r.renderers[n.name]=function(...s){let l=n.renderer.apply(this,s);return l===!1&&(l=o.apply(this,s)),l}:r.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=r[n.level];o?o.unshift(n.tokenizer):r[n.level]=[n.tokenizer],n.start&&(n.level==="block"?r.startBlock?r.startBlock.push(n.start):r.startBlock=[n.start]:n.level==="inline"&&(r.startInline?r.startInline.push(n.start):r.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(r.childTokens[n.name]=n.childTokens)}),a.extensions=r),i.renderer){const n=this.defaults.renderer||new ga(this.defaults);for(const o in i.renderer){if(!(o in n))throw new Error(`renderer '${o}' does not exist`);if(["options","parser"].includes(o))continue;const s=o,l=i.renderer[s],c=n[s];n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u||""}}a.renderer=n}if(i.tokenizer){const n=this.defaults.tokenizer||new pa(this.defaults);for(const o in i.tokenizer){if(!(o in n))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const s=o,l=i.tokenizer[s],c=n[s];n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u}}a.tokenizer=n}if(i.hooks){const n=this.defaults.hooks||new Yi;for(const o in i.hooks){if(!(o in n))throw new Error(`hook '${o}' does not exist`);if(["options","block"].includes(o))continue;const s=o,l=i.hooks[s],c=n[s];Yi.passThroughHooks.has(o)?n[s]=h=>{if(this.defaults.async)return Promise.resolve(l.call(n,h)).then(p=>c.call(n,p));const u=l.call(n,h);return c.call(n,u)}:n[s]=(...h)=>{let u=l.apply(n,h);return u===!1&&(u=c.apply(n,h)),u}}a.hooks=n}if(i.walkTokens){const n=this.defaults.walkTokens,o=i.walkTokens;a.walkTokens=function(s){let l=[];return l.push(o.call(this,s)),n&&(l=l.concat(n.call(this,s))),l}}this.defaults={...this.defaults,...a}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,r){return Jt.lex(t,r??this.defaults)}parser(t,r){return te.parse(t,r??this.defaults)}parseMarkdown(t){return(i,a)=>{const n={...a},o={...this.defaults,...n},s=this.onError(!!o.silent,!!o.async);if(this.defaults.async===!0&&n.async===!1)return s(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof i>"u"||i===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof i!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(i)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=t);const l=o.hooks?o.hooks.provideLexer():t?Jt.lex:Jt.lexInline,c=o.hooks?o.hooks.provideParser():t?te.parse:te.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(i):i).then(h=>l(h,o)).then(h=>o.hooks?o.hooks.processAllTokens(h):h).then(h=>o.walkTokens?Promise.all(this.walkTokens(h,o.walkTokens)).then(()=>h):h).then(h=>c(h,o)).then(h=>o.hooks?o.hooks.postprocess(h):h).catch(s);try{o.hooks&&(i=o.hooks.preprocess(i));let h=l(i,o);o.hooks&&(h=o.hooks.processAllTokens(h)),o.walkTokens&&this.walkTokens(h,o.walkTokens);let u=c(h,o);return o.hooks&&(u=o.hooks.postprocess(u)),u}catch(h){return s(h)}}}onError(t,r){return i=>{if(i.message+=`
Please report this to https://github.com/markedjs/marked.`,t){const a="<p>An error occurred:</p><pre>"+ue(i.message+"",!0)+"</pre>";return r?Promise.resolve(a):a}if(r)return Promise.reject(i);throw i}}}const rr=new O1;function ut(e,t){return rr.parse(e,t)}ut.options=ut.setOptions=function(e){return rr.setOptions(e),ut.defaults=rr.defaults,Cu(ut.defaults),ut};ut.getDefaults=Rs;ut.defaults=lr;ut.use=function(...e){return rr.use(...e),ut.defaults=rr.defaults,Cu(ut.defaults),ut};ut.walkTokens=function(e,t){return rr.walkTokens(e,t)};ut.parseInline=rr.parseInline;ut.Parser=te;ut.parser=te.parse;ut.Renderer=ga;ut.TextRenderer=js;ut.Lexer=Jt;ut.lexer=Jt.lex;ut.Tokenizer=pa;ut.Hooks=Yi;ut.parse=ut;ut.options;ut.setOptions;ut.use;ut.walkTokens;ut.parseInline;te.parse;Jt.lex;function Eu(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var a=i.reduce(function(s,l){var c=l.match(/\n([\t ]+|(?!\s).)/g);return c?s.concat(c.map(function(h){var u,p;return(p=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&p!==void 0?p:0})):s},[]);if(a.length){var n=new RegExp(`
[	 ]{`+Math.min.apply(Math,a)+"}","g");i=i.map(function(s){return s.replace(n,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,l){var c=o.match(/(?:^|\n)( *)$/),h=c?c[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(p,d){return d===0?p:""+h+p}).join(`
`)),o+=u+i[l+1]}),o}function Fu(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),a=Eu(i);return t===!1?a.replace(/ /g,"&nbsp;"):a}f(Fu,"preprocessMarkdown");function $u(e,t={}){const r=Fu(e,t),i=ut.lexer(r),a=[[]];let n=0;function o(s,l="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(n++,a.push([])),h.split(" ").forEach(p=>{p=p.replace(/&#39;/g,"'"),p&&a[n].push({content:p,type:l})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(c=>{o(c,s.type)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}return f(o,"processNode"),i.forEach(s=>{s.type==="paragraph"?s.tokens?.forEach(l=>{o(l)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}),a}f($u,"markdownToLines");function Ou(e,{markdownAutoWrap:t}={}){const r=ut.lexer(e);function i(a){return a.type==="text"?t===!1?a.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):a.text.replace(/\n */g,"<br/>"):a.type==="strong"?`<strong>${a.tokens?.map(i).join("")}</strong>`:a.type==="em"?`<em>${a.tokens?.map(i).join("")}</em>`:a.type==="paragraph"?`<p>${a.tokens?.map(i).join("")}</p>`:a.type==="space"?"":a.type==="html"?`${a.text}`:a.type==="escape"?a.text:`Unsupported markdown: ${a.type}`}return f(i,"output"),r.map(i).join("")}f(Ou,"markdownToHTML");function Du(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}f(Du,"splitTextToChars");function Ru(e,t){const r=Du(t.content);return Us(e,[],r,t.type)}f(Ru,"splitWordToFitWidth");function Us(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[a,...n]=r,o=[...t,a];return e([{content:o.join(""),type:i}])?Us(e,o,n,i):(t.length===0&&a&&(t.push(a),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}f(Us,"splitWordToFitWidthRecursion");function Iu(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return ma(e,t)}f(Iu,"splitLineToFitWidth");function ma(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let a="";e[0].content===" "&&(a=" ",e.shift());const n=e.shift()??{content:" ",type:"normal"},o=[...i];if(a!==""&&o.push({content:a,type:"normal"}),o.push(n),t(o))return ma(e,t,r,o);if(i.length>0)r.push(i),e.unshift(n);else if(n.content){const[s,l]=Ru(t,n);r.push([s]),l.content&&e.unshift(l)}return ma(e,t,r)}f(ma,"splitLineToFitWidthRecursion");function Gn(e,t){t&&e.attr("style",t)}f(Gn,"applyStyle");async function Pu(e,t,r,i,a=!1){const n=e.append("foreignObject");n.attr("width",`${10*r}px`),n.attr("height",`${10*r}px`);const o=n.append("xhtml:div");let s=t.label;t.label&&Sr(t.label)&&(s=await cs(t.label.replace(Ar.lineBreakRegex,`
`),ht()));const l=t.isNode?"nodeLabel":"edgeLabel",c=o.append("span");c.html(s),Gn(c,t.labelStyle),c.attr("class",`${l} ${i}`),Gn(o,t.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",r+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),a&&o.attr("class","labelBkg");let h=o.node().getBoundingClientRect();return h.width===r&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",r+"px"),h=o.node().getBoundingClientRect()),n.node()}f(Pu,"addHtmlSpan");function za(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}f(za,"createTspan");function Nu(e,t,r){const i=e.append("text"),a=za(i,1,t);Wa(a,r);const n=a.node().getComputedTextLength();return i.remove(),n}f(Nu,"computeWidthOfText");function D1(e,t,r){const i=e.append("text"),a=za(i,1,t);Wa(a,[{content:r,type:"normal"}]);const n=a.node()?.getBoundingClientRect();return n&&i.remove(),n}f(D1,"computeDimensionOfText");function zu(e,t,r,i=!1){const n=t.append("g"),o=n.insert("rect").attr("class","background").attr("style","stroke: none"),s=n.append("text").attr("y","-10.1");let l=0;for(const c of r){const h=f(p=>Nu(n,1.1,p)<=e,"checkWidth"),u=h(c)?[c]:Iu(c,h);for(const p of u){const d=za(s,l,1.1);Wa(d,p),l++}}if(i){const c=s.node().getBBox(),h=2;return o.attr("x",c.x-h).attr("y",c.y-h).attr("width",c.width+2*h).attr("height",c.height+2*h),n.node()}else return s.node()}f(zu,"createFormattedText");function Wa(e,t){e.text(""),t.forEach((r,i)=>{const a=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?a.text(r.content):a.text(" "+r.content)})}f(Wa,"updateTextContentAndStyles");function Wu(e){return e.replace(/fa[bklrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`)}f(Wu,"replaceIconSubstring");var He=f(async(e,t="",{style:r="",isTitle:i=!1,classes:a="",useHtmlLabels:n=!0,isNode:o=!0,width:s=200,addSvgBackground:l=!1}={},c)=>{if($.debug("XYZ createText",t,r,i,a,n,o,"addSvgBackground: ",l),n){const h=Ou(t,c),u=Wu(or(h)),p=t.replace(/\\\\/g,"\\"),d={isNode:o,label:Sr(t)?p:u,labelStyle:r.replace("fill:","color:")};return await Pu(e,d,s,a,l)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=$u(h.replace("<br>","<br/>"),c),p=zu(s,e,u,t?l:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(p).attr("style",d)}else{const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");lt(p).select("rect").attr("style",d.replace(/background:/g,"fill:"));const g=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(p).select("text").attr("style",g)}return p}},"createText");function un(e,t,r){if(e&&e.length){const[i,a]=t,n=Math.PI/180*r,o=Math.cos(n),s=Math.sin(n);for(const l of e){const[c,h]=l;l[0]=(c-i)*o-(h-a)*s+i,l[1]=(c-i)*s+(h-a)*o+a}}}function R1(e,t){return e[0]===t[0]&&e[1]===t[1]}function I1(e,t,r,i=1){const a=r,n=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(a)for(const c of o)un(c,s,a);const l=function(c,h,u){const p=[];for(const b of c){const k=[...b];R1(k[0],k[k.length-1])||k.push([k[0][0],k[0][1]]),k.length>2&&p.push(k)}const d=[];h=Math.max(h,.1);const g=[];for(const b of p)for(let k=0;k<b.length-1;k++){const v=b[k],S=b[k+1];if(v[1]!==S[1]){const L=Math.min(v[1],S[1]);g.push({ymin:L,ymax:Math.max(v[1],S[1]),x:L===v[1]?v[0]:S[0],islope:(S[0]-v[0])/(S[1]-v[1])})}}if(g.sort((b,k)=>b.ymin<k.ymin?-1:b.ymin>k.ymin?1:b.x<k.x?-1:b.x>k.x?1:b.ymax===k.ymax?0:(b.ymax-k.ymax)/Math.abs(b.ymax-k.ymax)),!g.length)return d;let m=[],y=g[0].ymin,x=0;for(;m.length||g.length;){if(g.length){let b=-1;for(let k=0;k<g.length&&!(g[k].ymin>y);k++)b=k;g.splice(0,b+1).forEach(k=>{m.push({s:y,edge:k})})}if(m=m.filter(b=>!(b.edge.ymax<=y)),m.sort((b,k)=>b.edge.x===k.edge.x?0:(b.edge.x-k.edge.x)/Math.abs(b.edge.x-k.edge.x)),(u!==1||x%h==0)&&m.length>1)for(let b=0;b<m.length;b+=2){const k=b+1;if(k>=m.length)break;const v=m[b].edge,S=m[k].edge;d.push([[Math.round(v.x),y],[Math.round(S.x),y]])}y+=u,m.forEach(b=>{b.edge.x=b.edge.x+u*b.edge.islope}),x++}return d}(o,n,i);if(a){for(const c of o)un(c,s,-a);(function(c,h,u){const p=[];c.forEach(d=>p.push(...d)),un(p,h,u)})(l,s,-a)}return l}function ki(e,t){var r;const i=t.hachureAngle+90;let a=t.hachureGap;a<0&&(a=4*t.strokeWidth),a=Math.round(Math.max(a,.1));let n=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(n=a),I1(e,a,i,n||1)}class Ys{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=ki(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const a of t)i.push(...this.helper.doubleLineOps(a[0][0],a[0][1],a[1][0],a[1][1],r));return i}}function qa(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class P1 extends Ys{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const a=ki(t,Object.assign({},r,{hachureGap:i})),n=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(n),l=.5*i*Math.sin(n);for(const[c,h]of a)qa([c,h])&&o.push([[c[0]-s,c[1]+l],[...h]],[[c[0]+s,c[1]-l],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class N1 extends Ys{fillPolygons(t,r){const i=this._fillPolygons(t,r),a=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),n=this._fillPolygons(t,a);return i.ops=i.ops.concat(n.ops),i}}class z1{constructor(t){this.helper=t}fillPolygons(t,r){const i=ki(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let a=r.hachureGap;a<0&&(a=4*r.strokeWidth),a=Math.max(a,.1);let n=r.fillWeight;n<0&&(n=r.strokeWidth/2);const o=a/4;for(const s of t){const l=qa(s),c=l/a,h=Math.ceil(c)-1,u=l-h*a,p=(s[0][0]+s[1][0])/2-a/4,d=Math.min(s[0][1],s[1][1]);for(let g=0;g<h;g++){const m=d+u+g*a,y=p-o+2*Math.random()*o,x=m-o+2*Math.random()*o,b=this.helper.ellipse(y,x,n,n,r);i.push(...b.ops)}}return{type:"fillSketch",ops:i}}}class W1{constructor(t){this.helper=t}fillPolygons(t,r){const i=ki(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,a=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,n=[];return t.forEach(o=>{const s=qa(o),l=Math.floor(s/(i+a)),c=(s+a-l*(i+a))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const p=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let d=0;d<l;d++){const g=d*(i+a),m=g+i,y=[h[0]+g*Math.cos(p)+c*Math.cos(p),h[1]+g*Math.sin(p)+c*Math.sin(p)],x=[h[0]+m*Math.cos(p)+c*Math.cos(p),h[1]+m*Math.sin(p)+c*Math.sin(p)];n.push(...this.helper.doubleLineOps(y[0],y[1],x[0],x[1],r))}}),n}}class q1{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,a=r.zigzagOffset<0?i:r.zigzagOffset,n=ki(t,r=Object.assign({},r,{hachureGap:i+a}));return{type:"fillSketch",ops:this.zigzagLines(n,a,r)}}zigzagLines(t,r,i){const a=[];return t.forEach(n=>{const o=qa(n),s=Math.round(o/(2*r));let l=n[0],c=n[1];l[0]>c[0]&&(l=n[1],c=n[0]);const h=Math.atan((c[1]-l[1])/(c[0]-l[0]));for(let u=0;u<s;u++){const p=2*u*r,d=2*(u+1)*r,g=Math.sqrt(2*Math.pow(r,2)),m=[l[0]+p*Math.cos(h),l[1]+p*Math.sin(h)],y=[l[0]+d*Math.cos(h),l[1]+d*Math.sin(h)],x=[m[0]+g*Math.cos(h+Math.PI/4),m[1]+g*Math.sin(h+Math.PI/4)];a.push(...this.helper.doubleLineOps(m[0],m[1],x[0],x[1],i),...this.helper.doubleLineOps(x[0],x[1],y[0],y[1],i))}}),a}}const jt={};class H1{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const j1=0,dn=1,hl=2,Oi={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function fn(e,t){return e.type===t}function Gs(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:j1,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:dn,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:hl,text:""},s}(e);let i="BOD",a=0,n=r[a];for(;!fn(n,hl);){let o=0;const s=[];if(i==="BOD"){if(n.text!=="M"&&n.text!=="m")return Gs("M0,0"+e);a++,o=Oi[n.text],i=n.text}else fn(n,dn)?o=Oi[i]:(a++,o=Oi[n.text],i=n.text);if(!(a+o<r.length))throw new Error("Path data ended short");for(let l=a;l<a+o;l++){const c=r[l];if(!fn(c,dn))throw new Error("Param not a number: "+i+","+c.text);s[s.length]=+c.text}if(typeof Oi[i]!="number")throw new Error("Bad segment: "+i);{const l={key:i,data:s};t.push(l),a+=o,n=r[a],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function qu(e){let t=0,r=0,i=0,a=0;const n=[];for(const{key:o,data:s}of e)switch(o){case"M":n.push({key:"M",data:[...s]}),[t,r]=s,[i,a]=s;break;case"m":t+=s[0],r+=s[1],n.push({key:"M",data:[t,r]}),i=t,a=r;break;case"L":n.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],n.push({key:"L",data:[t,r]});break;case"C":n.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"C",data:l}),t=l[4],r=l[5];break}case"Q":n.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"Q",data:l}),t=l[2],r=l[3];break}case"A":n.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],n.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":n.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],n.push({key:"H",data:[t]});break;case"V":n.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],n.push({key:"V",data:[r]});break;case"S":n.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const l=s.map((c,h)=>h%2?c+r:c+t);n.push({key:"S",data:l}),t=l[2],r=l[3];break}case"T":n.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],n.push({key:"T",data:[t,r]});break;case"Z":case"z":n.push({key:"Z",data:[]}),t=i,r=a}return n}function Hu(e){const t=[];let r="",i=0,a=0,n=0,o=0,s=0,l=0;for(const{key:c,data:h}of e){switch(c){case"M":t.push({key:"M",data:[...h]}),[i,a]=h,[n,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],a=h[5],s=h[2],l=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,a]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,a]});break;case"V":a=h[0],t.push({key:"L",data:[i,a]});break;case"S":{let u=0,p=0;r==="C"||r==="S"?(u=i+(i-s),p=a+(a-l)):(u=i,p=a),t.push({key:"C",data:[u,p,...h]}),s=h[0],l=h[1],i=h[2],a=h[3];break}case"T":{const[u,p]=h;let d=0,g=0;r==="Q"||r==="T"?(d=i+(i-s),g=a+(a-l)):(d=i,g=a);const m=i+2*(d-i)/3,y=a+2*(g-a)/3,x=u+2*(d-u)/3,b=p+2*(g-p)/3;t.push({key:"C",data:[m,y,x,b,u,p]}),s=d,l=g,i=u,a=p;break}case"Q":{const[u,p,d,g]=h,m=i+2*(u-i)/3,y=a+2*(p-a)/3,x=d+2*(u-d)/3,b=g+2*(p-g)/3;t.push({key:"C",data:[m,y,x,b,d,g]}),s=u,l=p,i=d,a=g;break}case"A":{const u=Math.abs(h[0]),p=Math.abs(h[1]),d=h[2],g=h[3],m=h[4],y=h[5],x=h[6];u===0||p===0?(t.push({key:"C",data:[i,a,y,x,y,x]}),i=y,a=x):(i!==y||a!==x)&&(ju(i,a,y,x,u,p,d,g,m).forEach(function(b){t.push({key:"C",data:b})}),i=y,a=x);break}case"Z":t.push({key:"Z",data:[]}),i=n,a=o}r=c}return t}function Yr(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function ju(e,t,r,i,a,n,o,s,l,c){const h=(u=o,Math.PI*u/180);var u;let p=[],d=0,g=0,m=0,y=0;if(c)[d,g,m,y]=c;else{[e,t]=Yr(e,t,-h),[r,i]=Yr(r,i,-h);const P=(e-r)/2,E=(t-i)/2;let M=P*P/(a*a)+E*E/(n*n);M>1&&(M=Math.sqrt(M),a*=M,n*=M);const _=a*a,F=n*n,A=_*F-_*E*E-F*P*P,N=_*E*E+F*P*P,q=(s===l?-1:1)*Math.sqrt(Math.abs(A/N));m=q*a*E/n+(e+r)/2,y=q*-n*P/a+(t+i)/2,d=Math.asin(parseFloat(((t-y)/n).toFixed(9))),g=Math.asin(parseFloat(((i-y)/n).toFixed(9))),e<m&&(d=Math.PI-d),r<m&&(g=Math.PI-g),d<0&&(d=2*Math.PI+d),g<0&&(g=2*Math.PI+g),l&&d>g&&(d-=2*Math.PI),!l&&g>d&&(g-=2*Math.PI)}let x=g-d;if(Math.abs(x)>120*Math.PI/180){const P=g,E=r,M=i;g=l&&g>d?d+120*Math.PI/180*1:d+120*Math.PI/180*-1,p=ju(r=m+a*Math.cos(g),i=y+n*Math.sin(g),E,M,a,n,o,0,l,[g,P,m,y])}x=g-d;const b=Math.cos(d),k=Math.sin(d),v=Math.cos(g),S=Math.sin(g),L=Math.tan(x/4),T=4/3*a*L,D=4/3*n*L,R=[e,t],O=[e+T*k,t-D*b],B=[r+T*S,i-D*v],z=[r,i];if(O[0]=2*R[0]-O[0],O[1]=2*R[1]-O[1],c)return[O,B,z].concat(p);{p=[O,B,z].concat(p);const P=[];for(let E=0;E<p.length;E+=3){const M=Yr(p[E][0],p[E][1],h),_=Yr(p[E+1][0],p[E+1][1],h),F=Yr(p[E+2][0],p[E+2][1],h);P.push([M[0],M[1],_[0],_[1],F[0],F[1]])}return P}}const U1={randOffset:function(e,t){return tt(e,t)},randOffsetWithRange:function(e,t,r){return ya(e,t,r)},ellipse:function(e,t,r,i,a){const n=Yu(r,i,a);return Vn(e,t,a,n).opset},doubleLineOps:function(e,t,r,i,a){return ze(e,t,r,i,a,!0)}};function Uu(e,t,r,i,a){return{type:"path",ops:ze(e,t,r,i,a)}}function Gi(e,t,r){const i=(e||[]).length;if(i>2){const a=[];for(let n=0;n<i-1;n++)a.push(...ze(e[n][0],e[n][1],e[n+1][0],e[n+1][1],r));return t&&a.push(...ze(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:a}}return i===2?Uu(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function Y1(e,t,r,i,a){return function(n,o){return Gi(n,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],a)}function ul(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=Di(r[0],1*(1+.2*t.roughness),t),a=t.disableMultiStroke?[]:Di(r[0],1.5*(1+.22*t.roughness),pl(t));for(let n=1;n<r.length;n++){const o=r[n];if(o.length){const s=Di(o,1*(1+.2*t.roughness),t),l=t.disableMultiStroke?[]:Di(o,1.5*(1+.22*t.roughness),pl(t));for(const c of s)c.op!=="move"&&i.push(c);for(const c of l)c.op!=="move"&&a.push(c)}}return{type:"path",ops:i.concat(a)}}return{type:"path",ops:[]}}function Yu(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),a=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),n=2*Math.PI/a;let o=Math.abs(e/2),s=Math.abs(t/2);const l=1-r.curveFitting;return o+=tt(o*l,r),s+=tt(s*l,r),{increment:n,rx:o,ry:s}}function Vn(e,t,r,i){const[a,n]=gl(i.increment,e,t,i.rx,i.ry,1,i.increment*ya(.1,ya(.4,1,r),r),r);let o=xa(a,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=gl(i.increment,e,t,i.rx,i.ry,1.5,0,r),l=xa(s,null,r);o=o.concat(l)}return{estimatedPoints:n,opset:{type:"path",ops:o}}}function dl(e,t,r,i,a,n,o,s,l){const c=e,h=t;let u=Math.abs(r/2),p=Math.abs(i/2);u+=tt(.01*u,l),p+=tt(.01*p,l);let d=a,g=n;for(;d<0;)d+=2*Math.PI,g+=2*Math.PI;g-d>2*Math.PI&&(d=0,g=2*Math.PI);const m=2*Math.PI/l.curveStepCount,y=Math.min(m/2,(g-d)/2),x=ml(y,c,h,u,p,d,g,1,l);if(!l.disableMultiStroke){const b=ml(y,c,h,u,p,d,g,1.5,l);x.push(...b)}return o&&(s?x.push(...ze(c,h,c+u*Math.cos(d),h+p*Math.sin(d),l),...ze(c,h,c+u*Math.cos(g),h+p*Math.sin(g),l)):x.push({op:"lineTo",data:[c,h]},{op:"lineTo",data:[c+u*Math.cos(d),h+p*Math.sin(d)]})),{type:"path",ops:x}}function fl(e,t){const r=Hu(qu(Gs(e))),i=[];let a=[0,0],n=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":n=[s[0],s[1]],a=[s[0],s[1]];break;case"L":i.push(...ze(n[0],n[1],s[0],s[1],t)),n=[s[0],s[1]];break;case"C":{const[l,c,h,u,p,d]=s;i.push(...G1(l,c,h,u,p,d,n,t)),n=[p,d];break}case"Z":i.push(...ze(n[0],n[1],a[0],a[1],t)),n=[a[0],a[1]]}return{type:"path",ops:i}}function pn(e,t){const r=[];for(const i of e)if(i.length){const a=t.maxRandomnessOffset||0,n=i.length;if(n>2){r.push({op:"move",data:[i[0][0]+tt(a,t),i[0][1]+tt(a,t)]});for(let o=1;o<n;o++)r.push({op:"lineTo",data:[i[o][0]+tt(a,t),i[o][1]+tt(a,t)]})}}return{type:"fillPath",ops:r}}function pr(e,t){return function(r,i){let a=r.fillStyle||"hachure";if(!jt[a])switch(a){case"zigzag":jt[a]||(jt[a]=new P1(i));break;case"cross-hatch":jt[a]||(jt[a]=new N1(i));break;case"dots":jt[a]||(jt[a]=new z1(i));break;case"dashed":jt[a]||(jt[a]=new W1(i));break;case"zigzag-line":jt[a]||(jt[a]=new q1(i));break;default:a="hachure",jt[a]||(jt[a]=new Ys(i))}return jt[a]}(t,U1).fillPolygons(e,t)}function pl(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function Gu(e){return e.randomizer||(e.randomizer=new H1(e.seed||0)),e.randomizer.next()}function ya(e,t,r,i=1){return r.roughness*i*(Gu(r)*(t-e)+e)}function tt(e,t,r=1){return ya(-e,e,t,r)}function ze(e,t,r,i,a,n=!1){const o=n?a.disableMultiStrokeFill:a.disableMultiStroke,s=Xn(e,t,r,i,a,!0,!1);if(o)return s;const l=Xn(e,t,r,i,a,!0,!0);return s.concat(l)}function Xn(e,t,r,i,a,n,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),l=Math.sqrt(s);let c=1;c=l<200?1:l>500?.4:-.0016668*l+1.233334;let h=a.maxRandomnessOffset||0;h*h*100>s&&(h=l/10);const u=h/2,p=.2+.2*Gu(a);let d=a.bowing*a.maxRandomnessOffset*(i-t)/200,g=a.bowing*a.maxRandomnessOffset*(e-r)/200;d=tt(d,a,c),g=tt(g,a,c);const m=[],y=()=>tt(u,a,c),x=()=>tt(h,a,c),b=a.preserveVertices;return o?m.push({op:"move",data:[e+(b?0:y()),t+(b?0:y())]}):m.push({op:"move",data:[e+(b?0:tt(h,a,c)),t+(b?0:tt(h,a,c))]}),o?m.push({op:"bcurveTo",data:[d+e+(r-e)*p+y(),g+t+(i-t)*p+y(),d+e+2*(r-e)*p+y(),g+t+2*(i-t)*p+y(),r+(b?0:y()),i+(b?0:y())]}):m.push({op:"bcurveTo",data:[d+e+(r-e)*p+x(),g+t+(i-t)*p+x(),d+e+2*(r-e)*p+x(),g+t+2*(i-t)*p+x(),r+(b?0:x()),i+(b?0:x())]}),m}function Di(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+tt(t,r),e[0][1]+tt(t,r)]),i.push([e[0][0]+tt(t,r),e[0][1]+tt(t,r)]);for(let a=1;a<e.length;a++)i.push([e[a][0]+tt(t,r),e[a][1]+tt(t,r)]),a===e.length-1&&i.push([e[a][0]+tt(t,r),e[a][1]+tt(t,r)]);return xa(i,null,r)}function xa(e,t,r){const i=e.length,a=[];if(i>3){const n=[],o=1-r.curveTightness;a.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const l=e[s];n[0]=[l[0],l[1]],n[1]=[l[0]+(o*e[s+1][0]-o*e[s-1][0])/6,l[1]+(o*e[s+1][1]-o*e[s-1][1])/6],n[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],n[3]=[e[s+1][0],e[s+1][1]],a.push({op:"bcurveTo",data:[n[1][0],n[1][1],n[2][0],n[2][1],n[3][0],n[3][1]]})}}else i===3?(a.push({op:"move",data:[e[1][0],e[1][1]]}),a.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&a.push(...Xn(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return a}function gl(e,t,r,i,a,n,o,s){const l=[],c=[];if(s.roughness===0){e/=4,c.push([t+i*Math.cos(-e),r+a*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+a*Math.sin(h)];l.push(u),c.push(u)}c.push([t+i*Math.cos(0),r+a*Math.sin(0)]),c.push([t+i*Math.cos(e),r+a*Math.sin(e)])}else{const h=tt(.5,s)-Math.PI/2;c.push([tt(n,s)+t+.9*i*Math.cos(h-e),tt(n,s)+r+.9*a*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let p=h;p<u;p+=e){const d=[tt(n,s)+t+i*Math.cos(p),tt(n,s)+r+a*Math.sin(p)];l.push(d),c.push(d)}c.push([tt(n,s)+t+i*Math.cos(h+2*Math.PI+.5*o),tt(n,s)+r+a*Math.sin(h+2*Math.PI+.5*o)]),c.push([tt(n,s)+t+.98*i*Math.cos(h+o),tt(n,s)+r+.98*a*Math.sin(h+o)]),c.push([tt(n,s)+t+.9*i*Math.cos(h+.5*o),tt(n,s)+r+.9*a*Math.sin(h+.5*o)])}return[c,l]}function ml(e,t,r,i,a,n,o,s,l){const c=n+tt(.1,l),h=[];h.push([tt(s,l)+t+.9*i*Math.cos(c-e),tt(s,l)+r+.9*a*Math.sin(c-e)]);for(let u=c;u<=o;u+=e)h.push([tt(s,l)+t+i*Math.cos(u),tt(s,l)+r+a*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),xa(h,null,l)}function G1(e,t,r,i,a,n,o,s){const l=[],c=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,p=s.preserveVertices;for(let d=0;d<u;d++)d===0?l.push({op:"move",data:[o[0],o[1]]}):l.push({op:"move",data:[o[0]+(p?0:tt(c[0],s)),o[1]+(p?0:tt(c[0],s))]}),h=p?[a,n]:[a+tt(c[d],s),n+tt(c[d],s)],l.push({op:"bcurveTo",data:[e+tt(c[d],s),t+tt(c[d],s),r+tt(c[d],s),i+tt(c[d],s),h[0],h[1]]});return l}function Gr(e){return[...e]}function yl(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(Gr(e[0]),Gr(e[1]),Gr(e[2]),Gr(e[2]));else{const a=[];a.push(e[0],e[0]);for(let s=1;s<e.length;s++)a.push(e[s]),s===e.length-1&&a.push(e[s]);const n=[],o=1-t;i.push(Gr(a[0]));for(let s=1;s+2<a.length;s++){const l=a[s];n[0]=[l[0],l[1]],n[1]=[l[0]+(o*a[s+1][0]-o*a[s-1][0])/6,l[1]+(o*a[s+1][1]-o*a[s-1][1])/6],n[2]=[a[s+1][0]+(o*a[s][0]-o*a[s+2][0])/6,a[s+1][1]+(o*a[s][1]-o*a[s+2][1])/6],n[3]=[a[s+1][0],a[s+1][1]],i.push(n[1],n[2],n[3])}}return i}function Vi(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function V1(e,t,r){const i=Vi(t,r);if(i===0)return Vi(e,t);let a=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return a=Math.max(0,Math.min(1,a)),Vi(e,Ge(t,r,a))}function Ge(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function Zn(e,t,r,i){const a=i||[];if(function(s,l){const c=s[l+0],h=s[l+1],u=s[l+2],p=s[l+3];let d=3*h[0]-2*c[0]-p[0];d*=d;let g=3*h[1]-2*c[1]-p[1];g*=g;let m=3*u[0]-2*p[0]-c[0];m*=m;let y=3*u[1]-2*p[1]-c[1];return y*=y,d<m&&(d=m),g<y&&(g=y),d+g}(e,t)<r){const s=e[t+0];a.length?(n=a[a.length-1],o=s,Math.sqrt(Vi(n,o))>1&&a.push(s)):a.push(s),a.push(e[t+3])}else{const l=e[t+0],c=e[t+1],h=e[t+2],u=e[t+3],p=Ge(l,c,.5),d=Ge(c,h,.5),g=Ge(h,u,.5),m=Ge(p,d,.5),y=Ge(d,g,.5),x=Ge(m,y,.5);Zn([l,p,m,x],0,r,a),Zn([x,y,g,u],0,r,a)}var n,o;return a}function X1(e,t){return ba(e,0,e.length,t)}function ba(e,t,r,i,a){const n=a||[],o=e[t],s=e[r-1];let l=0,c=1;for(let h=t+1;h<r-1;++h){const u=V1(e[h],o,s);u>l&&(l=u,c=h)}return Math.sqrt(l)>i?(ba(e,t,c+1,i,n),ba(e,c,r,i,n)):(n.length||n.push(o),n.push(s)),n}function gn(e,t=.15,r){const i=[],a=(e.length-1)/3;for(let n=0;n<a;n++)Zn(e,3*n,t,i);return r&&r>0?ba(i,0,i.length,r):i}const Xt="none";class Ca{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,a,n){const o=this._o(n);return this._d("line",[Uu(t,r,i,a,o)],o)}rectangle(t,r,i,a,n){const o=this._o(n),s=[],l=Y1(t,r,i,a,o);if(o.fill){const c=[[t,r],[t+i,r],[t+i,r+a],[t,r+a]];o.fillStyle==="solid"?s.push(pn([c],o)):s.push(pr([c],o))}return o.stroke!==Xt&&s.push(l),this._d("rectangle",s,o)}ellipse(t,r,i,a,n){const o=this._o(n),s=[],l=Yu(i,a,o),c=Vn(t,r,o,l);if(o.fill)if(o.fillStyle==="solid"){const h=Vn(t,r,o,l).opset;h.type="fillPath",s.push(h)}else s.push(pr([c.estimatedPoints],o));return o.stroke!==Xt&&s.push(c.opset),this._d("ellipse",s,o)}circle(t,r,i,a){const n=this.ellipse(t,r,i,i,a);return n.shape="circle",n}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[Gi(t,!1,i)],i)}arc(t,r,i,a,n,o,s=!1,l){const c=this._o(l),h=[],u=dl(t,r,i,a,n,o,s,!0,c);if(s&&c.fill)if(c.fillStyle==="solid"){const p=Object.assign({},c);p.disableMultiStroke=!0;const d=dl(t,r,i,a,n,o,!0,!1,p);d.type="fillPath",h.push(d)}else h.push(function(p,d,g,m,y,x,b){const k=p,v=d;let S=Math.abs(g/2),L=Math.abs(m/2);S+=tt(.01*S,b),L+=tt(.01*L,b);let T=y,D=x;for(;T<0;)T+=2*Math.PI,D+=2*Math.PI;D-T>2*Math.PI&&(T=0,D=2*Math.PI);const R=(D-T)/b.curveStepCount,O=[];for(let B=T;B<=D;B+=R)O.push([k+S*Math.cos(B),v+L*Math.sin(B)]);return O.push([k+S*Math.cos(D),v+L*Math.sin(D)]),O.push([k,v]),pr([O],b)}(t,r,i,a,n,o,c));return c.stroke!==Xt&&h.push(u),this._d("arc",h,c)}curve(t,r){const i=this._o(r),a=[],n=ul(t,i);if(i.fill&&i.fill!==Xt)if(i.fillStyle==="solid"){const o=ul(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const l=typeof s[0][0]=="number"?[s]:s;for(const c of l)c.length<3?o.push(...c):c.length===3?o.push(...gn(yl([c[0],c[0],c[1],c[2]]),10,(1+i.roughness)/2)):o.push(...gn(yl(c),10,(1+i.roughness)/2))}o.length&&a.push(pr([o],i))}return i.stroke!==Xt&&a.push(n),this._d("curve",a,i)}polygon(t,r){const i=this._o(r),a=[],n=Gi(t,!0,i);return i.fill&&(i.fillStyle==="solid"?a.push(pn([t],i)):a.push(pr([t],i))),i.stroke!==Xt&&a.push(n),this._d("polygon",a,i)}path(t,r){const i=this._o(r),a=[];if(!t)return this._d("path",a,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const n=i.fill&&i.fill!=="transparent"&&i.fill!==Xt,o=i.stroke!==Xt,s=!!(i.simplification&&i.simplification<1),l=function(h,u,p){const d=Hu(qu(Gs(h))),g=[];let m=[],y=[0,0],x=[];const b=()=>{x.length>=4&&m.push(...gn(x,u)),x=[]},k=()=>{b(),m.length&&(g.push(m),m=[])};for(const{key:S,data:L}of d)switch(S){case"M":k(),y=[L[0],L[1]],m.push(y);break;case"L":b(),m.push([L[0],L[1]]);break;case"C":if(!x.length){const T=m.length?m[m.length-1]:y;x.push([T[0],T[1]])}x.push([L[0],L[1]]),x.push([L[2],L[3]]),x.push([L[4],L[5]]);break;case"Z":b(),m.push([y[0],y[1]])}if(k(),!p)return g;const v=[];for(const S of g){const L=X1(S,p);L.length&&v.push(L)}return v}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),c=fl(t,i);if(n)if(i.fillStyle==="solid")if(l.length===1){const h=fl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else a.push(pn(l,i));else a.push(pr(l,i));return o&&(s?l.forEach(h=>{a.push(Gi(h,!1,i))}):a.push(c)),this._d("path",a,i)}opsToPath(t,r){let i="";for(const a of t.ops){const n=typeof r=="number"&&r>=0?a.data.map(o=>+o.toFixed(r)):a.data;switch(a.op){case"move":i+=`M${n[0]} ${n[1]} `;break;case"bcurveTo":i+=`C${n[0]} ${n[1]}, ${n[2]} ${n[3]}, ${n[4]} ${n[5]} `;break;case"lineTo":i+=`L${n[0]} ${n[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,a=[];for(const n of r){let o=null;switch(n.type){case"path":o={d:this.opsToPath(n),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:Xt};break;case"fillPath":o={d:this.opsToPath(n),stroke:Xt,strokeWidth:0,fill:i.fill||Xt};break;case"fillSketch":o=this.fillSketch(n,i)}o&&a.push(o)}return a}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||Xt,strokeWidth:i,fill:Xt}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class Z1{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new Ca(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.ctx,n=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":a.save(),a.strokeStyle=i.stroke==="none"?"transparent":i.stroke,a.lineWidth=i.strokeWidth,i.strokeLineDash&&a.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(a.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(a,o,n),a.restore();break;case"fillPath":{a.save(),a.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(a,o,n,s),a.restore();break}case"fillSketch":this.fillSketch(a,o,i)}}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=a,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,a="nonzero"){t.beginPath();for(const n of r.ops){const o=typeof i=="number"&&i>=0?n.data.map(s=>+s.toFixed(i)):n.data;switch(n.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(a):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o),o}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o),o}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o),o}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n),n}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,a,n,o,s=!1,l){const c=this.gen.arc(t,r,i,a,n,o,s,l);return this.draw(c),c}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const Ri="http://www.w3.org/2000/svg";class K1{constructor(t,r){this.svg=t,this.gen=new Ca(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.svg.ownerDocument||window.document,n=a.createElementNS(Ri,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let l=null;switch(s.type){case"path":l=a.createElementNS(Ri,"path"),l.setAttribute("d",this.opsToPath(s,o)),l.setAttribute("stroke",i.stroke),l.setAttribute("stroke-width",i.strokeWidth+""),l.setAttribute("fill","none"),i.strokeLineDash&&l.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&l.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":l=a.createElementNS(Ri,"path"),l.setAttribute("d",this.opsToPath(s,o)),l.setAttribute("stroke","none"),l.setAttribute("stroke-width","0"),l.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||l.setAttribute("fill-rule","evenodd");break;case"fillSketch":l=this.fillSketch(a,s,i)}l&&n.appendChild(l)}return n}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2);const n=t.createElementNS(Ri,"path");return n.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),n.setAttribute("stroke",i.fill||""),n.setAttribute("stroke-width",a+""),n.setAttribute("fill","none"),i.fillLineDash&&n.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&n.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),n}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o)}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o)}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o)}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,a,n,o,s=!1,l){const c=this.gen.arc(t,r,i,a,n,o,s,l);return this.draw(c)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var U={canvas:(e,t)=>new Z1(e,t),svg:(e,t)=>new K1(e,t),generator:e=>new Ca(e),newSeed:()=>Ca.newSeed()},it=f(async(e,t,r)=>{let i;const a=t.useHtmlLabels||kt(ht()?.htmlLabels);r?i=r:i="node default";const n=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=n.insert("g").attr("class","label").attr("style",zt(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const l=await He(o,Je(or(s),ht()),{useHtmlLabels:a,width:t.width||ht().flowchart?.wrappingWidth,cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let c=l.getBBox();const h=(t?.padding??0)/2;if(a){const u=l.children[0],p=lt(l),d=u.getElementsByTagName("img");if(d){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...d].map(m=>new Promise(y=>{function x(){if(m.style.display="flex",m.style.flexDirection="column",g){const b=ht().fontSize?ht().fontSize:window.getComputedStyle(document.body).fontSize,k=5,[v=Kl.fontSize]=Ia(b),S=v*k+"px";m.style.minWidth=S,m.style.maxWidth=S}else m.style.width="100%";y(m)}f(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}c=u.getBoundingClientRect(),p.attr("width",c.width),p.attr("height",c.height)}return a?o.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"):o.attr("transform","translate(0, "+-c.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:n,bbox:c,halfPadding:h,label:o}},"labelHelper"),mn=f(async(e,t,r)=>{const i=r.useHtmlLabels||kt(ht()?.flowchart?.htmlLabels),a=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),n=await He(a,Je(or(t),ht()),{useHtmlLabels:i,width:r.width||ht()?.flowchart?.wrappingWidth,style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=n.getBBox();const s=r.padding/2;if(kt(ht()?.flowchart?.htmlLabels)){const l=n.children[0],c=lt(n);o=l.getBoundingClientRect(),c.attr("width",o.width),c.attr("height",o.height)}return i?a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):a.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),a.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:a}},"insertLabel"),V=f((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),et=f((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function ct(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}f(ct,"createPathFromPoints");function We(e,t,r,i,a,n){const o=[],l=r-e,c=i-t,h=l/n,u=2*Math.PI/h,p=t+c/2;for(let d=0;d<=50;d++){const g=d/50,m=e+g*l,y=p+a*Math.sin(u*(m-e));o.push({x:m,y})}return o}f(We,"generateFullSineWavePoints");function Vs(e,t,r,i,a,n){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(Vs,"generateCirclePoints");var Q1=f((e,t)=>{var r=e.x,i=e.y,a=t.x-r,n=t.y-i,o=e.width/2,s=e.height/2,l,c;return Math.abs(n)*o>Math.abs(a)*s?(n<0&&(s=-s),l=n===0?0:s*a/n,c=s):(a<0&&(o=-o),l=o,c=a===0?0:o*n/a),{x:r+l,y:i+c}},"intersectRect"),Fr=Q1;function Vu(e,t){t&&e.attr("style",t)}f(Vu,"applyStyle");async function Xu(e){const t=lt(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div");let i=e.label;e.label&&Sr(e.label)&&(i=await cs(e.label.replace(Ar.lineBreakRegex,`
`),ht()));const a=e.isNode?"nodeLabel":"edgeLabel";return r.html('<span class="'+a+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+i+"</span>"),Vu(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}f(Xu,"addHtmlLabel");var J1=f(async(e,t,r,i)=>{let a=e||"";if(typeof a=="object"&&(a=a[0]),kt(ht().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />"),$.info("vertexText"+a);const n={isNode:i,label:or(a).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await Xu(n)}else{const n=document.createElementNS("http://www.w3.org/2000/svg","text");n.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof a=="string"?o=a.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(a)?o=a:o=[];for(const s of o){const l=document.createElementNS("http://www.w3.org/2000/svg","tspan");l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),l.setAttribute("dy","1em"),l.setAttribute("x","0"),r?l.setAttribute("class","title-row"):l.setAttribute("class","row"),l.textContent=s.trim(),n.appendChild(l)}return n}},"createLabel"),Qe=J1,Ee=f((e,t,r,i,a)=>["M",e+a,t,"H",e+r-a,"A",a,a,0,0,1,e+r,t+a,"V",t+i-a,"A",a,a,0,0,1,e+r-a,t+i,"H",e+a,"A",a,a,0,0,1,e,t+i-a,"V",t+a,"A",a,a,0,0,1,e+a,t,"Z"].join(" "),"createRoundedRectPathD"),t2=f(e=>{const{handDrawnSeed:t}=ht();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),$r=f(e=>{const t=e2([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),e2=f(e=>{const t=new Map;return e.forEach(r=>{const[i,a]=r.split(":");t.set(i.trim(),a?.trim())}),t},"styles2Map"),Zu=f(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),X=f(e=>{const{stylesArray:t}=$r(e),r=[],i=[],a=[],n=[];return t.forEach(o=>{const s=o[0];Zu(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&a.push(o.join(":")+" !important"),s==="fill"&&n.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:a,backgroundStyles:n}},"styles2String"),G=f((e,t)=>{const{themeVariables:r,handDrawnSeed:i}=ht(),{nodeBorder:a,mainBkg:n}=r,{stylesMap:o}=$r(e);return Object.assign({roughness:.7,fill:o.get("fill")||n,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||a,seed:i,strokeWidth:o.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0]},t)},"userNodeOverrides"),Ku=f(async(e,t)=>{$.info("Creating subgraph rect for ",t.id,t);const r=ht(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:l,borderStyles:c,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),p=kt(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await He(d,t.label,{style:t.labelStyle,useHtmlLabels:p,isNode:!0});let m=g.getBBox();if(kt(r.flowchart.htmlLabels)){const T=g.children[0],D=lt(g);m=T.getBoundingClientRect(),D.attr("width",m.width),D.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,k=t.y-x/2;$.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const T=U.svg(u),D=G(t,{roughness:.7,fill:n,stroke:o,fillWeight:3,seed:a}),R=T.path(Ee(b,k,y,x,0),D);v=u.insert(()=>($.debug("Rough node insert CXC",R),R),":first-child"),v.select("path:nth-child(2)").attr("style",c.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",k).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:S}=ws(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+S})`),s){const T=d.select("span");T&&T.attr("style",s)}const L=v.node().getBBox();return t.offsetX=0,t.width=L.width,t.height=L.height,t.offsetY=m.height-t.padding/2,t.intersect=function(T){return Fr(t,T)},{cluster:u,labelBBox:m}},"rect"),r2=f((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),a=0*t.padding,n=a/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-n).attr("y",t.y-t.height/2-n).attr("width",t.width+a).attr("height",t.height+a).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return Fr(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),i2=f(async(e,t)=>{const r=ht(),{themeVariables:i,handDrawnSeed:a}=r,{altBackground:n,compositeBackground:o,compositeTitleBackground:s,nodeBorder:l}=i,c=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=c.insert("g",":first-child"),u=c.insert("g").attr("class","cluster-label");let p=c.append("rect");const d=u.node().appendChild(await Qe(t.label,t.labelStyle,void 0,!0));let g=d.getBBox();if(kt(r.flowchart.htmlLabels)){const R=d.children[0],O=lt(d);g=R.getBoundingClientRect(),O.attr("width",g.width),O.attr("height",g.height)}const m=0*t.padding,y=m/2,x=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+m;t.width<=g.width+t.padding?t.diff=(x-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height+m,k=t.height+m-g.height-6,v=t.x-x/2,S=t.y-b/2;t.width=x;const L=t.y-t.height/2-y+g.height+2;let T;if(t.look==="handDrawn"){const R=t.cssClasses.includes("statediagram-cluster-alt"),O=U.svg(c),B=t.rx||t.ry?O.path(Ee(v,S,x,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:l,seed:a}):O.rectangle(v,S,x,b,{seed:a});T=c.insert(()=>B,":first-child");const z=O.rectangle(v,L,x,k,{fill:R?n:o,fillStyle:R?"hachure":"solid",stroke:l,seed:a});T=c.insert(()=>B,":first-child"),p=c.insert(()=>z)}else T=h.insert("rect",":first-child"),T.attr("class","outer").attr("x",v).attr("y",S).attr("width",x).attr("height",b).attr("data-look",t.look),p.attr("class","inner").attr("x",v).attr("y",L).attr("width",x).attr("height",k);u.attr("transform",`translate(${t.x-g.width/2}, ${S+1-(kt(r.flowchart.htmlLabels)?0:3)})`);const D=T.node().getBBox();return t.height=D.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(R){return Fr(t,R)},{cluster:c,labelBBox:g}},"roundedWithTitle"),a2=f(async(e,t)=>{$.info("Creating subgraph rect for ",t.id,t);const r=ht(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:l,borderStyles:c,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),p=kt(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await He(d,t.label,{style:t.labelStyle,useHtmlLabels:p,isNode:!0,width:t.width});let m=g.getBBox();if(kt(r.flowchart.htmlLabels)){const T=g.children[0],D=lt(g);m=T.getBoundingClientRect(),D.attr("width",m.width),D.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,k=t.y-x/2;$.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const T=U.svg(u),D=G(t,{roughness:.7,fill:n,stroke:o,fillWeight:4,seed:a}),R=T.path(Ee(b,k,y,x,t.rx),D);v=u.insert(()=>($.debug("Rough node insert CXC",R),R),":first-child"),v.select("path:nth-child(2)").attr("style",c.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",k).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:S}=ws(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+S})`),s){const T=d.select("span");T&&T.attr("style",s)}const L=v.node().getBBox();return t.offsetX=0,t.width=L.width,t.height=L.height,t.offsetY=m.height-t.padding/2,t.intersect=function(T){return Fr(t,T)},{cluster:u,labelBBox:m}},"kanbanSection"),n2=f((e,t)=>{const r=ht(),{themeVariables:i,handDrawnSeed:a}=r,{nodeBorder:n}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),l=0*t.padding,c=t.width+l;t.diff=-t.padding;const h=t.height+l,u=t.x-c/2,p=t.y-h/2;t.width=c;let d;if(t.look==="handDrawn"){const y=U.svg(o).rectangle(u,p,c,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:a});d=o.insert(()=>y,":first-child")}else d=s.insert("rect",":first-child"),d.attr("class","divider").attr("x",u).attr("y",p).attr("width",c).attr("height",h).attr("data-look",t.look);const g=d.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return Fr(t,m)},{cluster:o,labelBBox:{}}},"divider"),s2=Ku,o2={rect:Ku,squareRect:s2,roundedWithTitle:i2,noteGroup:r2,divider:n2,kanbanSection:a2},Qu=new Map,l2=f(async(e,t)=>{const r=t.shape||"rect",i=await o2[r](e,t);return Qu.set(t.id,i),i},"insertCluster"),yS=f(()=>{Qu=new Map},"clear");function Ju(e,t){return e.intersect(t)}f(Ju,"intersectNode");var c2=Ju;function td(e,t,r,i){var a=e.x,n=e.y,o=a-i.x,s=n-i.y,l=Math.sqrt(t*t*s*s+r*r*o*o),c=Math.abs(t*r*o/l);i.x<a&&(c=-c);var h=Math.abs(t*r*s/l);return i.y<n&&(h=-h),{x:a+c,y:n+h}}f(td,"intersectEllipse");var ed=td;function rd(e,t,r){return ed(e,t,t,r)}f(rd,"intersectCircle");var h2=rd;function id(e,t,r,i){var a,n,o,s,l,c,h,u,p,d,g,m,y,x,b;if(a=t.y-e.y,o=e.x-t.x,l=t.x*e.y-e.x*t.y,p=a*r.x+o*r.y+l,d=a*i.x+o*i.y+l,!(p!==0&&d!==0&&Kn(p,d))&&(n=i.y-r.y,s=r.x-i.x,c=i.x*r.y-r.x*i.y,h=n*e.x+s*e.y+c,u=n*t.x+s*t.y+c,!(h!==0&&u!==0&&Kn(h,u))&&(g=a*s-n*o,g!==0)))return m=Math.abs(g/2),y=o*c-s*l,x=y<0?(y-m)/g:(y+m)/g,y=n*l-a*c,b=y<0?(y-m)/g:(y+m)/g,{x,y:b}}f(id,"intersectLine");function Kn(e,t){return e*t>0}f(Kn,"sameSign");var u2=id;function ad(e,t,r){let i=e.x,a=e.y,n=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let l=i-e.width/2-o,c=a-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],p=t[h<t.length-1?h+1:0],d=u2(e,r,{x:l+u.x,y:c+u.y},{x:l+p.x,y:c+p.y});d&&n.push(d)}return n.length?(n.length>1&&n.sort(function(h,u){let p=h.x-r.x,d=h.y-r.y,g=Math.sqrt(p*p+d*d),m=u.x-r.x,y=u.y-r.y,x=Math.sqrt(m*m+y*y);return g<x?-1:g===x?0:1}),n[0]):e}f(ad,"intersectPolygon");var d2=ad,j={node:c2,circle:h2,ellipse:ed,polygon:d2,rect:Fr};function nd(e,t){const{labelStyles:r}=X(t);t.labelStyle=r;const i=et(t);let a=i;i||(a="anchor");const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,l=U.svg(n),c=G(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(c.roughness=0);const h=l.circle(0,0,o*2,c),u=n.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",zt(s)),V(t,u),t.intersect=function(p){return $.info("Circle intersect",t,o,p),j.circle(t,o,p)},n}f(nd,"anchor");function Qn(e,t,r,i,a,n,o){const l=(e+r)/2,c=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,p=(i-t)/2,d=u/a,g=p/n,m=Math.sqrt(d**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=l+y*n*Math.sin(h)*(o?-1:1),b=c-y*a*Math.cos(h)*(o?-1:1),k=Math.atan2((t-b)/n,(e-x)/a);let S=Math.atan2((i-b)/n,(r-x)/a)-k;o&&S<0&&(S+=2*Math.PI),!o&&S>0&&(S-=2*Math.PI);const L=[];for(let T=0;T<20;T++){const D=T/19,R=k+D*S,O=x+a*Math.cos(R),B=b+n*Math.sin(R);L.push({x:O,y:B})}return L}f(Qn,"generateArcPoints");async function sd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=n.width+t.padding+20,s=n.height+t.padding,l=s/2,c=l/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...Qn(-o/2,-s/2,-o/2,s/2,c,l,!1),{x:o/2,y:s/2},...Qn(o/2,s/2,o/2,-s/2,c,l,!0)],p=U.svg(a),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=ct(u),m=p.path(g,d),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${c/2}, 0)`),V(t,y),t.intersect=function(x){return j.polygon(t,u,x)},a}f(sd,"bowTieRect");function Fe(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}f(Fe,"insertPolygonShape");async function od(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=n.height+t.padding,s=12,l=n.width+t.padding+s,c=0,h=l,u=-o,p=0,d=[{x:c+s,y:u},{x:h,y:u},{x:h,y:p},{x:c,y:p},{x:c,y:u+s},{x:c+s,y:u}];let g;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=U.svg(a),x=G(t,{}),b=ct(d),k=y.path(b,x);g=a.insert(()=>k,":first-child").attr("transform",`translate(${-l/2}, ${o/2})`),m&&g.attr("style",m)}else g=Fe(a,l,o,d);return i&&g.attr("style",i),V(t,g),t.intersect=function(y){return j.polygon(t,d,y)},a}f(od,"card");function ld(e,t){const{nodeStyles:r}=X(t);t.label="";const i=e.insert("g").attr("class",et(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,n=Math.max(28,t.width??0),o=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}],s=U.svg(i),l=G(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const c=ct(o),h=s.path(c,l),u=i.insert(()=>h,":first-child");return a&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",a),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(p){return j.polygon(t,o,p)},i}f(ld,"choice");async function cd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await it(e,t,et(t)),s=n.width/2+o;let l;const{cssStyles:c}=t;if(t.look==="handDrawn"){const h=U.svg(a),u=G(t,{}),p=h.circle(0,0,s*2,u);l=a.insert(()=>p,":first-child"),l.attr("class","basic label-container").attr("style",zt(c))}else l=a.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return V(t,l),t.intersect=function(h){return $.info("Circle intersect",t,s,h),j.circle(t,s,h)},a}f(cd,"circle");function hd(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,a={x:i/2*t,y:i/2*r},n={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${n.x},${n.y} L ${s.x},${s.y}
                   M ${a.x},${a.y} L ${o.x},${o.y}`}f(hd,"createLine");function ud(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r,t.label="";const a=e.insert("g").attr("class",et(t)).attr("id",t.domId??t.id),n=Math.max(30,t?.width??0),{cssStyles:o}=t,s=U.svg(a),l=G(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const c=s.circle(0,0,n*2,l),h=hd(n),u=s.path(h,l),p=a.insert(()=>c,":first-child");return p.insert(()=>u),o&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",i),V(t,p),t.intersect=function(d){return $.info("crossedCircle intersect",t,{radius:n,point:d}),j.circle(t,n,d)},a}f(ud,"crossedCircle");function ve(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(ve,"generateCirclePoints");async function dd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[...ve(s/2,-l/2,c,30,-90,0),{x:-s/2-c,y:c},...ve(s/2+c*2,-c,c,20,-180,-270),...ve(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:-l/2},...ve(s/2,l/2,c,20,0,90)],p=[{x:s/2,y:-l/2-c},{x:-s/2,y:-l/2-c},...ve(s/2,-l/2,c,20,-90,0),{x:-s/2-c,y:-c},...ve(s/2+s*.1,-c,c,20,-180,-270),...ve(s/2+s*.1,c,c,20,-90,-180),{x:-s/2-c,y:l/2},...ve(s/2,l/2,c,20,0,90),{x:-s/2,y:l/2+c},{x:s/2,y:l/2+c}],d=U.svg(a),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=d.path(y,g),b=ct(p),k=d.path(b,{...g}),v=a.insert("g",":first-child");return v.insert(()=>k,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${c}, 0)`),o.attr("transform",`translate(${-s/2+c-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,v),t.intersect=function(S){return j.polygon(t,p,S)},a}f(dd,"curlyBraceLeft");function Se(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:d,y:g})}return o}f(Se,"generateCirclePoints");async function fd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[...Se(s/2,-l/2,c,20,-90,0),{x:s/2+c,y:-c},...Se(s/2+c*2,-c,c,20,-180,-270),...Se(s/2+c*2,c,c,20,-90,-180),{x:s/2+c,y:l/2},...Se(s/2,l/2,c,20,0,90)],p=[{x:-s/2,y:-l/2-c},{x:s/2,y:-l/2-c},...Se(s/2,-l/2,c,20,-90,0),{x:s/2+c,y:-c},...Se(s/2+c*2,-c,c,20,-180,-270),...Se(s/2+c*2,c,c,20,-90,-180),{x:s/2+c,y:l/2},...Se(s/2,l/2,c,20,0,90),{x:s/2,y:l/2+c},{x:-s/2,y:l/2+c}],d=U.svg(a),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=d.path(y,g),b=ct(p),k=d.path(b,{...g}),v=a.insert("g",":first-child");return v.insert(()=>k,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${-c}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,v),t.intersect=function(S){return j.polygon(t,p,S)},a}f(fd,"curlyBraceRight");function _t(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(_t,"generateCirclePoints");async function pd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=n.width+(t.padding??0),l=n.height+(t.padding??0),c=Math.max(5,l*.1),{cssStyles:h}=t,u=[..._t(s/2,-l/2,c,30,-90,0),{x:-s/2-c,y:c},..._t(s/2+c*2,-c,c,20,-180,-270),..._t(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:-l/2},..._t(s/2,l/2,c,20,0,90)],p=[..._t(-s/2+c+c/2,-l/2,c,20,-90,-180),{x:s/2-c/2,y:c},..._t(-s/2-c/2,-c,c,20,0,90),..._t(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},..._t(-s/2+c+c/2,l/2,c,30,-180,-270)],d=[{x:s/2,y:-l/2-c},{x:-s/2,y:-l/2-c},..._t(s/2,-l/2,c,20,-90,0),{x:-s/2-c,y:-c},..._t(s/2+c*2,-c,c,20,-180,-270),..._t(s/2+c*2,c,c,20,-90,-180),{x:-s/2-c,y:l/2},..._t(s/2,l/2,c,20,0,90),{x:-s/2,y:l/2+c},{x:s/2-c-c/2,y:l/2+c},..._t(-s/2+c+c/2,-l/2,c,20,-90,-180),{x:s/2-c/2,y:c},..._t(-s/2-c/2,-c,c,20,0,90),..._t(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},..._t(-s/2+c+c/2,l/2,c,30,-180,-270)],g=U.svg(a),m=G(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const x=ct(u).replace("Z",""),b=g.path(x,m),v=ct(p).replace("Z",""),S=g.path(v,m),L=ct(d),T=g.path(L,{...m}),D=a.insert("g",":first-child");return D.insert(()=>T,":first-child").attr("stroke-opacity",0),D.insert(()=>b,":first-child"),D.insert(()=>S,":first-child"),D.attr("class","text"),h&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",i),D.attr("transform",`translate(${c-c/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,D),t.intersect=function(R){return j.polygon(t,d,R)},a}f(pd,"curlyBraces");async function gd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=80,s=20,l=Math.max(o,(n.width+(t.padding??0)*2)*1.25,t?.width??0),c=Math.max(s,n.height+(t.padding??0)*2,t?.height??0),h=c/2,{cssStyles:u}=t,p=U.svg(a),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=l,m=c,y=g-h,x=m/4,b=[{x:y,y:0},{x,y:0},{x:0,y:m/2},{x,y:m},{x:y,y:m},...Vs(-y,-m/2,h,50,270,90)],k=ct(b),v=p.path(k,d),S=a.insert(()=>v,":first-child");return S.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",i),S.attr("transform",`translate(${-l/2}, ${-c/2})`),V(t,S),t.intersect=function(L){return j.polygon(t,b,L)},a}f(gd,"curvedTrapezoid");var f2=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),p2=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),g2=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function md(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+t.padding,t.width??0),l=s/2,c=l/(2.5+s/50),h=Math.max(n.height+c+t.padding,t.height??0);let u;const{cssStyles:p}=t;if(t.look==="handDrawn"){const d=U.svg(a),g=p2(0,0,s,h,l,c),m=g2(0,c,s,h,l,c),y=d.path(g,G(t,{})),x=d.path(m,G(t,{fill:"none"}));u=a.insert(()=>x,":first-child"),u=a.insert(()=>y,":first-child"),u.attr("class","basic label-container"),p&&u.attr("style",p)}else{const d=f2(0,0,s,h,l,c);u=a.insert("path",":first-child").attr("d",d).attr("class","basic label-container").attr("style",zt(p)).attr("style",i)}return u.attr("label-offset-y",c),u.attr("transform",`translate(${-s/2}, ${-(h/2+c)})`),V(t,u),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+(t.padding??0)/1.5-(n.y-(n.top??0))})`),t.intersect=function(d){const g=j.rect(t,d),m=g.x-(t.x??0);if(l!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-c)){let y=c*c*(1-m*m/(l*l));y>0&&(y=Math.sqrt(y)),y=c-y,d.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},a}f(md,"cylinder");async function yd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=n.width+t.padding,l=n.height+t.padding,c=l*.2,h=-s/2,u=-l/2-c/2,{cssStyles:p}=t,d=U.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u+c},{x:-h,y:u+c},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+c}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(n.x-(n.left??0))}, ${u+c+(t.padding??0)/2-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return j.rect(t,b)},a}f(yd,"dividedRectangle");async function xd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await it(e,t,et(t)),l=n.width/2+o+5,c=n.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const p=U.svg(a),d=G(t,{roughness:.2,strokeWidth:2.5}),g=G(t,{roughness:.2,strokeWidth:1.5}),m=p.circle(0,0,l*2,d),y=p.circle(0,0,c*2,g);h=a.insert("g",":first-child"),h.attr("class",zt(t.cssClasses)).attr("style",zt(u)),h.node()?.appendChild(m),h.node()?.appendChild(y)}else{h=a.insert("g",":first-child");const p=h.insert("circle",":first-child"),d=h.insert("circle");h.attr("class","basic label-container").attr("style",i),p.attr("class","outer-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0),d.attr("class","inner-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0)}return V(t,h),t.intersect=function(p){return $.info("DoubleCircle intersect",t,l,p),j.circle(t,l,p)},a}f(xd,"doublecircle");function bd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=X(t);t.label="",t.labelStyle=i;const n=e.insert("g").attr("class",et(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,l=U.svg(n),{nodeBorder:c}=r,h=G(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=l.circle(0,0,o*2,h),p=n.insert(()=>u,":first-child");return p.selectAll("path").attr("style",`fill: ${c} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",a),V(t,p),t.intersect=function(d){return $.info("filledCircle intersect",t,{radius:o,point:d}),j.circle(t,o,d)},n}f(bd,"filledCircle");async function Cd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=n.width+(t.padding??0),l=s+n.height,c=s+n.height,h=[{x:0,y:-l},{x:c,y:-l},{x:c/2,y:0}],{cssStyles:u}=t,p=U.svg(a),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=ct(h),m=p.path(g,d),y=a.insert(()=>m,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=l,V(t,y),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${-l/2+(t.padding??0)/2+(n.y-(n.top??0))})`),t.intersect=function(x){return $.info("Triangle intersect",t,h,x),j.polygon(t,h,x)},a}f(Cd,"flippedTriangle");function kd(e,t,{dir:r,config:{state:i,themeVariables:a}}){const{nodeStyles:n}=X(t);t.label="";const o=e.insert("g").attr("class",et(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let l=Math.max(70,t?.width??0),c=Math.max(10,t?.height??0);r==="LR"&&(l=Math.max(10,t?.width??0),c=Math.max(70,t?.height??0));const h=-1*l/2,u=-1*c/2,p=U.svg(o),d=G(t,{stroke:a.lineColor,fill:a.lineColor});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=p.rectangle(h,u,l,c,d),m=o.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",n),V(t,m);const y=i?.padding??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(x){return j.rect(t,x)},o}f(kd,"forkJoin");async function wd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const a=80,n=50,{shapeSvg:o,bbox:s}=await it(e,t,et(t)),l=Math.max(a,s.width+(t.padding??0)*2,t?.width??0),c=Math.max(n,s.height+(t.padding??0)*2,t?.height??0),h=c/2,{cssStyles:u}=t,p=U.svg(o),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-l/2,y:-c/2},{x:l/2-h,y:-c/2},...Vs(-l/2+h,0,h,50,90,270),{x:l/2-h,y:c/2},{x:-l/2,y:c/2}],m=ct(g),y=p.path(m,d),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),V(t,x),t.intersect=function(b){return $.info("Pill intersect",t,{radius:h,point:b}),j.polygon(t,g,b)},o}f(wd,"halfRoundedRectangle");var m2=f((e,t,r,i,a)=>[`M${e+a},${t}`,`L${e+r-a},${t}`,`L${e+r},${t-i/2}`,`L${e+r-a},${t-i}`,`L${e+a},${t-i}`,`L${e},${t-i/2}`,"Z"].join(" "),"createHexagonPathD");async function vd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=4,s=n.height+t.padding,l=s/o,c=n.width+2*l+t.padding,h=[{x:l,y:0},{x:c-l,y:0},{x:c,y:-s/2},{x:c-l,y:-s},{x:l,y:-s},{x:0,y:-s/2}];let u;const{cssStyles:p}=t;if(t.look==="handDrawn"){const d=U.svg(a),g=G(t,{}),m=m2(0,0,c,s,l),y=d.path(m,g);u=a.insert(()=>y,":first-child").attr("transform",`translate(${-c/2}, ${s/2})`),p&&u.attr("style",p)}else u=Fe(a,c,s,h);return i&&u.attr("style",i),t.width=c,t.height=s,V(t,u),t.intersect=function(d){return j.polygon(t,h,d)},a}f(vd,"hexagon");async function Sd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const{shapeSvg:a}=await it(e,t,et(t)),n=Math.max(30,t?.width??0),o=Math.max(30,t?.height??0),{cssStyles:s}=t,l=U.svg(a),c=G(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const h=[{x:0,y:0},{x:n,y:0},{x:0,y:o},{x:n,y:o}],u=ct(h),p=l.path(u,c),d=a.insert(()=>p,":first-child");return d.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",i),d.attr("transform",`translate(${-n/2}, ${-o/2})`),V(t,d),t.intersect=function(g){return $.info("Pill intersect",t,{points:h}),j.polygon(t,h,g)},a}f(Sd,"hourglass");async function Td(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i?.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,label:u}=await it(e,t,"icon-shape default"),p=t.pos==="t",d=s,g=s,{nodeBorder:m}=r,{stylesMap:y}=$r(t),x=-g/2,b=-d/2,k=t.label?8:0,v=U.svg(c),S=G(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");const L=v.rectangle(x,b,g,d,S),T=Math.max(g,h.width),D=d+h.height+k,R=v.rectangle(-T/2,-D/2,T,D,{...S,fill:"transparent",stroke:"none"}),O=c.insert(()=>L,":first-child"),B=c.insert(()=>R);if(t.icon){const z=c.append("g");z.html(`<g>${await Ma(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const P=z.node().getBBox(),E=P.width,M=P.height,_=P.x,F=P.y;z.attr("transform",`translate(${-E/2-_},${p?h.height/2+k/2-M/2-F:-h.height/2-k/2-M/2-F})`),z.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-D/2:D/2-h.height})`),O.attr("transform",`translate(0,${p?h.height/2+k/2:-h.height/2-k/2})`),V(t,B),t.intersect=function(z){if($.info("iconSquare intersect",t,z),!t.label)return j.rect(t,z);const P=t.x??0,E=t.y??0,M=t.height??0;let _=[];return p?_=[{x:P-h.width/2,y:E-M/2},{x:P+h.width/2,y:E-M/2},{x:P+h.width/2,y:E-M/2+h.height+k},{x:P+g/2,y:E-M/2+h.height+k},{x:P+g/2,y:E+M/2},{x:P-g/2,y:E+M/2},{x:P-g/2,y:E-M/2+h.height+k},{x:P-h.width/2,y:E-M/2+h.height+k}]:_=[{x:P-g/2,y:E-M/2},{x:P+g/2,y:E-M/2},{x:P+g/2,y:E-M/2+d},{x:P+h.width/2,y:E-M/2+d},{x:P+h.width/2/2,y:E+M/2},{x:P-h.width/2,y:E+M/2},{x:P-h.width/2,y:E-M/2+d},{x:P-g/2,y:E-M/2+d}],j.polygon(t,_,z)},c}f(Td,"icon");async function _d(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i?.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,label:u}=await it(e,t,"icon-shape default"),p=20,d=t.label?8:0,g=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:x}=$r(t),b=U.svg(c),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const v=x.get("fill");k.stroke=v??y;const S=c.append("g");t.icon&&S.html(`<g>${await Ma(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const L=S.node().getBBox(),T=L.width,D=L.height,R=L.x,O=L.y,B=Math.max(T,D)*Math.SQRT2+p*2,z=b.circle(0,0,B,k),P=Math.max(B,h.width),E=B+h.height+d,M=b.rectangle(-P/2,-E/2,P,E,{...k,fill:"transparent",stroke:"none"}),_=c.insert(()=>z,":first-child"),F=c.insert(()=>M);return S.attr("transform",`translate(${-T/2-R},${g?h.height/2+d/2-D/2-O:-h.height/2-d/2-D/2-O})`),S.attr("style",`color: ${x.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${g?-E/2:E/2-h.height})`),_.attr("transform",`translate(0,${g?h.height/2+d/2:-h.height/2-d/2})`),V(t,F),t.intersect=function(A){return $.info("iconSquare intersect",t,A),j.rect(t,A)},c}f(_d,"iconCircle");async function Bd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i?.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,halfPadding:u,label:p}=await it(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=$r(t),k=-m/2,v=-g/2,S=t.label?8:0,L=U.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=b.get("fill");T.stroke=D??x;const R=L.path(Ee(k,v,m,g,5),T),O=Math.max(m,h.width),B=g+h.height+S,z=L.rectangle(-O/2,-B/2,O,B,{...T,fill:"transparent",stroke:"none"}),P=c.insert(()=>R,":first-child").attr("class","icon-shape2"),E=c.insert(()=>z);if(t.icon){const M=c.append("g");M.html(`<g>${await Ma(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=M.node().getBBox(),F=_.width,A=_.height,N=_.x,q=_.y;M.attr("transform",`translate(${-F/2-N},${d?h.height/2+S/2-A/2-q:-h.height/2-S/2-A/2-q})`),M.attr("style",`color: ${b.get("stroke")??y};`)}return p.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-B/2:B/2-h.height})`),P.attr("transform",`translate(0,${d?h.height/2+S/2:-h.height/2-S/2})`),V(t,E),t.intersect=function(M){if($.info("iconSquare intersect",t,M),!t.label)return j.rect(t,M);const _=t.x??0,F=t.y??0,A=t.height??0;let N=[];return d?N=[{x:_-h.width/2,y:F-A/2},{x:_+h.width/2,y:F-A/2},{x:_+h.width/2,y:F-A/2+h.height+S},{x:_+m/2,y:F-A/2+h.height+S},{x:_+m/2,y:F+A/2},{x:_-m/2,y:F+A/2},{x:_-m/2,y:F-A/2+h.height+S},{x:_-h.width/2,y:F-A/2+h.height+S}]:N=[{x:_-m/2,y:F-A/2},{x:_+m/2,y:F-A/2},{x:_+m/2,y:F-A/2+g},{x:_+h.width/2,y:F-A/2+g},{x:_+h.width/2/2,y:F+A/2},{x:_-h.width/2,y:F+A/2},{x:_-h.width/2,y:F-A/2+g},{x:_-m/2,y:F-A/2+g}],j.polygon(t,N,M)},c}f(Bd,"iconRounded");async function Ld(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=X(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),l=i?.wrappingWidth;t.width=Math.max(s,l??0);const{shapeSvg:c,bbox:h,halfPadding:u,label:p}=await it(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=$r(t),k=-m/2,v=-g/2,S=t.label?8:0,L=U.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=b.get("fill");T.stroke=D??x;const R=L.path(Ee(k,v,m,g,.1),T),O=Math.max(m,h.width),B=g+h.height+S,z=L.rectangle(-O/2,-B/2,O,B,{...T,fill:"transparent",stroke:"none"}),P=c.insert(()=>R,":first-child"),E=c.insert(()=>z);if(t.icon){const M=c.append("g");M.html(`<g>${await Ma(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=M.node().getBBox(),F=_.width,A=_.height,N=_.x,q=_.y;M.attr("transform",`translate(${-F/2-N},${d?h.height/2+S/2-A/2-q:-h.height/2-S/2-A/2-q})`),M.attr("style",`color: ${b.get("stroke")??y};`)}return p.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-B/2:B/2-h.height})`),P.attr("transform",`translate(0,${d?h.height/2+S/2:-h.height/2-S/2})`),V(t,E),t.intersect=function(M){if($.info("iconSquare intersect",t,M),!t.label)return j.rect(t,M);const _=t.x??0,F=t.y??0,A=t.height??0;let N=[];return d?N=[{x:_-h.width/2,y:F-A/2},{x:_+h.width/2,y:F-A/2},{x:_+h.width/2,y:F-A/2+h.height+S},{x:_+m/2,y:F-A/2+h.height+S},{x:_+m/2,y:F+A/2},{x:_-m/2,y:F+A/2},{x:_-m/2,y:F-A/2+h.height+S},{x:_-h.width/2,y:F-A/2+h.height+S}]:N=[{x:_-m/2,y:F-A/2},{x:_+m/2,y:F-A/2},{x:_+m/2,y:F-A/2+g},{x:_+h.width/2,y:F-A/2+g},{x:_+h.width/2/2,y:F+A/2},{x:_-h.width/2,y:F+A/2},{x:_-h.width/2,y:F-A/2+g},{x:_-m/2,y:F-A/2+g}],j.polygon(t,N,M)},c}f(Ld,"iconSquare");async function Ad(e,t,{config:{flowchart:r}}){const i=new Image;i.src=t?.img??"",await i.decode();const a=Number(i.naturalWidth.toString().replace("px","")),n=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=a/n;const{labelStyles:o}=X(t);t.labelStyle=o;const s=r?.wrappingWidth;t.defaultWidth=r?.wrappingWidth;const l=Math.max(t.label?s??0:0,t?.assetWidth??a),c=t.constraint==="on"&&t?.assetHeight?t.assetHeight*t.imageAspectRatio:l,h=t.constraint==="on"?c/t.imageAspectRatio:t?.assetHeight??n;t.width=Math.max(c,s??0);const{shapeSvg:u,bbox:p,label:d}=await it(e,t,"image-shape default"),g=t.pos==="t",m=-c/2,y=-h/2,x=t.label?8:0,b=U.svg(u),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const v=b.rectangle(m,y,c,h,k),S=Math.max(c,p.width),L=h+p.height+x,T=b.rectangle(-S/2,-L/2,S,L,{...k,fill:"none",stroke:"none"}),D=u.insert(()=>v,":first-child"),R=u.insert(()=>T);if(t.img){const O=u.append("image");O.attr("href",t.img),O.attr("width",c),O.attr("height",h),O.attr("preserveAspectRatio","none"),O.attr("transform",`translate(${-c/2},${g?L/2-h:-L/2})`)}return d.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${g?-h/2-p.height/2-x/2:h/2-p.height/2+x/2})`),D.attr("transform",`translate(0,${g?p.height/2+x/2:-p.height/2-x/2})`),V(t,R),t.intersect=function(O){if($.info("iconSquare intersect",t,O),!t.label)return j.rect(t,O);const B=t.x??0,z=t.y??0,P=t.height??0;let E=[];return g?E=[{x:B-p.width/2,y:z-P/2},{x:B+p.width/2,y:z-P/2},{x:B+p.width/2,y:z-P/2+p.height+x},{x:B+c/2,y:z-P/2+p.height+x},{x:B+c/2,y:z+P/2},{x:B-c/2,y:z+P/2},{x:B-c/2,y:z-P/2+p.height+x},{x:B-p.width/2,y:z-P/2+p.height+x}]:E=[{x:B-c/2,y:z-P/2},{x:B+c/2,y:z-P/2},{x:B+c/2,y:z-P/2+h},{x:B+p.width/2,y:z-P/2+h},{x:B+p.width/2/2,y:z+P/2},{x:B-p.width/2,y:z+P/2},{x:B-p.width/2,y:z-P/2+h},{x:B-c/2,y:z-P/2+h}],j.polygon(t,E,O)},u}f(Ad,"imageSquare");async function Md(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=Math.max(n.width+(t.padding??0)*2,t?.width??0),s=Math.max(n.height+(t.padding??0)*2,t?.height??0),l=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=U.svg(a),p=G(t,{}),d=ct(l),g=u.path(d,p);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Fe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return j.polygon(t,l,u)},a}f(Md,"inv_trapezoid");async function wi(e,t,r){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i;const{shapeSvg:n,bbox:o}=await it(e,t,et(t)),s=Math.max(o.width+r.labelPaddingX*2,t?.width||0),l=Math.max(o.height+r.labelPaddingY*2,t?.height||0),c=-s/2,h=-l/2;let u,{rx:p,ry:d}=t;const{cssStyles:g}=t;if(r?.rx&&r.ry&&(p=r.rx,d=r.ry),t.look==="handDrawn"){const m=U.svg(n),y=G(t,{}),x=p||d?m.path(Ee(c,h,s,l,p||0),y):m.rectangle(c,h,s,l,y);u=n.insert(()=>x,":first-child"),u.attr("class","basic label-container").attr("style",zt(g))}else u=n.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",a).attr("rx",zt(p)).attr("ry",zt(d)).attr("x",c).attr("y",h).attr("width",s).attr("height",l);return V(t,u),t.intersect=function(m){return j.rect(t,m)},n}f(wi,"drawRect");async function Ed(e,t){const{shapeSvg:r,bbox:i,label:a}=await it(e,t,"label"),n=r.insert("rect",":first-child");return n.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),a.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),V(t,n),t.intersect=function(l){return j.rect(t,l)},r}f(Ed,"labelRect");async function Fd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=Math.max(n.width+(t.padding??0),t?.width??0),s=Math.max(n.height+(t.padding??0),t?.height??0),l=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=U.svg(a),p=G(t,{}),d=ct(l),g=u.path(d,p);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Fe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return j.polygon(t,l,u)},a}f(Fd,"lean_left");async function $d(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=Math.max(n.width+(t.padding??0),t?.width??0),s=Math.max(n.height+(t.padding??0),t?.height??0),l=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=U.svg(a),p=G(t,{}),d=ct(l),g=u.path(d,p);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Fe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return j.polygon(t,l,u)},a}f($d,"lean_right");function Od(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const a=e.insert("g").attr("class",et(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,o=Math.max(35,t?.width??0),s=Math.max(35,t?.height??0),l=7,c=[{x:o,y:0},{x:0,y:s+l/2},{x:o-2*l,y:s+l/2},{x:0,y:2*s},{x:o,y:s-l/2},{x:2*l,y:s-l/2}],h=U.svg(a),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const p=ct(c),d=h.path(p,u),g=a.insert(()=>d,":first-child");return n&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",n),i&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",i),g.attr("transform",`translate(-${o/2},${-s})`),V(t,g),t.intersect=function(m){return $.info("lightningBolt intersect",t,m),j.polygon(t,c,m)},a}f(Od,"lightningBolt");var y2=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),x2=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),b2=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Dd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0),t.width??0),l=s/2,c=l/(2.5+s/50),h=Math.max(n.height+c+(t.padding??0),t.height??0),u=h*.1;let p;const{cssStyles:d}=t;if(t.look==="handDrawn"){const g=U.svg(a),m=x2(0,0,s,h,l,c,u),y=b2(0,c,s,h,l,c),x=G(t,{}),b=g.path(m,x),k=g.path(y,x);a.insert(()=>k,":first-child").attr("class","line"),p=a.insert(()=>b,":first-child"),p.attr("class","basic label-container"),d&&p.attr("style",d)}else{const g=y2(0,0,s,h,l,c,u);p=a.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",zt(d)).attr("style",i)}return p.attr("label-offset-y",c),p.attr("transform",`translate(${-s/2}, ${-(h/2+c)})`),V(t,p),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+c-(n.y-(n.top??0))})`),t.intersect=function(g){const m=j.rect(t,g),y=m.x-(t.x??0);if(l!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-c)){let x=c*c*(1-y*y/(l*l));x>0&&(x=Math.sqrt(x)),x=c-x,g.y-(t.y??0)>0&&(x=-x),m.y+=x}return m},a}f(Dd,"linedCylinder");async function Rd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=l/4,h=l+c,{cssStyles:u}=t,p=U.svg(a),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...We(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,c,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=p.polygon(g.map(x=>[x.x,x.y]),d),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c/2-(n.y-(n.top??0))})`),V(t,y),t.intersect=function(x){return j.polygon(t,g,x)},a}f(Rd,"linedWaveEdgedRect");async function Id(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=5,h=-s/2,u=-l/2,{cssStyles:p}=t,d=U.svg(a),g=G(t,{}),m=[{x:h-c,y:u+c},{x:h-c,y:u+l+c},{x:h+s-c,y:u+l+c},{x:h+s-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u+l-c},{x:h+s+c,y:u+l-c},{x:h+s+c,y:u-c},{x:h+c,y:u-c},{x:h+c,y:u},{x:h,y:u},{x:h,y:u+c}],y=[{x:h,y:u+c},{x:h+s-c,y:u+c},{x:h+s-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=d.path(x,g),k=ct(y),v=d.path(k,{...g,fill:"none"}),S=a.insert(()=>v,":first-child");return S.insert(()=>b,":first-child"),S.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)-c-(n.x-(n.left??0))}, ${-(n.height/2)+c-(n.y-(n.top??0))})`),V(t,S),t.intersect=function(L){return j.polygon(t,m,L)},a}f(Id,"multiRect");async function Pd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=l/4,h=l+c,u=-s/2,p=-h/2,d=5,{cssStyles:g}=t,m=We(u-d,p+h+d,u+s-d,p+h+d,c,.8),y=m?.[m.length-1],x=[{x:u-d,y:p+d},{x:u-d,y:p+h+d},...m,{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:y.y-2*d},{x:u+s+d,y:y.y-2*d},{x:u+s+d,y:p-d},{x:u+d,y:p-d},{x:u+d,y:p},{x:u,y:p},{x:u,y:p+d}],b=[{x:u,y:p+d},{x:u+s-d,y:p+d},{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:p},{x:u,y:p}],k=U.svg(a),v=G(t,{});t.look!=="handDrawn"&&(v.roughness=0,v.fillStyle="solid");const S=ct(x),L=k.path(S,v),T=ct(b),D=k.path(T,v),R=a.insert(()=>L,":first-child");return R.insert(()=>D),R.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",g),i&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",i),R.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-(n.width/2)-d-(n.x-(n.left??0))}, ${-(n.height/2)+d-c/2-(n.y-(n.top??0))})`),V(t,R),t.intersect=function(O){return j.polygon(t,x,O)},a}f(Pd,"multiWaveEdgedRectangle");async function Nd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i,t.useHtmlLabels||Gt().flowchart?.htmlLabels!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s}=await it(e,t,et(t)),l=Math.max(s.width+(t.padding??0)*2,t?.width??0),c=Math.max(s.height+(t.padding??0)*2,t?.height??0),h=-l/2,u=-c/2,{cssStyles:p}=t,d=U.svg(o),g=G(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=d.rectangle(h,u,l,c,g),y=o.insert(()=>m,":first-child");return y.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",p),a&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",a),V(t,y),t.intersect=function(x){return j.rect(t,x)},o}f(Nd,"note");var C2=f((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function zd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=n.width+t.padding,s=n.height+t.padding,l=o+s,c=[{x:l/2,y:0},{x:l,y:-l/2},{x:l/2,y:-l},{x:0,y:-l/2}];let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const p=U.svg(a),d=G(t,{}),g=C2(0,0,l),m=p.path(g,d);h=a.insert(()=>m,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`),u&&h.attr("style",u)}else h=Fe(a,l,l,c);return i&&h.attr("style",i),V(t,h),t.intersect=function(p){return $.debug(`APA12 Intersect called SPLIT
point:`,p,`
node:
`,t,`
res:`,j.polygon(t,c,p)),j.polygon(t,c,p)},a}f(zd,"question");async function Wd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0),t?.width??0),l=Math.max(n.height+(t.padding??0),t?.height??0),c=-s/2,h=-l/2,u=h/2,p=[{x:c+u,y:h},{x:c,y:0},{x:c+u,y:-h},{x:-c,y:-h},{x:-c,y:h}],{cssStyles:d}=t,g=U.svg(a),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=ct(p),x=g.path(y,m),b=a.insert(()=>x,":first-child");return b.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),b.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-n.width/2-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),V(t,b),t.intersect=function(k){return j.polygon(t,p,k)},a}f(Wd,"rect_left_inv_arrow");async function qd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;let a;t.cssClasses?a="node "+t.cssClasses:a="node default";const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=n.insert("g"),s=n.insert("g").attr("class","label").attr("style",i),l=t.description,c=t.label,h=s.node().appendChild(await Qe(c,t.labelStyle,!0,!0));let u={width:0,height:0};if(kt(ht()?.flowchart?.htmlLabels)){const D=h.children[0],R=lt(h);u=D.getBoundingClientRect(),R.attr("width",u.width),R.attr("height",u.height)}$.info("Text 2",l);const p=l||[],d=h.getBBox(),g=s.node().appendChild(await Qe(p.join?p.join("<br/>"):p,t.labelStyle,!0,!0)),m=g.children[0],y=lt(g);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const x=(t.padding||0)/2;lt(g).attr("transform","translate( "+(u.width>d.width?0:(d.width-u.width)/2)+", "+(d.height+x+5)+")"),lt(h).attr("transform","translate( "+(u.width<d.width?0:-(d.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-x+3)+")");const b=u.width+(t.padding||0),k=u.height+(t.padding||0),v=-u.width/2-x,S=-u.height/2-x;let L,T;if(t.look==="handDrawn"){const D=U.svg(n),R=G(t,{}),O=D.path(Ee(v,S,b,k,t.rx||0),R),B=D.line(-u.width/2-x,-u.height/2-x+d.height+x,u.width/2+x,-u.height/2-x+d.height+x,R);T=n.insert(()=>($.debug("Rough node insert CXC",O),B),":first-child"),L=n.insert(()=>($.debug("Rough node insert CXC",O),O),":first-child")}else L=o.insert("rect",":first-child"),T=o.insert("line"),L.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-x).attr("y",-u.height/2-x).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),T.attr("class","divider").attr("x1",-u.width/2-x).attr("x2",u.width/2+x).attr("y1",-u.height/2-x+d.height+x).attr("y2",-u.height/2-x+d.height+x);return V(t,L),t.intersect=function(D){return j.rect(t,D)},n}f(qd,"rectWithTitle");async function Hd(e,t){const r={rx:5,ry:5,classes:"",labelPaddingX:(t?.padding||0)*1,labelPaddingY:(t?.padding||0)*1};return wi(e,t,r)}f(Hd,"roundedRect");async function jd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=t?.padding??0,l=Math.max(n.width+(t.padding??0)*2,t?.width??0),c=Math.max(n.height+(t.padding??0)*2,t?.height??0),h=-n.width/2-s,u=-n.height/2-s,{cssStyles:p}=t,d=U.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u},{x:h+l+8,y:u},{x:h+l+8,y:u+c},{x:h-8,y:u+c},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+c}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container").attr("style",zt(p)),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),p&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-l/2+4+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return j.rect(t,b)},a}f(jd,"shadedProcess");async function Ud(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=-s/2,h=-l/2,{cssStyles:u}=t,p=U.svg(a),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:c,y:h},{x:c,y:h+l},{x:c+s,y:h+l},{x:c+s,y:h-l/2}],m=ct(g),y=p.path(m,d),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),x.attr("transform",`translate(0, ${l/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))}, ${-l/4+(t.padding??0)-(n.y-(n.top??0))})`),V(t,x),t.intersect=function(b){return j.polygon(t,g,b)},a}f(Ud,"slopedRect");async function Yd(e,t){const r={rx:0,ry:0,classes:"",labelPaddingX:(t?.padding||0)*2,labelPaddingY:(t?.padding||0)*1};return wi(e,t,r)}f(Yd,"squareRect");async function Gd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=n.height+t.padding,s=n.width+o/4+t.padding;let l;const{cssStyles:c}=t;if(t.look==="handDrawn"){const h=U.svg(a),u=G(t,{}),p=Ee(-s/2,-o/2,s,o,o/2),d=h.path(p,u);l=a.insert(()=>d,":first-child"),l.attr("class","basic label-container").attr("style",zt(c))}else l=a.insert("rect",":first-child"),l.attr("class","basic label-container").attr("style",i).attr("rx",o/2).attr("ry",o/2).attr("x",-s/2).attr("y",-o/2).attr("width",s).attr("height",o);return V(t,l),t.intersect=function(h){return j.rect(t,h)},a}f(Gd,"stadium");async function Vd(e,t){return wi(e,t,{rx:5,ry:5,classes:"flowchart-node"})}f(Vd,"state");function Xd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i;const{cssStyles:n}=t,{lineColor:o,stateBorder:s,nodeBorder:l}=r,c=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=U.svg(c),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const p=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),d=s??l,g=h.circle(0,0,5,{...u,fill:d,stroke:d,strokeWidth:2,fillStyle:"solid"}),m=c.insert(()=>p,":first-child");return m.insert(()=>g),n&&m.selectAll("path").attr("style",n),a&&m.selectAll("path").attr("style",a),V(t,m),t.intersect=function(y){return j.circle(t,7,y)},c}f(Xd,"stateEnd");function Zd(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let n;if(t.look==="handDrawn"){const s=U.svg(a).circle(0,0,14,t2(i));n=a.insert(()=>s),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else n=a.insert("circle",":first-child"),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return V(t,n),t.intersect=function(o){return j.circle(t,7,o)},a}f(Zd,"stateStart");async function Kd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=(t?.padding||0)/2,s=n.width+t.padding,l=n.height+t.padding,c=-n.width/2-o,h=-n.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-l},{x:0,y:-l},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-l},{x:-8,y:-l},{x:-8,y:0}];if(t.look==="handDrawn"){const p=U.svg(a),d=G(t,{}),g=p.rectangle(c-8,h,s+16,l,d),m=p.line(c,h,c,h+l,d),y=p.line(c+s,h,c+s,h+l,d);a.insert(()=>m,":first-child"),a.insert(()=>y,":first-child");const x=a.insert(()=>g,":first-child"),{cssStyles:b}=t;x.attr("class","basic label-container").attr("style",zt(b)),V(t,x)}else{const p=Fe(a,s,l,u);i&&p.attr("style",i),V(t,p)}return t.intersect=function(p){return j.polygon(t,u,p)},a}f(Kd,"subroutine");async function Qd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=Math.max(n.width+(t.padding??0)*2,t?.width??0),s=Math.max(n.height+(t.padding??0)*2,t?.height??0),l=-o/2,c=-s/2,h=.2*s,u=.2*s,{cssStyles:p}=t,d=U.svg(a),g=G(t,{}),m=[{x:l-h/2,y:c},{x:l+o+h/2,y:c},{x:l+o+h/2,y:c+s},{x:l-h/2,y:c+s}],y=[{x:l+o-h/2,y:c+s},{x:l+o+h/2,y:c+s},{x:l+o+h/2,y:c+s-u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=d.path(x,g),k=ct(y),v=d.path(k,{...g,fillStyle:"solid"}),S=a.insert(()=>v,":first-child");return S.insert(()=>b,":first-child"),S.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),V(t,S),t.intersect=function(L){return j.polygon(t,m,L)},a}f(Qd,"taggedRect");async function Jd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=l/4,h=.2*s,u=.2*l,p=l+c,{cssStyles:d}=t,g=U.svg(a),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:p/2},...We(-s/2-s/2*.1,p/2,s/2+s/2*.1,p/2,c,.8),{x:s/2+s/2*.1,y:-p/2},{x:-s/2-s/2*.1,y:-p/2}],x=-s/2+s/2*.1,b=-p/2-u*.4,k=[{x:x+s-h,y:(b+l)*1.4},{x:x+s,y:b+l-u},{x:x+s,y:(b+l)*.9},...We(x+s,(b+l)*1.3,x+s-h,(b+l)*1.5,-l*.03,.5)],v=ct(y),S=g.path(v,m),L=ct(k),T=g.path(L,{...m,fillStyle:"solid"}),D=a.insert(()=>T,":first-child");return D.insert(()=>S,":first-child"),D.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&D.selectAll("path").attr("style",i),D.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c/2-(n.y-(n.top??0))})`),V(t,D),t.intersect=function(R){return j.polygon(t,y,R)},a}f(Jd,"taggedWaveEdgedRectangle");async function tf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=Math.max(n.width+t.padding,t?.width||0),s=Math.max(n.height+t.padding,t?.height||0),l=-o/2,c=-s/2,h=a.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",l).attr("y",c).attr("width",o).attr("height",s),V(t,h),t.intersect=function(u){return j.rect(t,u)},a}f(tf,"text");var k2=f((e,t,r,i,a,n)=>`M${e},${t}
    a${a},${n} 0,0,1 0,${-i}
    l${r},0
    a${a},${n} 0,0,1 0,${i}
    M${r},${-i}
    a${a},${n} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),w2=f((e,t,r,i,a,n)=>[`M${e},${t}`,`M${e+r},${t}`,`a${a},${n} 0,0,0 0,${-i}`,`l${-r},0`,`a${a},${n} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),v2=f((e,t,r,i,a,n)=>[`M${e+r/2},${-i/2}`,`a${a},${n} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function ef(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o,halfPadding:s}=await it(e,t,et(t)),l=t.look==="neo"?s*2:s,c=n.height+l,h=c/2,u=h/(2.5+c/50),p=n.width+u+l,{cssStyles:d}=t;let g;if(t.look==="handDrawn"){const m=U.svg(a),y=w2(0,0,p,c,u,h),x=v2(0,0,p,c,u,h),b=m.path(y,G(t,{})),k=m.path(x,G(t,{fill:"none"}));g=a.insert(()=>k,":first-child"),g=a.insert(()=>b,":first-child"),g.attr("class","basic label-container"),d&&g.attr("style",d)}else{const m=k2(0,0,p,c,u,h);g=a.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",zt(d)).attr("style",i),g.attr("class","basic label-container"),d&&g.selectAll("path").attr("style",d),i&&g.selectAll("path").attr("style",i)}return g.attr("label-offset-x",u),g.attr("transform",`translate(${-p/2}, ${c/2} )`),o.attr("transform",`translate(${-(n.width/2)-u-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),V(t,g),t.intersect=function(m){const y=j.rect(t,m),x=y.y-(t.y??0);if(h!=0&&(Math.abs(x)<(t.height??0)/2||Math.abs(x)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let b=u*u*(1-x*x/(h*h));b!=0&&(b=Math.sqrt(Math.abs(b))),b=u-b,m.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},a}f(ef,"tiltedCylinder");async function rf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=n.width+t.padding,s=n.height+t.padding,l=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let c;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=U.svg(a),p=G(t,{}),d=ct(l),g=u.path(d,p);c=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&c.attr("style",h)}else c=Fe(a,o,s,l);return i&&c.attr("style",i),t.width=o,t.height=s,V(t,c),t.intersect=function(u){return j.polygon(t,l,u)},a}f(rf,"trapezoid");async function af(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=60,s=20,l=Math.max(o,n.width+(t.padding??0)*2,t?.width??0),c=Math.max(s,n.height+(t.padding??0)*2,t?.height??0),{cssStyles:h}=t,u=U.svg(a),p=G(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const d=[{x:-l/2*.8,y:-c/2},{x:l/2*.8,y:-c/2},{x:l/2,y:-c/2*.6},{x:l/2,y:c/2},{x:-l/2,y:c/2},{x:-l/2,y:-c/2*.6}],g=ct(d),m=u.path(g,p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),V(t,y),t.intersect=function(x){return j.polygon(t,d,x)},a}f(af,"trapezoidalPentagon");async function nf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=kt(ht().flowchart?.htmlLabels),l=n.width+(t.padding??0),c=l+n.height,h=l+n.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-c}],{cssStyles:p}=t,d=U.svg(a),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=ct(u),y=d.path(m,g),x=a.insert(()=>y,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return p&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",p),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),t.width=l,t.height=c,V(t,x),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${c/2-(n.height+(t.padding??0)/(s?2:1)-(n.y-(n.top??0)))})`),t.intersect=function(b){return $.info("Triangle intersect",t,u,b),j.polygon(t,u,b)},a}f(nf,"triangle");async function sf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=l/8,h=l+c,{cssStyles:u}=t,d=70-s,g=d>0?d/2:0,m=U.svg(a),y=G(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const x=[{x:-s/2-g,y:h/2},...We(-s/2-g,h/2,s/2+g,h/2,c,.8),{x:s/2+g,y:-h/2},{x:-s/2-g,y:-h/2}],b=ct(x),k=m.path(b,y),v=a.insert(()=>k,":first-child");return v.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(0,${-c/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-c-(n.y-(n.top??0))})`),V(t,v),t.intersect=function(S){return j.polygon(t,x,S)},a}f(sf,"waveEdgedRectangle");async function of(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await it(e,t,et(t)),o=100,s=50,l=Math.max(n.width+(t.padding??0)*2,t?.width??0),c=Math.max(n.height+(t.padding??0)*2,t?.height??0),h=l/c;let u=l,p=c;u>p*h?p=u/h:u=p*h,u=Math.max(u,o),p=Math.max(p,s);const d=Math.min(p*.2,p/4),g=p+d*2,{cssStyles:m}=t,y=U.svg(a),x=G(t,{});t.look!=="handDrawn"&&(x.roughness=0,x.fillStyle="solid");const b=[{x:-u/2,y:g/2},...We(-u/2,g/2,u/2,g/2,d,1),{x:u/2,y:-g/2},...We(u/2,-g/2,-u/2,-g/2,d,-1)],k=ct(b),v=y.path(k,x),S=a.insert(()=>v,":first-child");return S.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),V(t,S),t.intersect=function(L){return j.polygon(t,b,L)},a}f(of,"waveRectangle");async function lf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await it(e,t,et(t)),s=Math.max(n.width+(t.padding??0)*2,t?.width??0),l=Math.max(n.height+(t.padding??0)*2,t?.height??0),c=5,h=-s/2,u=-l/2,{cssStyles:p}=t,d=U.svg(a),g=G(t,{}),m=[{x:h-c,y:u-c},{x:h-c,y:u+l},{x:h+s,y:u+l},{x:h+s,y:u-c}],y=`M${h-c},${u-c} L${h+s},${u-c} L${h+s},${u+l} L${h-c},${u+l} L${h-c},${u-c}
                M${h-c},${u} L${h+s},${u}
                M${h},${u-c} L${h},${u+l}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=d.path(y,g),b=a.insert(()=>x,":first-child");return b.attr("transform",`translate(${c/2}, ${c/2})`),b.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)+c/2-(n.x-(n.left??0))}, ${-(n.height/2)+c/2-(n.y-(n.top??0))})`),V(t,b),t.intersect=function(k){return j.polygon(t,m,k)},a}f(lf,"windowPane");async function Xs(e,t){const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:N}=Gt(),{background:q}=N,Z={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${q}`]};await Xs(e,Z)}const i=Gt();t.useHtmlLabels=i.htmlLabels;let a=i.er?.diagramPadding??10,n=i.er?.entityPadding??6;const{cssStyles:o}=t,{labelStyles:s}=X(t);if(r.attributes.length===0&&t.label){const N={rx:0,ry:0,labelPaddingX:a,labelPaddingY:a*1.5,classes:""};Le(t.label,i)+N.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const q=await wi(e,t,N);if(!kt(i.htmlLabels)){const Z=q.select("text"),nt=Z.node()?.getBBox();Z.attr("transform",`translate(${-nt.width/2}, 0)`)}return q}i.htmlLabels||(a*=1.25,n*=1.25);let l=et(t);l||(l="node default");const c=e.insert("g").attr("class",l).attr("id",t.domId||t.id),h=await mr(c,t.label??"",i,0,0,["name"],s);h.height+=n;let u=0;const p=[];let d=0,g=0,m=0,y=0,x=!0,b=!0;for(const N of r.attributes){const q=await mr(c,N.type,i,0,u,["attribute-type"],s);d=Math.max(d,q.width+a);const Z=await mr(c,N.name,i,0,u,["attribute-name"],s);g=Math.max(g,Z.width+a);const nt=await mr(c,N.keys.join(),i,0,u,["attribute-keys"],s);m=Math.max(m,nt.width+a);const at=await mr(c,N.comment,i,0,u,["attribute-comment"],s);y=Math.max(y,at.width+a),u+=Math.max(q.height,Z.height,nt.height,at.height)+n,p.push(u)}p.pop();let k=4;m<=a&&(x=!1,m=0,k--),y<=a&&(b=!1,y=0,k--);const v=c.node().getBBox();if(h.width+a*2-(d+g+m+y)>0){const N=h.width+a*2-(d+g+m+y);d+=N/k,g+=N/k,m>0&&(m+=N/k),y>0&&(y+=N/k)}const S=d+g+m+y,L=U.svg(c),T=G(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const D=Math.max(v.width+a*2,t?.width||0,S),R=Math.max(v.height+(p[0]||u)+n,t?.height||0),O=-D/2,B=-R/2;c.selectAll("g:not(:first-child)").each((N,q,Z)=>{const nt=lt(Z[q]),at=nt.attr("transform");let pt=0,st=0;if(at){const Ot=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(at);Ot&&(pt=parseFloat(Ot[1]),st=parseFloat(Ot[2]),nt.attr("class").includes("attribute-name")?pt+=d:nt.attr("class").includes("attribute-keys")?pt+=d+g:nt.attr("class").includes("attribute-comment")&&(pt+=d+g+m))}nt.attr("transform",`translate(${O+a/2+pt}, ${st+B+h.height+n/2})`)}),c.select(".name").attr("transform","translate("+-h.width/2+", "+(B+n/2)+")");const z=L.rectangle(O,B,D,R,T),P=c.insert(()=>z,":first-child").attr("style",o.join("")),{themeVariables:E}=Gt(),{rowEven:M,rowOdd:_,nodeBorder:F}=E;p.push(0);for(const[N,q]of p.entries()){if(N===0&&p.length>1)continue;const Z=N%2===0&&q!==0,nt=L.rectangle(O,h.height+B+q,D,h.height,{...T,fill:Z?M:_,stroke:F});c.insert(()=>nt,"g.label").attr("style",o.join("")).attr("class",`row-rect-${N%2===0?"even":"odd"}`)}let A=L.line(O,h.height+B,D+O,h.height+B,T);c.insert(()=>A).attr("class","divider"),A=L.line(d+O,h.height+B,d+O,R+B,T),c.insert(()=>A).attr("class","divider"),x&&(A=L.line(d+g+O,h.height+B,d+g+O,R+B,T),c.insert(()=>A).attr("class","divider")),b&&(A=L.line(d+g+m+O,h.height+B,d+g+m+O,R+B,T),c.insert(()=>A).attr("class","divider"));for(const N of p)A=L.line(O,h.height+B+N,D+O,h.height+B+N,T),c.insert(()=>A).attr("class","divider");return V(t,P),t.intersect=function(N){return j.rect(t,N)},c}f(Xs,"erBox");async function mr(e,t,r,i=0,a=0,n=[],o=""){const s=e.insert("g").attr("class",`label ${n.join(" ")}`).attr("transform",`translate(${i}, ${a})`).attr("style",o);t!==Fo(t)&&(t=Fo(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const l=s.node().appendChild(await He(s,t,{width:Le(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=l.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let c=l.getBBox();if(kt(r.htmlLabels)){const h=l.children[0];h.style.textAlign="start";const u=lt(l);c=h.getBoundingClientRect(),u.attr("width",c.width),u.attr("height",c.height)}return c}f(mr,"addText");async function cf(e,t,r,i,a=r.class.padding??12){const n=i?0:3,o=e.insert("g").attr("class",et(t)).attr("id",t.domId||t.id);let s=null,l=null,c=null,h=null,u=0,p=0,d=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const b=t.annotations[0];await Kr(s,{text:`«${b}»`},0),u=s.node().getBBox().height}l=o.insert("g").attr("class","label-group text"),await Kr(l,t,0,["font-weight: bolder"]);const g=l.node().getBBox();p=g.height,c=o.insert("g").attr("class","members-group text");let m=0;for(const b of t.members){const k=await Kr(c,b,m,[b.parseClassifier()]);m+=k+n}d=c.node().getBBox().height,d<=0&&(d=a/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const b of t.methods){const k=await Kr(h,b,y,[b.parseClassifier()]);y+=k+n}let x=o.node().getBBox();if(s!==null){const b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return l.attr("transform",`translate(${-g.width/2}, ${u})`),x=o.node().getBBox(),c.attr("transform",`translate(0, ${u+p+a*2})`),x=o.node().getBBox(),h.attr("transform",`translate(0, ${u+p+(d?d+a*4:a*2)})`),x=o.node().getBBox(),{shapeSvg:o,bbox:x}}f(cf,"textHelper");async function Kr(e,t,r,i=[]){const a=e.insert("g").attr("class","label").attr("style",i.join("; ")),n=Gt();let o="useHtmlLabels"in t?t.useHtmlLabels:kt(n.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),Sr(s)&&(o=!0);const l=await He(a,ps(or(s)),{width:Le(s,n)+50,classes:"markdown-node-label",useHtmlLabels:o},n);let c,h=1;if(o){const u=l.children[0],p=lt(l);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const d=u.getElementsByTagName("img");if(d){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...d].map(m=>new Promise(y=>{function x(){if(m.style.display="flex",m.style.flexDirection="column",g){const b=n.fontSize?.toString()??window.getComputedStyle(document.body).fontSize,v=parseInt(b,10)*5+"px";m.style.minWidth=v,m.style.maxWidth=v}else m.style.width="100%";y(m)}f(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}c=u.getBoundingClientRect(),p.attr("width",c.width),p.attr("height",c.height)}else{i.includes("font-weight: bolder")&&lt(l).selectAll("tspan").attr("font-weight",""),h=l.children.length;const u=l.children[0];(l.textContent===""||l.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),c=l.getBBox()}return a.attr("transform","translate(0,"+(-c.height/(2*h)+r)+")"),c.height}f(Kr,"addText");async function hf(e,t){const r=ht(),i=r.class.padding??12,a=i,n=t.useHtmlLabels??kt(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:l}=await cf(e,t,r,n,a),{labelStyles:c,nodeStyles:h}=X(t);t.labelStyle=c,t.cssStyles=o.styles||"";const u=o.styles?.join(";")||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const p=o.members.length===0&&o.methods.length===0&&!r.class?.hideEmptyMembersBox,d=U.svg(s),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=l.width;let y=l.height;o.members.length===0&&o.methods.length===0?y+=a:o.members.length>0&&o.methods.length===0&&(y+=a*2);const x=-m/2,b=-y/2,k=d.rectangle(x-i,b-i-(p?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(p?i*2:o.members.length===0&&o.methods.length===0?-i:0),g),v=s.insert(()=>k,":first-child");v.attr("class","basic label-container");const S=v.node().getBBox();s.selectAll(".text").each((R,O,B)=>{const z=lt(B[O]),P=z.attr("transform");let E=0;if(P){const A=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(P);A&&(E=parseFloat(A[2]))}let M=E+b+i-(p?i:o.members.length===0&&o.methods.length===0?-i/2:0);n||(M-=4);let _=x;(z.attr("class").includes("label-group")||z.attr("class").includes("annotation-group"))&&(_=-z.node()?.getBBox().width/2||0,s.selectAll("text").each(function(F,A,N){window.getComputedStyle(N[A]).textAnchor==="middle"&&(_=0)})),z.attr("transform",`translate(${_}, ${M})`)});const L=s.select(".annotation-group").node().getBBox().height-(p?i/2:0)||0,T=s.select(".label-group").node().getBBox().height-(p?i/2:0)||0,D=s.select(".members-group").node().getBBox().height-(p?i/2:0)||0;if(o.members.length>0||o.methods.length>0||p){const R=d.line(S.x,L+T+b+i,S.x+S.width,L+T+b+i,g);s.insert(()=>R).attr("class","divider").attr("style",u)}if(p||o.members.length>0||o.methods.length>0){const R=d.line(S.x,L+T+D+b+a*2+i,S.x+S.width,L+T+D+b+i+a*2,g);s.insert(()=>R).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),v.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!n){const R=RegExp(/color\s*:\s*([^;]*)/),O=R.exec(u);if(O){const B=O[0].replace("color","fill");s.selectAll("tspan").attr("style",B)}else if(c){const B=R.exec(c);if(B){const z=B[0].replace("color","fill");s.selectAll("tspan").attr("style",z)}}}return V(t,v),t.intersect=function(R){return j.rect(t,R)},s}f(hf,"classBox");async function uf(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const a=t,n=t,o=20,s=20,l="verifyMethod"in t,c=et(t),h=e.insert("g").attr("class",c).attr("id",t.domId??t.id);let u;l?u=await de(h,`&lt;&lt;${a.type}&gt;&gt;`,0,t.labelStyle):u=await de(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let p=u;const d=await de(h,a.name,p,t.labelStyle+"; font-weight: bold;");if(p+=d+s,l){const L=await de(h,`${a.requirementId?`Id: ${a.requirementId}`:""}`,p,t.labelStyle);p+=L;const T=await de(h,`${a.text?`Text: ${a.text}`:""}`,p,t.labelStyle);p+=T;const D=await de(h,`${a.risk?`Risk: ${a.risk}`:""}`,p,t.labelStyle);p+=D,await de(h,`${a.verifyMethod?`Verification: ${a.verifyMethod}`:""}`,p,t.labelStyle)}else{const L=await de(h,`${n.type?`Type: ${n.type}`:""}`,p,t.labelStyle);p+=L,await de(h,`${n.docRef?`Doc Ref: ${n.docRef}`:""}`,p,t.labelStyle)}const g=(h.node()?.getBBox().width??200)+o,m=(h.node()?.getBBox().height??200)+o,y=-g/2,x=-m/2,b=U.svg(h),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const v=b.rectangle(y,x,g,m,k),S=h.insert(()=>v,":first-child");if(S.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((L,T,D)=>{const R=lt(D[T]),O=R.attr("transform");let B=0,z=0;if(O){const _=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(O);_&&(B=parseFloat(_[1]),z=parseFloat(_[2]))}const P=z-m/2;let E=y+o/2;(T===0||T===1)&&(E=B),R.attr("transform",`translate(${E}, ${P+o})`)}),p>u+d+s){const L=b.line(y,x+u+d+s,y+g,x+u+d+s,k);h.insert(()=>L).attr("style",i)}return V(t,S),t.intersect=function(L){return j.rect(t,L)},h}f(uf,"requirementBox");async function de(e,t,r,i=""){if(t==="")return 0;const a=e.insert("g").attr("class","label").attr("style",i),n=ht(),o=n.htmlLabels??!0,s=await He(a,ps(or(t)),{width:Le(t,n)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},n);let l;if(o){const c=s.children[0],h=lt(s);l=c.getBoundingClientRect(),h.attr("width",l.width),h.attr("height",l.height)}else{const c=s.children[0];for(const h of c.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);l=s.getBBox(),l.height+=6}return a.attr("transform",`translate(${-l.width/2},${-l.height/2+r})`),l.height}f(de,"addText");var S2=f(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function df(e,t,{config:r}){const{labelStyles:i,nodeStyles:a}=X(t);t.labelStyle=i||"";const n=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:l,label:c}=await it(e,t,et(t)),h=t.padding||10;let u="",p;"ticket"in t&&t.ticket&&r?.kanban?.ticketBaseUrl&&(u=r?.kanban?.ticketBaseUrl.replace("#TICKET#",t.ticket),p=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const d={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let g,m;p?{label:g,bbox:m}=await mn(p,"ticket"in t&&t.ticket||"",d):{label:g,bbox:m}=await mn(s,"ticket"in t&&t.ticket||"",d);const{label:y,bbox:x}=await mn(s,"assigned"in t&&t.assigned||"",d);t.width=o;const b=10,k=t?.width||0,v=Math.max(m.height,x.height)/2,S=Math.max(l.height+b*2,t?.height||0)+v,L=-k/2,T=-S/2;c.attr("transform","translate("+(h-k/2)+", "+(-v-l.height/2)+")"),g.attr("transform","translate("+(h-k/2)+", "+(-v+l.height/2)+")"),y.attr("transform","translate("+(h+k/2-x.width-2*n)+", "+(-v+l.height/2)+")");let D;const{rx:R,ry:O}=t,{cssStyles:B}=t;if(t.look==="handDrawn"){const z=U.svg(s),P=G(t,{}),E=R||O?z.path(Ee(L,T,k,S,R||0),P):z.rectangle(L,T,k,S,P);D=s.insert(()=>E,":first-child"),D.attr("class","basic label-container").attr("style",B||null)}else{D=s.insert("rect",":first-child"),D.attr("class","basic label-container __APA__").attr("style",a).attr("rx",R??5).attr("ry",O??5).attr("x",L).attr("y",T).attr("width",k).attr("height",S);const z="priority"in t&&t.priority;if(z){const P=s.append("line"),E=L+2,M=T+Math.floor((R??0)/2),_=T+S-Math.floor((R??0)/2);P.attr("x1",E).attr("y1",M).attr("x2",E).attr("y2",_).attr("stroke-width","4").attr("stroke",S2(z))}}return V(t,D),t.height=S,t.intersect=function(z){return j.rect(t,z)},s}f(df,"kanbanItem");var T2=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:Yd},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:Hd},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:Gd},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:Kd},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:md},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:cd},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:zd},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:vd},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:$d},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:Fd},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:rf},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:Md},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:xd},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:tf},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:od},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:jd},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:Zd},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:Xd},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:kd},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:Sd},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:dd},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:fd},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:pd},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Od},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:sf},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:wd},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:ef},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:Dd},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:gd},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:yd},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:nf},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:lf},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:bd},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:af},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:Cd},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:Ud},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:Pd},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:Id},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:sd},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:ud},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:Jd},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:Qd},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:of},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:Wd},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:Rd}],_2=f(()=>{const t=[...Object.entries({state:Vd,choice:ld,note:Nd,rectWithTitle:qd,labelRect:Ed,iconSquare:Ld,iconCircle:_d,icon:Td,iconRounded:Bd,imageSquare:Ad,anchor:nd,kanbanItem:df,classBox:hf,erBox:Xs,requirementBox:uf}),...T2.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(a=>[a,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),ff=_2();function B2(e){return e in ff}f(B2,"isValidShape");var Ha=new Map;async function pf(e,t,r){let i,a;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const n=t.shape?ff[t.shape]:void 0;if(!n)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),a=await n(i,t,r)}else a=await n(e,t,r),i=a;return t.tooltip&&a.attr("title",t.tooltip),Ha.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}f(pf,"insertNode");var xS=f((e,t)=>{Ha.set(t.id,e)},"setNodeElem"),bS=f(()=>{Ha.clear()},"clear"),CS=f(e=>{const t=Ha.get(e.id);$.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),Qt={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function Qr(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=yt(e),t=yt(t);const[r,i]=[e.x,e.y],[a,n]=[t.x,t.y],o=a-r,s=n-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}f(Qr,"calculateDeltaAndAngle");var yt=f(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),L2=f(e=>({x:f(function(t,r,i){let a=0;const n=yt(i[0]).x<yt(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(Qt,e.arrowTypeStart)){const{angle:d,deltaX:g}=Qr(i[0],i[1]);a=Qt[e.arrowTypeStart]*Math.cos(d)*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(Qt,e.arrowTypeEnd)){const{angle:d,deltaX:g}=Qr(i[i.length-1],i[i.length-2]);a=Qt[e.arrowTypeEnd]*Math.cos(d)*(g>=0?1:-1)}const o=Math.abs(yt(t).x-yt(i[i.length-1]).x),s=Math.abs(yt(t).y-yt(i[i.length-1]).y),l=Math.abs(yt(t).x-yt(i[0]).x),c=Math.abs(yt(t).y-yt(i[0]).y),h=Qt[e.arrowTypeStart],u=Qt[e.arrowTypeEnd],p=1;if(o<u&&o>0&&s<u){let d=u+p-o;d*=n==="right"?-1:1,a-=d}if(l<h&&l>0&&c<h){let d=h+p-l;d*=n==="right"?-1:1,a+=d}return yt(t).x+a},"x"),y:f(function(t,r,i){let a=0;const n=yt(i[0]).y<yt(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(Qt,e.arrowTypeStart)){const{angle:d,deltaY:g}=Qr(i[0],i[1]);a=Qt[e.arrowTypeStart]*Math.abs(Math.sin(d))*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(Qt,e.arrowTypeEnd)){const{angle:d,deltaY:g}=Qr(i[i.length-1],i[i.length-2]);a=Qt[e.arrowTypeEnd]*Math.abs(Math.sin(d))*(g>=0?1:-1)}const o=Math.abs(yt(t).y-yt(i[i.length-1]).y),s=Math.abs(yt(t).x-yt(i[i.length-1]).x),l=Math.abs(yt(t).y-yt(i[0]).y),c=Math.abs(yt(t).x-yt(i[0]).x),h=Qt[e.arrowTypeStart],u=Qt[e.arrowTypeEnd],p=1;if(o<u&&o>0&&s<u){let d=u+p-o;d*=n==="up"?-1:1,a-=d}if(l<h&&l>0&&c<h){let d=h+p-l;d*=n==="up"?-1:1,a+=d}return yt(t).y+a},"y")}),"getLineFunctionsWithOffset"),A2=f((e,t,r,i,a,n)=>{t.arrowTypeStart&&xl(e,"start",t.arrowTypeStart,r,i,a,n),t.arrowTypeEnd&&xl(e,"end",t.arrowTypeEnd,r,i,a,n)},"addEdgeMarkers"),M2={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},xl=f((e,t,r,i,a,n,o)=>{const s=M2[r];if(!s){$.warn(`Unknown arrow type: ${r}`);return}const l=s.type,h=`${a}_${n}-${l}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const u=o.replace(/[^\dA-Za-z]/g,"_"),p=`${h}_${u}`;if(!document.getElementById(p)){const d=document.getElementById(h);if(d){const g=d.cloneNode(!0);g.id=p,g.querySelectorAll("path, circle, line").forEach(y=>{y.setAttribute("stroke",o),s.fill&&y.setAttribute("fill",o)}),d.parentNode?.appendChild(g)}}e.attr(`marker-${t}`,`url(${i}#${p})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),ka=new Map,Bt=new Map,kS=f(()=>{ka.clear(),Bt.clear()},"clear"),Vr=f(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),E2=f(async(e,t)=>{let r=kt(ht().flowchart.htmlLabels);const i=await He(e,t.label,{style:Vr(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});$.info("abc82",t,t.labelType);const a=e.insert("g").attr("class","edgeLabel"),n=a.insert("g").attr("class","label");n.node().appendChild(i);let o=i.getBBox();if(r){const l=i.children[0],c=lt(i);o=l.getBoundingClientRect(),c.attr("width",o.width),c.attr("height",o.height)}n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),ka.set(t.id,a),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const l=await Qe(t.startLabelLeft,Vr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),Bt.get(t.id)||Bt.set(t.id,{}),Bt.get(t.id).startLeft=c,Jr(s,t.startLabelLeft)}if(t.startLabelRight){const l=await Qe(t.startLabelRight,Vr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=c.node().appendChild(l),h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),Bt.get(t.id)||Bt.set(t.id,{}),Bt.get(t.id).startRight=c,Jr(s,t.startLabelRight)}if(t.endLabelLeft){const l=await Qe(t.endLabelLeft,Vr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),c.node().appendChild(l),Bt.get(t.id)||Bt.set(t.id,{}),Bt.get(t.id).endLeft=c,Jr(s,t.endLabelLeft)}if(t.endLabelRight){const l=await Qe(t.endLabelRight,Vr(t.labelStyle)),c=e.insert("g").attr("class","edgeTerminals"),h=c.insert("g").attr("class","inner");s=h.node().appendChild(l);const u=l.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),c.node().appendChild(l),Bt.get(t.id)||Bt.set(t.id,{}),Bt.get(t.id).endRight=c,Jr(s,t.endLabelRight)}return i},"insertEdgeLabel");function Jr(e,t){ht().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}f(Jr,"setTerminalWidth");var F2=f((e,t)=>{$.debug("Moving label abc88 ",e.id,e.label,ka.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=ht(),{subGraphTitleTotalMargin:a}=ws(i);if(e.label){const n=ka.get(e.id);let o=e.x,s=e.y;if(r){const l=pe.calcLabelPosition(r);$.debug("Moving label "+e.label+" from (",o,",",s,") to (",l.x,",",l.y,") abc88"),t.updatedPath&&(o=l.x,s=l.y)}n.attr("transform",`translate(${o}, ${s+a/2})`)}if(e.startLabelLeft){const n=Bt.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const l=pe.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const n=Bt.get(e.id).startRight;let o=e.x,s=e.y;if(r){const l=pe.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const n=Bt.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const l=pe.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const n=Bt.get(e.id).endRight;let o=e.x,s=e.y;if(r){const l=pe.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=l.x,s=l.y}n.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),$2=f((e,t)=>{const r=e.x,i=e.y,a=Math.abs(t.x-r),n=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return a>=o||n>=s},"outsideNode"),O2=f((e,t,r)=>{$.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,a=e.y,n=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-n:o+n;const l=e.height/2,c=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(a-t.y)*o>Math.abs(i-t.x)*l){let u=r.y<t.y?t.y-l-a:a-l-t.y;s=h*u/c;const p={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+c-u:r.y-c+u};return s===0&&(p.x=t.x,p.y=t.y),h===0&&(p.x=t.x),c===0&&(p.y=t.y),$.debug(`abc89 top/bottom calc, Q ${c}, q ${u}, R ${h}, r ${s}`,p),p}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=c*s/h,p=r.x<t.x?r.x+h-s:r.x-h+s,d=r.y<t.y?r.y+u:r.y-u;return $.debug(`sides calc abc89, Q ${c}, q ${u}, R ${h}, r ${s}`,{_x:p,_y:d}),s===0&&(p=t.x,d=t.y),h===0&&(p=t.x),c===0&&(d=t.y),{x:p,y:d}}},"intersection"),bl=f((e,t)=>{$.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],a=!1;return e.forEach(n=>{if($.info("abc88 checking point",n,t),!$2(t,n)&&!a){const o=O2(t,i,n);$.debug("abc88 inside",n,i,o),$.debug("abc88 intersection",o,t);let s=!1;r.forEach(l=>{s=s||l.x===o.x&&l.y===o.y}),r.some(l=>l.x===o.x&&l.y===o.y)?$.warn("abc88 no intersect",o,r):r.push(o),a=!0}else $.warn("abc88 outside",n,i),i=n,a||r.push(n)}),$.debug("returning points",r),r},"cutPathAtIntersect");function gf(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const a=e[i-1],n=e[i],o=e[i+1];(a.x===n.x&&n.y===o.y&&Math.abs(n.x-o.x)>5&&Math.abs(n.y-a.y)>5||a.y===n.y&&n.x===o.x&&Math.abs(n.x-a.x)>5&&Math.abs(n.y-o.y)>5)&&(t.push(n),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}f(gf,"extractCornerPoints");var Cl=f(function(e,t,r){const i=t.x-e.x,a=t.y-e.y,n=Math.sqrt(i*i+a*a),o=r/n;return{x:t.x-o*i,y:t.y-o*a}},"findAdjacentPoint"),D2=f(function(e){const{cornerPointPositions:t}=gf(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const a=e[i-1],n=e[i+1],o=e[i],s=Cl(a,o,5),l=Cl(n,o,5),c=l.x-s.x,h=l.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let p={x:o.x,y:o.y};if(Math.abs(n.x-a.x)>10&&Math.abs(n.y-a.y)>=10){$.debug("Corner point fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));const d=5;o.x===s.x?p={x:c<0?s.x-d+u:s.x+d-u,y:h<0?s.y-u:s.y+u}:p={x:c<0?s.x-u:s.x+u,y:h<0?s.y-d+u:s.y+d-u}}else $.debug("Corner point skipping fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));r.push(p,l)}else r.push(e[i]);return r},"fixCorners"),R2=f(function(e,t,r,i,a,n,o){const{handDrawnSeed:s}=ht();let l=t.points,c=!1;const h=a;var u=n;const p=[];for(const R in t.cssCompiledStyles)Zu(R)||p.push(t.cssCompiledStyles[R]);u.intersect&&h.intersect&&(l=l.slice(1,t.points.length-1),l.unshift(h.intersect(l[0])),$.debug("Last point APA12",t.start,"-->",t.end,l[l.length-1],u,u.intersect(l[l.length-1])),l.push(u.intersect(l[l.length-1]))),t.toCluster&&($.info("to cluster abc88",r.get(t.toCluster)),l=bl(t.points,r.get(t.toCluster).node),c=!0),t.fromCluster&&($.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(l,null,2)),l=bl(l.reverse(),r.get(t.fromCluster).node).reverse(),c=!0);let d=l.filter(R=>!Number.isNaN(R.y));d=D2(d);let g=Pi;switch(g=bn,t.curve){case"linear":g=bn;break;case"basis":g=Pi;break;case"cardinal":g=Ol;break;default:g=Pi}const{x:m,y}=L2(t),x=qp().x(m).y(y).curve(g);let b;switch(t.thickness){case"normal":b="edge-thickness-normal";break;case"thick":b="edge-thickness-thick";break;case"invisible":b="edge-thickness-invisible";break;default:b="edge-thickness-normal"}switch(t.pattern){case"solid":b+=" edge-pattern-solid";break;case"dotted":b+=" edge-pattern-dotted";break;case"dashed":b+=" edge-pattern-dashed";break;default:b+=" edge-pattern-solid"}let k,v=x(d);const S=Array.isArray(t.style)?t.style:[t.style];let L=S.find(R=>R?.startsWith("stroke:"));if(t.look==="handDrawn"){const R=U.svg(e);Object.assign([],d);const O=R.path(v,{roughness:.3,seed:s});b+=" transition",k=lt(O).select("path").attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")).attr("style",S?S.reduce((z,P)=>z+";"+P,""):"");let B=k.attr("d");k.attr("d",B),e.node().appendChild(k.node())}else{const R=p.join(";"),O=S?S.reduce((P,E)=>P+E+";",""):"";let B="";t.animate&&(B=" edge-animation-fast"),t.animation&&(B=" edge-animation-"+t.animation);const z=R?R+";"+O+";":O;k=e.append("path").attr("d",v).attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")+(B??"")).attr("style",z),L=z.match(/stroke:([^;]+)/)?.[1]}let T="";(ht().flowchart.arrowMarkerAbsolute||ht().state.arrowMarkerAbsolute)&&(T=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,T=T.replace(/\(/g,"\\(").replace(/\)/g,"\\)")),$.info("arrowTypeStart",t.arrowTypeStart),$.info("arrowTypeEnd",t.arrowTypeEnd),A2(k,t,T,o,i,L);let D={};return c&&(D.updatedPath=l),D.originalPath=t.points,D},"insertEdge"),I2=f((e,t,r,i)=>{t.forEach(a=>{J2[a](e,r,i)})},"insertMarkers"),P2=f((e,t,r)=>{$.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),N2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),z2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),W2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),q2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),H2=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),j2=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),U2=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),Y2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),G2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),V2=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),a.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),X2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),Z2=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),a.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),K2=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),Q2=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",10).attr("fill","none"),i.append("line").attr("x1",0).attr("x2",20).attr("y1",10).attr("y2",10),i.append("line").attr("y1",0).attr("y2",20).attr("x1",10).attr("x2",10)},"requirement_contains"),J2={extension:P2,composition:N2,aggregation:z2,dependency:W2,lollipop:q2,point:H2,circle:j2,cross:U2,barb:Y2,only_one:G2,zero_or_one:V2,one_or_more:X2,zero_or_more:Z2,requirement_arrow:K2,requirement_contains:Q2},tk=I2,ek={common:Ar,getConfig:Gt,insertCluster:l2,insertEdge:R2,insertEdgeLabel:E2,insertMarkers:tk,insertNode:pf,interpolateToCurve:Ms,labelHelper:it,log:$,positionEdgeLabel:F2},fi={},mf=f(e=>{for(const t of e)fi[t.name]=t},"registerLayoutLoaders"),rk=f(()=>{mf([{name:"dagre",loader:f(async()=>await ft(()=>import("./dagre-QXRM2OYR-B_pfLEzv.js"),__vite__mapDeps([5,6,7,8,9,10,2,3,11,12,13]),import.meta.url),"loader")}])},"registerDefaultLayoutLoaders");rk();var wS=f(async(e,t)=>{if(!(e.layoutAlgorithm in fi))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=fi[e.layoutAlgorithm];return(await r.loader()).render(e,t,ek,{algorithm:r.algorithm})},"render"),vS=f((e="",{fallback:t="dagre"}={})=>{if(e in fi)return e;if(t in fi)return $.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),kl={name:"mermaid",version:"11.5.0",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.4",katex:"^0.16.9",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^15.0.7",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",chokidar:"^4.0.3",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.0.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}},ik=f(e=>{const{securityLevel:t}=ht();let r=lt("body");if(t==="sandbox"){const n=lt(`#i${e}`).node()?.contentDocument??document;r=lt(n.body)}return r.select(`#${e}`)},"selectSvgElement"),yf="comm",xf="rule",bf="decl",ak="@import",nk="@namespace",sk="@keyframes",ok="@layer",Cf=Math.abs,Zs=String.fromCharCode;function kf(e){return e.trim()}function Xi(e,t,r){return e.replace(t,r)}function lk(e,t,r){return e.indexOf(t,r)}function br(e,t){return e.charCodeAt(t)|0}function Br(e,t,r){return e.slice(t,r)}function fe(e){return e.length}function ck(e){return e.length}function Ii(e,t){return t.push(e),e}var ja=1,Lr=1,wf=0,re=0,bt=0,Or="";function Ks(e,t,r,i,a,n,o,s){return{value:e,root:t,parent:r,type:i,props:a,children:n,line:ja,column:Lr,length:o,return:"",siblings:s}}function hk(){return bt}function uk(){return bt=re>0?br(Or,--re):0,Lr--,bt===10&&(Lr=1,ja--),bt}function oe(){return bt=re<wf?br(Or,re++):0,Lr++,bt===10&&(Lr=1,ja++),bt}function Ie(){return br(Or,re)}function Zi(){return re}function Ua(e,t){return Br(Or,e,t)}function pi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function dk(e){return ja=Lr=1,wf=fe(Or=e),re=0,[]}function fk(e){return Or="",e}function yn(e){return kf(Ua(re-1,Jn(e===91?e+2:e===40?e+1:e)))}function pk(e){for(;(bt=Ie())&&bt<33;)oe();return pi(e)>2||pi(bt)>3?"":" "}function gk(e,t){for(;--t&&oe()&&!(bt<48||bt>102||bt>57&&bt<65||bt>70&&bt<97););return Ua(e,Zi()+(t<6&&Ie()==32&&oe()==32))}function Jn(e){for(;oe();)switch(bt){case e:return re;case 34:case 39:e!==34&&e!==39&&Jn(bt);break;case 40:e===41&&Jn(e);break;case 92:oe();break}return re}function mk(e,t){for(;oe()&&e+bt!==57;)if(e+bt===84&&Ie()===47)break;return"/*"+Ua(t,re-1)+"*"+Zs(e===47?e:oe())}function yk(e){for(;!pi(Ie());)oe();return Ua(e,re)}function xk(e){return fk(Ki("",null,null,null,[""],e=dk(e),0,[0],e))}function Ki(e,t,r,i,a,n,o,s,l){for(var c=0,h=0,u=o,p=0,d=0,g=0,m=1,y=1,x=1,b=0,k="",v=a,S=n,L=i,T=k;y;)switch(g=b,b=oe()){case 40:if(g!=108&&br(T,u-1)==58){lk(T+=Xi(yn(b),"&","&\f"),"&\f",Cf(c?s[c-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:T+=yn(b);break;case 9:case 10:case 13:case 32:T+=pk(g);break;case 92:T+=gk(Zi()-1,7);continue;case 47:switch(Ie()){case 42:case 47:Ii(bk(mk(oe(),Zi()),t,r,l),l),(pi(g||1)==5||pi(Ie()||1)==5)&&fe(T)&&Br(T,-1,void 0)!==" "&&(T+=" ");break;default:T+="/"}break;case 123*m:s[c++]=fe(T)*x;case 125*m:case 59:case 0:switch(b){case 0:case 125:y=0;case 59+h:x==-1&&(T=Xi(T,/\f/g,"")),d>0&&(fe(T)-u||m===0&&g===47)&&Ii(d>32?vl(T+";",i,r,u-1,l):vl(Xi(T," ","")+";",i,r,u-2,l),l);break;case 59:T+=";";default:if(Ii(L=wl(T,t,r,c,h,a,s,k,v=[],S=[],u,n),n),b===123)if(h===0)Ki(T,t,L,L,v,n,u,s,S);else{switch(p){case 99:if(br(T,3)===110)break;case 108:if(br(T,2)===97)break;default:h=0;case 100:case 109:case 115:}h?Ki(e,L,L,i&&Ii(wl(e,L,L,0,0,a,s,k,a,v=[],u,S),S),a,S,u,s,i?v:S):Ki(T,L,L,L,[""],S,0,s,S)}}c=h=d=0,m=x=1,k=T="",u=o;break;case 58:u=1+fe(T),d=g;default:if(m<1){if(b==123)--m;else if(b==125&&m++==0&&uk()==125)continue}switch(T+=Zs(b),b*m){case 38:x=h>0?1:(T+="\f",-1);break;case 44:s[c++]=(fe(T)-1)*x,x=1;break;case 64:Ie()===45&&(T+=yn(oe())),p=Ie(),h=u=fe(k=T+=yk(Zi())),b++;break;case 45:g===45&&fe(T)==2&&(m=0)}}return n}function wl(e,t,r,i,a,n,o,s,l,c,h,u){for(var p=a-1,d=a===0?n:[""],g=ck(d),m=0,y=0,x=0;m<i;++m)for(var b=0,k=Br(e,p+1,p=Cf(y=o[m])),v=e;b<g;++b)(v=kf(y>0?d[b]+" "+k:Xi(k,/&\f/g,d[b])))&&(l[x++]=v);return Ks(e,t,r,a===0?xf:s,l,c,h,u)}function bk(e,t,r,i){return Ks(e,t,r,yf,Zs(hk()),Br(e,2,-2),0,i)}function vl(e,t,r,i,a){return Ks(e,t,r,bf,Br(e,0,i),Br(e,i+1,-1),i,a)}function ts(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function Ck(e,t,r,i){switch(e.type){case ok:if(e.children.length)break;case ak:case nk:case bf:return e.return=e.return||e.value;case yf:return"";case sk:return e.return=e.value+"{"+ts(e.children,i)+"}";case xf:if(!fe(e.value=e.props.join(",")))return""}return fe(r=ts(e.children,i))?e.return=e.value+"{"+r+"}":""}var kk=eu(Object.keys,Object),wk=Object.prototype,vk=wk.hasOwnProperty;function Sk(e){if(!Da(e))return kk(e);var t=[];for(var r in Object(e))vk.call(e,r)&&r!="constructor"&&t.push(r);return t}var es=sr(xe,"DataView"),rs=sr(xe,"Promise"),is=sr(xe,"Set"),as=sr(xe,"WeakMap"),Sl="[object Map]",Tk="[object Object]",Tl="[object Promise]",_l="[object Set]",Bl="[object WeakMap]",Ll="[object DataView]",_k=nr(es),Bk=nr(di),Lk=nr(rs),Ak=nr(is),Mk=nr(as),Ve=Mr;(es&&Ve(new es(new ArrayBuffer(1)))!=Ll||di&&Ve(new di)!=Sl||rs&&Ve(rs.resolve())!=Tl||is&&Ve(new is)!=_l||as&&Ve(new as)!=Bl)&&(Ve=function(e){var t=Mr(e),r=t==Tk?e.constructor:void 0,i=r?nr(r):"";if(i)switch(i){case _k:return Ll;case Bk:return Sl;case Lk:return Tl;case Ak:return _l;case Mk:return Bl}return t});var Ek="[object Map]",Fk="[object Set]",$k=Object.prototype,Ok=$k.hasOwnProperty;function Al(e){if(e==null)return!0;if(Ra(e)&&(da(e)||typeof e=="string"||typeof e.splice=="function"||Ls(e)||As(e)||ua(e)))return!e.length;var t=Ve(e);if(t==Ek||t==Fk)return!e.size;if(Da(e))return!Sk(e).length;for(var r in e)if(Ok.call(e,r))return!1;return!0}var vf="c4",Dk=f(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),Rk=f(async()=>{const{diagram:e}=await ft(()=>import("./c4Diagram-7JAJQR3Y-Dhf491tz.js"),__vite__mapDeps([14,15,13,2,3,11,12]),import.meta.url);return{id:vf,diagram:e}},"loader"),Ik={id:vf,detector:Dk,loader:Rk},Pk=Ik,Sf="flowchart",Nk=f((e,t)=>t?.flowchart?.defaultRenderer==="dagre-wrapper"||t?.flowchart?.defaultRenderer==="elk"?!1:/^\s*graph/.test(e),"detector"),zk=f(async()=>{const{diagram:e}=await ft(()=>import("./flowDiagram-27HWSH3H-Dn6SZx5b.js"),__vite__mapDeps([16,17,13,18,2,3,11,12]),import.meta.url);return{id:Sf,diagram:e}},"loader"),Wk={id:Sf,detector:Nk,loader:zk},qk=Wk,Tf="flowchart-v2",Hk=f((e,t)=>t?.flowchart?.defaultRenderer==="dagre-d3"?!1:(t?.flowchart?.defaultRenderer==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&t?.flowchart?.defaultRenderer==="dagre-wrapper"?!0:/^\s*flowchart/.test(e)),"detector"),jk=f(async()=>{const{diagram:e}=await ft(()=>import("./flowDiagram-27HWSH3H-Dn6SZx5b.js"),__vite__mapDeps([16,17,13,18,2,3,11,12]),import.meta.url);return{id:Tf,diagram:e}},"loader"),Uk={id:Tf,detector:Hk,loader:jk},Yk=Uk,_f="er",Gk=f(e=>/^\s*erDiagram/.test(e),"detector"),Vk=f(async()=>{const{diagram:e}=await ft(()=>import("./erDiagram-MVNNDQJ5-DhoSfK7x.js"),__vite__mapDeps([19,17,13,18,2,3,11,12]),import.meta.url);return{id:_f,diagram:e}},"loader"),Xk={id:_f,detector:Gk,loader:Vk},Zk=Xk,Bf="gitGraph",Kk=f(e=>/^\s*gitGraph/.test(e),"detector"),Qk=f(async()=>{const{diagram:e}=await ft(()=>import("./gitGraphDiagram-ISGV4O2Y-kryu9vhB.js"),__vite__mapDeps([20,21,22,23,2,3,7,9,10,13,11,12]),import.meta.url);return{id:Bf,diagram:e}},"loader"),Jk={id:Bf,detector:Kk,loader:Qk},tw=Jk,Lf="gantt",ew=f(e=>/^\s*gantt/.test(e),"detector"),rw=f(async()=>{const{diagram:e}=await ft(()=>import("./ganttDiagram-ZCE2YOAT-9KrQO0Hw.js"),__vite__mapDeps([24,2,3,25,12,26,27,13,11]),import.meta.url);return{id:Lf,diagram:e}},"loader"),iw={id:Lf,detector:ew,loader:rw},aw=iw,Af="info",nw=f(e=>/^\s*info/.test(e),"detector"),sw=f(async()=>{const{diagram:e}=await ft(()=>import("./infoDiagram-SDLB2J7W-DHVrZ69o.js"),__vite__mapDeps([28,23,2,3,7,9,10,11,12,13]),import.meta.url);return{id:Af,diagram:e}},"loader"),ow={id:Af,detector:nw,loader:sw},Mf="pie",lw=f(e=>/^\s*pie/.test(e),"detector"),cw=f(async()=>{const{diagram:e}=await ft(()=>import("./pieDiagram-OC6WZ2SS-Co4CCibQ.js"),__vite__mapDeps([29,21,23,2,3,7,9,10,30,12,31,27,11,13]),import.meta.url);return{id:Mf,diagram:e}},"loader"),hw={id:Mf,detector:lw,loader:cw},Ef="quadrantChart",uw=f(e=>/^\s*quadrantChart/.test(e),"detector"),dw=f(async()=>{const{diagram:e}=await ft(()=>import("./quadrantDiagram-OT6RYTWY-GH8KmUhM.js"),__vite__mapDeps([32,26,12,27,13,2,3,11]),import.meta.url);return{id:Ef,diagram:e}},"loader"),fw={id:Ef,detector:uw,loader:dw},pw=fw,Ff="xychart",gw=f(e=>/^\s*xychart-beta/.test(e),"detector"),mw=f(async()=>{const{diagram:e}=await ft(()=>import("./xychartDiagram-NJOKMNIP-DChUQIgb.js"),__vite__mapDeps([33,27,31,34,26,12,2,3,11,13]),import.meta.url);return{id:Ff,diagram:e}},"loader"),yw={id:Ff,detector:gw,loader:mw},xw=yw,$f="requirement",bw=f(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),Cw=f(async()=>{const{diagram:e}=await ft(()=>import("./requirementDiagram-BKGUWIPO-D3YJMkOf.js"),__vite__mapDeps([35,17,13,2,3,11,12]),import.meta.url);return{id:$f,diagram:e}},"loader"),kw={id:$f,detector:bw,loader:Cw},ww=kw,Of="sequence",vw=f(e=>/^\s*sequenceDiagram/.test(e),"detector"),Sw=f(async()=>{const{diagram:e}=await ft(()=>import("./sequenceDiagram-C4VUPXDP-DbrsMme5.js"),__vite__mapDeps([36,15,22,13,2,3,11,12]),import.meta.url);return{id:Of,diagram:e}},"loader"),Tw={id:Of,detector:vw,loader:Sw},_w=Tw,Df="class",Bw=f((e,t)=>t?.class?.defaultRenderer==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e),"detector"),Lw=f(async()=>{const{diagram:e}=await ft(()=>import("./classDiagram-L266QK7U-C6d8YZyD.js"),__vite__mapDeps([37,38,17,13,2,3,11,12]),import.meta.url);return{id:Df,diagram:e}},"loader"),Aw={id:Df,detector:Bw,loader:Lw},Mw=Aw,Rf="classDiagram",Ew=f((e,t)=>/^\s*classDiagram/.test(e)&&t?.class?.defaultRenderer==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e),"detector"),Fw=f(async()=>{const{diagram:e}=await ft(()=>import("./classDiagram-v2-JRWBCVM4-C6d8YZyD.js"),__vite__mapDeps([39,38,17,13,2,3,11,12]),import.meta.url);return{id:Rf,diagram:e}},"loader"),$w={id:Rf,detector:Ew,loader:Fw},Ow=$w,If="state",Dw=f((e,t)=>t?.state?.defaultRenderer==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e),"detector"),Rw=f(async()=>{const{diagram:e}=await ft(()=>import("./stateDiagram-BVO7J4UH-BS86ek3y.js"),__vite__mapDeps([40,41,17,13,6,7,8,9,12,2,3,11]),import.meta.url);return{id:If,diagram:e}},"loader"),Iw={id:If,detector:Dw,loader:Rw},Pw=Iw,Pf="stateDiagram",Nw=f((e,t)=>!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&t?.state?.defaultRenderer==="dagre-wrapper"),"detector"),zw=f(async()=>{const{diagram:e}=await ft(()=>import("./stateDiagram-v2-WR7QG3WR-BSCKtWyM.js"),__vite__mapDeps([42,41,17,13,2,3,11,12]),import.meta.url);return{id:Pf,diagram:e}},"loader"),Ww={id:Pf,detector:Nw,loader:zw},qw=Ww,Nf="journey",Hw=f(e=>/^\s*journey/.test(e),"detector"),jw=f(async()=>{const{diagram:e}=await ft(()=>import("./journeyDiagram-D7A75E63-CfbQA05t.js"),__vite__mapDeps([43,15,13,30,12,2,3,11]),import.meta.url);return{id:Nf,diagram:e}},"loader"),Uw={id:Nf,detector:Hw,loader:jw},Yw=Uw,Gw=f((e,t,r)=>{$.debug(`rendering svg for syntax error
`);const i=ik(t),a=i.append("g");i.attr("viewBox","0 0 2412 512"),sc(i,100,512,!0),a.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),a.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),a.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),a.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),a.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),a.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),a.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),a.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),zf={draw:Gw},Vw=zf,Xw={db:{},renderer:zf,parser:{parse:f(()=>{},"parse")}},Zw=Xw,Wf="flowchart-elk",Kw=f((e,t={})=>/^\s*flowchart-elk/.test(e)||/^\s*flowchart|graph/.test(e)&&t?.flowchart?.defaultRenderer==="elk"?(t.layout="elk",!0):!1,"detector"),Qw=f(async()=>{const{diagram:e}=await ft(()=>import("./flowDiagram-27HWSH3H-Dn6SZx5b.js"),__vite__mapDeps([16,17,13,18,2,3,11,12]),import.meta.url);return{id:Wf,diagram:e}},"loader"),Jw={id:Wf,detector:Kw,loader:Qw},tv=Jw,qf="timeline",ev=f(e=>/^\s*timeline/.test(e),"detector"),rv=f(async()=>{const{diagram:e}=await ft(()=>import("./timeline-definition-WOTUTIAU-DW9lAhIx.js"),__vite__mapDeps([44,13,30,12,2,3,11]),import.meta.url);return{id:qf,diagram:e}},"loader"),iv={id:qf,detector:ev,loader:rv},av=iv,Hf="mindmap",nv=f(e=>/^\s*mindmap/.test(e),"detector"),sv=f(async()=>{const{diagram:e}=await ft(()=>import("./mindmap-definition-7EJRZJGK-pbSPzA9j.js"),__vite__mapDeps([45,46,2,3,13,11,12]),import.meta.url);return{id:Hf,diagram:e}},"loader"),ov={id:Hf,detector:nv,loader:sv},lv=ov,jf="kanban",cv=f(e=>/^\s*kanban/.test(e),"detector"),hv=f(async()=>{const{diagram:e}=await ft(()=>import("./kanban-definition-4GR4SRK3-j3vcolHs.js"),__vite__mapDeps([47,2,3,11,12,13]),import.meta.url);return{id:jf,diagram:e}},"loader"),uv={id:jf,detector:cv,loader:hv},dv=uv,Uf="sankey",fv=f(e=>/^\s*sankey-beta/.test(e),"detector"),pv=f(async()=>{const{diagram:e}=await ft(()=>import("./sankeyDiagram-3MH5UGAL-B4rSfPq8.js"),__vite__mapDeps([48,31,27,13,2,3,11,12]),import.meta.url);return{id:Uf,diagram:e}},"loader"),gv={id:Uf,detector:fv,loader:pv},mv=gv,Yf="packet",yv=f(e=>/^\s*packet-beta/.test(e),"detector"),xv=f(async()=>{const{diagram:e}=await ft(()=>import("./diagram-DHSB7DV3-DzQPZow3.js"),__vite__mapDeps([49,21,23,2,3,7,9,10,11,12,13]),import.meta.url);return{id:Yf,diagram:e}},"loader"),bv={id:Yf,detector:yv,loader:xv},Gf="block",Cv=f(e=>/^\s*block-beta/.test(e),"detector"),kv=f(async()=>{const{diagram:e}=await ft(()=>import("./blockDiagram-5JUZGEFE-XsfnSuGn.js"),__vite__mapDeps([50,10,7,6,18,13,12,2,3,11]),import.meta.url);return{id:Gf,diagram:e}},"loader"),wv={id:Gf,detector:Cv,loader:kv},vv=wv,Vf="architecture",Sv=f(e=>/^\s*architecture/.test(e),"detector"),Tv=f(async()=>{const{diagram:e}=await ft(()=>import("./architectureDiagram-PQUH6ZAG-BOwpk7xQ.js"),__vite__mapDeps([51,21,22,23,2,3,7,9,10,46,13,11,12]),import.meta.url);return{id:Vf,diagram:e}},"loader"),_v={id:Vf,detector:Sv,loader:Tv},Bv=_v,Ml=!1,Ya=f(()=>{Ml||(Ml=!0,ea("error",Zw,e=>e.toLowerCase().trim()==="error"),ea("---",{db:{clear:f(()=>{},"clear")},styles:{},renderer:{draw:f(()=>{},"draw")},parser:{parse:f(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:f(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),Gl(Pk,dv,Ow,Mw,Zk,aw,ow,hw,ww,_w,tv,Yk,qk,lv,av,tw,qw,Pw,Yw,pw,mv,bv,xw,vv,Bv))},"addDiagrams"),Lv=f(async()=>{$.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(kr).map(async([r,{detector:i,loader:a}])=>{if(a)try{Tn(r)}catch{try{const{diagram:n,id:o}=await a();ea(o,n,i)}catch(n){throw $.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete kr[r],n}}}))).filter(r=>r.status==="rejected");if(t.length>0){$.error(`Failed to load ${t.length} external diagrams`);for(const r of t)$.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),Av="graphics-document document";function Xf(e,t){e.attr("role",Av),t!==""&&e.attr("aria-roledescription",t)}f(Xf,"setA11yDiagramInfo");function Zf(e,t,r,i){if(e.insert!==void 0){if(r){const a=`chart-desc-${i}`;e.attr("aria-describedby",a),e.insert("desc",":first-child").attr("id",a).text(r)}if(t){const a=`chart-title-${i}`;e.attr("aria-labelledby",a),e.insert("title",":first-child").attr("id",a).text(t)}}}f(Zf,"addSVGa11yTitleDescription");var ns=class Kf{constructor(t,r,i,a,n){this.type=t,this.text=r,this.db=i,this.parser=a,this.renderer=n}static{f(this,"Diagram")}static async fromText(t,r={}){const i=Gt(),a=ls(t,i);t=QC(t)+`
`;try{Tn(a)}catch{const c=Sg(a);if(!c)throw new Yl(`Diagram ${a} not found.`);const{id:h,diagram:u}=await c();ea(h,u)}const{db:n,parser:o,renderer:s,init:l}=Tn(a);return o.parser&&(o.parser.yy=n),n.clear?.(),l?.(i),r.title&&n.setDiagramTitle?.(r.title),await o.parse(t),new Kf(a,t,n,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},El=[],Mv=f(()=>{El.forEach(e=>{e()}),El=[]},"attachFunctions"),Ev=f(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function Qf(e){const t=e.match(Ul);if(!t)return{text:e,metadata:{}};let r=vy(t[1],{schema:wy})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}f(Qf,"extractFrontMatter");var Fv=f(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),$v=f(e=>{const{text:t,metadata:r}=Qf(e),{displayMode:i,title:a,config:n={}}=r;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:a,config:n,text:t}},"processFrontmatter"),Ov=f(e=>{const t=pe.detectInit(e)??{},r=pe.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):r?.type==="wrap"&&(t.wrap=!0),{text:NC(e),directive:t}},"processDirectives");function Qs(e){const t=Fv(e),r=$v(t),i=Ov(r.text),a=Ds(r.config,i.directive);return e=Ev(i.text),{code:e,title:r.title,config:a}}f(Qs,"preprocessDiagram");function Jf(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}f(Jf,"toBase64");var Dv=5e4,Rv="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",Iv="sandbox",Pv="loose",Nv="http://www.w3.org/2000/svg",zv="http://www.w3.org/1999/xlink",Wv="http://www.w3.org/1999/xhtml",qv="100%",Hv="100%",jv="border:0;margin:0;",Uv="margin:0",Yv="allow-top-navigation-by-user-activation allow-popups",Gv='The "iframe" tag is not supported by your browser.',Vv=["foreignobject"],Xv=["dominant-baseline"];function Js(e){const t=Qs(e);return Ji(),zg(t.config??{}),t}f(Js,"processAndSetConfigs");async function tp(e,t){Ya();try{const{code:r,config:i}=Js(e);return{diagramType:(await rp(r)).type,config:i}}catch(r){if(t?.suppressErrors)return!1;throw r}}f(tp,"parse");var Fl=f((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),Zv=f((e,t=new Map)=>{let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const o=e.htmlLabels??e.flowchart?.htmlLabels?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(s=>{Al(s.styles)||o.forEach(l=>{r+=Fl(s.id,l,s.styles)}),Al(s.textStyles)||(r+=Fl(s.id,"tspan",(s?.textStyles||[]).map(l=>l.replace("color","fill"))))})}return r},"createCssStyles"),Kv=f((e,t,r,i)=>{const a=Zv(e,r),n=nm(t,a,e.themeVariables);return ts(xk(`${i}{${n}}`),Ck)},"createUserStyles"),Qv=f((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=or(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),Jv=f((e="",t)=>{const r=t?.viewBox?.baseVal?.height?t.viewBox.baseVal.height+"px":Hv,i=Jf(`<body style="${Uv}">${e}</body>`);return`<iframe style="width:${qv};height:${r};${jv}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${Yv}">
  ${Gv}
</iframe>`},"putIntoIFrame"),$l=f((e,t,r,i,a)=>{const n=e.append("div");n.attr("id",r),i&&n.attr("style",i);const o=n.append("svg").attr("id",t).attr("width","100%").attr("xmlns",Nv);return a&&o.attr("xmlns:xlink",a),o.append("g"),e},"appendDivSvgG");function ss(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}f(ss,"sandboxedIframe");var tS=f((e,t,r,i)=>{e.getElementById(t)?.remove(),e.getElementById(r)?.remove(),e.getElementById(i)?.remove()},"removeExistingElements"),eS=f(async function(e,t,r){Ya();const i=Js(t);t=i.code;const a=Gt();$.debug(a),t.length>(a?.maxTextSize??Dv)&&(t=Rv);const n="#"+e,o="i"+e,s="#"+o,l="d"+e,c="#"+l,h=f(()=>{const P=lt(p?s:c).node();P&&"remove"in P&&P.remove()},"removeTempElements");let u=lt("body");const p=a.securityLevel===Iv,d=a.securityLevel===Pv,g=a.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),p){const z=ss(lt(r),o);u=lt(z.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt(r);$l(u,e,l,`font-family: ${g}`,zv)}else{if(tS(document,e,l,o),p){const z=ss(lt("body"),o);u=lt(z.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt("body");$l(u,e,l)}let m,y;try{m=await ns.fromText(t,{title:i.title})}catch(z){if(a.suppressErrorRendering)throw h(),z;m=await ns.fromText("error"),y=z}const x=u.select(c).node(),b=m.type,k=x.firstChild,v=k.firstChild,S=m.renderer.getClasses?.(t,m),L=Kv(a,b,S,n),T=document.createElement("style");T.innerHTML=L,k.insertBefore(T,v);try{await m.renderer.draw(t,e,kl.version,m)}catch(z){throw a.suppressErrorRendering?h():Vw.draw(t,e,kl.version),z}const D=u.select(`${c} svg`),R=m.db.getAccTitle?.(),O=m.db.getAccDescription?.();ip(b,D,R,O),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",Wv);let B=u.select(c).node().innerHTML;if($.debug("config.arrowMarkerAbsolute",a.arrowMarkerAbsolute),B=Qv(B,p,kt(a.arrowMarkerAbsolute)),p){const z=u.select(c+" svg").node();B=Jv(B,z)}else d||(B=Cr.sanitize(B,{ADD_TAGS:Vv,ADD_ATTR:Xv,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(Mv(),y)throw y;return h(),{diagramType:b,svg:B,bindFunctions:m.db.bindFunctions}},"render");function ep(e={}){const t=Lt({},e);t?.fontFamily&&!t.themeVariables?.fontFamily&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),Pg(t),t?.theme&&t.theme in Te?t.themeVariables=Te[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Te.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?Ig(t):Jl();os(r.logLevel),Ya()}f(ep,"initialize");var rp=f((e,t={})=>{const{code:r}=Qs(e);return ns.fromText(r,t)},"getDiagramFromText");function ip(e,t,r,i){Xf(t,e),Zf(t,r,i,t.attr("id"))}f(ip,"addA11yInfo");var ir=Object.freeze({render:eS,parse:tp,getDiagramFromText:rp,initialize:ep,getConfig:Gt,setConfig:tc,getSiteConfig:Jl,updateSiteConfig:Ng,reset:f(()=>{Ji()},"reset"),globalReset:f(()=>{Ji(wr)},"globalReset"),defaultConfig:wr});os(Gt().logLevel);Ji(Gt());var rS=f((e,t,r)=>{$.warn(e),Os(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),ap=f(async function(e={querySelector:".mermaid"}){try{await iS(e)}catch(t){if(Os(t)&&$.error(t.str),Ae.parseError&&Ae.parseError(t),!e.suppressErrors)throw $.error("Use the suppressErrors option to suppress these errors"),t}},"run"),iS=f(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=ir.getConfig();$.debug(`${e?"":"No "}Callback function found`);let a;if(r)a=r;else if(t)a=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");$.debug(`Found ${a.length} diagrams`),i?.startOnLoad!==void 0&&($.debug("Start On Load: "+i?.startOnLoad),ir.updateSiteConfig({startOnLoad:i?.startOnLoad}));const n=new pe.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const l of Array.from(a)){if($.info("Rendering diagram: "+l.id),l.getAttribute("data-processed"))continue;l.setAttribute("data-processed","true");const c=`mermaid-${n.next()}`;o=l.innerHTML,o=Eu(pe.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=pe.detectInit(o);h&&$.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:p}=await lp(c,o,l);l.innerHTML=u,e&&await e(c),p&&p(l)}catch(u){rS(u,s,Ae.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),np=f(function(e){ir.initialize(e)},"initialize"),aS=f(async function(e,t,r){$.warn("mermaid.init is deprecated. Please use run instead."),e&&np(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await ap(i)},"init"),nS=f(async(e,{lazyLoad:t=!0}={})=>{Ya(),Gl(...e),t===!1&&await Lv()},"registerExternalDiagrams"),sp=f(function(){if(Ae.startOnLoad){const{startOnLoad:e}=ir.getConfig();e&&Ae.run().catch(t=>$.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",sp,!1);var sS=f(function(e){Ae.parseError=e},"setParseErrorHandler"),wa=[],xn=!1,op=f(async()=>{if(!xn){for(xn=!0;wa.length>0;){const e=wa.shift();if(e)try{await e()}catch(t){$.error("Error executing queue",t)}}xn=!1}},"executeQueue"),oS=f(async(e,t)=>new Promise((r,i)=>{const a=f(()=>new Promise((n,o)=>{ir.parse(e,t).then(s=>{n(s),r(s)},s=>{$.error("Error parsing",s),Ae.parseError?.(s),o(s),i(s)})}),"performCall");wa.push(a),op().catch(i)}),"parse"),lp=f((e,t,r)=>new Promise((i,a)=>{const n=f(()=>new Promise((o,s)=>{ir.render(e,t,r).then(l=>{o(l),i(l)},l=>{$.error("Error parsing",l),Ae.parseError?.(l),s(l),a(l)})}),"performCall");wa.push(n),op().catch(a)}),"render"),Ae={startOnLoad:!0,mermaidAPI:ir,parse:oS,render:lp,init:aS,run:ap,registerExternalDiagrams:nS,registerLayoutLoaders:mf,initialize:np,parseError:void 0,contentLoaded:sp,setParseErrorHandler:sS,detectType:ls,registerIconPacks:Uy},lS=Ae;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */const SS=Object.freeze(Object.defineProperty({__proto__:null,default:lS},Symbol.toStringTag,{value:"Module"}));export{pf as $,ei as A,wg as B,ge as C,Kl as D,Ds as E,Gt as F,jC as G,pm as H,ik as I,wy as J,kl as K,Mg as L,Sr as M,fS as N,cs as O,Ia as P,Fo as Q,HC as R,oc as S,rm as T,rt as U,gi as V,H as W,J as X,l2 as Y,DC as Z,f as _,Lt as a,au as a$,CS as a0,yu as a1,kt as a2,He as a3,ws as a4,L2 as a5,Wu as a6,or as a7,D1 as a8,EC as a9,MC as aA,vC as aB,hb as aC,Bs as aD,aC as aE,OC as aF,bi as aG,Mr as aH,ca as aI,gC as aJ,Sk as aK,xi as aL,ua as aM,cC as aN,ru as aO,fb as aP,pb as aQ,Ve as aR,tl as aS,gb as aT,Ls as aU,db as aV,xb as aW,Er as aX,qe as aY,Xo as aZ,As as a_,vb as aa,SC as ab,_s as ac,Al as ad,tk as ae,bS as af,kS as ag,yS as ah,V as ai,xS as aj,R2 as ak,F2 as al,E2 as am,jy as an,Uy as ao,Ma as ap,Gp as aq,ar,FC as as,cu as at,Fa as au,Ra as av,da as aw,uu as ax,lu as ay,lC as az,hm as b,is as b0,$C as b1,Da as b2,SS as b3,cm as c,ht as d,Ar as e,bu as f,lm as g,Le as h,Je as i,sc as j,Yh as k,$ as l,mi as m,um as n,dm as o,vy as p,B2 as q,mS as r,om as s,sm as t,pe as u,gS as v,GC as w,pS as x,vS as y,wS as z};
//# sourceMappingURL=mermaid.core-DGK6UhOk.js.map
