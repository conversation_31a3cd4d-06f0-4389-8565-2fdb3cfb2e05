import{f as S3}from"./index-Ccc2t4AG.js";import{m as xc,a as qr,r as $3,q as B3,u as ns,i as _3,v as R3,w as O3,x as T3,y as E2,s as eh,z as w2,A as D2,B as qa,C as th,D as bc,E as C2,F as L3,G as N3,H as z3,c as rp,p as F2,d as _o,e as P3,f as I3,g as k2,I as U3,b as q3,J as j3,K as G3,t as W3,L as M2}from"./time-Bgyi_H-V.js";import{d as tu,n as Y3,e as H3,i as Cu,f as X3,g as V3,h as K3,o as J3,j as al,k as ip,m as Hi,p as nh,a as rh,q as ap,r as Ac,s as S2,u as ar,v as Hr,b as ih,t as nu,w as Q3,x as Z3,y as eD,z as tD,A as nD,l as rD,B as iD,C as aD}from"./linear-CV3SENcB.js";import{d as uD}from"./dsv-DB8NKgIY.js";import{r as Et}from"./range-OtVwhkKS.js";import{R as $2,r as oD,o as sD,q as lD,C as fD,D as up,E as op,v as ah,F as rs,u as jt,G as cD,H as dD,y as hD,A as gD,I as pD,J as mD,B as yD,K as vD,L as xD,M as bD,T as AD,N as ED,w as B2,a as We,O as _2,P as wD,Q as DD,n as CD,l as R2,t as FD,b as kD,p as MD,k as SD,S as $D,U as BD,V as _D,W as RD,X as OD,Y as TD,Z as LD,_ as ND,$ as zD,a0 as PD,a1 as ID,a2 as UD,a3 as qD,a4 as jD,a5 as GD,a6 as WD,a7 as uh,a8 as _i}from"./step-Ce-xBr2D.js";import{i as ii,a as Dr}from"./init-Dmth1JHB.js";import{I as is,o as O2,i as YD}from"./ordinal-BeghXfj9.js";import{d as HD}from"./arc-Ctxh2KTd.js";import{d as XD}from"./dispatch-kxCwF96_.js";function VD(e,t){let n=0,r,i=0,a=0;if(t===void 0)for(let u of e)u!=null&&(u=+u)>=u&&(r=u-i,i+=r/++n,a+=r*(u-i));else{let u=-1;for(let o of e)(o=t(o,++u,e))!=null&&(o=+o)>=o&&(r=o-i,i+=r/++n,a+=r*(o-i))}if(n>1)return a/(n-1)}function KD(e,t){const n=VD(e,t);return n&&Math.sqrt(n)}class ct{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const a=n[i],u=t+a,o=Math.abs(t)<Math.abs(a)?t-(u-a):a-(u-t);o&&(n[r++]=o),t=u}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,i,a,u=0;if(n>0){for(u=t[--n];n>0&&(r=u,i=t[--n],u=r+i,a=i-(u-r),!a););n>0&&(a<0&&t[n-1]<0||a>0&&t[n-1]>0)&&(i=a*2,r=u+i,i==r-u&&(u=r))}return u}}function JD(e,t){return Array.from(t,n=>e[n])}function QD(e=tu){if(e===tu)return T2;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,n)=>{const r=e(t,n);return r||r===0?r:(e(n,n)===0)-(e(t,t)===0)}}function T2(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}function L2(e,t,n=0,r=1/0,i){if(t=Math.floor(t),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(e.length-1,r)),!(n<=t&&t<=r))return e;for(i=i===void 0?T2:QD(i);r>n;){if(r-n>600){const s=r-n+1,l=t-n+1,f=Math.log(s),c=.5*Math.exp(2*f/3),d=.5*Math.sqrt(f*c*(s-c)/s)*(l-s/2<0?-1:1),h=Math.max(n,Math.floor(t-l*c/s+d)),g=Math.min(r,Math.floor(t+(s-l)*c/s+d));L2(e,t,h,g,i)}const a=e[t];let u=n,o=r;for(sa(e,n,t),i(e[r],a)>0&&sa(e,n,r);u<o;){for(sa(e,u,o),++u,--o;i(e[u],a)<0;)++u;for(;i(e[o],a)>0;)--o}i(e[n],a)===0?sa(e,n,o):(++o,sa(e,o,r)),o<=t&&(n=o+1),t<=o&&(r=o-1)}return e}function sa(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}function Ec(e,t,n){if(e=Float64Array.from(Y3(e,n)),!(!(r=e.length)||isNaN(t=+t))){if(t<=0||r<2)return xc(e);if(t>=1)return qr(e);var r,i=(r-1)*t,a=Math.floor(i),u=qr(L2(e,a).subarray(0,a+1)),o=xc(e.subarray(a+1));return u+(o-u)*(i-a)}}function N2(e,t,n=H3){if(!(!(r=e.length)||isNaN(t=+t))){if(t<=0||r<2)return+n(e[0],0,e);if(t>=1)return+n(e[r-1],r-1,e);var r,i=(r-1)*t,a=Math.floor(i),u=+n(e[a],a,e),o=+n(e[a+1],a+1,e);return u+(o-u)*(i-a)}}function ZD(e,t){let n=0,r=0;if(t===void 0)for(let i of e)i!=null&&(i=+i)>=i&&(++n,r+=i);else{let i=-1;for(let a of e)(a=t(a,++i,e))!=null&&(a=+a)>=a&&(++n,r+=a)}if(n)return r/n}function z2(e,t){return Ec(e,.5,t)}function*eC(e){for(const t of e)yield*t}function P2(e){return Array.from(eC(e))}function I2(e,t){let n=0;for(let r of e)(r=+r)&&(n+=r);return n}function tC(e,...t){e=new is(e),t=t.map(nC);e:for(const n of e)for(const r of t)if(!r.has(n)){e.delete(n);continue e}return e}function nC(e){return e instanceof is?e:new is(e)}function rC(...e){const t=new is;for(const n of e)for(const r of n)t.add(r);return t}var U2=-.14861,oh=1.78277,sh=-.29227,ul=-.90649,ru=1.97294,sp=ru*ul,lp=ru*oh,fp=oh*sh-ul*U2;function iC(e){if(e instanceof jr)return new jr(e.h,e.s,e.l,e.opacity);e instanceof $2||(e=oD(e));var t=e.r/255,n=e.g/255,r=e.b/255,i=(fp*r+sp*t-lp*n)/(fp+sp-lp),a=r-i,u=(ru*(n-i)-sh*a)/ul,o=Math.sqrt(u*u+a*a)/(ru*i*(1-i)),s=o?Math.atan2(u,a)*B3-120:NaN;return new jr(s<0?s+360:s,o,i,e.opacity)}function wc(e,t,n,r){return arguments.length===1?iC(e):new jr(e,t,n,r??1)}function jr(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}sD(jr,wc,lD(fD,{brighter(e){return e=e==null?up:Math.pow(up,e),new jr(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?op:Math.pow(op,e),new jr(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=isNaN(this.h)?0:(this.h+120)*$3,t=+this.l,n=isNaN(this.s)?0:this.s*t*(1-t),r=Math.cos(e),i=Math.sin(e);return new $2(255*(t+n*(U2*r+oh*i)),255*(t+n*(sh*r+ul*i)),255*(t+n*(ru*r)),this.opacity)}}));function aC(e){var t=e.length;return function(n){return e[Math.max(0,Math.min(t-1,Math.floor(n*t)))]}}function uC(e,t){var n=ah(+e,+t);return function(r){var i=n(r);return i-360*Math.floor(i/360)}}var oC=1e-12;function cp(e){return((e=Math.exp(e))+1/e)/2}function sC(e){return((e=Math.exp(e))-1/e)/2}function lC(e){return((e=Math.exp(2*e))-1)/(e+1)}const fC=function e(t,n,r){function i(a,u){var o=a[0],s=a[1],l=a[2],f=u[0],c=u[1],d=u[2],h=f-o,g=c-s,p=h*h+g*g,m,y;if(p<oC)y=Math.log(d/l)/t,m=function(A){return[o+A*h,s+A*g,l*Math.exp(t*A*y)]};else{var v=Math.sqrt(p),x=(d*d-l*l+r*p)/(2*l*n*v),b=(d*d-l*l-r*p)/(2*d*n*v),E=Math.log(Math.sqrt(x*x+1)-x),w=Math.log(Math.sqrt(b*b+1)-b);y=(w-E)/t,m=function(A){var D=A*y,C=cp(E),F=l/(n*v)*(C*lC(t*D+E)-sC(E));return[o+F*h,s+F*g,l*C/cp(t*D+E)]}}return m.duration=y*1e3*t/Math.SQRT2,m}return i.rho=function(a){var u=Math.max(.001,+a),o=u*u,s=o*o;return e(u,o,s)},i}(Math.SQRT2,2,4);function q2(e){return function(t,n){var r=e((t=rs(t)).h,(n=rs(n)).h),i=jt(t.s,n.s),a=jt(t.l,n.l),u=jt(t.opacity,n.opacity);return function(o){return t.h=r(o),t.s=i(o),t.l=a(o),t.opacity=u(o),t+""}}}const cC=q2(ah);var dC=q2(jt);function hC(e,t){var n=jt((e=ns(e)).l,(t=ns(t)).l),r=jt(e.a,t.a),i=jt(e.b,t.b),a=jt(e.opacity,t.opacity);return function(u){return e.l=n(u),e.a=r(u),e.b=i(u),e.opacity=a(u),e+""}}function j2(e){return function t(n){n=+n;function r(i,a){var u=e((i=wc(i)).h,(a=wc(a)).h),o=jt(i.s,a.s),s=jt(i.l,a.l),l=jt(i.opacity,a.opacity);return function(f){return i.h=u(f),i.s=o(f),i.l=s(Math.pow(f,n)),i.opacity=l(f),i+""}}return r.gamma=t,r}(1)}const gC=j2(ah);var pC=j2(jt);function lh(e,t){t===void 0&&(t=e,e=Cu);for(var n=0,r=t.length-1,i=t[0],a=new Array(r<0?0:r);n<r;)a[n]=e(i,i=t[++n]);return function(u){var o=Math.max(0,Math.min(r-1,Math.floor(u*=r)));return a[o](u-o)}}function mC(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e(r/(t-1));return n}const yC=Object.freeze(Object.defineProperty({__proto__:null,interpolate:Cu,interpolateArray:X3,interpolateBasis:cD,interpolateBasisClosed:dD,interpolateCubehelix:gC,interpolateCubehelixLong:pC,interpolateDate:V3,interpolateDiscrete:aC,interpolateHcl:_3,interpolateHclLong:R3,interpolateHsl:cC,interpolateHslLong:dC,interpolateHue:uC,interpolateLab:hC,interpolateNumber:hD,interpolateNumberArray:K3,interpolateObject:J3,interpolateRgb:gD,interpolateRgbBasis:pD,interpolateRgbBasisClosed:mD,interpolateRound:al,interpolateString:yD,interpolateTransformCss:vD,interpolateTransformSvg:xD,interpolateZoom:fC,piecewise:lh,quantize:mC},Symbol.toStringTag,{value:"Module"}));function vC(e,t,n){var r=new AD,i=t;return t==null?(r.restart(e,t,n),r):(r._restart=r.restart,r.restart=function(a,u,o){u=+u,o=o==null?bD():+o,r._restart(function s(l){l+=i,r._restart(s,i+=u,o),a(l)},u,o)},r.restart(e,t,n),r)}const jn=11102230246251565e-32,je=134217729,xC=(3+8*jn)*jn;function Ff(e,t,n,r,i){let a,u,o,s,l=t[0],f=r[0],c=0,d=0;f>l==f>-l?(a=l,l=t[++c]):(a=f,f=r[++d]);let h=0;if(c<e&&d<n)for(f>l==f>-l?(u=l+a,o=a-(u-l),l=t[++c]):(u=f+a,o=a-(u-f),f=r[++d]),a=u,o!==0&&(i[h++]=o);c<e&&d<n;)f>l==f>-l?(u=a+l,s=u-a,o=a-(u-s)+(l-s),l=t[++c]):(u=a+f,s=u-a,o=a-(u-s)+(f-s),f=r[++d]),a=u,o!==0&&(i[h++]=o);for(;c<e;)u=a+l,s=u-a,o=a-(u-s)+(l-s),l=t[++c],a=u,o!==0&&(i[h++]=o);for(;d<n;)u=a+f,s=u-a,o=a-(u-s)+(f-s),f=r[++d],a=u,o!==0&&(i[h++]=o);return(a!==0||h===0)&&(i[h++]=a),h}function bC(e,t){let n=t[0];for(let r=1;r<e;r++)n+=t[r];return n}function Fu(e){return new Float64Array(e)}const AC=(3+16*jn)*jn,EC=(2+12*jn)*jn,wC=(9+64*jn)*jn*jn,hi=Fu(4),dp=Fu(8),hp=Fu(12),gp=Fu(16),nt=Fu(4);function DC(e,t,n,r,i,a,u){let o,s,l,f,c,d,h,g,p,m,y,v,x,b,E,w,A,D;const C=e-i,F=n-i,M=t-a,O=r-a;b=C*O,d=je*C,h=d-(d-C),g=C-h,d=je*O,p=d-(d-O),m=O-p,E=g*m-(b-h*p-g*p-h*m),w=M*F,d=je*M,h=d-(d-M),g=M-h,d=je*F,p=d-(d-F),m=F-p,A=g*m-(w-h*p-g*p-h*m),y=E-A,c=E-y,hi[0]=E-(y+c)+(c-A),v=b+y,c=v-b,x=b-(v-c)+(y-c),y=x-w,c=x-y,hi[1]=x-(y+c)+(c-w),D=v+y,c=D-v,hi[2]=v-(D-c)+(y-c),hi[3]=D;let L=bC(4,hi),N=EC*u;if(L>=N||-L>=N||(c=e-C,o=e-(C+c)+(c-i),c=n-F,l=n-(F+c)+(c-i),c=t-M,s=t-(M+c)+(c-a),c=r-O,f=r-(O+c)+(c-a),o===0&&s===0&&l===0&&f===0)||(N=wC*u+xC*Math.abs(L),L+=C*f+O*o-(M*l+F*s),L>=N||-L>=N))return L;b=o*O,d=je*o,h=d-(d-o),g=o-h,d=je*O,p=d-(d-O),m=O-p,E=g*m-(b-h*p-g*p-h*m),w=s*F,d=je*s,h=d-(d-s),g=s-h,d=je*F,p=d-(d-F),m=F-p,A=g*m-(w-h*p-g*p-h*m),y=E-A,c=E-y,nt[0]=E-(y+c)+(c-A),v=b+y,c=v-b,x=b-(v-c)+(y-c),y=x-w,c=x-y,nt[1]=x-(y+c)+(c-w),D=v+y,c=D-v,nt[2]=v-(D-c)+(y-c),nt[3]=D;const k=Ff(4,hi,4,nt,dp);b=C*f,d=je*C,h=d-(d-C),g=C-h,d=je*f,p=d-(d-f),m=f-p,E=g*m-(b-h*p-g*p-h*m),w=M*l,d=je*M,h=d-(d-M),g=M-h,d=je*l,p=d-(d-l),m=l-p,A=g*m-(w-h*p-g*p-h*m),y=E-A,c=E-y,nt[0]=E-(y+c)+(c-A),v=b+y,c=v-b,x=b-(v-c)+(y-c),y=x-w,c=x-y,nt[1]=x-(y+c)+(c-w),D=v+y,c=D-v,nt[2]=v-(D-c)+(y-c),nt[3]=D;const R=Ff(k,dp,4,nt,hp);b=o*f,d=je*o,h=d-(d-o),g=o-h,d=je*f,p=d-(d-f),m=f-p,E=g*m-(b-h*p-g*p-h*m),w=s*l,d=je*s,h=d-(d-s),g=s-h,d=je*l,p=d-(d-l),m=l-p,A=g*m-(w-h*p-g*p-h*m),y=E-A,c=E-y,nt[0]=E-(y+c)+(c-A),v=b+y,c=v-b,x=b-(v-c)+(y-c),y=x-w,c=x-y,nt[1]=x-(y+c)+(c-w),D=v+y,c=D-v,nt[2]=v-(D-c)+(y-c),nt[3]=D;const j=Ff(R,hp,4,nt,gp);return gp[j-1]}function uo(e,t,n,r,i,a){const u=(t-a)*(n-i),o=(e-i)*(r-a),s=u-o,l=Math.abs(u+o);return Math.abs(s)>=AC*l?s:-DC(e,t,n,r,i,a,l)}const pp=Math.pow(2,-52),oo=new Uint32Array(512);class as{static from(t,n=SC,r=$C){const i=t.length,a=new Float64Array(i*2);for(let u=0;u<i;u++){const o=t[u];a[2*u]=n(o),a[2*u+1]=r(o)}return new as(a)}constructor(t){const n=t.length>>1;if(n>0&&typeof t[0]!="number")throw new Error("Expected coords to contain numbers.");this.coords=t;const r=Math.max(2*n-5,0);this._triangles=new Uint32Array(r*3),this._halfedges=new Int32Array(r*3),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){const{coords:t,_hullPrev:n,_hullNext:r,_hullTri:i,_hullHash:a}=this,u=t.length>>1;let o=1/0,s=1/0,l=-1/0,f=-1/0;for(let C=0;C<u;C++){const F=t[2*C],M=t[2*C+1];F<o&&(o=F),M<s&&(s=M),F>l&&(l=F),M>f&&(f=M),this._ids[C]=C}const c=(o+l)/2,d=(s+f)/2;let h,g,p;for(let C=0,F=1/0;C<u;C++){const M=kf(c,d,t[2*C],t[2*C+1]);M<F&&(h=C,F=M)}const m=t[2*h],y=t[2*h+1];for(let C=0,F=1/0;C<u;C++){if(C===h)continue;const M=kf(m,y,t[2*C],t[2*C+1]);M<F&&M>0&&(g=C,F=M)}let v=t[2*g],x=t[2*g+1],b=1/0;for(let C=0;C<u;C++){if(C===h||C===g)continue;const F=kC(m,y,v,x,t[2*C],t[2*C+1]);F<b&&(p=C,b=F)}let E=t[2*p],w=t[2*p+1];if(b===1/0){for(let M=0;M<u;M++)this._dists[M]=t[2*M]-t[0]||t[2*M+1]-t[1];wi(this._ids,this._dists,0,u-1);const C=new Uint32Array(u);let F=0;for(let M=0,O=-1/0;M<u;M++){const L=this._ids[M],N=this._dists[L];N>O&&(C[F++]=L,O=N)}this.hull=C.subarray(0,F),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(uo(m,y,v,x,E,w)<0){const C=g,F=v,M=x;g=p,v=E,x=w,p=C,E=F,w=M}const A=MC(m,y,v,x,E,w);this._cx=A.x,this._cy=A.y;for(let C=0;C<u;C++)this._dists[C]=kf(t[2*C],t[2*C+1],A.x,A.y);wi(this._ids,this._dists,0,u-1),this._hullStart=h;let D=3;r[h]=n[p]=g,r[g]=n[h]=p,r[p]=n[g]=h,i[h]=0,i[g]=1,i[p]=2,a.fill(-1),a[this._hashKey(m,y)]=h,a[this._hashKey(v,x)]=g,a[this._hashKey(E,w)]=p,this.trianglesLen=0,this._addTriangle(h,g,p,-1,-1,-1);for(let C=0,F,M;C<this._ids.length;C++){const O=this._ids[C],L=t[2*O],N=t[2*O+1];if(C>0&&Math.abs(L-F)<=pp&&Math.abs(N-M)<=pp||(F=L,M=N,O===h||O===g||O===p))continue;let k=0;for(let Ce=0,Qt=this._hashKey(L,N);Ce<this._hashSize&&(k=a[(Qt+Ce)%this._hashSize],!(k!==-1&&k!==r[k]));Ce++);k=n[k];let R=k,j;for(;j=r[R],uo(L,N,t[2*R],t[2*R+1],t[2*j],t[2*j+1])>=0;)if(R=j,R===k){R=-1;break}if(R===-1)continue;let ee=this._addTriangle(R,O,r[R],-1,-1,i[R]);i[O]=this._legalize(ee+2),i[R]=ee,D++;let Q=r[R];for(;j=r[Q],uo(L,N,t[2*Q],t[2*Q+1],t[2*j],t[2*j+1])<0;)ee=this._addTriangle(Q,O,j,i[O],-1,i[Q]),i[O]=this._legalize(ee+2),r[Q]=Q,D--,Q=j;if(R===k)for(;j=n[R],uo(L,N,t[2*j],t[2*j+1],t[2*R],t[2*R+1])<0;)ee=this._addTriangle(j,O,R,-1,i[R],i[j]),this._legalize(ee+2),i[j]=ee,r[R]=R,D--,R=j;this._hullStart=n[O]=R,r[R]=n[Q]=O,r[O]=Q,a[this._hashKey(L,N)]=O,a[this._hashKey(t[2*R],t[2*R+1])]=R}this.hull=new Uint32Array(D);for(let C=0,F=this._hullStart;C<D;C++)this.hull[C]=F,F=r[F];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,n){return Math.floor(CC(t-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(t){const{_triangles:n,_halfedges:r,coords:i}=this;let a=0,u=0;for(;;){const o=r[t],s=t-t%3;if(u=s+(t+2)%3,o===-1){if(a===0)break;t=oo[--a];continue}const l=o-o%3,f=s+(t+1)%3,c=l+(o+2)%3,d=n[u],h=n[t],g=n[f],p=n[c];if(FC(i[2*d],i[2*d+1],i[2*h],i[2*h+1],i[2*g],i[2*g+1],i[2*p],i[2*p+1])){n[t]=p,n[o]=d;const y=r[c];if(y===-1){let x=this._hullStart;do{if(this._hullTri[x]===c){this._hullTri[x]=t;break}x=this._hullPrev[x]}while(x!==this._hullStart)}this._link(t,y),this._link(o,r[u]),this._link(u,c);const v=l+(o+1)%3;a<oo.length&&(oo[a++]=v)}else{if(a===0)break;t=oo[--a]}}return u}_link(t,n){this._halfedges[t]=n,n!==-1&&(this._halfedges[n]=t)}_addTriangle(t,n,r,i,a,u){const o=this.trianglesLen;return this._triangles[o]=t,this._triangles[o+1]=n,this._triangles[o+2]=r,this._link(o,i),this._link(o+1,a),this._link(o+2,u),this.trianglesLen+=3,o}}function CC(e,t){const n=e/(Math.abs(e)+Math.abs(t));return(t>0?3-n:1+n)/4}function kf(e,t,n,r){const i=e-n,a=t-r;return i*i+a*a}function FC(e,t,n,r,i,a,u,o){const s=e-u,l=t-o,f=n-u,c=r-o,d=i-u,h=a-o,g=s*s+l*l,p=f*f+c*c,m=d*d+h*h;return s*(c*m-p*h)-l*(f*m-p*d)+g*(f*h-c*d)<0}function kC(e,t,n,r,i,a){const u=n-e,o=r-t,s=i-e,l=a-t,f=u*u+o*o,c=s*s+l*l,d=.5/(u*l-o*s),h=(l*f-o*c)*d,g=(u*c-s*f)*d;return h*h+g*g}function MC(e,t,n,r,i,a){const u=n-e,o=r-t,s=i-e,l=a-t,f=u*u+o*o,c=s*s+l*l,d=.5/(u*l-o*s),h=e+(l*f-o*c)*d,g=t+(u*c-s*f)*d;return{x:h,y:g}}function wi(e,t,n,r){if(r-n<=20)for(let i=n+1;i<=r;i++){const a=e[i],u=t[a];let o=i-1;for(;o>=n&&t[e[o]]>u;)e[o+1]=e[o--];e[o+1]=a}else{const i=n+r>>1;let a=n+1,u=r;la(e,i,a),t[e[n]]>t[e[r]]&&la(e,n,r),t[e[a]]>t[e[r]]&&la(e,a,r),t[e[n]]>t[e[a]]&&la(e,n,a);const o=e[a],s=t[o];for(;;){do a++;while(t[e[a]]<s);do u--;while(t[e[u]]>s);if(u<a)break;la(e,a,u)}e[n+1]=e[u],e[u]=o,r-a+1>=u-n?(wi(e,t,a,r),wi(e,t,n,u-1)):(wi(e,t,n,u-1),wi(e,t,a,r))}}function la(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}function SC(e){return e[0]}function $C(e){return e[1]}const mp=1e-6;class Pr{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,n){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+=`L${this._x1=+t},${this._y1=+n}`}arc(t,n,r){t=+t,n=+n,r=+r;const i=t+r,a=n;if(r<0)throw new Error("negative radius");this._x1===null?this._+=`M${i},${a}`:(Math.abs(this._x1-i)>mp||Math.abs(this._y1-a)>mp)&&(this._+="L"+i+","+a),r&&(this._+=`A${r},${r},0,1,1,${t-r},${n}A${r},${r},0,1,1,${this._x1=i},${this._y1=a}`)}rect(t,n,r,i){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${+r}v${+i}h${-r}Z`}value(){return this._||null}}class Dc{constructor(){this._=[]}moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}}let BC=class{constructor(t,[n,r,i,a]=[0,0,960,500]){if(!((i=+i)>=(n=+n))||!((a=+a)>=(r=+r)))throw new Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(t.points.length*2),this.vectors=new Float64Array(t.points.length*2),this.xmax=i,this.xmin=n,this.ymax=a,this.ymin=r,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:t,hull:n,triangles:r},vectors:i}=this;let a,u;const o=this.circumcenters=this._circumcenters.subarray(0,r.length/3*2);for(let p=0,m=0,y=r.length,v,x;p<y;p+=3,m+=2){const b=r[p]*2,E=r[p+1]*2,w=r[p+2]*2,A=t[b],D=t[b+1],C=t[E],F=t[E+1],M=t[w],O=t[w+1],L=C-A,N=F-D,k=M-A,R=O-D,j=(L*R-N*k)*2;if(Math.abs(j)<1e-9){if(a===void 0){a=u=0;for(const Q of n)a+=t[Q*2],u+=t[Q*2+1];a/=n.length,u/=n.length}const ee=1e9*Math.sign((a-A)*R-(u-D)*k);v=(A+M)/2-ee*R,x=(D+O)/2+ee*k}else{const ee=1/j,Q=L*L+N*N,Ce=k*k+R*R;v=A+(R*Q-N*Ce)*ee,x=D+(L*Ce-k*Q)*ee}o[m]=v,o[m+1]=x}let s=n[n.length-1],l,f=s*4,c,d=t[2*s],h,g=t[2*s+1];i.fill(0);for(let p=0;p<n.length;++p)s=n[p],l=f,c=d,h=g,f=s*4,d=t[2*s],g=t[2*s+1],i[l+2]=i[f]=h-g,i[l+3]=i[f+1]=d-c}render(t){const n=t==null?t=new Pr:void 0,{delaunay:{halfedges:r,inedges:i,hull:a},circumcenters:u,vectors:o}=this;if(a.length<=1)return null;for(let f=0,c=r.length;f<c;++f){const d=r[f];if(d<f)continue;const h=Math.floor(f/3)*2,g=Math.floor(d/3)*2,p=u[h],m=u[h+1],y=u[g],v=u[g+1];this._renderSegment(p,m,y,v,t)}let s,l=a[a.length-1];for(let f=0;f<a.length;++f){s=l,l=a[f];const c=Math.floor(i[l]/3)*2,d=u[c],h=u[c+1],g=s*4,p=this._project(d,h,o[g+2],o[g+3]);p&&this._renderSegment(d,h,p[0],p[1],t)}return n&&n.value()}renderBounds(t){const n=t==null?t=new Pr:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,n){const r=n==null?n=new Pr:void 0,i=this._clip(t);if(i===null||!i.length)return;n.moveTo(i[0],i[1]);let a=i.length;for(;i[0]===i[a-2]&&i[1]===i[a-1]&&a>1;)a-=2;for(let u=2;u<a;u+=2)(i[u]!==i[u-2]||i[u+1]!==i[u-1])&&n.lineTo(i[u],i[u+1]);return n.closePath(),r&&r.value()}*cellPolygons(){const{delaunay:{points:t}}=this;for(let n=0,r=t.length/2;n<r;++n){const i=this.cellPolygon(n);i&&(i.index=n,yield i)}}cellPolygon(t){const n=new Dc;return this.renderCell(t,n),n.value()}_renderSegment(t,n,r,i,a){let u;const o=this._regioncode(t,n),s=this._regioncode(r,i);o===0&&s===0?(a.moveTo(t,n),a.lineTo(r,i)):(u=this._clipSegment(t,n,r,i,o,s))&&(a.moveTo(u[0],u[1]),a.lineTo(u[2],u[3]))}contains(t,n,r){return n=+n,n!==n||(r=+r,r!==r)?!1:this.delaunay._step(t,n,r)===t}*neighbors(t){const n=this._clip(t);if(n)for(const r of this.delaunay.neighbors(t)){const i=this._clip(r);if(i){e:for(let a=0,u=n.length;a<u;a+=2)for(let o=0,s=i.length;o<s;o+=2)if(n[a]===i[o]&&n[a+1]===i[o+1]&&n[(a+2)%u]===i[(o+s-2)%s]&&n[(a+3)%u]===i[(o+s-1)%s]){yield r;break e}}}}_cell(t){const{circumcenters:n,delaunay:{inedges:r,halfedges:i,triangles:a}}=this,u=r[t];if(u===-1)return null;const o=[];let s=u;do{const l=Math.floor(s/3);if(o.push(n[l*2],n[l*2+1]),s=s%3===2?s-2:s+1,a[s]!==t)break;s=i[s]}while(s!==u&&s!==-1);return o}_clip(t){if(t===0&&this.delaunay.hull.length===1)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const n=this._cell(t);if(n===null)return null;const{vectors:r}=this,i=t*4;return this._simplify(r[i]||r[i+1]?this._clipInfinite(t,n,r[i],r[i+1],r[i+2],r[i+3]):this._clipFinite(t,n))}_clipFinite(t,n){const r=n.length;let i=null,a,u,o=n[r-2],s=n[r-1],l,f=this._regioncode(o,s),c,d=0;for(let h=0;h<r;h+=2)if(a=o,u=s,o=n[h],s=n[h+1],l=f,f=this._regioncode(o,s),l===0&&f===0)c=d,d=0,i?i.push(o,s):i=[o,s];else{let g,p,m,y,v;if(l===0){if((g=this._clipSegment(a,u,o,s,l,f))===null)continue;[p,m,y,v]=g}else{if((g=this._clipSegment(o,s,a,u,f,l))===null)continue;[y,v,p,m]=g,c=d,d=this._edgecode(p,m),c&&d&&this._edge(t,c,d,i,i.length),i?i.push(p,m):i=[p,m]}c=d,d=this._edgecode(y,v),c&&d&&this._edge(t,c,d,i,i.length),i?i.push(y,v):i=[y,v]}if(i)c=d,d=this._edgecode(i[0],i[1]),c&&d&&this._edge(t,c,d,i,i.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return i}_clipSegment(t,n,r,i,a,u){const o=a<u;for(o&&([t,n,r,i,a,u]=[r,i,t,n,u,a]);;){if(a===0&&u===0)return o?[r,i,t,n]:[t,n,r,i];if(a&u)return null;let s,l,f=a||u;f&8?(s=t+(r-t)*(this.ymax-n)/(i-n),l=this.ymax):f&4?(s=t+(r-t)*(this.ymin-n)/(i-n),l=this.ymin):f&2?(l=n+(i-n)*(this.xmax-t)/(r-t),s=this.xmax):(l=n+(i-n)*(this.xmin-t)/(r-t),s=this.xmin),a?(t=s,n=l,a=this._regioncode(t,n)):(r=s,i=l,u=this._regioncode(r,i))}}_clipInfinite(t,n,r,i,a,u){let o=Array.from(n),s;if((s=this._project(o[0],o[1],r,i))&&o.unshift(s[0],s[1]),(s=this._project(o[o.length-2],o[o.length-1],a,u))&&o.push(s[0],s[1]),o=this._clipFinite(t,o))for(let l=0,f=o.length,c,d=this._edgecode(o[f-2],o[f-1]);l<f;l+=2)c=d,d=this._edgecode(o[l],o[l+1]),c&&d&&(l=this._edge(t,c,d,o,l),f=o.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(o=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return o}_edge(t,n,r,i,a){for(;n!==r;){let u,o;switch(n){case 5:n=4;continue;case 4:n=6,u=this.xmax,o=this.ymin;break;case 6:n=2;continue;case 2:n=10,u=this.xmax,o=this.ymax;break;case 10:n=8;continue;case 8:n=9,u=this.xmin,o=this.ymax;break;case 9:n=1;continue;case 1:n=5,u=this.xmin,o=this.ymin;break}(i[a]!==u||i[a+1]!==o)&&this.contains(t,u,o)&&(i.splice(a,0,u,o),a+=2)}return a}_project(t,n,r,i){let a=1/0,u,o,s;if(i<0){if(n<=this.ymin)return null;(u=(this.ymin-n)/i)<a&&(s=this.ymin,o=t+(a=u)*r)}else if(i>0){if(n>=this.ymax)return null;(u=(this.ymax-n)/i)<a&&(s=this.ymax,o=t+(a=u)*r)}if(r>0){if(t>=this.xmax)return null;(u=(this.xmax-t)/r)<a&&(o=this.xmax,s=n+(a=u)*i)}else if(r<0){if(t<=this.xmin)return null;(u=(this.xmin-t)/r)<a&&(o=this.xmin,s=n+(a=u)*i)}return[o,s]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let n=0;n<t.length;n+=2){const r=(n+2)%t.length,i=(n+4)%t.length;(t[n]===t[r]&&t[r]===t[i]||t[n+1]===t[r+1]&&t[r+1]===t[i+1])&&(t.splice(r,2),n-=2)}t.length||(t=null)}return t}};const _C=2*Math.PI,gi=Math.pow;function RC(e){return e[0]}function OC(e){return e[1]}function TC(e){const{triangles:t,coords:n}=e;for(let r=0;r<t.length;r+=3){const i=2*t[r],a=2*t[r+1],u=2*t[r+2];if((n[u]-n[i])*(n[a+1]-n[i+1])-(n[a]-n[i])*(n[u+1]-n[i+1])>1e-10)return!1}return!0}function LC(e,t,n){return[e+Math.sin(e+t)*n,t+Math.cos(e-t)*n]}class fh{static from(t,n=RC,r=OC,i){return new fh("length"in t?NC(t,n,r,i):Float64Array.from(zC(t,n,r,i)))}constructor(t){this._delaunator=new as(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const t=this._delaunator,n=this.points;if(t.hull&&t.hull.length>2&&TC(t)){this.collinear=Int32Array.from({length:n.length/2},(d,h)=>h).sort((d,h)=>n[2*d]-n[2*h]||n[2*d+1]-n[2*h+1]);const s=this.collinear[0],l=this.collinear[this.collinear.length-1],f=[n[2*s],n[2*s+1],n[2*l],n[2*l+1]],c=1e-8*Math.hypot(f[3]-f[1],f[2]-f[0]);for(let d=0,h=n.length/2;d<h;++d){const g=LC(n[2*d],n[2*d+1],c);n[2*d]=g[0],n[2*d+1]=g[1]}this._delaunator=new as(n)}else delete this.collinear;const r=this.halfedges=this._delaunator.halfedges,i=this.hull=this._delaunator.hull,a=this.triangles=this._delaunator.triangles,u=this.inedges.fill(-1),o=this._hullIndex.fill(-1);for(let s=0,l=r.length;s<l;++s){const f=a[s%3===2?s-2:s+1];(r[s]===-1||u[f]===-1)&&(u[f]=s)}for(let s=0,l=i.length;s<l;++s)o[i[s]]=s;i.length<=2&&i.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=i[0],u[i[0]]=1,i.length===2&&(u[i[1]]=0,this.triangles[1]=i[1],this.triangles[2]=i[1]))}voronoi(t){return new BC(this,t)}*neighbors(t){const{inedges:n,hull:r,_hullIndex:i,halfedges:a,triangles:u,collinear:o}=this;if(o){const c=o.indexOf(t);c>0&&(yield o[c-1]),c<o.length-1&&(yield o[c+1]);return}const s=n[t];if(s===-1)return;let l=s,f=-1;do{if(yield f=u[l],l=l%3===2?l-2:l+1,u[l]!==t)return;if(l=a[l],l===-1){const c=r[(i[t]+1)%r.length];c!==f&&(yield c);return}}while(l!==s)}find(t,n,r=0){if(t=+t,t!==t||(n=+n,n!==n))return-1;const i=r;let a;for(;(a=this._step(r,t,n))>=0&&a!==r&&a!==i;)r=a;return a}_step(t,n,r){const{inedges:i,hull:a,_hullIndex:u,halfedges:o,triangles:s,points:l}=this;if(i[t]===-1||!l.length)return(t+1)%(l.length>>1);let f=t,c=gi(n-l[t*2],2)+gi(r-l[t*2+1],2);const d=i[t];let h=d;do{let g=s[h];const p=gi(n-l[g*2],2)+gi(r-l[g*2+1],2);if(p<c&&(c=p,f=g),h=h%3===2?h-2:h+1,s[h]!==t)break;if(h=o[h],h===-1){if(h=a[(u[t]+1)%a.length],h!==g&&gi(n-l[h*2],2)+gi(r-l[h*2+1],2)<c)return h;break}}while(h!==d);return f}render(t){const n=t==null?t=new Pr:void 0,{points:r,halfedges:i,triangles:a}=this;for(let u=0,o=i.length;u<o;++u){const s=i[u];if(s<u)continue;const l=a[u]*2,f=a[s]*2;t.moveTo(r[l],r[l+1]),t.lineTo(r[f],r[f+1])}return this.renderHull(t),n&&n.value()}renderPoints(t,n){n===void 0&&(!t||typeof t.moveTo!="function")&&(n=t,t=null),n=n==null?2:+n;const r=t==null?t=new Pr:void 0,{points:i}=this;for(let a=0,u=i.length;a<u;a+=2){const o=i[a],s=i[a+1];t.moveTo(o+n,s),t.arc(o,s,n,0,_C)}return r&&r.value()}renderHull(t){const n=t==null?t=new Pr:void 0,{hull:r,points:i}=this,a=r[0]*2,u=r.length;t.moveTo(i[a],i[a+1]);for(let o=1;o<u;++o){const s=2*r[o];t.lineTo(i[s],i[s+1])}return t.closePath(),n&&n.value()}hullPolygon(){const t=new Dc;return this.renderHull(t),t.value()}renderTriangle(t,n){const r=n==null?n=new Pr:void 0,{points:i,triangles:a}=this,u=a[t*=3]*2,o=a[t+1]*2,s=a[t+2]*2;return n.moveTo(i[u],i[u+1]),n.lineTo(i[o],i[o+1]),n.lineTo(i[s],i[s+1]),n.closePath(),r&&r.value()}*trianglePolygons(){const{triangles:t}=this;for(let n=0,r=t.length/3;n<r;++n)yield this.trianglePolygon(n)}trianglePolygon(t){const n=new Dc;return this.renderTriangle(t,n),n.value()}}function NC(e,t,n,r){const i=e.length,a=new Float64Array(i*2);for(let u=0;u<i;++u){const o=e[u];a[u*2]=t.call(r,o,u,e),a[u*2+1]=n.call(r,o,u,e)}return a}function*zC(e,t,n,r){let i=0;for(const a of e)yield t.call(r,a,i,e),yield n.call(r,a,i,e),++i}function PC(e,t){var n,r=1;e==null&&(e=0),t==null&&(t=0);function i(){var a,u=n.length,o,s=0,l=0;for(a=0;a<u;++a)o=n[a],s+=o.x,l+=o.y;for(s=(s/u-e)*r,l=(l/u-t)*r,a=0;a<u;++a)o=n[a],o.x-=s,o.y-=l}return i.initialize=function(a){n=a},i.x=function(a){return arguments.length?(e=+a,i):e},i.y=function(a){return arguments.length?(t=+a,i):t},i.strength=function(a){return arguments.length?(r=+a,i):r},i}function IC(e){const t=+this._x.call(null,e),n=+this._y.call(null,e);return G2(this.cover(t,n),t,n,e)}function G2(e,t,n,r){if(isNaN(t)||isNaN(n))return e;var i,a=e._root,u={data:r},o=e._x0,s=e._y0,l=e._x1,f=e._y1,c,d,h,g,p,m,y,v;if(!a)return e._root=u,e;for(;a.length;)if((p=t>=(c=(o+l)/2))?o=c:l=c,(m=n>=(d=(s+f)/2))?s=d:f=d,i=a,!(a=a[y=m<<1|p]))return i[y]=u,e;if(h=+e._x.call(null,a.data),g=+e._y.call(null,a.data),t===h&&n===g)return u.next=a,i?i[y]=u:e._root=u,e;do i=i?i[y]=new Array(4):e._root=new Array(4),(p=t>=(c=(o+l)/2))?o=c:l=c,(m=n>=(d=(s+f)/2))?s=d:f=d;while((y=m<<1|p)===(v=(g>=d)<<1|h>=c));return i[v]=a,i[y]=u,e}function UC(e){var t,n,r=e.length,i,a,u=new Array(r),o=new Array(r),s=1/0,l=1/0,f=-1/0,c=-1/0;for(n=0;n<r;++n)isNaN(i=+this._x.call(null,t=e[n]))||isNaN(a=+this._y.call(null,t))||(u[n]=i,o[n]=a,i<s&&(s=i),i>f&&(f=i),a<l&&(l=a),a>c&&(c=a));if(s>f||l>c)return this;for(this.cover(s,l).cover(f,c),n=0;n<r;++n)G2(this,u[n],o[n],e[n]);return this}function qC(e,t){if(isNaN(e=+e)||isNaN(t=+t))return this;var n=this._x0,r=this._y0,i=this._x1,a=this._y1;if(isNaN(n))i=(n=Math.floor(e))+1,a=(r=Math.floor(t))+1;else{for(var u=i-n||1,o=this._root,s,l;n>e||e>=i||r>t||t>=a;)switch(l=(t<r)<<1|e<n,s=new Array(4),s[l]=o,o=s,u*=2,l){case 0:i=n+u,a=r+u;break;case 1:n=i-u,a=r+u;break;case 2:i=n+u,r=a-u;break;case 3:n=i-u,r=a-u;break}this._root&&this._root.length&&(this._root=o)}return this._x0=n,this._y0=r,this._x1=i,this._y1=a,this}function jC(){var e=[];return this.visit(function(t){if(!t.length)do e.push(t.data);while(t=t.next)}),e}function GC(e){return arguments.length?this.cover(+e[0][0],+e[0][1]).cover(+e[1][0],+e[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]}function lt(e,t,n,r,i){this.node=e,this.x0=t,this.y0=n,this.x1=r,this.y1=i}function WC(e,t,n){var r,i=this._x0,a=this._y0,u,o,s,l,f=this._x1,c=this._y1,d=[],h=this._root,g,p;for(h&&d.push(new lt(h,i,a,f,c)),n==null?n=1/0:(i=e-n,a=t-n,f=e+n,c=t+n,n*=n);g=d.pop();)if(!(!(h=g.node)||(u=g.x0)>f||(o=g.y0)>c||(s=g.x1)<i||(l=g.y1)<a))if(h.length){var m=(u+s)/2,y=(o+l)/2;d.push(new lt(h[3],m,y,s,l),new lt(h[2],u,y,m,l),new lt(h[1],m,o,s,y),new lt(h[0],u,o,m,y)),(p=(t>=y)<<1|e>=m)&&(g=d[d.length-1],d[d.length-1]=d[d.length-1-p],d[d.length-1-p]=g)}else{var v=e-+this._x.call(null,h.data),x=t-+this._y.call(null,h.data),b=v*v+x*x;if(b<n){var E=Math.sqrt(n=b);i=e-E,a=t-E,f=e+E,c=t+E,r=h.data}}return r}function YC(e){if(isNaN(f=+this._x.call(null,e))||isNaN(c=+this._y.call(null,e)))return this;var t,n=this._root,r,i,a,u=this._x0,o=this._y0,s=this._x1,l=this._y1,f,c,d,h,g,p,m,y;if(!n)return this;if(n.length)for(;;){if((g=f>=(d=(u+s)/2))?u=d:s=d,(p=c>=(h=(o+l)/2))?o=h:l=h,t=n,!(n=n[m=p<<1|g]))return this;if(!n.length)break;(t[m+1&3]||t[m+2&3]||t[m+3&3])&&(r=t,y=m)}for(;n.data!==e;)if(i=n,!(n=n.next))return this;return(a=n.next)&&delete n.next,i?(a?i.next=a:delete i.next,this):t?(a?t[m]=a:delete t[m],(n=t[0]||t[1]||t[2]||t[3])&&n===(t[3]||t[2]||t[1]||t[0])&&!n.length&&(r?r[y]=n:this._root=n),this):(this._root=a,this)}function HC(e){for(var t=0,n=e.length;t<n;++t)this.remove(e[t]);return this}function XC(){return this._root}function VC(){var e=0;return this.visit(function(t){if(!t.length)do++e;while(t=t.next)}),e}function KC(e){var t=[],n,r=this._root,i,a,u,o,s;for(r&&t.push(new lt(r,this._x0,this._y0,this._x1,this._y1));n=t.pop();)if(!e(r=n.node,a=n.x0,u=n.y0,o=n.x1,s=n.y1)&&r.length){var l=(a+o)/2,f=(u+s)/2;(i=r[3])&&t.push(new lt(i,l,f,o,s)),(i=r[2])&&t.push(new lt(i,a,f,l,s)),(i=r[1])&&t.push(new lt(i,l,u,o,f)),(i=r[0])&&t.push(new lt(i,a,u,l,f))}return this}function JC(e){var t=[],n=[],r;for(this._root&&t.push(new lt(this._root,this._x0,this._y0,this._x1,this._y1));r=t.pop();){var i=r.node;if(i.length){var a,u=r.x0,o=r.y0,s=r.x1,l=r.y1,f=(u+s)/2,c=(o+l)/2;(a=i[0])&&t.push(new lt(a,u,o,f,c)),(a=i[1])&&t.push(new lt(a,f,o,s,c)),(a=i[2])&&t.push(new lt(a,u,c,f,l)),(a=i[3])&&t.push(new lt(a,f,c,s,l))}n.push(r)}for(;r=n.pop();)e(r.node,r.x0,r.y0,r.x1,r.y1);return this}function QC(e){return e[0]}function ZC(e){return arguments.length?(this._x=e,this):this._x}function eF(e){return e[1]}function tF(e){return arguments.length?(this._y=e,this):this._y}function ch(e,t,n){var r=new dh(t??QC,n??eF,NaN,NaN,NaN,NaN);return e==null?r:r.addAll(e)}function dh(e,t,n,r,i,a){this._x=e,this._y=t,this._x0=n,this._y0=r,this._x1=i,this._y1=a,this._root=void 0}function yp(e){for(var t={data:e.data},n=t;e=e.next;)n=n.next={data:e.data};return t}var pt=ch.prototype=dh.prototype;pt.copy=function(){var e=new dh(this._x,this._y,this._x0,this._y0,this._x1,this._y1),t=this._root,n,r;if(!t)return e;if(!t.length)return e._root=yp(t),e;for(n=[{source:t,target:e._root=new Array(4)}];t=n.pop();)for(var i=0;i<4;++i)(r=t.source[i])&&(r.length?n.push({source:r,target:t.target[i]=new Array(4)}):t.target[i]=yp(r));return e};pt.add=IC;pt.addAll=UC;pt.cover=qC;pt.data=jC;pt.extent=GC;pt.find=WC;pt.remove=YC;pt.removeAll=HC;pt.root=XC;pt.size=VC;pt.visit=KC;pt.visitAfter=JC;pt.x=ZC;pt.y=tF;function ft(e){return function(){return e}}function ur(e){return(e()-.5)*1e-6}function nF(e){return e.x+e.vx}function rF(e){return e.y+e.vy}function iF(e){var t,n,r,i=1,a=1;typeof e!="function"&&(e=ft(e==null?1:+e));function u(){for(var l,f=t.length,c,d,h,g,p,m,y=0;y<a;++y)for(c=ch(t,nF,rF).visitAfter(o),l=0;l<f;++l)d=t[l],p=n[d.index],m=p*p,h=d.x+d.vx,g=d.y+d.vy,c.visit(v);function v(x,b,E,w,A){var D=x.data,C=x.r,F=p+C;if(D){if(D.index>d.index){var M=h-D.x-D.vx,O=g-D.y-D.vy,L=M*M+O*O;L<F*F&&(M===0&&(M=ur(r),L+=M*M),O===0&&(O=ur(r),L+=O*O),L=(F-(L=Math.sqrt(L)))/L*i,d.vx+=(M*=L)*(F=(C*=C)/(m+C)),d.vy+=(O*=L)*F,D.vx-=M*(F=1-F),D.vy-=O*F)}return}return b>h+F||w<h-F||E>g+F||A<g-F}}function o(l){if(l.data)return l.r=n[l.data.index];for(var f=l.r=0;f<4;++f)l[f]&&l[f].r>l.r&&(l.r=l[f].r)}function s(){if(t){var l,f=t.length,c;for(n=new Array(f),l=0;l<f;++l)c=t[l],n[c.index]=+e(c,l,t)}}return u.initialize=function(l,f){t=l,r=f,s()},u.iterations=function(l){return arguments.length?(a=+l,u):a},u.strength=function(l){return arguments.length?(i=+l,u):i},u.radius=function(l){return arguments.length?(e=typeof l=="function"?l:ft(+l),s(),u):e},u}function aF(e){return e.index}function vp(e,t){var n=e.get(t);if(!n)throw new Error("node not found: "+t);return n}function uF(e){var t=aF,n=c,r,i=ft(30),a,u,o,s,l,f=1;e==null&&(e=[]);function c(m){return 1/Math.min(o[m.source.index],o[m.target.index])}function d(m){for(var y=0,v=e.length;y<f;++y)for(var x=0,b,E,w,A,D,C,F;x<v;++x)b=e[x],E=b.source,w=b.target,A=w.x+w.vx-E.x-E.vx||ur(l),D=w.y+w.vy-E.y-E.vy||ur(l),C=Math.sqrt(A*A+D*D),C=(C-a[x])/C*m*r[x],A*=C,D*=C,w.vx-=A*(F=s[x]),w.vy-=D*F,E.vx+=A*(F=1-F),E.vy+=D*F}function h(){if(u){var m,y=u.length,v=e.length,x=new Map(u.map((E,w)=>[t(E,w,u),E])),b;for(m=0,o=new Array(y);m<v;++m)b=e[m],b.index=m,typeof b.source!="object"&&(b.source=vp(x,b.source)),typeof b.target!="object"&&(b.target=vp(x,b.target)),o[b.source.index]=(o[b.source.index]||0)+1,o[b.target.index]=(o[b.target.index]||0)+1;for(m=0,s=new Array(v);m<v;++m)b=e[m],s[m]=o[b.source.index]/(o[b.source.index]+o[b.target.index]);r=new Array(v),g(),a=new Array(v),p()}}function g(){if(u)for(var m=0,y=e.length;m<y;++m)r[m]=+n(e[m],m,e)}function p(){if(u)for(var m=0,y=e.length;m<y;++m)a[m]=+i(e[m],m,e)}return d.initialize=function(m,y){u=m,l=y,h()},d.links=function(m){return arguments.length?(e=m,h(),d):e},d.id=function(m){return arguments.length?(t=m,d):t},d.iterations=function(m){return arguments.length?(f=+m,d):f},d.strength=function(m){return arguments.length?(n=typeof m=="function"?m:ft(+m),g(),d):n},d.distance=function(m){return arguments.length?(i=typeof m=="function"?m:ft(+m),p(),d):i},d}const oF=1664525,sF=1013904223,xp=4294967296;function lF(){let e=1;return()=>(e=(oF*e+sF)%xp)/xp}function fF(e){return e.x}function cF(e){return e.y}var dF=10,hF=Math.PI*(3-Math.sqrt(5));function gF(e){var t,n=1,r=.001,i=1-Math.pow(r,1/300),a=0,u=.6,o=new Map,s=ED(c),l=XD("tick","end"),f=lF();e==null&&(e=[]);function c(){d(),l.call("tick",t),n<r&&(s.stop(),l.call("end",t))}function d(p){var m,y=e.length,v;p===void 0&&(p=1);for(var x=0;x<p;++x)for(n+=(a-n)*i,o.forEach(function(b){b(n)}),m=0;m<y;++m)v=e[m],v.fx==null?v.x+=v.vx*=u:(v.x=v.fx,v.vx=0),v.fy==null?v.y+=v.vy*=u:(v.y=v.fy,v.vy=0);return t}function h(){for(var p=0,m=e.length,y;p<m;++p){if(y=e[p],y.index=p,y.fx!=null&&(y.x=y.fx),y.fy!=null&&(y.y=y.fy),isNaN(y.x)||isNaN(y.y)){var v=dF*Math.sqrt(.5+p),x=p*hF;y.x=v*Math.cos(x),y.y=v*Math.sin(x)}(isNaN(y.vx)||isNaN(y.vy))&&(y.vx=y.vy=0)}}function g(p){return p.initialize&&p.initialize(e,f),p}return h(),t={tick:d,restart:function(){return s.restart(c),t},stop:function(){return s.stop(),t},nodes:function(p){return arguments.length?(e=p,h(),o.forEach(g),t):e},alpha:function(p){return arguments.length?(n=+p,t):n},alphaMin:function(p){return arguments.length?(r=+p,t):r},alphaDecay:function(p){return arguments.length?(i=+p,t):+i},alphaTarget:function(p){return arguments.length?(a=+p,t):a},velocityDecay:function(p){return arguments.length?(u=1-p,t):1-u},randomSource:function(p){return arguments.length?(f=p,o.forEach(g),t):f},force:function(p,m){return arguments.length>1?(m==null?o.delete(p):o.set(p,g(m)),t):o.get(p)},find:function(p,m,y){var v=0,x=e.length,b,E,w,A,D;for(y==null?y=1/0:y*=y,v=0;v<x;++v)A=e[v],b=p-A.x,E=m-A.y,w=b*b+E*E,w<y&&(D=A,y=w);return D},on:function(p,m){return arguments.length>1?(l.on(p,m),t):l.on(p)}}}function pF(){var e,t,n,r,i=ft(-30),a,u=1,o=1/0,s=.81;function l(h){var g,p=e.length,m=ch(e,fF,cF).visitAfter(c);for(r=h,g=0;g<p;++g)t=e[g],m.visit(d)}function f(){if(e){var h,g=e.length,p;for(a=new Array(g),h=0;h<g;++h)p=e[h],a[p.index]=+i(p,h,e)}}function c(h){var g=0,p,m,y=0,v,x,b;if(h.length){for(v=x=b=0;b<4;++b)(p=h[b])&&(m=Math.abs(p.value))&&(g+=p.value,y+=m,v+=m*p.x,x+=m*p.y);h.x=v/y,h.y=x/y}else{p=h,p.x=p.data.x,p.y=p.data.y;do g+=a[p.data.index];while(p=p.next)}h.value=g}function d(h,g,p,m){if(!h.value)return!0;var y=h.x-t.x,v=h.y-t.y,x=m-g,b=y*y+v*v;if(x*x/s<b)return b<o&&(y===0&&(y=ur(n),b+=y*y),v===0&&(v=ur(n),b+=v*v),b<u&&(b=Math.sqrt(u*b)),t.vx+=y*h.value*r/b,t.vy+=v*h.value*r/b),!0;if(h.length||b>=o)return;(h.data!==t||h.next)&&(y===0&&(y=ur(n),b+=y*y),v===0&&(v=ur(n),b+=v*v),b<u&&(b=Math.sqrt(u*b)));do h.data!==t&&(x=a[h.data.index]*r/b,t.vx+=y*x,t.vy+=v*x);while(h=h.next)}return l.initialize=function(h,g){e=h,n=g,f()},l.strength=function(h){return arguments.length?(i=typeof h=="function"?h:ft(+h),f(),l):i},l.distanceMin=function(h){return arguments.length?(u=h*h,l):Math.sqrt(u)},l.distanceMax=function(h){return arguments.length?(o=h*h,l):Math.sqrt(o)},l.theta=function(h){return arguments.length?(s=h*h,l):Math.sqrt(s)},l}function mF(e){var t=ft(.1),n,r,i;typeof e!="function"&&(e=ft(e==null?0:+e));function a(o){for(var s=0,l=n.length,f;s<l;++s)f=n[s],f.vx+=(i[s]-f.x)*r[s]*o}function u(){if(n){var o,s=n.length;for(r=new Array(s),i=new Array(s),o=0;o<s;++o)r[o]=isNaN(i[o]=+e(n[o],o,n))?0:+t(n[o],o,n)}}return a.initialize=function(o){n=o,u()},a.strength=function(o){return arguments.length?(t=typeof o=="function"?o:ft(+o),u(),a):t},a.x=function(o){return arguments.length?(e=typeof o=="function"?o:ft(+o),u(),a):e},a}function yF(e){var t=ft(.1),n,r,i;typeof e!="function"&&(e=ft(e==null?0:+e));function a(o){for(var s=0,l=n.length,f;s<l;++s)f=n[s],f.vy+=(i[s]-f.y)*r[s]*o}function u(){if(n){var o,s=n.length;for(r=new Array(s),i=new Array(s),o=0;o<s;++o)r[o]=isNaN(i[o]=+e(n[o],o,n))?0:+t(n[o],o,n)}}return a.initialize=function(o){n=o,u()},a.strength=function(o){return arguments.length?(t=typeof o=="function"?o:ft(+o),u(),a):t},a.y=function(o){return arguments.length?(e=typeof o=="function"?o:ft(+o),u(),a):e},a}var X=1e-6,us=1e-12,ne=Math.PI,Ae=ne/2,os=ne/4,ht=ne*2,De=180/ne,te=ne/180,ie=Math.abs,Xi=Math.atan,Xt=Math.atan2,V=Math.cos,so=Math.ceil,W2=Math.exp,Cc=Math.hypot,ss=Math.log,Mf=Math.pow,H=Math.sin,Gt=Math.sign||function(e){return e>0?1:e<0?-1:0},gt=Math.sqrt,hh=Math.tan;function Y2(e){return e>1?0:e<-1?ne:Math.acos(e)}function Dt(e){return e>1?Ae:e<-1?-Ae:Math.asin(e)}function Le(){}function ls(e,t){e&&Ap.hasOwnProperty(e.type)&&Ap[e.type](e,t)}var bp={Feature:function(e,t){ls(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,i=n.length;++r<i;)ls(n[r].geometry,t)}},Ap={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Fc(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Fc(n[r],t,0)},Polygon:function(e,t){Ep(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Ep(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,i=n.length;++r<i;)ls(n[r],t)}};function Fc(e,t,n){var r=-1,i=e.length-n,a;for(t.lineStart();++r<i;)a=e[r],t.point(a[0],a[1],a[2]);t.lineEnd()}function Ep(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Fc(e[n],t,1);t.polygonEnd()}function In(e,t){e&&bp.hasOwnProperty(e.type)?bp[e.type](e,t):ls(e,t)}var fs=new ct,cs=new ct,H2,X2,kc,Mc,Sc,wn={point:Le,lineStart:Le,lineEnd:Le,polygonStart:function(){fs=new ct,wn.lineStart=vF,wn.lineEnd=xF},polygonEnd:function(){var e=+fs;cs.add(e<0?ht+e:e),this.lineStart=this.lineEnd=this.point=Le},sphere:function(){cs.add(ht)}};function vF(){wn.point=bF}function xF(){V2(H2,X2)}function bF(e,t){wn.point=V2,H2=e,X2=t,e*=te,t*=te,kc=e,Mc=V(t=t/2+os),Sc=H(t)}function V2(e,t){e*=te,t*=te,t=t/2+os;var n=e-kc,r=n>=0?1:-1,i=r*n,a=V(t),u=H(t),o=Sc*u,s=Mc*a+o*V(i),l=o*r*H(i);fs.add(Xt(l,s)),kc=e,Mc=a,Sc=u}function K2(e){return cs=new ct,In(e,wn),cs*2}function ds(e){return[Xt(e[1],e[0]),Dt(e[2])]}function Xr(e){var t=e[0],n=e[1],r=V(n);return[r*V(t),r*H(t),H(n)]}function lo(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function Ri(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function Sf(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function fo(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function hs(e){var t=gt(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}var ve,vt,xe,Bt,_r,J2,Q2,Si,ja,er,Wn,Nn={point:$c,lineStart:wp,lineEnd:Dp,polygonStart:function(){Nn.point=ev,Nn.lineStart=AF,Nn.lineEnd=EF,ja=new ct,wn.polygonStart()},polygonEnd:function(){wn.polygonEnd(),Nn.point=$c,Nn.lineStart=wp,Nn.lineEnd=Dp,fs<0?(ve=-(xe=180),vt=-(Bt=90)):ja>X?Bt=90:ja<-X&&(vt=-90),Wn[0]=ve,Wn[1]=xe},sphere:function(){ve=-(xe=180),vt=-(Bt=90)}};function $c(e,t){er.push(Wn=[ve=e,xe=e]),t<vt&&(vt=t),t>Bt&&(Bt=t)}function Z2(e,t){var n=Xr([e*te,t*te]);if(Si){var r=Ri(Si,n),i=[r[1],-r[0],0],a=Ri(i,r);hs(a),a=ds(a);var u=e-_r,o=u>0?1:-1,s=a[0]*De*o,l,f=ie(u)>180;f^(o*_r<s&&s<o*e)?(l=a[1]*De,l>Bt&&(Bt=l)):(s=(s+360)%360-180,f^(o*_r<s&&s<o*e)?(l=-a[1]*De,l<vt&&(vt=l)):(t<vt&&(vt=t),t>Bt&&(Bt=t))),f?e<_r?$t(ve,e)>$t(ve,xe)&&(xe=e):$t(e,xe)>$t(ve,xe)&&(ve=e):xe>=ve?(e<ve&&(ve=e),e>xe&&(xe=e)):e>_r?$t(ve,e)>$t(ve,xe)&&(xe=e):$t(e,xe)>$t(ve,xe)&&(ve=e)}else er.push(Wn=[ve=e,xe=e]);t<vt&&(vt=t),t>Bt&&(Bt=t),Si=n,_r=e}function wp(){Nn.point=Z2}function Dp(){Wn[0]=ve,Wn[1]=xe,Nn.point=$c,Si=null}function ev(e,t){if(Si){var n=e-_r;ja.add(ie(n)>180?n+(n>0?360:-360):n)}else J2=e,Q2=t;wn.point(e,t),Z2(e,t)}function AF(){wn.lineStart()}function EF(){ev(J2,Q2),wn.lineEnd(),ie(ja)>X&&(ve=-(xe=180)),Wn[0]=ve,Wn[1]=xe,Si=null}function $t(e,t){return(t-=e)<0?t+360:t}function wF(e,t){return e[0]-t[0]}function Cp(e,t){return e[0]<=e[1]?e[0]<=t&&t<=e[1]:t<e[0]||e[1]<t}function tv(e){var t,n,r,i,a,u,o;if(Bt=xe=-(ve=vt=1/0),er=[],In(e,Nn),n=er.length){for(er.sort(wF),t=1,r=er[0],a=[r];t<n;++t)i=er[t],Cp(r,i[0])||Cp(r,i[1])?($t(r[0],i[1])>$t(r[0],r[1])&&(r[1]=i[1]),$t(i[0],r[1])>$t(r[0],r[1])&&(r[0]=i[0])):a.push(r=i);for(u=-1/0,n=a.length-1,t=0,r=a[n];t<=n;r=i,++t)i=a[t],(o=$t(r[1],i[0]))>u&&(u=o,ve=i[0],xe=r[1])}return er=Wn=null,ve===1/0||vt===1/0?[[NaN,NaN],[NaN,NaN]]:[[ve,vt],[xe,Bt]]}var wa,gs,ps,ms,ys,vs,xs,bs,Bc,_c,Rc,nv,rv,ut,ot,st,an={sphere:Le,point:gh,lineStart:Fp,lineEnd:kp,polygonStart:function(){an.lineStart=FF,an.lineEnd=kF},polygonEnd:function(){an.lineStart=Fp,an.lineEnd=kp}};function gh(e,t){e*=te,t*=te;var n=V(t);ku(n*V(e),n*H(e),H(t))}function ku(e,t,n){++wa,ps+=(e-ps)/wa,ms+=(t-ms)/wa,ys+=(n-ys)/wa}function Fp(){an.point=DF}function DF(e,t){e*=te,t*=te;var n=V(t);ut=n*V(e),ot=n*H(e),st=H(t),an.point=CF,ku(ut,ot,st)}function CF(e,t){e*=te,t*=te;var n=V(t),r=n*V(e),i=n*H(e),a=H(t),u=Xt(gt((u=ot*a-st*i)*u+(u=st*r-ut*a)*u+(u=ut*i-ot*r)*u),ut*r+ot*i+st*a);gs+=u,vs+=u*(ut+(ut=r)),xs+=u*(ot+(ot=i)),bs+=u*(st+(st=a)),ku(ut,ot,st)}function kp(){an.point=gh}function FF(){an.point=MF}function kF(){iv(nv,rv),an.point=gh}function MF(e,t){nv=e,rv=t,e*=te,t*=te,an.point=iv;var n=V(t);ut=n*V(e),ot=n*H(e),st=H(t),ku(ut,ot,st)}function iv(e,t){e*=te,t*=te;var n=V(t),r=n*V(e),i=n*H(e),a=H(t),u=ot*a-st*i,o=st*r-ut*a,s=ut*i-ot*r,l=Cc(u,o,s),f=Dt(l),c=l&&-f/l;Bc.add(c*u),_c.add(c*o),Rc.add(c*s),gs+=f,vs+=f*(ut+(ut=r)),xs+=f*(ot+(ot=i)),bs+=f*(st+(st=a)),ku(ut,ot,st)}function av(e){wa=gs=ps=ms=ys=vs=xs=bs=0,Bc=new ct,_c=new ct,Rc=new ct,In(e,an);var t=+Bc,n=+_c,r=+Rc,i=Cc(t,n,r);return i<us&&(t=vs,n=xs,r=bs,gs<X&&(t=ps,n=ms,r=ys),i=Cc(t,n,r),i<us)?[NaN,NaN]:[Xt(n,t)*De,Dt(r/i)*De]}function Oc(e,t){function n(r,i){return r=e(r,i),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,i){return r=t.invert(r,i),r&&e.invert(r[0],r[1])}),n}function Tc(e,t){return ie(e)>ne&&(e-=Math.round(e/ht)*ht),[e,t]}Tc.invert=Tc;function uv(e,t,n){return(e%=ht)?t||n?Oc(Sp(e),$p(t,n)):Sp(e):t||n?$p(t,n):Tc}function Mp(e){return function(t,n){return t+=e,ie(t)>ne&&(t-=Math.round(t/ht)*ht),[t,n]}}function Sp(e){var t=Mp(e);return t.invert=Mp(-e),t}function $p(e,t){var n=V(e),r=H(e),i=V(t),a=H(t);function u(o,s){var l=V(s),f=V(o)*l,c=H(o)*l,d=H(s),h=d*n+f*r;return[Xt(c*i-h*a,f*n-d*r),Dt(h*i+c*a)]}return u.invert=function(o,s){var l=V(s),f=V(o)*l,c=H(o)*l,d=H(s),h=d*i-c*a;return[Xt(c*i+d*a,f*n+h*r),Dt(h*n-f*r)]},u}function SF(e){e=uv(e[0]*te,e[1]*te,e.length>2?e[2]*te:0);function t(n){return n=e(n[0]*te,n[1]*te),n[0]*=De,n[1]*=De,n}return t.invert=function(n){return n=e.invert(n[0]*te,n[1]*te),n[0]*=De,n[1]*=De,n},t}function $F(e,t,n,r,i,a){if(n){var u=V(t),o=H(t),s=r*n;i==null?(i=t+r*ht,a=t-s/2):(i=Bp(u,i),a=Bp(u,a),(r>0?i<a:i>a)&&(i+=r*ht));for(var l,f=i;r>0?f>a:f<a;f-=s)l=ds([u,-o*V(f),-o*H(f)]),e.point(l[0],l[1])}}function Bp(e,t){t=Xr(t),t[0]-=e,hs(t);var n=Y2(-t[1]);return((-t[2]<0?-n:n)+ht-X)%ht}function ov(){var e=[],t;return{point:function(n,r,i){t.push([n,r,i])},lineStart:function(){e.push(t=[])},lineEnd:Le,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function Ro(e,t){return ie(e[0]-t[0])<X&&ie(e[1]-t[1])<X}function co(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function sv(e,t,n,r,i){var a=[],u=[],o,s;if(e.forEach(function(g){if(!((p=g.length-1)<=0)){var p,m=g[0],y=g[p],v;if(Ro(m,y)){if(!m[2]&&!y[2]){for(i.lineStart(),o=0;o<p;++o)i.point((m=g[o])[0],m[1]);i.lineEnd();return}y[0]+=2*X}a.push(v=new co(m,g,null,!0)),u.push(v.o=new co(m,null,v,!1)),a.push(v=new co(y,g,null,!1)),u.push(v.o=new co(y,null,v,!0))}}),!!a.length){for(u.sort(t),_p(a),_p(u),o=0,s=u.length;o<s;++o)u[o].e=n=!n;for(var l=a[0],f,c;;){for(var d=l,h=!0;d.v;)if((d=d.n)===l)return;f=d.z,i.lineStart();do{if(d.v=d.o.v=!0,d.e){if(h)for(o=0,s=f.length;o<s;++o)i.point((c=f[o])[0],c[1]);else r(d.x,d.n.x,1,i);d=d.n}else{if(h)for(f=d.p.z,o=f.length-1;o>=0;--o)i.point((c=f[o])[0],c[1]);else r(d.x,d.p.x,-1,i);d=d.p}d=d.o,f=d.z,h=!h}while(!d.v);i.lineEnd()}}}function _p(e){if(t=e.length){for(var t,n=0,r=e[0],i;++n<t;)r.n=i=e[n],i.p=r,r=i;r.n=i=e[0],i.p=r}}function $f(e){return ie(e[0])<=ne?e[0]:Gt(e[0])*((ie(e[0])+ne)%ht-ne)}function BF(e,t){var n=$f(t),r=t[1],i=H(r),a=[H(n),-V(n),0],u=0,o=0,s=new ct;i===1?r=Ae+X:i===-1&&(r=-Ae-X);for(var l=0,f=e.length;l<f;++l)if(d=(c=e[l]).length)for(var c,d,h=c[d-1],g=$f(h),p=h[1]/2+os,m=H(p),y=V(p),v=0;v<d;++v,g=b,m=w,y=A,h=x){var x=c[v],b=$f(x),E=x[1]/2+os,w=H(E),A=V(E),D=b-g,C=D>=0?1:-1,F=C*D,M=F>ne,O=m*w;if(s.add(Xt(O*C*H(F),y*A+O*V(F))),u+=M?D+C*ht:D,M^g>=n^b>=n){var L=Ri(Xr(h),Xr(x));hs(L);var N=Ri(a,L);hs(N);var k=(M^D>=0?-1:1)*Dt(N[2]);(r>k||r===k&&(L[0]||L[1]))&&(o+=M^D>=0?1:-1)}}return(u<-X||u<X&&s<-us)^o&1}function lv(e,t,n,r){return function(i){var a=t(i),u=ov(),o=t(u),s=!1,l,f,c,d={point:h,lineStart:p,lineEnd:m,polygonStart:function(){d.point=y,d.lineStart=v,d.lineEnd=x,f=[],l=[]},polygonEnd:function(){d.point=h,d.lineStart=p,d.lineEnd=m,f=P2(f);var b=BF(l,r);f.length?(s||(i.polygonStart(),s=!0),sv(f,RF,b,n,i)):b&&(s||(i.polygonStart(),s=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),s&&(i.polygonEnd(),s=!1),f=l=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function h(b,E){e(b,E)&&i.point(b,E)}function g(b,E){a.point(b,E)}function p(){d.point=g,a.lineStart()}function m(){d.point=h,a.lineEnd()}function y(b,E){c.push([b,E]),o.point(b,E)}function v(){o.lineStart(),c=[]}function x(){y(c[0][0],c[0][1]),o.lineEnd();var b=o.clean(),E=u.result(),w,A=E.length,D,C,F;if(c.pop(),l.push(c),c=null,!!A){if(b&1){if(C=E[0],(D=C.length-1)>0){for(s||(i.polygonStart(),s=!0),i.lineStart(),w=0;w<D;++w)i.point((F=C[w])[0],F[1]);i.lineEnd()}return}A>1&&b&2&&E.push(E.pop().concat(E.shift())),f.push(E.filter(_F))}}return d}}function _F(e){return e.length>1}function RF(e,t){return((e=e.x)[0]<0?e[1]-Ae-X:Ae-e[1])-((t=t.x)[0]<0?t[1]-Ae-X:Ae-t[1])}const Rp=lv(function(){return!0},OF,LF,[-ne,-Ae]);function OF(e){var t=NaN,n=NaN,r=NaN,i;return{lineStart:function(){e.lineStart(),i=1},point:function(a,u){var o=a>0?ne:-ne,s=ie(a-t);ie(s-ne)<X?(e.point(t,n=(n+u)/2>0?Ae:-Ae),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(o,n),e.point(a,n),i=0):r!==o&&s>=ne&&(ie(t-r)<X&&(t-=r*X),ie(a-o)<X&&(a-=o*X),n=TF(t,n,a,u),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(o,n),i=0),e.point(t=a,n=u),r=o},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-i}}}function TF(e,t,n,r){var i,a,u=H(e-n);return ie(u)>X?Xi((H(t)*(a=V(r))*H(n)-H(r)*(i=V(t))*H(e))/(i*a*u)):(t+r)/2}function LF(e,t,n,r){var i;if(e==null)i=n*Ae,r.point(-ne,i),r.point(0,i),r.point(ne,i),r.point(ne,0),r.point(ne,-i),r.point(0,-i),r.point(-ne,-i),r.point(-ne,0),r.point(-ne,i);else if(ie(e[0]-t[0])>X){var a=e[0]<t[0]?ne:-ne;i=n*a/2,r.point(-a,i),r.point(0,i),r.point(a,i)}else r.point(t[0],t[1])}function NF(e){var t=V(e),n=6*te,r=t>0,i=ie(t)>X;function a(f,c,d,h){$F(h,e,n,d,f,c)}function u(f,c){return V(f)*V(c)>t}function o(f){var c,d,h,g,p;return{lineStart:function(){g=h=!1,p=1},point:function(m,y){var v=[m,y],x,b=u(m,y),E=r?b?0:l(m,y):b?l(m+(m<0?ne:-ne),y):0;if(!c&&(g=h=b)&&f.lineStart(),b!==h&&(x=s(c,v),(!x||Ro(c,x)||Ro(v,x))&&(v[2]=1)),b!==h)p=0,b?(f.lineStart(),x=s(v,c),f.point(x[0],x[1])):(x=s(c,v),f.point(x[0],x[1],2),f.lineEnd()),c=x;else if(i&&c&&r^b){var w;!(E&d)&&(w=s(v,c,!0))&&(p=0,r?(f.lineStart(),f.point(w[0][0],w[0][1]),f.point(w[1][0],w[1][1]),f.lineEnd()):(f.point(w[1][0],w[1][1]),f.lineEnd(),f.lineStart(),f.point(w[0][0],w[0][1],3)))}b&&(!c||!Ro(c,v))&&f.point(v[0],v[1]),c=v,h=b,d=E},lineEnd:function(){h&&f.lineEnd(),c=null},clean:function(){return p|(g&&h)<<1}}}function s(f,c,d){var h=Xr(f),g=Xr(c),p=[1,0,0],m=Ri(h,g),y=lo(m,m),v=m[0],x=y-v*v;if(!x)return!d&&f;var b=t*y/x,E=-t*v/x,w=Ri(p,m),A=fo(p,b),D=fo(m,E);Sf(A,D);var C=w,F=lo(A,C),M=lo(C,C),O=F*F-M*(lo(A,A)-1);if(!(O<0)){var L=gt(O),N=fo(C,(-F-L)/M);if(Sf(N,A),N=ds(N),!d)return N;var k=f[0],R=c[0],j=f[1],ee=c[1],Q;R<k&&(Q=k,k=R,R=Q);var Ce=R-k,Qt=ie(Ce-ne)<X,Mr=Qt||Ce<X;if(!Qt&&ee<j&&(Q=j,j=ee,ee=Q),Mr?Qt?j+ee>0^N[1]<(ie(N[0]-k)<X?j:ee):j<=N[1]&&N[1]<=ee:Ce>ne^(k<=N[0]&&N[0]<=R)){var _n=fo(C,(-F+L)/M);return Sf(_n,A),[N,ds(_n)]}}}function l(f,c){var d=r?e:ne-e,h=0;return f<-d?h|=1:f>d&&(h|=2),c<-d?h|=4:c>d&&(h|=8),h}return lv(u,o,a,r?[0,-e]:[-ne,e-ne])}function zF(e,t,n,r,i,a){var u=e[0],o=e[1],s=t[0],l=t[1],f=0,c=1,d=s-u,h=l-o,g;if(g=n-u,!(!d&&g>0)){if(g/=d,d<0){if(g<f)return;g<c&&(c=g)}else if(d>0){if(g>c)return;g>f&&(f=g)}if(g=i-u,!(!d&&g<0)){if(g/=d,d<0){if(g>c)return;g>f&&(f=g)}else if(d>0){if(g<f)return;g<c&&(c=g)}if(g=r-o,!(!h&&g>0)){if(g/=h,h<0){if(g<f)return;g<c&&(c=g)}else if(h>0){if(g>c)return;g>f&&(f=g)}if(g=a-o,!(!h&&g<0)){if(g/=h,h<0){if(g>c)return;g>f&&(f=g)}else if(h>0){if(g<f)return;g<c&&(c=g)}return f>0&&(e[0]=u+f*d,e[1]=o+f*h),c<1&&(t[0]=u+c*d,t[1]=o+c*h),!0}}}}}var Da=1e9,ho=-Da;function fv(e,t,n,r){function i(l,f){return e<=l&&l<=n&&t<=f&&f<=r}function a(l,f,c,d){var h=0,g=0;if(l==null||(h=u(l,c))!==(g=u(f,c))||s(l,f)<0^c>0)do d.point(h===0||h===3?e:n,h>1?r:t);while((h=(h+c+4)%4)!==g);else d.point(f[0],f[1])}function u(l,f){return ie(l[0]-e)<X?f>0?0:3:ie(l[0]-n)<X?f>0?2:1:ie(l[1]-t)<X?f>0?1:0:f>0?3:2}function o(l,f){return s(l.x,f.x)}function s(l,f){var c=u(l,1),d=u(f,1);return c!==d?c-d:c===0?f[1]-l[1]:c===1?l[0]-f[0]:c===2?l[1]-f[1]:f[0]-l[0]}return function(l){var f=l,c=ov(),d,h,g,p,m,y,v,x,b,E,w,A={point:D,lineStart:O,lineEnd:L,polygonStart:F,polygonEnd:M};function D(k,R){i(k,R)&&f.point(k,R)}function C(){for(var k=0,R=0,j=h.length;R<j;++R)for(var ee=h[R],Q=1,Ce=ee.length,Qt=ee[0],Mr,_n,ao=Qt[0],di=Qt[1];Q<Ce;++Q)Mr=ao,_n=di,Qt=ee[Q],ao=Qt[0],di=Qt[1],_n<=r?di>r&&(ao-Mr)*(r-_n)>(di-_n)*(e-Mr)&&++k:di<=r&&(ao-Mr)*(r-_n)<(di-_n)*(e-Mr)&&--k;return k}function F(){f=c,d=[],h=[],w=!0}function M(){var k=C(),R=w&&k,j=(d=P2(d)).length;(R||j)&&(l.polygonStart(),R&&(l.lineStart(),a(null,null,1,l),l.lineEnd()),j&&sv(d,o,k,a,l),l.polygonEnd()),f=l,d=h=g=null}function O(){A.point=N,h&&h.push(g=[]),E=!0,b=!1,v=x=NaN}function L(){d&&(N(p,m),y&&b&&c.rejoin(),d.push(c.result())),A.point=D,b&&f.lineEnd()}function N(k,R){var j=i(k,R);if(h&&g.push([k,R]),E)p=k,m=R,y=j,E=!1,j&&(f.lineStart(),f.point(k,R));else if(j&&b)f.point(k,R);else{var ee=[v=Math.max(ho,Math.min(Da,v)),x=Math.max(ho,Math.min(Da,x))],Q=[k=Math.max(ho,Math.min(Da,k)),R=Math.max(ho,Math.min(Da,R))];zF(ee,Q,e,t,n,r)?(b||(f.lineStart(),f.point(ee[0],ee[1])),f.point(Q[0],Q[1]),j||f.lineEnd(),w=!1):j&&(f.lineStart(),f.point(k,R),w=!1)}v=k,x=R,b=j}return A}}function Op(e,t,n){var r=Et(e,t-X,n).concat(t);return function(i){return r.map(function(a){return[i,a]})}}function Tp(e,t,n){var r=Et(e,t-X,n).concat(t);return function(i){return r.map(function(a){return[a,i]})}}function PF(){var e,t,n,r,i,a,u,o,s=10,l=s,f=90,c=360,d,h,g,p,m=2.5;function y(){return{type:"MultiLineString",coordinates:v()}}function v(){return Et(so(r/f)*f,n,f).map(g).concat(Et(so(o/c)*c,u,c).map(p)).concat(Et(so(t/s)*s,e,s).filter(function(x){return ie(x%f)>X}).map(d)).concat(Et(so(a/l)*l,i,l).filter(function(x){return ie(x%c)>X}).map(h))}return y.lines=function(){return v().map(function(x){return{type:"LineString",coordinates:x}})},y.outline=function(){return{type:"Polygon",coordinates:[g(r).concat(p(u).slice(1),g(n).reverse().slice(1),p(o).reverse().slice(1))]}},y.extent=function(x){return arguments.length?y.extentMajor(x).extentMinor(x):y.extentMinor()},y.extentMajor=function(x){return arguments.length?(r=+x[0][0],n=+x[1][0],o=+x[0][1],u=+x[1][1],r>n&&(x=r,r=n,n=x),o>u&&(x=o,o=u,u=x),y.precision(m)):[[r,o],[n,u]]},y.extentMinor=function(x){return arguments.length?(t=+x[0][0],e=+x[1][0],a=+x[0][1],i=+x[1][1],t>e&&(x=t,t=e,e=x),a>i&&(x=a,a=i,i=x),y.precision(m)):[[t,a],[e,i]]},y.step=function(x){return arguments.length?y.stepMajor(x).stepMinor(x):y.stepMinor()},y.stepMajor=function(x){return arguments.length?(f=+x[0],c=+x[1],y):[f,c]},y.stepMinor=function(x){return arguments.length?(s=+x[0],l=+x[1],y):[s,l]},y.precision=function(x){return arguments.length?(m=+x,d=Op(a,i,90),h=Tp(t,e,m),g=Op(o,u,90),p=Tp(r,n,m),y):m},y.extentMajor([[-180,-90+X],[180,90-X]]).extentMinor([[-180,-80-X],[180,80+X]])}const iu=e=>e;var Bf=new ct,Lc=new ct,cv,dv,Nc,zc,Un={point:Le,lineStart:Le,lineEnd:Le,polygonStart:function(){Un.lineStart=IF,Un.lineEnd=qF},polygonEnd:function(){Un.lineStart=Un.lineEnd=Un.point=Le,Bf.add(ie(Lc)),Lc=new ct},result:function(){var e=Bf/2;return Bf=new ct,e}};function IF(){Un.point=UF}function UF(e,t){Un.point=hv,cv=Nc=e,dv=zc=t}function hv(e,t){Lc.add(zc*e-Nc*t),Nc=e,zc=t}function qF(){hv(cv,dv)}var Oi=1/0,As=Oi,au=-Oi,Es=au,ws={point:jF,lineStart:Le,lineEnd:Le,polygonStart:Le,polygonEnd:Le,result:function(){var e=[[Oi,As],[au,Es]];return au=Es=-(As=Oi=1/0),e}};function jF(e,t){e<Oi&&(Oi=e),e>au&&(au=e),t<As&&(As=t),t>Es&&(Es=t)}var Pc=0,Ic=0,Ca=0,Ds=0,Cs=0,Di=0,Uc=0,qc=0,Fa=0,gv,pv,xn,bn,qt={point:Vr,lineStart:Lp,lineEnd:Np,polygonStart:function(){qt.lineStart=YF,qt.lineEnd=HF},polygonEnd:function(){qt.point=Vr,qt.lineStart=Lp,qt.lineEnd=Np},result:function(){var e=Fa?[Uc/Fa,qc/Fa]:Di?[Ds/Di,Cs/Di]:Ca?[Pc/Ca,Ic/Ca]:[NaN,NaN];return Pc=Ic=Ca=Ds=Cs=Di=Uc=qc=Fa=0,e}};function Vr(e,t){Pc+=e,Ic+=t,++Ca}function Lp(){qt.point=GF}function GF(e,t){qt.point=WF,Vr(xn=e,bn=t)}function WF(e,t){var n=e-xn,r=t-bn,i=gt(n*n+r*r);Ds+=i*(xn+e)/2,Cs+=i*(bn+t)/2,Di+=i,Vr(xn=e,bn=t)}function Np(){qt.point=Vr}function YF(){qt.point=XF}function HF(){mv(gv,pv)}function XF(e,t){qt.point=mv,Vr(gv=xn=e,pv=bn=t)}function mv(e,t){var n=e-xn,r=t-bn,i=gt(n*n+r*r);Ds+=i*(xn+e)/2,Cs+=i*(bn+t)/2,Di+=i,i=bn*e-xn*t,Uc+=i*(xn+e),qc+=i*(bn+t),Fa+=i*3,Vr(xn=e,bn=t)}function yv(e){this._context=e}yv.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,ht);break}}},result:Le};var jc=new ct,_f,vv,xv,ka,Ma,uu={point:Le,lineStart:function(){uu.point=VF},lineEnd:function(){_f&&bv(vv,xv),uu.point=Le},polygonStart:function(){_f=!0},polygonEnd:function(){_f=null},result:function(){var e=+jc;return jc=new ct,e}};function VF(e,t){uu.point=bv,vv=ka=e,xv=Ma=t}function bv(e,t){ka-=e,Ma-=t,jc.add(gt(ka*ka+Ma*Ma)),ka=e,Ma=t}let zp,Fs,Pp,Ip;class Up{constructor(t){this._append=t==null?Av:KF(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==Pp||this._append!==Fs){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,Pp=r,Fs=this._append,Ip=this._,this._=i}this._+=Ip;break}}}result(){const t=this._;return this._="",t.length?t:null}}function Av(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function KF(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return Av;if(t!==zp){const n=10**t;zp=t,Fs=function(i){let a=1;this._+=i[0];for(const u=i.length;a<u;++a)this._+=Math.round(arguments[a]*n)/n+i[a]}}return Fs}function Ev(e,t){let n=3,r=4.5,i,a;function u(o){return o&&(typeof r=="function"&&a.pointRadius(+r.apply(this,arguments)),In(o,i(a))),a.result()}return u.area=function(o){return In(o,i(Un)),Un.result()},u.measure=function(o){return In(o,i(uu)),uu.result()},u.bounds=function(o){return In(o,i(ws)),ws.result()},u.centroid=function(o){return In(o,i(qt)),qt.result()},u.projection=function(o){return arguments.length?(i=o==null?(e=null,iu):(e=o).stream,u):e},u.context=function(o){return arguments.length?(a=o==null?(t=null,new Up(n)):new yv(t=o),typeof r!="function"&&a.pointRadius(r),u):t},u.pointRadius=function(o){return arguments.length?(r=typeof o=="function"?o:(a.pointRadius(+o),+o),u):r},u.digits=function(o){if(!arguments.length)return n;if(o==null)n=null;else{const s=Math.floor(o);if(!(s>=0))throw new RangeError(`invalid digits: ${o}`);n=s}return t===null&&(a=new Up(n)),u},u.projection(e).digits(n).context(t)}function ol(e){return function(t){var n=new Gc;for(var r in e)n[r]=e[r];return n.stream=t,n}}function Gc(){}Gc.prototype={constructor:Gc,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function ph(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),In(n,e.stream(ws)),t(ws.result()),r!=null&&e.clipExtent(r),e}function sl(e,t,n){return ph(e,function(r){var i=t[1][0]-t[0][0],a=t[1][1]-t[0][1],u=Math.min(i/(r[1][0]-r[0][0]),a/(r[1][1]-r[0][1])),o=+t[0][0]+(i-u*(r[1][0]+r[0][0]))/2,s=+t[0][1]+(a-u*(r[1][1]+r[0][1]))/2;e.scale(150*u).translate([o,s])},n)}function mh(e,t,n){return sl(e,[[0,0],t],n)}function yh(e,t,n){return ph(e,function(r){var i=+t,a=i/(r[1][0]-r[0][0]),u=(i-a*(r[1][0]+r[0][0]))/2,o=-a*r[0][1];e.scale(150*a).translate([u,o])},n)}function vh(e,t,n){return ph(e,function(r){var i=+t,a=i/(r[1][1]-r[0][1]),u=-a*r[0][0],o=(i-a*(r[1][1]+r[0][1]))/2;e.scale(150*a).translate([u,o])},n)}var qp=16,JF=V(30*te);function jp(e,t){return+t?ZF(e,t):QF(e)}function QF(e){return ol({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function ZF(e,t){function n(r,i,a,u,o,s,l,f,c,d,h,g,p,m){var y=l-r,v=f-i,x=y*y+v*v;if(x>4*t&&p--){var b=u+d,E=o+h,w=s+g,A=gt(b*b+E*E+w*w),D=Dt(w/=A),C=ie(ie(w)-1)<X||ie(a-c)<X?(a+c)/2:Xt(E,b),F=e(C,D),M=F[0],O=F[1],L=M-r,N=O-i,k=v*L-y*N;(k*k/x>t||ie((y*L+v*N)/x-.5)>.3||u*d+o*h+s*g<JF)&&(n(r,i,a,u,o,s,M,O,C,b/=A,E/=A,w,p,m),m.point(M,O),n(M,O,C,b,E,w,l,f,c,d,h,g,p,m))}}return function(r){var i,a,u,o,s,l,f,c,d,h,g,p,m={point:y,lineStart:v,lineEnd:b,polygonStart:function(){r.polygonStart(),m.lineStart=E},polygonEnd:function(){r.polygonEnd(),m.lineStart=v}};function y(D,C){D=e(D,C),r.point(D[0],D[1])}function v(){c=NaN,m.point=x,r.lineStart()}function x(D,C){var F=Xr([D,C]),M=e(D,C);n(c,d,f,h,g,p,c=M[0],d=M[1],f=D,h=F[0],g=F[1],p=F[2],qp,r),r.point(c,d)}function b(){m.point=y,r.lineEnd()}function E(){v(),m.point=w,m.lineEnd=A}function w(D,C){x(i=D,C),a=c,u=d,o=h,s=g,l=p,m.point=x}function A(){n(c,d,f,h,g,p,a,u,i,o,s,l,qp,r),m.lineEnd=b,b()}return m}}var e5=ol({point:function(e,t){this.stream.point(e*te,t*te)}});function t5(e){return ol({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function n5(e,t,n,r,i){function a(u,o){return u*=r,o*=i,[t+e*u,n-e*o]}return a.invert=function(u,o){return[(u-t)/e*r,(n-o)/e*i]},a}function Gp(e,t,n,r,i,a){if(!a)return n5(e,t,n,r,i);var u=V(a),o=H(a),s=u*e,l=o*e,f=u/e,c=o/e,d=(o*n-u*t)/e,h=(o*t+u*n)/e;function g(p,m){return p*=r,m*=i,[s*p-l*m+t,n-l*p-s*m]}return g.invert=function(p,m){return[r*(f*p-c*m+d),i*(h-c*p-f*m)]},g}function Fn(e){return wv(function(){return e})()}function wv(e){var t,n=150,r=480,i=250,a=0,u=0,o=0,s=0,l=0,f,c=0,d=1,h=1,g=null,p=Rp,m=null,y,v,x,b=iu,E=.5,w,A,D,C,F;function M(k){return D(k[0]*te,k[1]*te)}function O(k){return k=D.invert(k[0],k[1]),k&&[k[0]*De,k[1]*De]}M.stream=function(k){return C&&F===k?C:C=e5(t5(f)(p(w(b(F=k)))))},M.preclip=function(k){return arguments.length?(p=k,g=void 0,N()):p},M.postclip=function(k){return arguments.length?(b=k,m=y=v=x=null,N()):b},M.clipAngle=function(k){return arguments.length?(p=+k?NF(g=k*te):(g=null,Rp),N()):g*De},M.clipExtent=function(k){return arguments.length?(b=k==null?(m=y=v=x=null,iu):fv(m=+k[0][0],y=+k[0][1],v=+k[1][0],x=+k[1][1]),N()):m==null?null:[[m,y],[v,x]]},M.scale=function(k){return arguments.length?(n=+k,L()):n},M.translate=function(k){return arguments.length?(r=+k[0],i=+k[1],L()):[r,i]},M.center=function(k){return arguments.length?(a=k[0]%360*te,u=k[1]%360*te,L()):[a*De,u*De]},M.rotate=function(k){return arguments.length?(o=k[0]%360*te,s=k[1]%360*te,l=k.length>2?k[2]%360*te:0,L()):[o*De,s*De,l*De]},M.angle=function(k){return arguments.length?(c=k%360*te,L()):c*De},M.reflectX=function(k){return arguments.length?(d=k?-1:1,L()):d<0},M.reflectY=function(k){return arguments.length?(h=k?-1:1,L()):h<0},M.precision=function(k){return arguments.length?(w=jp(A,E=k*k),N()):gt(E)},M.fitExtent=function(k,R){return sl(M,k,R)},M.fitSize=function(k,R){return mh(M,k,R)},M.fitWidth=function(k,R){return yh(M,k,R)},M.fitHeight=function(k,R){return vh(M,k,R)};function L(){var k=Gp(n,0,0,d,h,c).apply(null,t(a,u)),R=Gp(n,r-k[0],i-k[1],d,h,c);return f=uv(o,s,l),A=Oc(t,R),D=Oc(f,A),w=jp(A,E),N()}function N(){return C=F=null,M}return function(){return t=e.apply(this,arguments),M.invert=t.invert&&O,L()}}function xh(e){var t=0,n=ne/3,r=wv(e),i=r(t,n);return i.parallels=function(a){return arguments.length?r(t=a[0]*te,n=a[1]*te):[t*De,n*De]},i}function r5(e){var t=V(e);function n(r,i){return[r*t,H(i)/t]}return n.invert=function(r,i){return[r/t,Dt(i*t)]},n}function i5(e,t){var n=H(e),r=(n+H(t))/2;if(ie(r)<X)return r5(e);var i=1+n*(2*r-n),a=gt(i)/r;function u(o,s){var l=gt(i-2*r*H(s))/r;return[l*H(o*=r),a-l*V(o)]}return u.invert=function(o,s){var l=a-s,f=Xt(o,ie(l))*Gt(l);return l*r<0&&(f-=ne*Gt(o)*Gt(l)),[f/r,Dt((i-(o*o+l*l)*r*r)/(2*r))]},u}function ks(){return xh(i5).scale(155.424).center([0,33.6442])}function Dv(){return ks().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function a5(e){var t=e.length;return{point:function(n,r){for(var i=-1;++i<t;)e[i].point(n,r)},sphere:function(){for(var n=-1;++n<t;)e[n].sphere()},lineStart:function(){for(var n=-1;++n<t;)e[n].lineStart()},lineEnd:function(){for(var n=-1;++n<t;)e[n].lineEnd()},polygonStart:function(){for(var n=-1;++n<t;)e[n].polygonStart()},polygonEnd:function(){for(var n=-1;++n<t;)e[n].polygonEnd()}}}function u5(){var e,t,n=Dv(),r,i=ks().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a,u=ks().rotate([157,0]).center([-3,19.9]).parallels([8,18]),o,s,l={point:function(d,h){s=[d,h]}};function f(d){var h=d[0],g=d[1];return s=null,r.point(h,g),s||(a.point(h,g),s)||(o.point(h,g),s)}f.invert=function(d){var h=n.scale(),g=n.translate(),p=(d[0]-g[0])/h,m=(d[1]-g[1])/h;return(m>=.12&&m<.234&&p>=-.425&&p<-.214?i:m>=.166&&m<.234&&p>=-.214&&p<-.115?u:n).invert(d)},f.stream=function(d){return e&&t===d?e:e=a5([n.stream(t=d),i.stream(d),u.stream(d)])},f.precision=function(d){return arguments.length?(n.precision(d),i.precision(d),u.precision(d),c()):n.precision()},f.scale=function(d){return arguments.length?(n.scale(d),i.scale(d*.35),u.scale(d),f.translate(n.translate())):n.scale()},f.translate=function(d){if(!arguments.length)return n.translate();var h=n.scale(),g=+d[0],p=+d[1];return r=n.translate(d).clipExtent([[g-.455*h,p-.238*h],[g+.455*h,p+.238*h]]).stream(l),a=i.translate([g-.307*h,p+.201*h]).clipExtent([[g-.425*h+X,p+.12*h+X],[g-.214*h-X,p+.234*h-X]]).stream(l),o=u.translate([g-.205*h,p+.212*h]).clipExtent([[g-.214*h+X,p+.166*h+X],[g-.115*h-X,p+.234*h-X]]).stream(l),c()},f.fitExtent=function(d,h){return sl(f,d,h)},f.fitSize=function(d,h){return mh(f,d,h)},f.fitWidth=function(d,h){return yh(f,d,h)},f.fitHeight=function(d,h){return vh(f,d,h)};function c(){return e=t=null,f}return f.scale(1070)}function Cv(e){return function(t,n){var r=V(t),i=V(n),a=e(r*i);return a===1/0?[2,0]:[a*i*H(t),a*H(n)]}}function Mu(e){return function(t,n){var r=gt(t*t+n*n),i=e(r),a=H(i),u=V(i);return[Xt(t*a,r*u),Dt(r&&n*a/r)]}}var Fv=Cv(function(e){return gt(2/(1+e))});Fv.invert=Mu(function(e){return 2*Dt(e/2)});function o5(){return Fn(Fv).scale(124.75).clipAngle(180-.001)}var kv=Cv(function(e){return(e=Y2(e))&&e/H(e)});kv.invert=Mu(function(e){return e});function s5(){return Fn(kv).scale(79.4188).clipAngle(180-.001)}function ll(e,t){return[e,ss(hh((Ae+t)/2))]}ll.invert=function(e,t){return[e,2*Xi(W2(t))-Ae]};function l5(){return Mv(ll).scale(961/ht)}function Mv(e){var t=Fn(e),n=t.center,r=t.scale,i=t.translate,a=t.clipExtent,u=null,o,s,l;t.scale=function(c){return arguments.length?(r(c),f()):r()},t.translate=function(c){return arguments.length?(i(c),f()):i()},t.center=function(c){return arguments.length?(n(c),f()):n()},t.clipExtent=function(c){return arguments.length?(c==null?u=o=s=l=null:(u=+c[0][0],o=+c[0][1],s=+c[1][0],l=+c[1][1]),f()):u==null?null:[[u,o],[s,l]]};function f(){var c=ne*r(),d=t(SF(t.rotate()).invert([0,0]));return a(u==null?[[d[0]-c,d[1]-c],[d[0]+c,d[1]+c]]:e===ll?[[Math.max(d[0]-c,u),o],[Math.min(d[0]+c,s),l]]:[[u,Math.max(d[1]-c,o)],[s,Math.min(d[1]+c,l)]])}return f()}function go(e){return hh((Ae+e)/2)}function f5(e,t){var n=V(e),r=e===t?H(e):ss(n/V(t))/ss(go(t)/go(e)),i=n*Mf(go(e),r)/r;if(!r)return ll;function a(u,o){i>0?o<-Ae+X&&(o=-Ae+X):o>Ae-X&&(o=Ae-X);var s=i/Mf(go(o),r);return[s*H(r*u),i-s*V(r*u)]}return a.invert=function(u,o){var s=i-o,l=Gt(r)*gt(u*u+s*s),f=Xt(u,ie(s))*Gt(s);return s*r<0&&(f-=ne*Gt(u)*Gt(s)),[f/r,2*Xi(Mf(i/l,1/r))-Ae]},a}function c5(){return xh(f5).scale(109.5).parallels([30,30])}function Ms(e,t){return[e,t]}Ms.invert=Ms;function d5(){return Fn(Ms).scale(152.63)}function h5(e,t){var n=V(e),r=e===t?H(e):(n-V(t))/(t-e),i=n/r+e;if(ie(r)<X)return Ms;function a(u,o){var s=i-o,l=r*u;return[s*H(l),i-s*V(l)]}return a.invert=function(u,o){var s=i-o,l=Xt(u,ie(s))*Gt(s);return s*r<0&&(l-=ne*Gt(u)*Gt(s)),[l/r,i-Gt(r)*gt(u*u+s*s)]},a}function g5(){return xh(h5).scale(131.154).center([0,13.9389])}var Ga=1.340264,Wa=-.081106,Ya=893e-6,Ha=.003796,Ss=gt(3)/2,p5=12;function Sv(e,t){var n=Dt(Ss*H(t)),r=n*n,i=r*r*r;return[e*V(n)/(Ss*(Ga+3*Wa*r+i*(7*Ya+9*Ha*r))),n*(Ga+Wa*r+i*(Ya+Ha*r))]}Sv.invert=function(e,t){for(var n=t,r=n*n,i=r*r*r,a=0,u,o,s;a<p5&&(o=n*(Ga+Wa*r+i*(Ya+Ha*r))-t,s=Ga+3*Wa*r+i*(7*Ya+9*Ha*r),n-=u=o/s,r=n*n,i=r*r*r,!(ie(u)<us));++a);return[Ss*e*(Ga+3*Wa*r+i*(7*Ya+9*Ha*r))/V(n),Dt(H(n)/Ss)]};function m5(){return Fn(Sv).scale(177.158)}function $v(e,t){var n=V(t),r=V(e)*n;return[n*H(e)/r,H(t)/r]}$v.invert=Mu(Xi);function y5(){return Fn($v).scale(144.049).clipAngle(60)}function v5(){var e=1,t=0,n=0,r=1,i=1,a=0,u,o,s=null,l,f,c,d=1,h=1,g=ol({point:function(b,E){var w=x([b,E]);this.stream.point(w[0],w[1])}}),p=iu,m,y;function v(){return d=e*r,h=e*i,m=y=null,x}function x(b){var E=b[0]*d,w=b[1]*h;if(a){var A=w*u-E*o;E=E*u+w*o,w=A}return[E+t,w+n]}return x.invert=function(b){var E=b[0]-t,w=b[1]-n;if(a){var A=w*u+E*o;E=E*u-w*o,w=A}return[E/d,w/h]},x.stream=function(b){return m&&y===b?m:m=g(p(y=b))},x.postclip=function(b){return arguments.length?(p=b,s=l=f=c=null,v()):p},x.clipExtent=function(b){return arguments.length?(p=b==null?(s=l=f=c=null,iu):fv(s=+b[0][0],l=+b[0][1],f=+b[1][0],c=+b[1][1]),v()):s==null?null:[[s,l],[f,c]]},x.scale=function(b){return arguments.length?(e=+b,v()):e},x.translate=function(b){return arguments.length?(t=+b[0],n=+b[1],v()):[t,n]},x.angle=function(b){return arguments.length?(a=b%360*te,o=H(a),u=V(a),v()):a*De},x.reflectX=function(b){return arguments.length?(r=b?-1:1,v()):r<0},x.reflectY=function(b){return arguments.length?(i=b?-1:1,v()):i<0},x.fitExtent=function(b,E){return sl(x,b,E)},x.fitSize=function(b,E){return mh(x,b,E)},x.fitWidth=function(b,E){return yh(x,b,E)},x.fitHeight=function(b,E){return vh(x,b,E)},x}function Bv(e,t){var n=t*t,r=n*n;return[e*(.8707-.131979*n+r*(-.013791+r*(.003971*n-.001529*r))),t*(1.007226+n*(.015085+r*(-.044475+.028874*n-.005916*r)))]}Bv.invert=function(e,t){var n=t,r=25,i;do{var a=n*n,u=a*a;n-=i=(n*(1.007226+a*(.015085+u*(-.044475+.028874*a-.005916*u)))-t)/(1.007226+a*(.015085*3+u*(-.044475*7+.028874*9*a-.005916*11*u)))}while(ie(i)>X&&--r>0);return[e/(.8707+(a=n*n)*(-.131979+a*(-.013791+a*a*a*(.003971-.001529*a)))),n]};function x5(){return Fn(Bv).scale(175.295)}function _v(e,t){return[V(t)*H(e),H(t)]}_v.invert=Mu(Dt);function b5(){return Fn(_v).scale(249.5).clipAngle(90+X)}function Rv(e,t){var n=V(t),r=1+V(e)*n;return[n*H(e)/r,H(t)/r]}Rv.invert=Mu(function(e){return 2*Xi(e)});function A5(){return Fn(Rv).scale(250).clipAngle(142)}function Ov(e,t){return[ss(hh((Ae+t)/2)),-e]}Ov.invert=function(e,t){return[-t,2*Xi(W2(e))-Ae]};function E5(){var e=Mv(Ov),t=e.center,n=e.rotate;return e.center=function(r){return arguments.length?t([-r[1],r[0]]):(r=t(),[r[1],-r[0]])},e.rotate=function(r){return arguments.length?n([r[0],r[1],r.length>2?r[2]+90:90]):(r=n(),[r[0],r[1],r[2]-90])},n([0,0,90]).scale(159.155)}function w5(e,t){return e.parent===t.parent?1:2}function D5(e){return e.reduce(C5,0)/e.length}function C5(e,t){return e+t.x}function F5(e){return 1+e.reduce(k5,0)}function k5(e,t){return Math.max(e,t.y)}function M5(e){for(var t;t=e.children;)e=t[0];return e}function S5(e){for(var t;t=e.children;)e=t[t.length-1];return e}function $5(){var e=w5,t=1,n=1,r=!1;function i(a){var u,o=0;a.eachAfter(function(d){var h=d.children;h?(d.x=D5(h),d.y=F5(h)):(d.x=u?o+=e(d,u):0,d.y=0,u=d)});var s=M5(a),l=S5(a),f=s.x-e(s,l)/2,c=l.x+e(l,s)/2;return a.eachAfter(r?function(d){d.x=(d.x-a.x)*t,d.y=(a.y-d.y)*n}:function(d){d.x=(d.x-f)/(c-f)*t,d.y=(1-(a.y?d.y/a.y:1))*n})}return i.separation=function(a){return arguments.length?(e=a,i):e},i.size=function(a){return arguments.length?(r=!1,t=+a[0],n=+a[1],i):r?null:[t,n]},i.nodeSize=function(a){return arguments.length?(r=!0,t=+a[0],n=+a[1],i):r?[t,n]:null},i}function B5(e){var t=0,n=e.children,r=n&&n.length;if(!r)t=1;else for(;--r>=0;)t+=n[r].value;e.value=t}function _5(){return this.eachAfter(B5)}function R5(e,t){let n=-1;for(const r of this)e.call(t,r,++n,this);return this}function O5(e,t){for(var n=this,r=[n],i,a,u=-1;n=r.pop();)if(e.call(t,n,++u,this),i=n.children)for(a=i.length-1;a>=0;--a)r.push(i[a]);return this}function T5(e,t){for(var n=this,r=[n],i=[],a,u,o,s=-1;n=r.pop();)if(i.push(n),a=n.children)for(u=0,o=a.length;u<o;++u)r.push(a[u]);for(;n=i.pop();)e.call(t,n,++s,this);return this}function L5(e,t){let n=-1;for(const r of this)if(e.call(t,r,++n,this))return r}function N5(e){return this.eachAfter(function(t){for(var n=+e(t.data)||0,r=t.children,i=r&&r.length;--i>=0;)n+=r[i].value;t.value=n})}function z5(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})}function P5(e){for(var t=this,n=I5(t,e),r=[t];t!==n;)t=t.parent,r.push(t);for(var i=r.length;e!==n;)r.splice(i,0,e),e=e.parent;return r}function I5(e,t){if(e===t)return e;var n=e.ancestors(),r=t.ancestors(),i=null;for(e=n.pop(),t=r.pop();e===t;)i=e,e=n.pop(),t=r.pop();return i}function U5(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t}function q5(){return Array.from(this)}function j5(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e}function G5(){var e=this,t=[];return e.each(function(n){n!==e&&t.push({source:n.parent,target:n})}),t}function*W5(){var e=this,t,n=[e],r,i,a;do for(t=n.reverse(),n=[];e=t.pop();)if(yield e,r=e.children)for(i=0,a=r.length;i<a;++i)n.push(r[i]);while(n.length)}function bh(e,t){e instanceof Map?(e=[void 0,e],t===void 0&&(t=X5)):t===void 0&&(t=H5);for(var n=new Ti(e),r,i=[n],a,u,o,s;r=i.pop();)if((u=t(r.data))&&(s=(u=Array.from(u)).length))for(r.children=u,o=s-1;o>=0;--o)i.push(a=u[o]=new Ti(u[o])),a.parent=r,a.depth=r.depth+1;return n.eachBefore(Tv)}function Y5(){return bh(this).eachBefore(V5)}function H5(e){return e.children}function X5(e){return Array.isArray(e)?e[1]:null}function V5(e){e.data.value!==void 0&&(e.value=e.data.value),e.data=e.data.data}function Tv(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function Ti(e){this.data=e,this.depth=this.height=0,this.parent=null}Ti.prototype=bh.prototype={constructor:Ti,count:_5,each:R5,eachAfter:T5,eachBefore:O5,find:L5,sum:N5,sort:z5,path:P5,ancestors:U5,descendants:q5,leaves:j5,links:G5,copy:Y5,[Symbol.iterator]:W5};function Oo(e){return e==null?null:Lv(e)}function Lv(e){if(typeof e!="function")throw new Error;return e}function Or(){return 0}function Ai(e){return function(){return e}}const K5=1664525,J5=1013904223,Wp=4294967296;function Q5(){let e=1;return()=>(e=(K5*e+J5)%Wp)/Wp}function Z5(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function e6(e,t){let n=e.length,r,i;for(;n;)i=t()*n--|0,r=e[n],e[n]=e[i],e[i]=r;return e}function t6(e,t){for(var n=0,r=(e=e6(Array.from(e),t)).length,i=[],a,u;n<r;)a=e[n],u&&Nv(u,a)?++n:(u=r6(i=n6(i,a)),n=0);return u}function n6(e,t){var n,r;if(Rf(t,e))return[t];for(n=0;n<e.length;++n)if(po(t,e[n])&&Rf(Sa(e[n],t),e))return[e[n],t];for(n=0;n<e.length-1;++n)for(r=n+1;r<e.length;++r)if(po(Sa(e[n],e[r]),t)&&po(Sa(e[n],t),e[r])&&po(Sa(e[r],t),e[n])&&Rf(zv(e[n],e[r],t),e))return[e[n],e[r],t];throw new Error}function po(e,t){var n=e.r-t.r,r=t.x-e.x,i=t.y-e.y;return n<0||n*n<r*r+i*i}function Nv(e,t){var n=e.r-t.r+Math.max(e.r,t.r,1)*1e-9,r=t.x-e.x,i=t.y-e.y;return n>0&&n*n>r*r+i*i}function Rf(e,t){for(var n=0;n<t.length;++n)if(!Nv(e,t[n]))return!1;return!0}function r6(e){switch(e.length){case 1:return i6(e[0]);case 2:return Sa(e[0],e[1]);case 3:return zv(e[0],e[1],e[2])}}function i6(e){return{x:e.x,y:e.y,r:e.r}}function Sa(e,t){var n=e.x,r=e.y,i=e.r,a=t.x,u=t.y,o=t.r,s=a-n,l=u-r,f=o-i,c=Math.sqrt(s*s+l*l);return{x:(n+a+s/c*f)/2,y:(r+u+l/c*f)/2,r:(c+i+o)/2}}function zv(e,t,n){var r=e.x,i=e.y,a=e.r,u=t.x,o=t.y,s=t.r,l=n.x,f=n.y,c=n.r,d=r-u,h=r-l,g=i-o,p=i-f,m=s-a,y=c-a,v=r*r+i*i-a*a,x=v-u*u-o*o+s*s,b=v-l*l-f*f+c*c,E=h*g-d*p,w=(g*b-p*x)/(E*2)-r,A=(p*m-g*y)/E,D=(h*x-d*b)/(E*2)-i,C=(d*y-h*m)/E,F=A*A+C*C-1,M=2*(a+w*A+D*C),O=w*w+D*D-a*a,L=-(Math.abs(F)>1e-6?(M+Math.sqrt(M*M-4*F*O))/(2*F):O/M);return{x:r+w+A*L,y:i+D+C*L,r:L}}function Yp(e,t,n){var r=e.x-t.x,i,a,u=e.y-t.y,o,s,l=r*r+u*u;l?(a=t.r+n.r,a*=a,s=e.r+n.r,s*=s,a>s?(i=(l+s-a)/(2*l),o=Math.sqrt(Math.max(0,s/l-i*i)),n.x=e.x-i*r-o*u,n.y=e.y-i*u+o*r):(i=(l+a-s)/(2*l),o=Math.sqrt(Math.max(0,a/l-i*i)),n.x=t.x+i*r-o*u,n.y=t.y+i*u+o*r)):(n.x=t.x+n.r,n.y=t.y)}function Hp(e,t){var n=e.r+t.r-1e-6,r=t.x-e.x,i=t.y-e.y;return n>0&&n*n>r*r+i*i}function Xp(e){var t=e._,n=e.next._,r=t.r+n.r,i=(t.x*n.r+n.x*t.r)/r,a=(t.y*n.r+n.y*t.r)/r;return i*i+a*a}function mo(e){this._=e,this.next=null,this.previous=null}function a6(e,t){if(!(a=(e=Z5(e)).length))return 0;var n,r,i,a,u,o,s,l,f,c,d;if(n=e[0],n.x=0,n.y=0,!(a>1))return n.r;if(r=e[1],n.x=-r.r,r.x=n.r,r.y=0,!(a>2))return n.r+r.r;Yp(r,n,i=e[2]),n=new mo(n),r=new mo(r),i=new mo(i),n.next=i.previous=r,r.next=n.previous=i,i.next=r.previous=n;e:for(s=3;s<a;++s){Yp(n._,r._,i=e[s]),i=new mo(i),l=r.next,f=n.previous,c=r._.r,d=n._.r;do if(c<=d){if(Hp(l._,i._)){r=l,n.next=r,r.previous=n,--s;continue e}c+=l._.r,l=l.next}else{if(Hp(f._,i._)){n=f,n.next=r,r.previous=n,--s;continue e}d+=f._.r,f=f.previous}while(l!==f.next);for(i.previous=n,i.next=r,n.next=r.previous=r=i,u=Xp(n);(i=i.next)!==r;)(o=Xp(i))<u&&(n=i,u=o);r=n.next}for(n=[r._],i=r;(i=i.next)!==r;)n.push(i._);for(i=t6(n,t),s=0;s<a;++s)n=e[s],n.x-=i.x,n.y-=i.y;return i.r}function u6(e){return Math.sqrt(e.value)}function o6(){var e=null,t=1,n=1,r=Or;function i(a){const u=Q5();return a.x=t/2,a.y=n/2,e?a.eachBefore(Vp(e)).eachAfter(Of(r,.5,u)).eachBefore(Kp(1)):a.eachBefore(Vp(u6)).eachAfter(Of(Or,1,u)).eachAfter(Of(r,a.r/Math.min(t,n),u)).eachBefore(Kp(Math.min(t,n)/(2*a.r))),a}return i.radius=function(a){return arguments.length?(e=Oo(a),i):e},i.size=function(a){return arguments.length?(t=+a[0],n=+a[1],i):[t,n]},i.padding=function(a){return arguments.length?(r=typeof a=="function"?a:Ai(+a),i):r},i}function Vp(e){return function(t){t.children||(t.r=Math.max(0,+e(t)||0))}}function Of(e,t,n){return function(r){if(i=r.children){var i,a,u=i.length,o=e(r)*t||0,s;if(o)for(a=0;a<u;++a)i[a].r+=o;if(s=a6(i,n),o)for(a=0;a<u;++a)i[a].r-=o;r.r=s+o}}}function Kp(e){return function(t){var n=t.parent;t.r*=e,n&&(t.x=n.x+e*t.x,t.y=n.y+e*t.y)}}function Pv(e){e.x0=Math.round(e.x0),e.y0=Math.round(e.y0),e.x1=Math.round(e.x1),e.y1=Math.round(e.y1)}function Su(e,t,n,r,i){for(var a=e.children,u,o=-1,s=a.length,l=e.value&&(r-t)/e.value;++o<s;)u=a[o],u.y0=n,u.y1=i,u.x0=t,u.x1=t+=u.value*l}function s6(){var e=1,t=1,n=0,r=!1;function i(u){var o=u.height+1;return u.x0=u.y0=n,u.x1=e,u.y1=t/o,u.eachBefore(a(t,o)),r&&u.eachBefore(Pv),u}function a(u,o){return function(s){s.children&&Su(s,s.x0,u*(s.depth+1)/o,s.x1,u*(s.depth+2)/o);var l=s.x0,f=s.y0,c=s.x1-n,d=s.y1-n;c<l&&(l=c=(l+c)/2),d<f&&(f=d=(f+d)/2),s.x0=l,s.y0=f,s.x1=c,s.y1=d}}return i.round=function(u){return arguments.length?(r=!!u,i):r},i.size=function(u){return arguments.length?(e=+u[0],t=+u[1],i):[e,t]},i.padding=function(u){return arguments.length?(n=+u,i):n},i}var l6={depth:-1},Jp={},Tf={};function f6(e){return e.id}function c6(e){return e.parentId}function Qp(){var e=f6,t=c6,n;function r(i){var a=Array.from(i),u=e,o=t,s,l,f,c,d,h,g,p,m=new Map;if(n!=null){const y=a.map((b,E)=>d6(n(b,E,i))),v=y.map(Zp),x=new Set(y).add("");for(const b of v)x.has(b)||(x.add(b),y.push(b),v.push(Zp(b)),a.push(Tf));u=(b,E)=>y[E],o=(b,E)=>v[E]}for(f=0,s=a.length;f<s;++f)l=a[f],h=a[f]=new Ti(l),(g=u(l,f,i))!=null&&(g+="")&&(p=h.id=g,m.set(p,m.has(p)?Jp:h)),(g=o(l,f,i))!=null&&(g+="")&&(h.parent=g);for(f=0;f<s;++f)if(h=a[f],g=h.parent){if(d=m.get(g),!d)throw new Error("missing: "+g);if(d===Jp)throw new Error("ambiguous: "+g);d.children?d.children.push(h):d.children=[h],h.parent=d}else{if(c)throw new Error("multiple roots");c=h}if(!c)throw new Error("no root");if(n!=null){for(;c.data===Tf&&c.children.length===1;)c=c.children[0],--s;for(let y=a.length-1;y>=0&&(h=a[y],h.data===Tf);--y)h.data=null}if(c.parent=l6,c.eachBefore(function(y){y.depth=y.parent.depth+1,--s}).eachBefore(Tv),c.parent=null,s>0)throw new Error("cycle");return c}return r.id=function(i){return arguments.length?(e=Oo(i),r):e},r.parentId=function(i){return arguments.length?(t=Oo(i),r):t},r.path=function(i){return arguments.length?(n=Oo(i),r):n},r}function d6(e){e=`${e}`;let t=e.length;return Wc(e,t-1)&&!Wc(e,t-2)&&(e=e.slice(0,-1)),e[0]==="/"?e:`/${e}`}function Zp(e){let t=e.length;if(t<2)return"";for(;--t>1&&!Wc(e,t););return e.slice(0,t)}function Wc(e,t){if(e[t]==="/"){let n=0;for(;t>0&&e[--t]==="\\";)++n;if(!(n&1))return!0}return!1}function h6(e,t){return e.parent===t.parent?1:2}function Lf(e){var t=e.children;return t?t[0]:e.t}function Nf(e){var t=e.children;return t?t[t.length-1]:e.t}function g6(e,t,n){var r=n/(t.i-e.i);t.c-=r,t.s+=n,e.c+=r,t.z+=n,t.m+=n}function p6(e){for(var t=0,n=0,r=e.children,i=r.length,a;--i>=0;)a=r[i],a.z+=t,a.m+=t,t+=a.s+(n+=a.c)}function m6(e,t,n){return e.a.parent===t.parent?e.a:n}function To(e,t){this._=e,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=t}To.prototype=Object.create(Ti.prototype);function y6(e){for(var t=new To(e,0),n,r=[t],i,a,u,o;n=r.pop();)if(a=n._.children)for(n.children=new Array(o=a.length),u=o-1;u>=0;--u)r.push(i=n.children[u]=new To(a[u],u)),i.parent=n;return(t.parent=new To(null,0)).children=[t],t}function v6(){var e=h6,t=1,n=1,r=null;function i(l){var f=y6(l);if(f.eachAfter(a),f.parent.m=-f.z,f.eachBefore(u),r)l.eachBefore(s);else{var c=l,d=l,h=l;l.eachBefore(function(v){v.x<c.x&&(c=v),v.x>d.x&&(d=v),v.depth>h.depth&&(h=v)});var g=c===d?1:e(c,d)/2,p=g-c.x,m=t/(d.x+g+p),y=n/(h.depth||1);l.eachBefore(function(v){v.x=(v.x+p)*m,v.y=v.depth*y})}return l}function a(l){var f=l.children,c=l.parent.children,d=l.i?c[l.i-1]:null;if(f){p6(l);var h=(f[0].z+f[f.length-1].z)/2;d?(l.z=d.z+e(l._,d._),l.m=l.z-h):l.z=h}else d&&(l.z=d.z+e(l._,d._));l.parent.A=o(l,d,l.parent.A||c[0])}function u(l){l._.x=l.z+l.parent.m,l.m+=l.parent.m}function o(l,f,c){if(f){for(var d=l,h=l,g=f,p=d.parent.children[0],m=d.m,y=h.m,v=g.m,x=p.m,b;g=Nf(g),d=Lf(d),g&&d;)p=Lf(p),h=Nf(h),h.a=l,b=g.z+v-d.z-m+e(g._,d._),b>0&&(g6(m6(g,l,c),l,b),m+=b,y+=b),v+=g.m,m+=d.m,x+=p.m,y+=h.m;g&&!Nf(h)&&(h.t=g,h.m+=v-y),d&&!Lf(p)&&(p.t=d,p.m+=m-x,c=l)}return c}function s(l){l.x*=t,l.y=l.depth*n}return i.separation=function(l){return arguments.length?(e=l,i):e},i.size=function(l){return arguments.length?(r=!1,t=+l[0],n=+l[1],i):r?null:[t,n]},i.nodeSize=function(l){return arguments.length?(r=!0,t=+l[0],n=+l[1],i):r?[t,n]:null},i}function fl(e,t,n,r,i){for(var a=e.children,u,o=-1,s=a.length,l=e.value&&(i-n)/e.value;++o<s;)u=a[o],u.x0=t,u.x1=r,u.y0=n,u.y1=n+=u.value*l}var Iv=(1+Math.sqrt(5))/2;function Uv(e,t,n,r,i,a){for(var u=[],o=t.children,s,l,f=0,c=0,d=o.length,h,g,p=t.value,m,y,v,x,b,E,w;f<d;){h=i-n,g=a-r;do m=o[c++].value;while(!m&&c<d);for(y=v=m,E=Math.max(g/h,h/g)/(p*e),w=m*m*E,b=Math.max(v/w,w/y);c<d;++c){if(m+=l=o[c].value,l<y&&(y=l),l>v&&(v=l),w=m*m*E,x=Math.max(v/w,w/y),x>b){m-=l;break}b=x}u.push(s={value:m,dice:h<g,children:o.slice(f,c)}),s.dice?Su(s,n,r,i,p?r+=g*m/p:a):fl(s,n,r,p?n+=h*m/p:i,a),p-=m,f=c}return u}const qv=function e(t){function n(r,i,a,u,o){Uv(t,r,i,a,u,o)}return n.ratio=function(r){return e((r=+r)>1?r:1)},n}(Iv);function x6(){var e=qv,t=!1,n=1,r=1,i=[0],a=Or,u=Or,o=Or,s=Or,l=Or;function f(d){return d.x0=d.y0=0,d.x1=n,d.y1=r,d.eachBefore(c),i=[0],t&&d.eachBefore(Pv),d}function c(d){var h=i[d.depth],g=d.x0+h,p=d.y0+h,m=d.x1-h,y=d.y1-h;m<g&&(g=m=(g+m)/2),y<p&&(p=y=(p+y)/2),d.x0=g,d.y0=p,d.x1=m,d.y1=y,d.children&&(h=i[d.depth+1]=a(d)/2,g+=l(d)-h,p+=u(d)-h,m-=o(d)-h,y-=s(d)-h,m<g&&(g=m=(g+m)/2),y<p&&(p=y=(p+y)/2),e(d,g,p,m,y))}return f.round=function(d){return arguments.length?(t=!!d,f):t},f.size=function(d){return arguments.length?(n=+d[0],r=+d[1],f):[n,r]},f.tile=function(d){return arguments.length?(e=Lv(d),f):e},f.padding=function(d){return arguments.length?f.paddingInner(d).paddingOuter(d):f.paddingInner()},f.paddingInner=function(d){return arguments.length?(a=typeof d=="function"?d:Ai(+d),f):a},f.paddingOuter=function(d){return arguments.length?f.paddingTop(d).paddingRight(d).paddingBottom(d).paddingLeft(d):f.paddingTop()},f.paddingTop=function(d){return arguments.length?(u=typeof d=="function"?d:Ai(+d),f):u},f.paddingRight=function(d){return arguments.length?(o=typeof d=="function"?d:Ai(+d),f):o},f.paddingBottom=function(d){return arguments.length?(s=typeof d=="function"?d:Ai(+d),f):s},f.paddingLeft=function(d){return arguments.length?(l=typeof d=="function"?d:Ai(+d),f):l},f}function b6(e,t,n,r,i){var a=e.children,u,o=a.length,s,l=new Array(o+1);for(l[0]=s=u=0;u<o;++u)l[u+1]=s+=a[u].value;f(0,o,e.value,t,n,r,i);function f(c,d,h,g,p,m,y){if(c>=d-1){var v=a[c];v.x0=g,v.y0=p,v.x1=m,v.y1=y;return}for(var x=l[c],b=h/2+x,E=c+1,w=d-1;E<w;){var A=E+w>>>1;l[A]<b?E=A+1:w=A}b-l[E-1]<l[E]-b&&c+1<E&&--E;var D=l[E]-x,C=h-D;if(m-g>y-p){var F=h?(g*C+m*D)/h:m;f(c,E,D,g,p,F,y),f(E,d,C,F,p,m,y)}else{var M=h?(p*C+y*D)/h:y;f(c,E,D,g,p,m,M),f(E,d,C,g,M,m,y)}}}function A6(e,t,n,r,i){(e.depth&1?fl:Su)(e,t,n,r,i)}const E6=function e(t){function n(r,i,a,u,o){if((s=r._squarify)&&s.ratio===t)for(var s,l,f,c,d=-1,h,g=s.length,p=r.value;++d<g;){for(l=s[d],f=l.children,c=l.value=0,h=f.length;c<h;++c)l.value+=f[c].value;l.dice?Su(l,i,a,u,p?a+=(o-a)*l.value/p:o):fl(l,i,a,p?i+=(u-i)*l.value/p:u,o),p-=l.value}else r._squarify=s=Uv(t,r,i,a,u,o),s.ratio=t}return n.ratio=function(r){return e((r=+r)>1?r:1)},n}(Iv);function jv(e){var t;function n(r){return r==null||isNaN(r=+r)?t:r}return n.invert=n,n.domain=n.range=function(r){return arguments.length?(e=Array.from(r,ip),n):e.slice()},n.unknown=function(r){return arguments.length?(t=r,n):t},n.copy=function(){return jv(e).unknown(t)},e=arguments.length?Array.from(e,ip):[0,1],Hi(n)}function em(e){return Math.log(e)}function tm(e){return Math.exp(e)}function w6(e){return-Math.log(-e)}function D6(e){return-Math.exp(-e)}function C6(e){return isFinite(e)?+("1e"+e):e<0?0:e}function F6(e){return e===10?C6:e===Math.E?Math.exp:t=>Math.pow(e,t)}function k6(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function nm(e){return(t,n)=>-e(-t,n)}function Ah(e){const t=e(em,tm),n=t.domain;let r=10,i,a;function u(){return i=k6(r),a=F6(r),n()[0]<0?(i=nm(i),a=nm(a),e(w6,D6)):e(em,tm),t}return t.base=function(o){return arguments.length?(r=+o,u()):r},t.domain=function(o){return arguments.length?(n(o),u()):n()},t.ticks=o=>{const s=n();let l=s[0],f=s[s.length-1];const c=f<l;c&&([l,f]=[f,l]);let d=i(l),h=i(f),g,p;const m=o==null?10:+o;let y=[];if(!(r%1)&&h-d<m){if(d=Math.floor(d),h=Math.ceil(h),l>0){for(;d<=h;++d)for(g=1;g<r;++g)if(p=d<0?g/a(-d):g*a(d),!(p<l)){if(p>f)break;y.push(p)}}else for(;d<=h;++d)for(g=r-1;g>=1;--g)if(p=d>0?g/a(-d):g*a(d),!(p<l)){if(p>f)break;y.push(p)}y.length*2<m&&(y=ap(l,f,m))}else y=ap(d,h,Math.min(h-d,m)).map(a);return c?y.reverse():y},t.tickFormat=(o,s)=>{if(o==null&&(o=10),s==null&&(s=r===10?"s":","),typeof s!="function"&&(!(r%1)&&(s=Ac(s)).precision==null&&(s.trim=!0),s=S2(s)),o===1/0)return s;const l=Math.max(1,r*o/t.ticks().length);return f=>{let c=f/a(Math.round(i(f)));return c*r<r-.5&&(c*=r),c<=l?s(f):""}},t.nice=()=>n(O3(n(),{floor:o=>a(Math.floor(i(o))),ceil:o=>a(Math.ceil(i(o)))})),t}function Gv(){const e=Ah(nh()).domain([1,10]);return e.copy=()=>rh(e,Gv()).base(e.base()),ii.apply(e,arguments),e}function rm(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function im(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Eh(e){var t=1,n=e(rm(t),im(t));return n.constant=function(r){return arguments.length?e(rm(t=+r),im(t)):t},Hi(n)}function Wv(){var e=Eh(nh());return e.copy=function(){return rh(e,Wv()).constant(e.constant())},ii.apply(e,arguments)}function am(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function M6(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function S6(e){return e<0?-e*e:e*e}function wh(e){var t=e(ar,ar),n=1;function r(){return n===1?e(ar,ar):n===.5?e(M6,S6):e(am(n),am(1/n))}return t.exponent=function(i){return arguments.length?(n=+i,r()):n},Hi(t)}function Dh(){var e=wh(nh());return e.copy=function(){return rh(e,Dh()).exponent(e.exponent())},ii.apply(e,arguments),e}function $6(){return Dh.apply(null,arguments).exponent(.5)}function Yv(){var e=[],t=[],n=[],r;function i(){var u=0,o=Math.max(1,t.length);for(n=new Array(o-1);++u<o;)n[u-1]=N2(e,u/o);return a}function a(u){return u==null||isNaN(u=+u)?r:t[Hr(n,u)]}return a.invertExtent=function(u){var o=t.indexOf(u);return o<0?[NaN,NaN]:[o>0?n[o-1]:e[0],o<n.length?n[o]:e[e.length-1]]},a.domain=function(u){if(!arguments.length)return e.slice();e=[];for(let o of u)o!=null&&!isNaN(o=+o)&&e.push(o);return e.sort(tu),i()},a.range=function(u){return arguments.length?(t=Array.from(u),i()):t.slice()},a.unknown=function(u){return arguments.length?(r=u,a):r},a.quantiles=function(){return n.slice()},a.copy=function(){return Yv().domain(e).range(t).unknown(r)},ii.apply(a,arguments)}function Hv(){var e=0,t=1,n=1,r=[.5],i=[0,1],a;function u(s){return s!=null&&s<=s?i[Hr(r,s,0,n)]:a}function o(){var s=-1;for(r=new Array(n);++s<n;)r[s]=((s+1)*t-(s-n)*e)/(n+1);return u}return u.domain=function(s){return arguments.length?([e,t]=s,e=+e,t=+t,o()):[e,t]},u.range=function(s){return arguments.length?(n=(i=Array.from(s)).length-1,o()):i.slice()},u.invertExtent=function(s){var l=i.indexOf(s);return l<0?[NaN,NaN]:l<1?[e,r[0]]:l>=n?[r[n-1],t]:[r[l-1],r[l]]},u.unknown=function(s){return arguments.length&&(a=s),u},u.thresholds=function(){return r.slice()},u.copy=function(){return Hv().domain([e,t]).range(i).unknown(a)},ii.apply(Hi(u),arguments)}function Xv(){var e=[.5],t=[0,1],n,r=1;function i(a){return a!=null&&a<=a?t[Hr(e,a,0,r)]:n}return i.domain=function(a){return arguments.length?(e=Array.from(a),r=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),r=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var u=t.indexOf(a);return[e[u-1],e[u]]},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Xv().domain(e).range(t).unknown(n)},ii.apply(i,arguments)}function B6(){return ii.apply(T3(N3,L3,C2,bc,th,qa,D2,w2,eh,E2).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cl(){var e=0,t=1,n,r,i,a,u=ar,o=!1,s;function l(c){return c==null||isNaN(c=+c)?s:u(i===0?.5:(c=(a(c)-n)*i,o?Math.max(0,Math.min(1,c)):c))}l.domain=function(c){return arguments.length?([e,t]=c,n=a(e=+e),r=a(t=+t),i=n===r?0:1/(r-n),l):[e,t]},l.clamp=function(c){return arguments.length?(o=!!c,l):o},l.interpolator=function(c){return arguments.length?(u=c,l):u};function f(c){return function(d){var h,g;return arguments.length?([h,g]=d,u=c(h,g),l):[u(0),u(1)]}}return l.range=f(Cu),l.rangeRound=f(al),l.unknown=function(c){return arguments.length?(s=c,l):s},function(c){return a=c,n=c(e),r=c(t),i=n===r?0:1/(r-n),l}}function Cr(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Ch(){var e=Hi(cl()(ar));return e.copy=function(){return Cr(e,Ch())},Dr.apply(e,arguments)}function Vv(){var e=Ah(cl()).domain([1,10]);return e.copy=function(){return Cr(e,Vv()).base(e.base())},Dr.apply(e,arguments)}function Kv(){var e=Eh(cl());return e.copy=function(){return Cr(e,Kv()).constant(e.constant())},Dr.apply(e,arguments)}function Fh(){var e=wh(cl());return e.copy=function(){return Cr(e,Fh()).exponent(e.exponent())},Dr.apply(e,arguments)}function _6(){return Fh.apply(null,arguments).exponent(.5)}function dl(){var e=0,t=.5,n=1,r=1,i,a,u,o,s,l=ar,f,c=!1,d;function h(p){return isNaN(p=+p)?d:(p=.5+((p=+f(p))-a)*(r*p<r*a?o:s),l(c?Math.max(0,Math.min(1,p)):p))}h.domain=function(p){return arguments.length?([e,t,n]=p,i=f(e=+e),a=f(t=+t),u=f(n=+n),o=i===a?0:.5/(a-i),s=a===u?0:.5/(u-a),r=a<i?-1:1,h):[e,t,n]},h.clamp=function(p){return arguments.length?(c=!!p,h):c},h.interpolator=function(p){return arguments.length?(l=p,h):l};function g(p){return function(m){var y,v,x;return arguments.length?([y,v,x]=m,l=lh(p,[y,v,x]),h):[l(0),l(.5),l(1)]}}return h.range=g(Cu),h.rangeRound=g(al),h.unknown=function(p){return arguments.length?(d=p,h):d},function(p){return f=p,i=p(e),a=p(t),u=p(n),o=i===a?0:.5/(a-i),s=a===u?0:.5/(u-a),r=a<i?-1:1,h}}function Jv(){var e=Hi(dl()(ar));return e.copy=function(){return Cr(e,Jv())},Dr.apply(e,arguments)}function Qv(){var e=Ah(dl()).domain([.1,1,10]);return e.copy=function(){return Cr(e,Qv()).base(e.base())},Dr.apply(e,arguments)}function Zv(){var e=Eh(dl());return e.copy=function(){return Cr(e,Zv()).constant(e.constant())},Dr.apply(e,arguments)}function kh(){var e=wh(dl());return e.copy=function(){return Cr(e,kh()).exponent(e.exponent())},Dr.apply(e,arguments)}function R6(){return kh.apply(null,arguments).exponent(.5)}function ex(e,t,n){var r=null,i=We(!0),a=null,u=_2,o=null,s=B2(l);e=typeof e=="function"?e:e===void 0?wD:We(+e),t=typeof t=="function"?t:t===void 0?We(0):We(+t),n=typeof n=="function"?n:n===void 0?DD:We(+n);function l(c){var d,h,g,p=(c=CD(c)).length,m,y=!1,v,x=new Array(p),b=new Array(p);for(a==null&&(o=u(v=s())),d=0;d<=p;++d){if(!(d<p&&i(m=c[d],d,c))===y)if(y=!y)h=d,o.areaStart(),o.lineStart();else{for(o.lineEnd(),o.lineStart(),g=d-1;g>=h;--g)o.point(x[g],b[g]);o.lineEnd(),o.areaEnd()}y&&(x[d]=+e(m,d,c),b[d]=+t(m,d,c),o.point(r?+r(m,d,c):x[d],n?+n(m,d,c):b[d]))}if(v)return o=null,v+""||null}function f(){return R2().defined(i).curve(u).context(a)}return l.x=function(c){return arguments.length?(e=typeof c=="function"?c:We(+c),r=null,l):e},l.x0=function(c){return arguments.length?(e=typeof c=="function"?c:We(+c),l):e},l.x1=function(c){return arguments.length?(r=c==null?null:typeof c=="function"?c:We(+c),l):r},l.y=function(c){return arguments.length?(t=typeof c=="function"?c:We(+c),n=null,l):t},l.y0=function(c){return arguments.length?(t=typeof c=="function"?c:We(+c),l):t},l.y1=function(c){return arguments.length?(n=c==null?null:typeof c=="function"?c:We(+c),l):n},l.lineX0=l.lineY0=function(){return f().x(e).y(t)},l.lineY1=function(){return f().x(e).y(n)},l.lineX1=function(){return f().x(r).y(t)},l.defined=function(c){return arguments.length?(i=typeof c=="function"?c:We(!!c),l):i},l.curve=function(c){return arguments.length?(u=c,a!=null&&(o=u(a)),l):u},l.context=function(c){return arguments.length?(c==null?a=o=null:o=u(a=c),l):a},l}const O6={draw(e,t){const n=kD(t/MD);e.moveTo(n,0),e.arc(0,0,n,0,FD)}};function T6(e,t){let n=null,r=B2(i);e=typeof e=="function"?e:We(e||O6),t=typeof t=="function"?t:We(t===void 0?64:+t);function i(){let a;if(n||(n=a=r()),e.apply(this,arguments).draw(n,+t.apply(this,arguments)),a)return n=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:We(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:We(+a),i):t},i.context=function(a){return arguments.length?(n=a??null,i):n},i}/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2022 Joachim Wester
 * MIT licensed
 */var L6=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)i.hasOwnProperty(a)&&(r[a]=i[a])},e(t,n)};return function(t,n){e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),N6=Object.prototype.hasOwnProperty;function Yc(e,t){return N6.call(e,t)}function Hc(e){if(Array.isArray(e)){for(var t=new Array(e.length),n=0;n<t.length;n++)t[n]=""+n;return t}if(Object.keys)return Object.keys(e);var r=[];for(var i in e)Yc(e,i)&&r.push(i);return r}function _t(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function Xc(e){for(var t=0,n=e.length,r;t<n;){if(r=e.charCodeAt(t),r>=48&&r<=57){t++;continue}return!1}return!0}function Rr(e){return e.indexOf("/")===-1&&e.indexOf("~")===-1?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function tx(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function Vc(e){if(e===void 0)return!0;if(e){if(Array.isArray(e)){for(var t=0,n=e.length;t<n;t++)if(Vc(e[t]))return!0}else if(typeof e=="object"){for(var r=Hc(e),i=r.length,a=0;a<i;a++)if(Vc(e[r[a]]))return!0}}return!1}function um(e,t){var n=[e];for(var r in t){var i=typeof t[r]=="object"?JSON.stringify(t[r],null,2):t[r];typeof i<"u"&&n.push(r+": "+i)}return n.join(`
`)}var nx=function(e){L6(t,e);function t(n,r,i,a,u){var o=this.constructor,s=e.call(this,um(n,{name:r,index:i,operation:a,tree:u}))||this;return s.name=r,s.index=i,s.operation=a,s.tree=u,Object.setPrototypeOf(s,o.prototype),s.message=um(n,{name:r,index:i,operation:a,tree:u}),s}return t}(Error),be=nx,z6=_t,Ci={add:function(e,t,n){return e[t]=this.value,{newDocument:n}},remove:function(e,t,n){var r=e[t];return delete e[t],{newDocument:n,removed:r}},replace:function(e,t,n){var r=e[t];return e[t]=this.value,{newDocument:n,removed:r}},move:function(e,t,n){var r=$s(n,this.path);r&&(r=_t(r));var i=Gr(n,{op:"remove",path:this.from}).removed;return Gr(n,{op:"add",path:this.path,value:i}),{newDocument:n,removed:r}},copy:function(e,t,n){var r=$s(n,this.from);return Gr(n,{op:"add",path:this.path,value:_t(r)}),{newDocument:n}},test:function(e,t,n){return{newDocument:n,test:ou(e[t],this.value)}},_get:function(e,t,n){return this.value=e[t],{newDocument:n}}},P6={add:function(e,t,n){return Xc(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:n,index:t}},remove:function(e,t,n){var r=e.splice(t,1);return{newDocument:n,removed:r[0]}},replace:function(e,t,n){var r=e[t];return e[t]=this.value,{newDocument:n,removed:r}},move:Ci.move,copy:Ci.copy,test:Ci.test,_get:Ci._get};function $s(e,t){if(t=="")return e;var n={op:"_get",path:t};return Gr(e,n),n.value}function Gr(e,t,n,r,i,a){if(n===void 0&&(n=!1),r===void 0&&(r=!0),i===void 0&&(i=!0),a===void 0&&(a=0),n&&(typeof n=="function"?n(t,0,e,t.path):Bs(t,0)),t.path===""){var u={newDocument:e};if(t.op==="add")return u.newDocument=t.value,u;if(t.op==="replace")return u.newDocument=t.value,u.removed=e,u;if(t.op==="move"||t.op==="copy")return u.newDocument=$s(e,t.from),t.op==="move"&&(u.removed=e),u;if(t.op==="test"){if(u.test=ou(e,t.value),u.test===!1)throw new be("Test operation failed","TEST_OPERATION_FAILED",a,t,e);return u.newDocument=e,u}else{if(t.op==="remove")return u.removed=e,u.newDocument=null,u;if(t.op==="_get")return t.value=e,u;if(n)throw new be("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",a,t,e);return u}}else{r||(e=_t(e));var o=t.path||"",s=o.split("/"),l=e,f=1,c=s.length,d=void 0,h=void 0,g=void 0;for(typeof n=="function"?g=n:g=Bs;;){if(h=s[f],h&&h.indexOf("~")!=-1&&(h=tx(h)),i&&(h=="__proto__"||h=="prototype"&&f>0&&s[f-1]=="constructor"))throw new TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(n&&d===void 0&&(l[h]===void 0?d=s.slice(0,f).join("/"):f==c-1&&(d=t.path),d!==void 0&&g(t,0,e,d)),f++,Array.isArray(l)){if(h==="-")h=l.length;else{if(n&&!Xc(h))throw new be("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",a,t,e);Xc(h)&&(h=~~h)}if(f>=c){if(n&&t.op==="add"&&h>l.length)throw new be("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",a,t,e);var u=P6[t.op].call(t,l,h,e);if(u.test===!1)throw new be("Test operation failed","TEST_OPERATION_FAILED",a,t,e);return u}}else if(f>=c){var u=Ci[t.op].call(t,l,h,e);if(u.test===!1)throw new be("Test operation failed","TEST_OPERATION_FAILED",a,t,e);return u}if(l=l[h],n&&f<c&&(!l||typeof l!="object"))throw new be("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",a,t,e)}}}function Mh(e,t,n,r,i){if(r===void 0&&(r=!0),i===void 0&&(i=!0),n&&!Array.isArray(t))throw new be("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");r||(e=_t(e));for(var a=new Array(t.length),u=0,o=t.length;u<o;u++)a[u]=Gr(e,t[u],n,!0,i,u),e=a[u].newDocument;return a.newDocument=e,a}function I6(e,t,n){var r=Gr(e,t);if(r.test===!1)throw new be("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r.newDocument}function Bs(e,t,n,r){if(typeof e!="object"||e===null||Array.isArray(e))throw new be("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,n);if(Ci[e.op]){if(typeof e.path!="string")throw new be("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,n);if(e.path.indexOf("/")!==0&&e.path.length>0)throw new be('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,n);if((e.op==="move"||e.op==="copy")&&typeof e.from!="string")throw new be("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,n);if((e.op==="add"||e.op==="replace"||e.op==="test")&&e.value===void 0)throw new be("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,n);if((e.op==="add"||e.op==="replace"||e.op==="test")&&Vc(e.value))throw new be("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,n);if(n){if(e.op=="add"){var i=e.path.split("/").length,a=r.split("/").length;if(i!==a+1&&i!==a)throw new be("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,n)}else if(e.op==="replace"||e.op==="remove"||e.op==="_get"){if(e.path!==r)throw new be("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,n)}else if(e.op==="move"||e.op==="copy"){var u={op:"_get",path:e.from,value:void 0},o=rx([u],n);if(o&&o.name==="OPERATION_PATH_UNRESOLVABLE")throw new be("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,n)}}}else throw new be("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,n)}function rx(e,t,n){try{if(!Array.isArray(e))throw new be("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)Mh(_t(t),_t(e),n||!0);else{n=n||Bs;for(var r=0;r<e.length;r++)n(e[r],r,t,void 0)}}catch(i){if(i instanceof be)return i;throw i}}function ou(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){var n=Array.isArray(e),r=Array.isArray(t),i,a,u;if(n&&r){if(a=e.length,a!=t.length)return!1;for(i=a;i--!==0;)if(!ou(e[i],t[i]))return!1;return!0}if(n!=r)return!1;var o=Object.keys(e);if(a=o.length,a!==Object.keys(t).length)return!1;for(i=a;i--!==0;)if(!t.hasOwnProperty(o[i]))return!1;for(i=a;i--!==0;)if(u=o[i],!ou(e[u],t[u]))return!1;return!0}return e!==e&&t!==t}const U6=Object.freeze(Object.defineProperty({__proto__:null,JsonPatchError:be,_areEquals:ou,applyOperation:Gr,applyPatch:Mh,applyReducer:I6,deepClone:z6,getValueByPointer:$s,validate:rx,validator:Bs},Symbol.toStringTag,{value:"Module"}));/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2021 Joachim Wester
 * MIT license
 */var Sh=new WeakMap,q6=function(){function e(t){this.observers=new Map,this.obj=t}return e}(),j6=function(){function e(t,n){this.callback=t,this.observer=n}return e}();function G6(e){return Sh.get(e)}function W6(e,t){return e.observers.get(t)}function Y6(e,t){e.observers.delete(t.callback)}function H6(e,t){t.unobserve()}function X6(e,t){var n=[],r,i=G6(e);if(!i)i=new q6(e),Sh.set(e,i);else{var a=W6(i,t);r=a&&a.observer}if(r)return r;if(r={},i.value=_t(e),t){r.callback=t,r.next=null;var u=function(){Kc(r)},o=function(){clearTimeout(r.next),r.next=setTimeout(u)};typeof window<"u"&&(window.addEventListener("mouseup",o),window.addEventListener("keyup",o),window.addEventListener("mousedown",o),window.addEventListener("keydown",o),window.addEventListener("change",o))}return r.patches=n,r.object=e,r.unobserve=function(){Kc(r),clearTimeout(r.next),Y6(i,r),typeof window<"u"&&(window.removeEventListener("mouseup",o),window.removeEventListener("keyup",o),window.removeEventListener("mousedown",o),window.removeEventListener("keydown",o),window.removeEventListener("change",o))},i.observers.set(t,new j6(t,r)),r}function Kc(e,t){t===void 0&&(t=!1);var n=Sh.get(e.object);$h(n.value,e.object,e.patches,"",t),e.patches.length&&Mh(n.value,e.patches);var r=e.patches;return r.length>0&&(e.patches=[],e.callback&&e.callback(r)),r}function $h(e,t,n,r,i){if(t!==e){typeof t.toJSON=="function"&&(t=t.toJSON());for(var a=Hc(t),u=Hc(e),o=!1,s=u.length-1;s>=0;s--){var l=u[s],f=e[l];if(Yc(t,l)&&!(t[l]===void 0&&f!==void 0&&Array.isArray(t)===!1)){var c=t[l];typeof f=="object"&&f!=null&&typeof c=="object"&&c!=null&&Array.isArray(f)===Array.isArray(c)?$h(f,c,n,r+"/"+Rr(l),i):f!==c&&(i&&n.push({op:"test",path:r+"/"+Rr(l),value:_t(f)}),n.push({op:"replace",path:r+"/"+Rr(l),value:_t(c)}))}else Array.isArray(e)===Array.isArray(t)?(i&&n.push({op:"test",path:r+"/"+Rr(l),value:_t(f)}),n.push({op:"remove",path:r+"/"+Rr(l)}),o=!0):(i&&n.push({op:"test",path:r,value:e}),n.push({op:"replace",path:r,value:t}))}if(!(!o&&a.length==u.length))for(var s=0;s<a.length;s++){var l=a[s];!Yc(e,l)&&t[l]!==void 0&&n.push({op:"add",path:r+"/"+Rr(l),value:_t(t[l])})}}}function V6(e,t,n){n===void 0&&(n=!1);var r=[];return $h(e,t,r,"",n),r}const K6=Object.freeze(Object.defineProperty({__proto__:null,compare:V6,generate:Kc,observe:X6,unobserve:H6},Symbol.toStringTag,{value:"Module"}));Object.assign({},U6,K6,{JsonPatchError:nx,deepClone:_t,escapePathComponent:Rr,unescapePathComponent:tx});var J6=/("(?:[^\\"]|\\.)*")|[:,]/g,Q6=function(t,n){var r,i,a;return n=n||{},r=JSON.stringify([1],void 0,n.indent===void 0?2:n.indent).slice(2,-3),i=r===""?1/0:n.maxLength===void 0?80:n.maxLength,a=n.replacer,function u(o,s,l){var f,c,d,h,g,p,m,y,v,x,b,E;if(o&&typeof o.toJSON=="function"&&(o=o.toJSON()),b=JSON.stringify(o,a),b===void 0)return b;if(m=i-s.length-l,b.length<=m&&(v=b.replace(J6,function(w,A){return A||w+" "}),v.length<=m))return v;if(a!=null&&(o=JSON.parse(b),a=void 0),typeof o=="object"&&o!==null){if(y=s+r,d=[],c=0,Array.isArray(o))for(x="[",f="]",m=o.length;c<m;c++)d.push(u(o[c],y,c===m-1?0:1)||"null");else for(x="{",f="}",p=Object.keys(o),m=p.length;c<m;c++)h=p[c],g=JSON.stringify(h)+": ",E=u(o[h],y,g.length+(c===m-1?0:1)),E!==void 0&&d.push(g+E);if(d.length>0)return[x,r+d.join(`,
`+y),f].join(`
`+s)}return b}(t,"",0)};const AU=S3(Q6);function Mt(e,t,n){return e.fields=t||[],e.fname=n,e}function Ee(e){return e==null?null:e.fname}function Xe(e){return e==null?null:e.fields}function ix(e){return e.length===1?Z6(e[0]):e8(e)}const Z6=e=>function(t){return t[e]},e8=e=>{const t=e.length;return function(n){for(let r=0;r<t;++r)n=n[e[r]];return n}};function S(e){throw Error(e)}function hl(e){const t=[],n=e.length;let r=null,i=0,a="",u,o,s;e=e+"";function l(){t.push(a+e.substring(u,o)),a="",u=o+1}for(u=o=0;o<n;++o)if(s=e[o],s==="\\")a+=e.substring(u,o++),u=o;else if(s===r)l(),r=null,i=-1;else{if(r)continue;u===i&&s==='"'||u===i&&s==="'"?(u=o+1,r=s):s==="."&&!i?o>u?l():u=o+1:s==="["?(o>u&&l(),i=u=o+1):s==="]"&&(i||S("Access path missing open bracket: "+e),i>0&&l(),i=0,u=o+1)}return i&&S("Access path missing closing bracket: "+e),r&&S("Access path missing closing quote: "+e),o>u&&(o++,l()),t}function Ot(e,t,n){const r=hl(e);return e=r.length===1?r[0]:e,Mt((n&&n.get||ix)(r),[e],t||e)}const $u=Ot("id"),et=Mt(e=>e,[],"identity"),ir=Mt(()=>0,[],"zero"),Vi=Mt(()=>1,[],"one"),Ct=Mt(()=>!0,[],"true"),Qn=Mt(()=>!1,[],"false");function t8(e,t,n){const r=[t].concat([].slice.call(n));console[e].apply(console,r)}const ax=0,Bh=1,ux=2,ox=3,sx=4;function lx(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t8,r=e||ax;return{level(i){return arguments.length?(r=+i,this):r},error(){return r>=Bh&&n(t||"error","ERROR",arguments),this},warn(){return r>=ux&&n(t||"warn","WARN",arguments),this},info(){return r>=ox&&n(t||"log","INFO",arguments),this},debug(){return r>=sx&&n(t||"log","DEBUG",arguments),this}}}var z=Array.isArray;function K(e){return e===Object(e)}const om=e=>e!=="__proto__";function fx(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((r,i)=>{for(const a in i)if(a==="signals")r.signals=n8(r.signals,i.signals);else{const u=a==="legend"?{layout:1}:a==="style"?!0:null;_h(r,a,i[a],u)}return r},{})}function _h(e,t,n,r){if(!om(t))return;let i,a;if(K(n)&&!z(n)){a=K(e[t])?e[t]:e[t]={};for(i in n)r&&(r===!0||r[i])?_h(a,i,n[i]):om(i)&&(a[i]=n[i])}else e[t]=n}function n8(e,t){if(e==null)return t;const n={},r=[];function i(a){n[a.name]||(n[a.name]=1,r.push(a))}return t.forEach(i),e.forEach(i),r}function re(e){return e[e.length-1]}function Ne(e){return e==null||e===""?null:+e}const cx=e=>t=>e*Math.exp(t),dx=e=>t=>Math.log(e*t),hx=e=>t=>Math.sign(t)*Math.log1p(Math.abs(t/e)),gx=e=>t=>Math.sign(t)*Math.expm1(Math.abs(t))*e,_s=e=>t=>t<0?-Math.pow(-t,e):Math.pow(t,e);function gl(e,t,n,r){const i=n(e[0]),a=n(re(e)),u=(a-i)*t;return[r(i-u),r(a-u)]}function Rh(e,t){return gl(e,t,Ne,et)}function Oh(e,t){var n=Math.sign(e[0]);return gl(e,t,dx(n),cx(n))}function Th(e,t,n){return gl(e,t,_s(n),_s(1/n))}function Lh(e,t,n){return gl(e,t,hx(n),gx(n))}function pl(e,t,n,r,i){const a=r(e[0]),u=r(re(e)),o=t!=null?r(t):(a+u)/2;return[i(o+(a-o)*n),i(o+(u-o)*n)]}function ml(e,t,n){return pl(e,t,n,Ne,et)}function yl(e,t,n){const r=Math.sign(e[0]);return pl(e,t,n,dx(r),cx(r))}function su(e,t,n,r){return pl(e,t,n,_s(r),_s(1/r))}function vl(e,t,n,r){return pl(e,t,n,hx(r),gx(r))}function Nh(e){return 1+~~(new Date(e).getMonth()/3)}function zh(e){return 1+~~(new Date(e).getUTCMonth()/3)}function q(e){return e!=null?z(e)?e:[e]:[]}function Ph(e,t,n){let r=e[0],i=e[1],a;return i<r&&(a=i,i=r,r=a),a=i-r,a>=n-t?[t,n]:[r=Math.min(Math.max(r,t),n-a),r+a]}function J(e){return typeof e=="function"}const r8="descending";function Ih(e,t,n){n=n||{},t=q(t)||[];const r=[],i=[],a={},u=n.comparator||i8;return q(e).forEach((o,s)=>{o!=null&&(r.push(t[s]===r8?-1:1),i.push(o=J(o)?o:Ot(o,null,n)),(Xe(o)||[]).forEach(l=>a[l]=1))}),i.length===0?null:Mt(u(i,r),Object.keys(a))}const xl=(e,t)=>(e<t||e==null)&&t!=null?-1:(e>t||t==null)&&e!=null?1:(t=t instanceof Date?+t:t,(e=e instanceof Date?+e:e)!==e&&t===t?-1:t!==t&&e===e?1:0),i8=(e,t)=>e.length===1?a8(e[0],t[0]):u8(e,t,e.length),a8=(e,t)=>function(n,r){return xl(e(n),e(r))*t},u8=(e,t,n)=>(t.push(0),function(r,i){let a,u=0,o=-1;for(;u===0&&++o<n;)a=e[o],u=xl(a(r),a(i));return u*t[o]});function tt(e){return J(e)?e:()=>e}function Uh(e,t){let n;return r=>{n&&clearTimeout(n),n=setTimeout(()=>(t(r),n=null),e)}}function Z(e){for(let t,n,r=1,i=arguments.length;r<i;++r){t=arguments[r];for(n in t)e[n]=t[n]}return e}function ln(e,t){let n=0,r,i,a,u;if(e&&(r=e.length))if(t==null){for(i=e[n];n<r&&(i==null||i!==i);i=e[++n]);for(a=u=i;n<r;++n)i=e[n],i!=null&&(i<a&&(a=i),i>u&&(u=i))}else{for(i=t(e[n]);n<r&&(i==null||i!==i);i=t(e[++n]));for(a=u=i;n<r;++n)i=t(e[n]),i!=null&&(i<a&&(a=i),i>u&&(u=i))}return[a,u]}function px(e,t){const n=e.length;let r=-1,i,a,u,o,s;if(t==null){for(;++r<n;)if(a=e[r],a!=null&&a>=a){i=u=a;break}if(r===n)return[-1,-1];for(o=s=r;++r<n;)a=e[r],a!=null&&(i>a&&(i=a,o=r),u<a&&(u=a,s=r))}else{for(;++r<n;)if(a=t(e[r],r,e),a!=null&&a>=a){i=u=a;break}if(r===n)return[-1,-1];for(o=s=r;++r<n;)a=t(e[r],r,e),a!=null&&(i>a&&(i=a,o=r),u<a&&(u=a,s=r))}return[o,s]}const o8=Object.prototype.hasOwnProperty;function G(e,t){return o8.call(e,t)}const yo={};function Ki(e){let t={},n;function r(a){return G(t,a)&&t[a]!==yo}const i={size:0,empty:0,object:t,has:r,get(a){return r(a)?t[a]:void 0},set(a,u){return r(a)||(++i.size,t[a]===yo&&--i.empty),t[a]=u,this},delete(a){return r(a)&&(--i.size,++i.empty,t[a]=yo),this},clear(){i.size=i.empty=0,i.object=t={}},test(a){return arguments.length?(n=a,i):n},clean(){const a={};let u=0;for(const o in t){const s=t[o];s!==yo&&(!n||!n(s))&&(a[o]=s,++u)}i.size=u,i.empty=0,i.object=t=a}};return e&&Object.keys(e).forEach(a=>{i.set(a,e[a])}),i}function qh(e,t,n,r,i,a){if(!n&&n!==0)return a;const u=+n;let o=e[0],s=re(e),l;s<o&&(l=o,o=s,s=l),l=Math.abs(t-o);const f=Math.abs(s-t);return l<f&&l<=u?r:f<=u?i:a}function T(e,t,n){const r=e.prototype=Object.create(t.prototype);return Object.defineProperty(r,"constructor",{value:e,writable:!0,enumerable:!0,configurable:!0}),Z(r,n)}function Ir(e,t,n,r){let i=t[0],a=t[t.length-1],u;return i>a&&(u=i,i=a,a=u),n=n===void 0||n,r=r===void 0||r,(n?i<=e:i<e)&&(r?e<=a:e<a)}function jh(e){return typeof e=="boolean"}function Gn(e){return Object.prototype.toString.call(e)==="[object Date]"}function mx(e){return e&&J(e[Symbol.iterator])}function Yn(e){return typeof e=="number"}function Gh(e){return Object.prototype.toString.call(e)==="[object RegExp]"}function fe(e){return typeof e=="string"}function Wh(e,t,n){e&&(e=t?q(e).map(o=>o.replace(/\\(.)/g,"$1")):q(e));const r=e&&e.length,i=n&&n.get||ix,a=o=>i(t?[o]:hl(o));let u;if(!r)u=function(){return""};else if(r===1){const o=a(e[0]);u=function(s){return""+o(s)}}else{const o=e.map(a);u=function(s){let l=""+o[0](s),f=0;for(;++f<r;)l+="|"+o[f](s);return l}}return Mt(u,e,"key")}function Yh(e,t){const n=e[0],r=re(e),i=+t;return i?i===1?r:n+i*(r-n):n}const s8=1e4;function yx(e){e=+e||s8;let t,n,r;const i=()=>{t={},n={},r=0},a=(u,o)=>(++r>e&&(n=t,t={},r=1),t[u]=o);return i(),{clear:i,has:u=>G(t,u)||G(n,u),get:u=>G(t,u)?t[u]:G(n,u)?a(u,n[u]):void 0,set:(u,o)=>G(t,u)?t[u]=o:a(u,o)}}function vx(e,t,n,r){const i=t.length,a=n.length;if(!a)return t;if(!i)return n;const u=r||new t.constructor(i+a);let o=0,s=0,l=0;for(;o<i&&s<a;++l)u[l]=e(t[o],n[s])>0?n[s++]:t[o++];for(;o<i;++o,++l)u[l]=t[o];for(;s<a;++s,++l)u[l]=n[s];return u}function $a(e,t){let n="";for(;--t>=0;)n+=e;return n}function Hh(e,t,n,r){const i=n||" ",a=e+"",u=t-a.length;return u<=0?a:r==="left"?$a(i,u)+a:r==="center"?$a(i,~~(u/2))+a+$a(i,Math.ceil(u/2)):a+$a(i,u)}function Ji(e){return e&&re(e)-e[0]||0}function U(e){return z(e)?"["+e.map(U)+"]":K(e)||fe(e)?JSON.stringify(e).replace("\u2028","\\u2028").replace("\u2029","\\u2029"):e}function bl(e){return e==null||e===""?null:!e||e==="false"||e==="0"?!1:!!e}const l8=e=>Yn(e)||Gn(e)?e:Date.parse(e);function Al(e,t){return t=t||l8,e==null||e===""?null:t(e)}function El(e){return e==null||e===""?null:e+""}function Tt(e){const t={},n=e.length;for(let r=0;r<n;++r)t[e[r]]=!0;return t}function Xh(e,t,n,r){const i=r??"…",a=e+"",u=a.length,o=Math.max(0,t-i.length);return u<=t?a:n==="left"?i+a.slice(u-o):n==="center"?a.slice(0,Math.ceil(o/2))+i+a.slice(u-~~(o/2)):a.slice(0,o)+i}function tr(e,t,n){if(e)if(t){const r=e.length;for(let i=0;i<r;++i){const a=t(e[i]);a&&n(a,i,e)}}else e.forEach(n)}function f8(e){return e}function c8(e){if(e==null)return f8;var t,n,r=e.scale[0],i=e.scale[1],a=e.translate[0],u=e.translate[1];return function(o,s){s||(t=n=0);var l=2,f=o.length,c=new Array(f);for(c[0]=(t+=o[0])*r+a,c[1]=(n+=o[1])*i+u;l<f;)c[l]=o[l],++l;return c}}function d8(e,t){for(var n,r=e.length,i=r-t;i<--r;)n=e[i],e[i++]=e[r],e[r]=n}function h8(e,t){return typeof t=="string"&&(t=e.objects[t]),t.type==="GeometryCollection"?{type:"FeatureCollection",features:t.geometries.map(function(n){return sm(e,n)})}:sm(e,t)}function sm(e,t){var n=t.id,r=t.bbox,i=t.properties==null?{}:t.properties,a=xx(e,t);return n==null&&r==null?{type:"Feature",properties:i,geometry:a}:r==null?{type:"Feature",id:n,properties:i,geometry:a}:{type:"Feature",id:n,bbox:r,properties:i,geometry:a}}function xx(e,t){var n=c8(e.transform),r=e.arcs;function i(f,c){c.length&&c.pop();for(var d=r[f<0?~f:f],h=0,g=d.length;h<g;++h)c.push(n(d[h],h));f<0&&d8(c,g)}function a(f){return n(f)}function u(f){for(var c=[],d=0,h=f.length;d<h;++d)i(f[d],c);return c.length<2&&c.push(c[0]),c}function o(f){for(var c=u(f);c.length<4;)c.push(c[0]);return c}function s(f){return f.map(o)}function l(f){var c=f.type,d;switch(c){case"GeometryCollection":return{type:c,geometries:f.geometries.map(l)};case"Point":d=a(f.coordinates);break;case"MultiPoint":d=f.coordinates.map(a);break;case"LineString":d=u(f.arcs);break;case"MultiLineString":d=f.arcs.map(u);break;case"Polygon":d=s(f.arcs);break;case"MultiPolygon":d=f.arcs.map(s);break;default:return null}return{type:c,coordinates:d}}return l(t)}function g8(e,t){var n={},r={},i={},a=[],u=-1;t.forEach(function(l,f){var c=e.arcs[l<0?~l:l],d;c.length<3&&!c[1][0]&&!c[1][1]&&(d=t[++u],t[u]=l,t[f]=d)}),t.forEach(function(l){var f=o(l),c=f[0],d=f[1],h,g;if(h=i[c])if(delete i[h.end],h.push(l),h.end=d,g=r[d]){delete r[g.start];var p=g===h?h:h.concat(g);r[p.start=h.start]=i[p.end=g.end]=p}else r[h.start]=i[h.end]=h;else if(h=r[d])if(delete r[h.start],h.unshift(l),h.start=c,g=i[c]){delete i[g.end];var m=g===h?h:g.concat(h);r[m.start=g.start]=i[m.end=h.end]=m}else r[h.start]=i[h.end]=h;else h=[l],r[h.start=c]=i[h.end=d]=h});function o(l){var f=e.arcs[l<0?~l:l],c=f[0],d;return e.transform?(d=[0,0],f.forEach(function(h){d[0]+=h[0],d[1]+=h[1]})):d=f[f.length-1],l<0?[d,c]:[c,d]}function s(l,f){for(var c in l){var d=l[c];delete f[d.start],delete d.start,delete d.end,d.forEach(function(h){n[h<0?~h:h]=1}),a.push(d)}}return s(i,r),s(r,i),t.forEach(function(l){n[l<0?~l:l]||a.push([l])}),a}function p8(e){return xx(e,m8.apply(this,arguments))}function m8(e,t,n){var r,i,a;if(arguments.length>1)r=y8(e,t,n);else for(i=0,r=new Array(a=e.arcs.length);i<a;++i)r[i]=i;return{type:"MultiLineString",arcs:g8(e,r)}}function y8(e,t,n){var r=[],i=[],a;function u(c){var d=c<0?~c:c;(i[d]||(i[d]=[])).push({i:c,g:a})}function o(c){c.forEach(u)}function s(c){c.forEach(o)}function l(c){c.forEach(s)}function f(c){switch(a=c,c.type){case"GeometryCollection":c.geometries.forEach(f);break;case"LineString":o(c.arcs);break;case"MultiLineString":case"Polygon":s(c.arcs);break;case"MultiPolygon":l(c.arcs);break}}return f(t),i.forEach(n==null?function(c){r.push(c[0].i)}:function(c){n(c[0].g,c[c.length-1].g)&&r.push(c[0].i)}),r}const Ie="year",Ft="quarter",Ve="month",Se="week",kt="date",He="day",Dn="dayofyear",Lt="hours",Nt="minutes",Vt="seconds",fn="milliseconds",Vh=[Ie,Ft,Ve,Se,kt,He,Dn,Lt,Nt,Vt,fn],zf=Vh.reduce((e,t,n)=>(e[t]=1+n,e),{});function Kh(e){const t=q(e).slice(),n={};return t.length||S("Missing time unit."),t.forEach(i=>{G(zf,i)?n[i]=1:S(`Invalid time unit: ${i}.`)}),(n[Se]||n[He]?1:0)+(n[Ft]||n[Ve]||n[kt]?1:0)+(n[Dn]?1:0)>1&&S(`Incompatible time units: ${e}`),t.sort((i,a)=>zf[i]-zf[a]),t}const v8={[Ie]:"%Y ",[Ft]:"Q%q ",[Ve]:"%b ",[kt]:"%d ",[Se]:"W%U ",[He]:"%a ",[Dn]:"%j ",[Lt]:"%H:00",[Nt]:"00:%M",[Vt]:":%S",[fn]:".%L",[`${Ie}-${Ve}`]:"%Y-%m ",[`${Ie}-${Ve}-${kt}`]:"%Y-%m-%d ",[`${Lt}-${Nt}`]:"%H:%M"};function Jh(e,t){const n=Z({},v8,t),r=Kh(e),i=r.length;let a="",u=0,o,s;for(u=0;u<i;)for(o=r.length;o>u;--o)if(s=r.slice(u,o).join("-"),n[s]!=null){a+=n[s],u=o;break}return a.trim()}const Tr=new Date;function Qh(e){return Tr.setFullYear(e),Tr.setMonth(0),Tr.setDate(1),Tr.setHours(0,0,0,0),Tr}function Zh(e){return bx(new Date(e))}function e0(e){return Jc(new Date(e))}function bx(e){return _o.count(Qh(e.getFullYear())-1,e)}function Jc(e){return F2.count(Qh(e.getFullYear())-1,e)}function Qc(e){return Qh(e).getDay()}function x8(e,t,n,r,i,a,u){if(0<=e&&e<100){const o=new Date(-1,t,n,r,i,a,u);return o.setFullYear(e),o}return new Date(e,t,n,r,i,a,u)}function t0(e){return Ax(new Date(e))}function n0(e){return Zc(new Date(e))}function Ax(e){const t=Date.UTC(e.getUTCFullYear(),0,1);return qa.count(t-1,e)}function Zc(e){const t=Date.UTC(e.getUTCFullYear(),0,1);return th.count(t-1,e)}function ed(e){return Tr.setTime(Date.UTC(e,0,1)),Tr.getUTCDay()}function b8(e,t,n,r,i,a,u){if(0<=e&&e<100){const o=new Date(Date.UTC(-1,t,n,r,i,a,u));return o.setUTCFullYear(n.y),o}return new Date(Date.UTC(e,t,n,r,i,a,u))}function Ex(e,t,n,r,i){const a=t||1,u=re(e),o=(y,v,x)=>(x=x||y,A8(n[x],r[x],y===u&&a,v)),s=new Date,l=Tt(e),f=l[Ie]?o(Ie):tt(2012),c=l[Ve]?o(Ve):l[Ft]?o(Ft):ir,d=l[Se]&&l[He]?o(He,1,Se+He):l[Se]?o(Se,1):l[He]?o(He,1):l[kt]?o(kt,1):l[Dn]?o(Dn,1):Vi,h=l[Lt]?o(Lt):ir,g=l[Nt]?o(Nt):ir,p=l[Vt]?o(Vt):ir,m=l[fn]?o(fn):ir;return function(y){s.setTime(+y);const v=f(s);return i(v,c(s),d(s,v),h(s),g(s),p(s),m(s))}}function A8(e,t,n,r){const i=n<=1?e:r?(a,u)=>r+n*Math.floor((e(a,u)-r)/n):(a,u)=>n*Math.floor(e(a,u)/n);return t?(a,u)=>t(i(a,u),u):i}function Li(e,t,n){return t+e*7-(n+6)%7}const E8={[Ie]:e=>e.getFullYear(),[Ft]:e=>Math.floor(e.getMonth()/3),[Ve]:e=>e.getMonth(),[kt]:e=>e.getDate(),[Lt]:e=>e.getHours(),[Nt]:e=>e.getMinutes(),[Vt]:e=>e.getSeconds(),[fn]:e=>e.getMilliseconds(),[Dn]:e=>bx(e),[Se]:e=>Jc(e),[Se+He]:(e,t)=>Li(Jc(e),e.getDay(),Qc(t)),[He]:(e,t)=>Li(1,e.getDay(),Qc(t))},w8={[Ft]:e=>3*e,[Se]:(e,t)=>Li(e,0,Qc(t))};function wx(e,t){return Ex(e,t||1,E8,w8,x8)}const D8={[Ie]:e=>e.getUTCFullYear(),[Ft]:e=>Math.floor(e.getUTCMonth()/3),[Ve]:e=>e.getUTCMonth(),[kt]:e=>e.getUTCDate(),[Lt]:e=>e.getUTCHours(),[Nt]:e=>e.getUTCMinutes(),[Vt]:e=>e.getUTCSeconds(),[fn]:e=>e.getUTCMilliseconds(),[Dn]:e=>Ax(e),[Se]:e=>Zc(e),[He]:(e,t)=>Li(1,e.getUTCDay(),ed(t)),[Se+He]:(e,t)=>Li(Zc(e),e.getUTCDay(),ed(t))},C8={[Ft]:e=>3*e,[Se]:(e,t)=>Li(e,0,ed(t))};function Dx(e,t){return Ex(e,t||1,D8,C8,b8)}const F8={[Ie]:z3,[Ft]:rp.every(3),[Ve]:rp,[Se]:F2,[kt]:_o,[He]:_o,[Dn]:_o,[Lt]:P3,[Nt]:I3,[Vt]:eh,[fn]:k2},k8={[Ie]:C2,[Ft]:bc.every(3),[Ve]:bc,[Se]:th,[kt]:qa,[He]:qa,[Dn]:qa,[Lt]:D2,[Nt]:w2,[Vt]:eh,[fn]:k2};function Qi(e){return F8[e]}function Zi(e){return k8[e]}function Cx(e,t,n){return e?e.offset(t,n):void 0}function r0(e,t,n){return Cx(Qi(e),t,n)}function i0(e,t,n){return Cx(Zi(e),t,n)}function Fx(e,t,n,r){return e?e.range(t,n,r):void 0}function a0(e,t,n,r){return Fx(Qi(e),t,n,r)}function u0(e,t,n,r){return Fx(Zi(e),t,n,r)}const Ba=1e3,_a=Ba*60,Ra=_a*60,wl=Ra*24,M8=wl*7,lm=wl*30,td=wl*365,kx=[Ie,Ve,kt,Lt,Nt,Vt,fn],Oa=kx.slice(0,-1),Ta=Oa.slice(0,-1),La=Ta.slice(0,-1),S8=La.slice(0,-1),$8=[Ie,Se],fm=[Ie,Ve],Mx=[Ie],fa=[[Oa,1,Ba],[Oa,5,5*Ba],[Oa,15,15*Ba],[Oa,30,30*Ba],[Ta,1,_a],[Ta,5,5*_a],[Ta,15,15*_a],[Ta,30,30*_a],[La,1,Ra],[La,3,3*Ra],[La,6,6*Ra],[La,12,12*Ra],[S8,1,wl],[$8,1,M8],[fm,1,lm],[fm,3,3*lm],[Mx,1,td]];function Sx(e){const t=e.extent,n=e.maxbins||40,r=Math.abs(Ji(t))/n;let i=ih(o=>o[2]).right(fa,r),a,u;return i===fa.length?(a=Mx,u=nu(t[0]/td,t[1]/td,n)):i?(i=fa[r/fa[i-1][2]<fa[i][2]/r?i-1:i],a=i[0],u=i[1]):(a=kx,u=Math.max(nu(t[0],t[1],n),1)),{units:a,step:u}}function Na(e){const t={};return n=>t[n]||(t[n]=e(n))}function B8(e,t){return n=>{const r=e(n),i=r.indexOf(t);if(i<0)return r;let a=_8(r,i);const u=a<r.length?r.slice(a):"";for(;--a>i;)if(r[a]!=="0"){++a;break}return r.slice(0,a)+u}}function _8(e,t){let n=e.lastIndexOf("e"),r;if(n>0)return n;for(n=e.length;--n>t;)if(r=e.charCodeAt(n),r>=48&&r<=57)return n+1}function $x(e){const t=Na(e.format),n=e.formatPrefix;return{format:t,formatPrefix:n,formatFloat(r){const i=Ac(r||",");if(i.precision==null){switch(i.precision=12,i.type){case"%":i.precision-=2;break;case"e":i.precision-=1;break}return B8(t(i),t(".1f")(1)[1])}else return t(i)},formatSpan(r,i,a,u){u=Ac(u??",f");const o=nu(r,i,a),s=Math.max(Math.abs(r),Math.abs(i));let l;if(u.precision==null)switch(u.type){case"s":return isNaN(l=tD(o,s))||(u.precision=l),n(u,s);case"":case"e":case"g":case"p":case"r":{isNaN(l=eD(o,s))||(u.precision=l-(u.type==="e"));break}case"f":case"%":{isNaN(l=Z3(o))||(u.precision=l-(u.type==="%")*2);break}}return t(u)}}}let nd;Bx();function Bx(){return nd=$x({format:S2,formatPrefix:nD})}function _x(e){return $x(Q3(e))}function Rs(e){return arguments.length?nd=_x(e):nd}function cm(e,t,n){n=n||{},K(n)||S(`Invalid time multi-format specifier: ${n}`);const r=t(Vt),i=t(Nt),a=t(Lt),u=t(kt),o=t(Se),s=t(Ve),l=t(Ft),f=t(Ie),c=e(n[fn]||".%L"),d=e(n[Vt]||":%S"),h=e(n[Nt]||"%I:%M"),g=e(n[Lt]||"%I %p"),p=e(n[kt]||n[He]||"%a %d"),m=e(n[Se]||"%b %d"),y=e(n[Ve]||"%B"),v=e(n[Ft]||"%B"),x=e(n[Ie]||"%Y");return b=>(r(b)<b?c:i(b)<b?d:a(b)<b?h:u(b)<b?g:s(b)<b?o(b)<b?p:m:f(b)<b?l(b)<b?y:v:x)(b)}function Rx(e){const t=Na(e.format),n=Na(e.utcFormat);return{timeFormat:r=>fe(r)?t(r):cm(t,Qi,r),utcFormat:r=>fe(r)?n(r):cm(n,Zi,r),timeParse:Na(e.parse),utcParse:Na(e.utcParse)}}let rd;Ox();function Ox(){return rd=Rx({format:q3,parse:j3,utcFormat:E2,utcParse:G3})}function Tx(e){return Rx(U3(e))}function lu(e){return arguments.length?rd=Tx(e):rd}const id=(e,t)=>Z({},e,t);function Lx(e,t){const n=e?_x(e):Rs(),r=t?Tx(t):lu();return id(n,r)}function o0(e,t){const n=arguments.length;return n&&n!==2&&S("defaultLocale expects either zero or two arguments."),n?id(Rs(e),lu(t)):id(Rs(),lu())}function R8(){return Bx(),Ox(),o0()}const O8=/^(data:|([A-Za-z]+:)?\/\/)/,T8=/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|file|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,L8=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205f\u3000]/g,dm="file://";function N8(e,t){return n=>({options:n||{},sanitize:P8,load:z8,fileAccess:!!t,file:I8(t),http:q8(e)})}async function z8(e,t){const n=await this.sanitize(e,t),r=n.href;return n.localFile?this.file(r):this.http(r,t)}async function P8(e,t){t=Z({},this.options,t);const n=this.fileAccess,r={href:null};let i,a,u;const o=T8.test(e.replace(L8,""));(e==null||typeof e!="string"||!o)&&S("Sanitize failure, invalid URI: "+U(e));const s=O8.test(e);return(u=t.baseURL)&&!s&&(!e.startsWith("/")&&!u.endsWith("/")&&(e="/"+e),e=u+e),a=(i=e.startsWith(dm))||t.mode==="file"||t.mode!=="http"&&!s&&n,i?e=e.slice(dm.length):e.startsWith("//")&&(t.defaultProtocol==="file"?(e=e.slice(2),a=!0):e=(t.defaultProtocol||"http")+":"+e),Object.defineProperty(r,"localFile",{value:!!a}),r.href=e,t.target&&(r.target=t.target+""),t.rel&&(r.rel=t.rel+""),t.context==="image"&&t.crossOrigin&&(r.crossOrigin=t.crossOrigin+""),r}function I8(e){return e?t=>new Promise((n,r)=>{e.readFile(t,(i,a)=>{i?r(i):n(a)})}):U8}async function U8(){S("No file system access.")}function q8(e){return e?async function(t,n){const r=Z({},this.options.http,n),i=n&&n.response,a=await e(t,r);return a.ok?J(a[i])?a[i]():a.text():S(a.status+""+a.statusText)}:j8}async function j8(){S("No HTTP fetch method available.")}const G8=e=>e!=null&&e===e,W8=e=>e==="true"||e==="false"||e===!0||e===!1,Y8=e=>!Number.isNaN(Date.parse(e)),Nx=e=>!Number.isNaN(+e)&&!(e instanceof Date),H8=e=>Nx(e)&&Number.isInteger(+e),ad={boolean:bl,integer:Ne,number:Ne,date:Al,string:El,unknown:et},vo=[W8,H8,Nx,Y8],X8=["boolean","integer","number","date"];function zx(e,t){if(!e||!e.length)return"unknown";const n=e.length,r=vo.length,i=vo.map((a,u)=>u+1);for(let a=0,u=0,o,s;a<n;++a)for(s=t?e[a][t]:e[a],o=0;o<r;++o)if(i[o]&&G8(s)&&!vo[o](s)&&(i[o]=0,++u,u===vo.length))return"string";return X8[i.reduce((a,u)=>a===0?u:a,0)-1]}function Px(e,t){return t.reduce((n,r)=>(n[r]=zx(e,r),n),{})}function hm(e){const t=function(n,r){const i={delimiter:e};return s0(n,r?Z(r,i):i)};return t.responseType="text",t}function s0(e,t){return t.header&&(e=t.header.map(U).join(t.delimiter)+`
`+e),uD(t.delimiter).parse(e+"")}s0.responseType="text";function V8(e){return typeof Buffer=="function"&&J(Buffer.isBuffer)?Buffer.isBuffer(e):!1}function l0(e,t){const n=t&&t.property?Ot(t.property):et;return K(e)&&!V8(e)?K8(n(e),t):n(JSON.parse(e))}l0.responseType="json";function K8(e,t){return!z(e)&&mx(e)&&(e=[...e]),t&&t.copy?JSON.parse(JSON.stringify(e)):e}const J8={interior:(e,t)=>e!==t,exterior:(e,t)=>e===t};function Ix(e,t){let n,r,i,a;return e=l0(e,t),t&&t.feature?(n=h8,i=t.feature):t&&t.mesh?(n=p8,i=t.mesh,a=J8[t.filter]):S("Missing TopoJSON feature or mesh parameter."),r=(r=e.objects[i])?n(e,r,a):S("Invalid TopoJSON object: "+i),r&&r.features||[r]}Ix.responseType="json";const Lo={dsv:s0,csv:hm(","),tsv:hm("	"),json:l0,topojson:Ix};function f0(e,t){return arguments.length>1?(Lo[e]=t,this):G(Lo,e)?Lo[e]:null}function Ux(e){const t=f0(e);return t&&t.responseType||"text"}function qx(e,t,n,r){t=t||{};const i=f0(t.type||"json");return i||S("Unknown data format type: "+t.type),e=i(e,t),t.parse&&Q8(e,t.parse,n,r),G(e,"columns")&&delete e.columns,e}function Q8(e,t,n,r){if(!e.length)return;const i=lu();n=n||i.timeParse,r=r||i.utcParse;let a=e.columns||Object.keys(e[0]),u,o,s,l,f,c;t==="auto"&&(t=Px(e,a)),a=Object.keys(t);const d=a.map(h=>{const g=t[h];let p,m;if(g&&(g.startsWith("date:")||g.startsWith("utc:")))return p=g.split(/:(.+)?/,2),m=p[1],(m[0]==="'"&&m[m.length-1]==="'"||m[0]==='"'&&m[m.length-1]==='"')&&(m=m.slice(1,-1)),(p[0]==="utc"?r:n)(m);if(!ad[g])throw Error("Illegal format pattern: "+h+":"+g);return ad[g]});for(s=0,f=e.length,c=a.length;s<f;++s)for(u=e[s],l=0;l<c;++l)o=a[l],u[o]=d[l](u[o])}const Dl=N8(typeof fetch<"u"&&fetch,null);function Cl(e){const t=e||et,n=[],r={};return n.add=i=>{const a=t(i);return r[a]||(r[a]=1,n.push(i)),n},n.remove=i=>{const a=t(i);if(r[a]){r[a]=0;const u=n.indexOf(i);u>=0&&n.splice(u,1)}return n},n}async function No(e,t){try{await t(e)}catch(n){e.error(n)}}const jx=Symbol("vega_id");let Z8=1;function ea(e){return!!(e&&Y(e))}function Y(e){return e[jx]}function Gx(e,t){return e[jx]=t,e}function le(e){const t=e===Object(e)?e:{data:e};return Y(t)?t:Gx(t,Z8++)}function c0(e){return Fl(e,le({}))}function Fl(e,t){for(const n in e)t[n]=e[n];return t}function Wx(e,t){return Gx(t,Y(e))}function ai(e,t){return e?t?(n,r)=>e(n,r)||Y(t(n))-Y(t(r)):(n,r)=>e(n,r)||Y(n)-Y(r):null}function Yx(e){return e&&e.constructor===ui}function ui(){const e=[],t=[],n=[],r=[],i=[];let a=null,u=!1;return{constructor:ui,insert(o){const s=q(o),l=s.length;for(let f=0;f<l;++f)e.push(s[f]);return this},remove(o){const s=J(o)?r:t,l=q(o),f=l.length;for(let c=0;c<f;++c)s.push(l[c]);return this},modify(o,s,l){const f={field:s,value:tt(l)};return J(o)?(f.filter=o,i.push(f)):(f.tuple=o,n.push(f)),this},encode(o,s){return J(o)?i.push({filter:o,field:s}):n.push({tuple:o,field:s}),this},clean(o){return a=o,this},reflow(){return u=!0,this},pulse(o,s){const l={},f={};let c,d,h,g,p,m;for(c=0,d=s.length;c<d;++c)l[Y(s[c])]=1;for(c=0,d=t.length;c<d;++c)p=t[c],l[Y(p)]=-1;for(c=0,d=r.length;c<d;++c)g=r[c],s.forEach(v=>{g(v)&&(l[Y(v)]=-1)});for(c=0,d=e.length;c<d;++c)p=e[c],m=Y(p),l[m]?l[m]=1:o.add.push(le(e[c]));for(c=0,d=s.length;c<d;++c)p=s[c],l[Y(p)]<0&&o.rem.push(p);function y(v,x,b){b?v[x]=b(v):o.encode=x,u||(f[Y(v)]=v)}for(c=0,d=n.length;c<d;++c)h=n[c],p=h.tuple,g=h.field,m=l[Y(p)],m>0&&(y(p,g,h.value),o.modifies(g));for(c=0,d=i.length;c<d;++c)h=i[c],g=h.filter,s.forEach(v=>{g(v)&&l[Y(v)]>0&&y(v,h.field,h.value)}),o.modifies(h.field);if(u)o.mod=t.length||r.length?s.filter(v=>l[Y(v)]>0):s.slice();else for(m in f)o.mod.push(f[m]);return(a||a==null&&(t.length||r.length))&&o.clean(!0),o}}}const zo="_:mod:_";function kl(){Object.defineProperty(this,zo,{writable:!0,value:{}})}kl.prototype={set(e,t,n,r){const i=this,a=i[e],u=i[zo];return t!=null&&t>=0?(a[t]!==n||r)&&(a[t]=n,u[t+":"+e]=-1,u[e]=-1):(a!==n||r)&&(i[e]=n,u[e]=z(n)?1+n.length:-1),i},modified(e,t){const n=this[zo];if(arguments.length){if(z(e)){for(let r=0;r<e.length;++r)if(n[e[r]])return!0;return!1}}else{for(const r in n)if(n[r])return!0;return!1}return t!=null&&t>=0?t+1<n[e]||!!n[t+":"+e]:!!n[e]},clear(){return this[zo]={},this}};let e9=0;const t9="pulse",n9=new kl,r9=1,i9=2;function ge(e,t,n,r){this.id=++e9,this.value=e,this.stamp=-1,this.rank=-1,this.qrank=-1,this.flags=0,t&&(this._update=t),n&&this.parameters(n,r)}function gm(e){return function(t){const n=this.flags;return arguments.length===0?!!(n&e):(this.flags=t?n|e:n&~e,this)}}ge.prototype={targets(){return this._targets||(this._targets=Cl($u))},set(e){return this.value!==e?(this.value=e,1):0},skip:gm(r9),modified:gm(i9),parameters(e,t,n){t=t!==!1;const r=this._argval=this._argval||new kl,i=this._argops=this._argops||[],a=[];let u,o,s,l;const f=(c,d,h)=>{h instanceof ge?(h!==this&&(t&&h.targets().add(this),a.push(h)),i.push({op:h,name:c,index:d})):r.set(c,d,h)};for(u in e)if(o=e[u],u===t9)q(o).forEach(c=>{c instanceof ge?c!==this&&(c.targets().add(this),a.push(c)):S("Pulse parameters must be operator instances.")}),this.source=o;else if(z(o))for(r.set(u,-1,Array(s=o.length)),l=0;l<s;++l)f(u,l,o[l]);else f(u,-1,o);return this.marshall().clear(),n&&(i.initonly=!0),a},marshall(e){const t=this._argval||n9,n=this._argops;let r,i,a,u;if(n){const o=n.length;for(i=0;i<o;++i)r=n[i],a=r.op,u=a.modified()&&a.stamp===e,t.set(r.name,r.index,a.value,u);if(n.initonly){for(i=0;i<o;++i)r=n[i],r.op.targets().remove(this);this._argops=null,this._update=null}}return t},detach(){const e=this._argops;let t,n,r,i;if(e)for(t=0,n=e.length;t<n;++t)r=e[t],i=r.op,i._targets&&i._targets.remove(this);this.pulse=null,this.source=null},evaluate(e){const t=this._update;if(t){const n=this.marshall(e.stamp),r=t.call(this,n,e);if(n.clear(),r!==this.value)this.value=r;else if(!this.modified())return e.StopPropagation}},run(e){if(e.stamp<this.stamp)return e.StopPropagation;let t;return this.skip()?(this.skip(!1),t=0):t=this.evaluate(e),this.pulse=t||e}};function a9(e,t,n,r){let i=1,a;return e instanceof ge?a=e:e&&e.prototype instanceof ge?a=new e:J(e)?a=new ge(null,e):(i=0,a=new ge(e,t)),this.rank(a),i&&(r=n,n=t),n&&this.connect(a,a.parameters(n,r)),this.touch(a),a}function u9(e,t){const n=e.rank,r=t.length;for(let i=0;i<r;++i)if(n<t[i].rank){this.rerank(e);return}}let o9=0;function Ml(e,t,n){this.id=++o9,this.value=null,n&&(this.receive=n),e&&(this._filter=e),t&&(this._apply=t)}function Zn(e,t,n){return new Ml(e,t,n)}Ml.prototype={_filter:Ct,_apply:et,targets(){return this._targets||(this._targets=Cl($u))},consume(e){return arguments.length?(this._consume=!!e,this):!!this._consume},receive(e){if(this._filter(e)){const t=this.value=this._apply(e),n=this._targets,r=n?n.length:0;for(let i=0;i<r;++i)n[i].receive(t);this._consume&&(e.preventDefault(),e.stopPropagation())}},filter(e){const t=Zn(e);return this.targets().add(t),t},apply(e){const t=Zn(null,e);return this.targets().add(t),t},merge(){const e=Zn();this.targets().add(e);for(let t=0,n=arguments.length;t<n;++t)arguments[t].targets().add(e);return e},throttle(e){let t=-1;return this.filter(()=>{const n=Date.now();return n-t>e?(t=n,1):0})},debounce(e){const t=Zn();return this.targets().add(Zn(null,null,Uh(e,n=>{const r=n.dataflow;t.receive(n),r&&r.run&&r.run()}))),t},between(e,t){let n=!1;return e.targets().add(Zn(null,null,()=>n=!0)),t.targets().add(Zn(null,null,()=>n=!1)),this.filter(()=>n)},detach(){this._filter=Ct,this._targets=null}};function s9(e,t,n,r){const i=this,a=Zn(n,r),u=function(l){l.dataflow=i;try{a.receive(l)}catch(f){i.error(f)}finally{i.run()}};let o;typeof e=="string"&&typeof document<"u"?o=document.querySelectorAll(e):o=q(e);const s=o.length;for(let l=0;l<s;++l)o[l].addEventListener(t,u);return a}function l9(e,t){const n=this.locale();return qx(e,t,n.timeParse,n.utcParse)}function f9(e,t,n){return t=this.parse(t,n),this.pulse(e,this.changeset().insert(t))}async function c9(e,t){const n=this;let r=0,i;try{i=await n.loader().load(e,{context:"dataflow",response:Ux(t&&t.type)});try{i=n.parse(i,t)}catch(a){r=-2,n.warn("Data ingestion failed",e,a)}}catch(a){r=-1,n.warn("Loading failed",e,a)}return{data:i,status:r}}async function d9(e,t,n){const r=this,i=r._pending||h9(r);i.requests+=1;const a=await r.request(t,n);return r.pulse(e,r.changeset().remove(Ct).insert(a.data||[])),i.done(),a}function h9(e){let t;const n=new Promise(r=>t=r);return n.requests=0,n.done=()=>{--n.requests===0&&(e._pending=null,t(e))},e._pending=n}const g9={skip:!0};function p9(e,t,n,r,i){return(e instanceof ge?y9:m9)(this,e,t,n,r,i),this}function m9(e,t,n,r,i,a){const u=Z({},a,g9);let o,s;J(n)||(n=tt(n)),r===void 0?o=l=>e.touch(n(l)):J(r)?(s=new ge(null,r,i,!1),o=l=>{s.evaluate(l);const f=n(l),c=s.value;Yx(c)?e.pulse(f,c,a):e.update(f,c,u)}):o=l=>e.update(n(l),r,u),t.apply(o)}function y9(e,t,n,r,i,a){if(r===void 0)t.targets().add(n);else{const u=a||{},o=new ge(null,v9(n,r),i,!1);o.modified(u.force),o.rank=t.rank,t.targets().add(o),n&&(o.skip(!0),o.value=n.value,o.targets().add(n),e.connect(n,[o]))}}function v9(e,t){return t=J(t)?t:tt(t),e?function(n,r){const i=t(n,r);return e.skip()||(e.skip(i!==this.value).value=i),i}:t}function x9(e){e.rank=++this._rank}function b9(e){const t=[e];let n,r,i;for(;t.length;)if(this.rank(n=t.pop()),r=n._targets)for(i=r.length;--i>=0;)t.push(n=r[i]),n===e&&S("Cycle detected in dataflow graph.")}const Os={},yn=1,nr=2,Ln=4,A9=yn|nr,pm=yn|Ln,pi=yn|nr|Ln,mm=8,ca=16,ym=32,vm=64;function gr(e,t,n){this.dataflow=e,this.stamp=t??-1,this.add=[],this.rem=[],this.mod=[],this.fields=null,this.encode=n||null}function Pf(e,t){const n=[];return tr(e,t,r=>n.push(r)),n}function xm(e,t){const n={};return e.visit(t,r=>{n[Y(r)]=1}),r=>n[Y(r)]?null:r}function xo(e,t){return e?(n,r)=>e(n,r)&&t(n,r):t}gr.prototype={StopPropagation:Os,ADD:yn,REM:nr,MOD:Ln,ADD_REM:A9,ADD_MOD:pm,ALL:pi,REFLOW:mm,SOURCE:ca,NO_SOURCE:ym,NO_FIELDS:vm,fork(e){return new gr(this.dataflow).init(this,e)},clone(){const e=this.fork(pi);return e.add=e.add.slice(),e.rem=e.rem.slice(),e.mod=e.mod.slice(),e.source&&(e.source=e.source.slice()),e.materialize(pi|ca)},addAll(){let e=this;return!e.source||e.add===e.rem||!e.rem.length&&e.source.length===e.add.length||(e=new gr(this.dataflow).init(this),e.add=e.source,e.rem=[]),e},init(e,t){const n=this;return n.stamp=e.stamp,n.encode=e.encode,e.fields&&!(t&vm)&&(n.fields=e.fields),t&yn?(n.addF=e.addF,n.add=e.add):(n.addF=null,n.add=[]),t&nr?(n.remF=e.remF,n.rem=e.rem):(n.remF=null,n.rem=[]),t&Ln?(n.modF=e.modF,n.mod=e.mod):(n.modF=null,n.mod=[]),t&ym?(n.srcF=null,n.source=null):(n.srcF=e.srcF,n.source=e.source,e.cleans&&(n.cleans=e.cleans)),n},runAfter(e){this.dataflow.runAfter(e)},changed(e){const t=e||pi;return t&yn&&this.add.length||t&nr&&this.rem.length||t&Ln&&this.mod.length},reflow(e){if(e)return this.fork(pi).reflow();const t=this.add.length,n=this.source&&this.source.length;return n&&n!==t&&(this.mod=this.source,t&&this.filter(Ln,xm(this,yn))),this},clean(e){return arguments.length?(this.cleans=!!e,this):this.cleans},modifies(e){const t=this.fields||(this.fields={});return z(e)?e.forEach(n=>t[n]=!0):t[e]=!0,this},modified(e,t){const n=this.fields;return(t||this.mod.length)&&n?arguments.length?z(e)?e.some(r=>n[r]):n[e]:!!n:!1},filter(e,t){const n=this;return e&yn&&(n.addF=xo(n.addF,t)),e&nr&&(n.remF=xo(n.remF,t)),e&Ln&&(n.modF=xo(n.modF,t)),e&ca&&(n.srcF=xo(n.srcF,t)),n},materialize(e){e=e||pi;const t=this;return e&yn&&t.addF&&(t.add=Pf(t.add,t.addF),t.addF=null),e&nr&&t.remF&&(t.rem=Pf(t.rem,t.remF),t.remF=null),e&Ln&&t.modF&&(t.mod=Pf(t.mod,t.modF),t.modF=null),e&ca&&t.srcF&&(t.source=t.source.filter(t.srcF),t.srcF=null),t},visit(e,t){const n=this,r=t;if(e&ca)return tr(n.source,n.srcF,r),n;e&yn&&tr(n.add,n.addF,r),e&nr&&tr(n.rem,n.remF,r),e&Ln&&tr(n.mod,n.modF,r);const i=n.source;if(e&mm&&i){const a=n.add.length+n.mod.length;a===i.length||(a?tr(i,xm(n,pm),r):tr(i,n.srcF,r))}return n}};function d0(e,t,n,r){const i=this;let a=0;this.dataflow=e,this.stamp=t,this.fields=null,this.encode=r||null,this.pulses=n;for(const u of n)if(u.stamp===t){if(u.fields){const o=i.fields||(i.fields={});for(const s in u.fields)o[s]=1}u.changed(i.ADD)&&(a|=i.ADD),u.changed(i.REM)&&(a|=i.REM),u.changed(i.MOD)&&(a|=i.MOD)}this.changes=a}T(d0,gr,{fork(e){const t=new gr(this.dataflow).init(this,e&this.NO_FIELDS);return e!==void 0&&(e&t.ADD&&this.visit(t.ADD,n=>t.add.push(n)),e&t.REM&&this.visit(t.REM,n=>t.rem.push(n)),e&t.MOD&&this.visit(t.MOD,n=>t.mod.push(n))),t},changed(e){return this.changes&e},modified(e){const t=this,n=t.fields;return n&&t.changes&t.MOD?z(e)?e.some(r=>n[r]):n[e]:0},filter(){S("MultiPulse does not support filtering.")},materialize(){S("MultiPulse does not support materialization.")},visit(e,t){const n=this,r=n.pulses,i=r.length;let a=0;if(e&n.SOURCE)for(;a<i;++a)r[a].visit(e,t);else for(;a<i;++a)r[a].stamp===n.stamp&&r[a].visit(e,t);return n}});async function E9(e,t,n){const r=this,i=[];if(r._pulse)return Hx(r);if(r._pending&&await r._pending,t&&await No(r,t),!r._touched.length)return r.debug("Dataflow invoked, but nothing to do."),r;const a=++r._clock;r._pulse=new gr(r,a,e),r._touched.forEach(f=>r._enqueue(f,!0)),r._touched=Cl($u);let u=0,o,s,l;try{for(;r._heap.size()>0;){if(o=r._heap.pop(),o.rank!==o.qrank){r._enqueue(o,!0);continue}s=o.run(r._getPulse(o,e)),s.then?s=await s:s.async&&(i.push(s.async),s=Os),s!==Os&&o._targets&&o._targets.forEach(f=>r._enqueue(f)),++u}}catch(f){r._heap.clear(),l=f}if(r._input={},r._pulse=null,r.debug(`Pulse ${a}: ${u} operators`),l&&(r._postrun=[],r.error(l)),r._postrun.length){const f=r._postrun.sort((c,d)=>d.priority-c.priority);r._postrun=[];for(let c=0;c<f.length;++c)await No(r,f[c].callback)}return n&&await No(r,n),i.length&&Promise.all(i).then(f=>r.runAsync(null,()=>{f.forEach(c=>{try{c(r)}catch(d){r.error(d)}})})),r}async function w9(e,t,n){for(;this._running;)await this._running;const r=()=>this._running=null;return(this._running=this.evaluate(e,t,n)).then(r,r),this._running}function D9(e,t,n){return this._pulse?Hx(this):(this.evaluate(e,t,n),this)}function C9(e,t,n){if(this._pulse||t)this._postrun.push({priority:n||0,callback:e});else try{e(this)}catch(r){this.error(r)}}function Hx(e){return e.error("Dataflow already running. Use runAsync() to chain invocations."),e}function F9(e,t){const n=e.stamp<this._clock;n&&(e.stamp=this._clock),(n||t)&&(e.qrank=e.rank,this._heap.push(e))}function k9(e,t){const n=e.source,r=this._clock;return n&&z(n)?new d0(this,r,n.map(i=>i.pulse),t):this._input[e.id]||M9(this._pulse,n&&n.pulse)}function M9(e,t){return t&&t.stamp===e.stamp?t:(e=e.fork(),t&&t!==Os&&(e.source=t.source),e)}const h0={skip:!1,force:!1};function S9(e,t){const n=t||h0;return this._pulse?this._enqueue(e):this._touched.add(e),n.skip&&e.skip(!0),this}function $9(e,t,n){const r=n||h0;return(e.set(t)||r.force)&&this.touch(e,r),this}function B9(e,t,n){this.touch(e,n||h0);const r=new gr(this,this._clock+(this._pulse?0:1)),i=e.pulse&&e.pulse.source||[];return r.target=e,this._input[e.id]=t.pulse(r,i),this}function _9(e){let t=[];return{clear:()=>t=[],size:()=>t.length,peek:()=>t[0],push:n=>(t.push(n),Xx(t,0,t.length-1,e)),pop:()=>{const n=t.pop();let r;return t.length?(r=t[0],t[0]=n,R9(t,0,e)):r=n,r}}}function Xx(e,t,n,r){let i,a;const u=e[n];for(;n>t;){if(a=n-1>>1,i=e[a],r(u,i)<0){e[n]=i,n=a;continue}break}return e[n]=u}function R9(e,t,n){const r=t,i=e.length,a=e[t];let u=(t<<1)+1,o;for(;u<i;)o=u+1,o<i&&n(e[u],e[o])>=0&&(u=o),e[t]=e[u],t=u,u=(t<<1)+1;return e[t]=a,Xx(e,r,t,n)}function $i(){this.logger(lx()),this.logLevel(Bh),this._clock=0,this._rank=0,this._locale=o0();try{this._loader=Dl()}catch{}this._touched=Cl($u),this._input={},this._pulse=null,this._heap=_9((e,t)=>e.qrank-t.qrank),this._postrun=[]}function da(e){return function(){return this._log[e].apply(this,arguments)}}$i.prototype={stamp(){return this._clock},loader(e){return arguments.length?(this._loader=e,this):this._loader},locale(e){return arguments.length?(this._locale=e,this):this._locale},logger(e){return arguments.length?(this._log=e,this):this._log},error:da("error"),warn:da("warn"),info:da("info"),debug:da("debug"),logLevel:da("level"),cleanThreshold:1e4,add:a9,connect:u9,rank:x9,rerank:b9,pulse:B9,touch:S9,update:$9,changeset:ui,ingest:f9,parse:l9,preload:d9,request:c9,events:s9,on:p9,evaluate:E9,run:D9,runAsync:w9,runAfter:C9,_enqueue:F9,_getPulse:k9};function $(e,t){ge.call(this,e,null,t)}T($,ge,{run(e){if(e.stamp<this.stamp)return e.StopPropagation;let t;return this.skip()?this.skip(!1):t=this.evaluate(e),t=t||e,t.then?t=t.then(n=>this.pulse=n):t!==e.StopPropagation&&(this.pulse=t),t},evaluate(e){const t=this.marshall(e.stamp),n=this.transform(t,e);return t.clear(),n},transform(){}});const Ni={};function Vx(e){const t=Kx(e);return t&&t.Definition||null}function Kx(e){return e=e&&e.toLowerCase(),G(Ni,e)?Ni[e]:null}function*Jx(e,t){if(t==null)for(let n of e)n!=null&&n!==""&&(n=+n)>=n&&(yield n);else{let n=-1;for(let r of e)r=t(r,++n,e),r!=null&&r!==""&&(r=+r)>=r&&(yield r)}}function g0(e,t,n){const r=Float64Array.from(Jx(e,n));return r.sort(tu),t.map(i=>N2(r,i))}function p0(e,t){return g0(e,[.25,.5,.75],t)}function m0(e,t){const n=e.length,r=KD(e,t),i=p0(e,t),a=(i[2]-i[0])/1.34;return 1.06*(Math.min(r,a)||r||Math.abs(i[0])||1)*Math.pow(n,-.2)}function Qx(e){const t=e.maxbins||20,n=e.base||10,r=Math.log(n),i=e.divide||[5,2];let a=e.extent[0],u=e.extent[1],o,s,l,f,c,d;const h=e.span||u-a||Math.abs(a)||1;if(e.step)o=e.step;else if(e.steps){for(f=h/t,c=0,d=e.steps.length;c<d&&e.steps[c]<f;++c);o=e.steps[Math.max(0,c-1)]}else{for(s=Math.ceil(Math.log(t)/r),l=e.minstep||0,o=Math.max(l,Math.pow(n,Math.round(Math.log(h)/r)-s));Math.ceil(h/o)>t;)o*=n;for(c=0,d=i.length;c<d;++c)f=o/i[c],f>=l&&h/f<=t&&(o=f)}f=Math.log(o);const g=f>=0?0:~~(-f/r)+1,p=Math.pow(n,-g-1);return(e.nice||e.nice===void 0)&&(f=Math.floor(a/o+p)*o,a=a<f?f-o:f,u=Math.ceil(u/o)*o),{start:a,stop:u===a?a+o:u,step:o}}var zt=Math.random;function O9(e){zt=e}function Zx(e,t,n,r){if(!e.length)return[void 0,void 0];const i=Float64Array.from(Jx(e,r)),a=i.length,u=t;let o,s,l,f;for(l=0,f=Array(u);l<u;++l){for(o=0,s=0;s<a;++s)o+=i[~~(zt()*a)];f[l]=o/a}return f.sort(tu),[Ec(f,n/2),Ec(f,1-n/2)]}function eb(e,t,n,r){r=r||(d=>d);const i=e.length,a=new Float64Array(i);let u=0,o=1,s=r(e[0]),l=s,f=s+t,c;for(;o<i;++o){if(c=r(e[o]),c>=f){for(l=(s+l)/2;u<o;++u)a[u]=l;f=c+t,s=c}l=c}for(l=(s+l)/2;u<o;++u)a[u]=l;return n?T9(a,t+t/4):a}function T9(e,t){const n=e.length;let r=0,i=1,a,u;for(;e[r]===e[i];)++i;for(;i<n;){for(a=i+1;e[i]===e[a];)++a;if(e[i]-e[i-1]<t){for(u=i+(r+a-i-i>>1);u<i;)e[u++]=e[i];for(;u>i;)e[u--]=e[r]}r=i,i=a}return e}function L9(e){return function(){return e=(1103515245*e+12345)%2147483647,e/2147483647}}function N9(e,t){t==null&&(t=e,e=0);let n,r,i;const a={min(u){return arguments.length?(n=u||0,i=r-n,a):n},max(u){return arguments.length?(r=u||0,i=r-n,a):r},sample(){return n+Math.floor(i*zt())},pdf(u){return u===Math.floor(u)&&u>=n&&u<r?1/i:0},cdf(u){const o=Math.floor(u);return o<n?0:o>=r?1:(o-n+1)/i},icdf(u){return u>=0&&u<=1?n-1+Math.floor(u*i):NaN}};return a.min(e).max(t)}const tb=Math.sqrt(2*Math.PI),z9=Math.SQRT2;let ha=NaN;function Bu(e,t){e=e||0,t=t??1;let n=0,r=0,i,a;if(ha===ha)n=ha,ha=NaN;else{do n=zt()*2-1,r=zt()*2-1,i=n*n+r*r;while(i===0||i>1);a=Math.sqrt(-2*Math.log(i)/i),n*=a,ha=r*a}return e+n*t}function Sl(e,t,n){n=n??1;const r=(e-(t||0))/n;return Math.exp(-.5*r*r)/(n*tb)}function _u(e,t,n){t=t||0,n=n??1;const r=(e-t)/n,i=Math.abs(r);let a;if(i>37)a=0;else{const u=Math.exp(-i*i/2);let o;i<7.07106781186547?(o=.0352624965998911*i+.700383064443688,o=o*i+6.37396220353165,o=o*i+33.912866078383,o=o*i+112.079291497871,o=o*i+221.213596169931,o=o*i+220.206867912376,a=u*o,o=.0883883476483184*i+1.75566716318264,o=o*i+16.064177579207,o=o*i+86.7807322029461,o=o*i+296.564248779674,o=o*i+637.333633378831,o=o*i+793.826512519948,o=o*i+440.413735824752,a=a/o):(o=i+.65,o=i+4/o,o=i+3/o,o=i+2/o,o=i+1/o,a=u/o/2.506628274631)}return r>0?1-a:a}function Ru(e,t,n){return e<0||e>1?NaN:(t||0)+(n??1)*z9*P9(2*e-1)}function P9(e){let t=-Math.log((1-e)*(1+e)),n;return t<6.25?(t-=3.125,n=-364441206401782e-35,n=-16850591381820166e-35+n*t,n=128584807152564e-32+n*t,n=11157877678025181e-33+n*t,n=-1333171662854621e-31+n*t,n=20972767875968562e-33+n*t,n=6637638134358324e-30+n*t,n=-4054566272975207e-29+n*t,n=-8151934197605472e-29+n*t,n=26335093153082323e-28+n*t,n=-12975133253453532e-27+n*t,n=-5415412054294628e-26+n*t,n=10512122733215323e-25+n*t,n=-4112633980346984e-24+n*t,n=-29070369957882005e-24+n*t,n=42347877827932404e-23+n*t,n=-13654692000834679e-22+n*t,n=-13882523362786469e-21+n*t,n=.00018673420803405714+n*t,n=-.000740702534166267+n*t,n=-.006033670871430149+n*t,n=.24015818242558962+n*t,n=1.6536545626831027+n*t):t<16?(t=Math.sqrt(t)-3.25,n=22137376921775787e-25,n=9075656193888539e-23+n*t,n=-27517406297064545e-23+n*t,n=18239629214389228e-24+n*t,n=15027403968909828e-22+n*t,n=-4013867526981546e-21+n*t,n=29234449089955446e-22+n*t,n=12475304481671779e-21+n*t,n=-47318229009055734e-21+n*t,n=6828485145957318e-20+n*t,n=24031110387097894e-21+n*t,n=-.0003550375203628475+n*t,n=.0009532893797373805+n*t,n=-.0016882755560235047+n*t,n=.002491442096107851+n*t,n=-.003751208507569241+n*t,n=.005370914553590064+n*t,n=1.0052589676941592+n*t,n=3.0838856104922208+n*t):Number.isFinite(t)?(t=Math.sqrt(t)-5,n=-27109920616438573e-27,n=-2555641816996525e-25+n*t,n=15076572693500548e-25+n*t,n=-3789465440126737e-24+n*t,n=761570120807834e-23+n*t,n=-1496002662714924e-23+n*t,n=2914795345090108e-23+n*t,n=-6771199775845234e-23+n*t,n=22900482228026655e-23+n*t,n=-99298272942317e-20+n*t,n=4526062597223154e-21+n*t,n=-1968177810553167e-20+n*t,n=7599527703001776e-20+n*t,n=-.00021503011930044477+n*t,n=-.00013871931833623122+n*t,n=1.0103004648645344+n*t,n=4.849906401408584+n*t):n=1/0,n*e}function y0(e,t){let n,r;const i={mean(a){return arguments.length?(n=a||0,i):n},stdev(a){return arguments.length?(r=a??1,i):r},sample:()=>Bu(n,r),pdf:a=>Sl(a,n,r),cdf:a=>_u(a,n,r),icdf:a=>Ru(a,n,r)};return i.mean(e).stdev(t)}function v0(e,t){const n=y0();let r=0;const i={data(a){return arguments.length?(e=a,r=a?a.length:0,i.bandwidth(t)):e},bandwidth(a){return arguments.length?(t=a,!t&&e&&(t=m0(e)),i):t},sample(){return e[~~(zt()*r)]+t*n.sample()},pdf(a){let u=0,o=0;for(;o<r;++o)u+=n.pdf((a-e[o])/t);return u/t/r},cdf(a){let u=0,o=0;for(;o<r;++o)u+=n.cdf((a-e[o])/t);return u/r},icdf(){throw Error("KDE icdf not supported.")}};return i.data(e)}function $l(e,t){return e=e||0,t=t??1,Math.exp(e+Bu()*t)}function Bl(e,t,n){if(e<=0)return 0;t=t||0,n=n??1;const r=(Math.log(e)-t)/n;return Math.exp(-.5*r*r)/(n*tb*e)}function _l(e,t,n){return _u(Math.log(e),t,n)}function Rl(e,t,n){return Math.exp(Ru(e,t,n))}function nb(e,t){let n,r;const i={mean(a){return arguments.length?(n=a||0,i):n},stdev(a){return arguments.length?(r=a??1,i):r},sample:()=>$l(n,r),pdf:a=>Bl(a,n,r),cdf:a=>_l(a,n,r),icdf:a=>Rl(a,n,r)};return i.mean(e).stdev(t)}function rb(e,t){let n=0,r;function i(u){const o=[];let s=0,l;for(l=0;l<n;++l)s+=o[l]=u[l]==null?1:+u[l];for(l=0;l<n;++l)o[l]/=s;return o}const a={weights(u){return arguments.length?(r=i(t=u||[]),a):t},distributions(u){return arguments.length?(u?(n=u.length,e=u):(n=0,e=[]),a.weights(t)):e},sample(){const u=zt();let o=e[n-1],s=r[0],l=0;for(;l<n-1;s+=r[++l])if(u<s){o=e[l];break}return o.sample()},pdf(u){let o=0,s=0;for(;s<n;++s)o+=r[s]*e[s].pdf(u);return o},cdf(u){let o=0,s=0;for(;s<n;++s)o+=r[s]*e[s].cdf(u);return o},icdf(){throw Error("Mixture icdf not supported.")}};return a.distributions(e).weights(t)}function Ol(e,t){return t==null&&(t=e??1,e=0),e+(t-e)*zt()}function Tl(e,t,n){return n==null&&(n=t??1,t=0),e>=t&&e<=n?1/(n-t):0}function Ll(e,t,n){return n==null&&(n=t??1,t=0),e<t?0:e>n?1:(e-t)/(n-t)}function Nl(e,t,n){return n==null&&(n=t??1,t=0),e>=0&&e<=1?t+e*(n-t):NaN}function ib(e,t){let n,r;const i={min(a){return arguments.length?(n=a||0,i):n},max(a){return arguments.length?(r=a??1,i):r},sample:()=>Ol(n,r),pdf:a=>Tl(a,n,r),cdf:a=>Ll(a,n,r),icdf:a=>Nl(a,n,r)};return t==null&&(t=e??1,e=0),i.min(e).max(t)}function Ou(e,t,n,r){const i=r-e*e,a=Math.abs(i)<1e-24?0:(n-e*t)/i;return[t-a*e,a]}function zl(e,t,n,r){e=e.filter(h=>{let g=t(h),p=n(h);return g!=null&&(g=+g)>=g&&p!=null&&(p=+p)>=p}),r&&e.sort((h,g)=>t(h)-t(g));const i=e.length,a=new Float64Array(i),u=new Float64Array(i);let o=0,s=0,l=0,f,c,d;for(d of e)a[o]=f=+t(d),u[o]=c=+n(d),++o,s+=(f-s)/o,l+=(c-l)/o;for(o=0;o<i;++o)a[o]-=s,u[o]-=l;return[a,u,s,l]}function Tu(e,t,n,r){let i=-1,a,u;for(const o of e)a=t(o),u=n(o),a!=null&&(a=+a)>=a&&u!=null&&(u=+u)>=u&&r(a,u,++i)}function ta(e,t,n,r,i){let a=0,u=0;return Tu(e,t,n,(o,s)=>{const l=s-i(o),f=s-r;a+=l*l,u+=f*f}),1-a/u}function x0(e,t,n){let r=0,i=0,a=0,u=0,o=0;Tu(e,t,n,(f,c)=>{++o,r+=(f-r)/o,i+=(c-i)/o,a+=(f*c-a)/o,u+=(f*f-u)/o});const s=Ou(r,i,a,u),l=f=>s[0]+s[1]*f;return{coef:s,predict:l,rSquared:ta(e,t,n,i,l)}}function ab(e,t,n){let r=0,i=0,a=0,u=0,o=0;Tu(e,t,n,(f,c)=>{++o,f=Math.log(f),r+=(f-r)/o,i+=(c-i)/o,a+=(f*c-a)/o,u+=(f*f-u)/o});const s=Ou(r,i,a,u),l=f=>s[0]+s[1]*Math.log(f);return{coef:s,predict:l,rSquared:ta(e,t,n,i,l)}}function ub(e,t,n){const[r,i,a,u]=zl(e,t,n);let o=0,s=0,l=0,f=0,c=0,d,h,g;Tu(e,t,n,(v,x)=>{d=r[c++],h=Math.log(x),g=d*x,o+=(x*h-o)/c,s+=(g-s)/c,l+=(g*h-l)/c,f+=(d*g-f)/c});const[p,m]=Ou(s/u,o/u,l/u,f/u),y=v=>Math.exp(p+m*(v-a));return{coef:[Math.exp(p-m*a),m],predict:y,rSquared:ta(e,t,n,u,y)}}function ob(e,t,n){let r=0,i=0,a=0,u=0,o=0,s=0;Tu(e,t,n,(c,d)=>{const h=Math.log(c),g=Math.log(d);++s,r+=(h-r)/s,i+=(g-i)/s,a+=(h*g-a)/s,u+=(h*h-u)/s,o+=(d-o)/s});const l=Ou(r,i,a,u),f=c=>l[0]*Math.pow(c,l[1]);return l[0]=Math.exp(l[0]),{coef:l,predict:f,rSquared:ta(e,t,n,o,f)}}function b0(e,t,n){const[r,i,a,u]=zl(e,t,n),o=r.length;let s=0,l=0,f=0,c=0,d=0,h,g,p,m;for(h=0;h<o;)g=r[h],p=i[h++],m=g*g,s+=(m-s)/h,l+=(m*g-l)/h,f+=(m*m-f)/h,c+=(g*p-c)/h,d+=(m*p-d)/h;const y=f-s*s,v=s*y-l*l,x=(d*s-c*l)/v,b=(c*y-d*l)/v,E=-x*s,w=A=>(A=A-a,x*A*A+b*A+E+u);return{coef:[E-b*a+x*a*a+u,b-2*x*a,x],predict:w,rSquared:ta(e,t,n,u,w)}}function sb(e,t,n,r){if(r===1)return x0(e,t,n);if(r===2)return b0(e,t,n);const[i,a,u,o]=zl(e,t,n),s=i.length,l=[],f=[],c=r+1;let d,h,g,p,m;for(d=0;d<c;++d){for(g=0,p=0;g<s;++g)p+=Math.pow(i[g],d)*a[g];for(l.push(p),m=new Float64Array(c),h=0;h<c;++h){for(g=0,p=0;g<s;++g)p+=Math.pow(i[g],d+h);m[h]=p}f.push(m)}f.push(l);const y=U9(f),v=x=>{x-=u;let b=o+y[0]+y[1]*x+y[2]*x*x;for(d=3;d<c;++d)b+=y[d]*Math.pow(x,d);return b};return{coef:I9(c,y,-u,o),predict:v,rSquared:ta(e,t,n,o,v)}}function I9(e,t,n,r){const i=Array(e);let a,u,o,s;for(a=0;a<e;++a)i[a]=0;for(a=e-1;a>=0;--a)for(o=t[a],s=1,i[a]+=o,u=1;u<=a;++u)s*=(a+1-u)/u,i[a-u]+=o*Math.pow(n,u)*s;return i[0]+=r,i}function U9(e){const t=e.length-1,n=[];let r,i,a,u,o;for(r=0;r<t;++r){for(u=r,i=r+1;i<t;++i)Math.abs(e[r][i])>Math.abs(e[r][u])&&(u=i);for(a=r;a<t+1;++a)o=e[a][r],e[a][r]=e[a][u],e[a][u]=o;for(i=r+1;i<t;++i)for(a=t;a>=r;a--)e[a][i]-=e[a][r]*e[r][i]/e[r][r]}for(i=t-1;i>=0;--i){for(o=0,a=i+1;a<t;++a)o+=e[a][i]*n[a];n[i]=(e[t][i]-o)/e[i][i]}return n}const bm=2,Am=1e-12;function lb(e,t,n,r){const[i,a,u,o]=zl(e,t,n,!0),s=i.length,l=Math.max(2,~~(r*s)),f=new Float64Array(s),c=new Float64Array(s),d=new Float64Array(s).fill(1);for(let h=-1;++h<=bm;){const g=[0,l-1];for(let m=0;m<s;++m){const y=i[m],v=g[0],x=g[1],b=y-i[v]>i[x]-y?v:x;let E=0,w=0,A=0,D=0,C=0;const F=1/Math.abs(i[b]-y||1);for(let L=v;L<=x;++L){const N=i[L],k=a[L],R=q9(Math.abs(y-N)*F)*d[L],j=N*R;E+=R,w+=j,A+=k*R,D+=k*j,C+=N*j}const[M,O]=Ou(w/E,A/E,D/E,C/E);f[m]=M+O*y,c[m]=Math.abs(a[m]-f[m]),j9(i,m+1,g)}if(h===bm)break;const p=z2(c);if(Math.abs(p)<Am)break;for(let m=0,y,v;m<s;++m)y=c[m]/(6*p),d[m]=y>=1?Am:(v=1-y*y)*v}return G9(i,f,u,o)}function q9(e){return(e=1-e*e*e)*e*e}function j9(e,t,n){const r=e[t];let i=n[0],a=n[1]+1;if(!(a>=e.length))for(;t>i&&e[a]-r<=r-e[i];)n[0]=++i,n[1]=a,++a}function G9(e,t,n,r){const i=e.length,a=[];let u=0,o=0,s=[],l;for(;u<i;++u)l=e[u]+n,s[0]===l?s[1]+=(t[u]-s[1])/++o:(o=0,s[1]+=r,s=[l,t[u]],a.push(s));return s[1]+=r,a}const W9=.5*Math.PI/180;function Pl(e,t,n,r){n=n||25,r=Math.max(n,r||200);const i=p=>[p,e(p)],a=t[0],u=t[1],o=u-a,s=o/r,l=[i(a)],f=[];if(n===r){for(let p=1;p<r;++p)l.push(i(a+p/n*o));return l.push(i(u)),l}else{f.push(i(u));for(let p=n;--p>0;)f.push(i(a+p/n*o))}let c=l[0],d=f[f.length-1];const h=1/o,g=Y9(c[1],f);for(;d;){const p=i((c[0]+d[0])/2);p[0]-c[0]>=s&&H9(c,p,d,h,g)>W9?f.push(p):(c=d,l.push(d),f.pop()),d=f[f.length-1]}return l}function Y9(e,t){let n=e,r=e;const i=t.length;for(let a=0;a<i;++a){const u=t[a][1];u<n&&(n=u),u>r&&(r=u)}return 1/(r-n)}function H9(e,t,n,r,i){const a=Math.atan2(i*(n[1]-e[1]),r*(n[0]-e[0])),u=Math.atan2(i*(t[1]-e[1]),r*(t[0]-e[0]));return Math.abs(a-u)}function X9(e){return t=>{const n=e.length;let r=1,i=String(e[0](t));for(;r<n;++r)i+="|"+e[r](t);return i}}function ud(e){return!e||!e.length?function(){return""}:e.length===1?e[0]:X9(e)}function fb(e,t,n){return n||e+(t?"_"+t:"")}const If=()=>{},V9={init:If,add:If,rem:If,idx:0},fu={values:{init:e=>e.cell.store=!0,value:e=>e.cell.data.values(),idx:-1},count:{value:e=>e.cell.num},__count__:{value:e=>e.missing+e.valid},missing:{value:e=>e.missing},valid:{value:e=>e.valid},sum:{init:e=>e.sum=0,value:e=>e.sum,add:(e,t)=>e.sum+=+t,rem:(e,t)=>e.sum-=t},product:{init:e=>e.product=1,value:e=>e.valid?e.product:void 0,add:(e,t)=>e.product*=t,rem:(e,t)=>e.product/=t},mean:{init:e=>e.mean=0,value:e=>e.valid?e.mean:void 0,add:(e,t)=>(e.mean_d=t-e.mean,e.mean+=e.mean_d/e.valid),rem:(e,t)=>(e.mean_d=t-e.mean,e.mean-=e.valid?e.mean_d/e.valid:e.mean)},average:{value:e=>e.valid?e.mean:void 0,req:["mean"],idx:1},variance:{init:e=>e.dev=0,value:e=>e.valid>1?e.dev/(e.valid-1):void 0,add:(e,t)=>e.dev+=e.mean_d*(t-e.mean),rem:(e,t)=>e.dev-=e.mean_d*(t-e.mean),req:["mean"],idx:1},variancep:{value:e=>e.valid>1?e.dev/e.valid:void 0,req:["variance"],idx:2},stdev:{value:e=>e.valid>1?Math.sqrt(e.dev/(e.valid-1)):void 0,req:["variance"],idx:2},stdevp:{value:e=>e.valid>1?Math.sqrt(e.dev/e.valid):void 0,req:["variance"],idx:2},stderr:{value:e=>e.valid>1?Math.sqrt(e.dev/(e.valid*(e.valid-1))):void 0,req:["variance"],idx:2},distinct:{value:e=>e.cell.data.distinct(e.get),req:["values"],idx:3},ci0:{value:e=>e.cell.data.ci0(e.get),req:["values"],idx:3},ci1:{value:e=>e.cell.data.ci1(e.get),req:["values"],idx:3},median:{value:e=>e.cell.data.q2(e.get),req:["values"],idx:3},q1:{value:e=>e.cell.data.q1(e.get),req:["values"],idx:3},q3:{value:e=>e.cell.data.q3(e.get),req:["values"],idx:3},min:{init:e=>e.min=void 0,value:e=>e.min=Number.isNaN(e.min)?e.cell.data.min(e.get):e.min,add:(e,t)=>{(t<e.min||e.min===void 0)&&(e.min=t)},rem:(e,t)=>{t<=e.min&&(e.min=NaN)},req:["values"],idx:4},max:{init:e=>e.max=void 0,value:e=>e.max=Number.isNaN(e.max)?e.cell.data.max(e.get):e.max,add:(e,t)=>{(t>e.max||e.max===void 0)&&(e.max=t)},rem:(e,t)=>{t>=e.max&&(e.max=NaN)},req:["values"],idx:4},argmin:{init:e=>e.argmin=void 0,value:e=>e.argmin||e.cell.data.argmin(e.get),add:(e,t,n)=>{t<e.min&&(e.argmin=n)},rem:(e,t)=>{t<=e.min&&(e.argmin=void 0)},req:["min","values"],idx:3},argmax:{init:e=>e.argmax=void 0,value:e=>e.argmax||e.cell.data.argmax(e.get),add:(e,t,n)=>{t>e.max&&(e.argmax=n)},rem:(e,t)=>{t>=e.max&&(e.argmax=void 0)},req:["max","values"],idx:3}},Lu=Object.keys(fu).filter(e=>e!=="__count__");function K9(e,t){return n=>Z({name:e,out:n||e},V9,t)}[...Lu,"__count__"].forEach(e=>{fu[e]=K9(e,fu[e])});function cb(e,t){return fu[e](t)}function db(e,t){return e.idx-t.idx}function J9(e){const t={};e.forEach(r=>t[r.name]=r);const n=r=>{r.req&&r.req.forEach(i=>{t[i]||n(t[i]=fu[i]())})};return e.forEach(n),Object.values(t).sort(db)}function Q9(){this.valid=0,this.missing=0,this._ops.forEach(e=>e.init(this))}function Z9(e,t){if(e==null||e===""){++this.missing;return}e===e&&(++this.valid,this._ops.forEach(n=>n.add(this,e,t)))}function ek(e,t){if(e==null||e===""){--this.missing;return}e===e&&(--this.valid,this._ops.forEach(n=>n.rem(this,e,t)))}function tk(e){return this._out.forEach(t=>e[t.out]=t.value(this)),e}function hb(e,t){const n=t||et,r=J9(e),i=e.slice().sort(db);function a(u){this._ops=r,this._out=i,this.cell=u,this.init()}return a.prototype.init=Q9,a.prototype.add=Z9,a.prototype.rem=ek,a.prototype.set=tk,a.prototype.get=n,a.fields=e.map(u=>u.out),a}function A0(e){this._key=e?Ot(e):Y,this.reset()}const qe=A0.prototype;qe.reset=function(){this._add=[],this._rem=[],this._ext=null,this._get=null,this._q=null};qe.add=function(e){this._add.push(e)};qe.rem=function(e){this._rem.push(e)};qe.values=function(){if(this._get=null,this._rem.length===0)return this._add;const e=this._add,t=this._rem,n=this._key,r=e.length,i=t.length,a=Array(r-i),u={};let o,s,l;for(o=0;o<i;++o)u[n(t[o])]=1;for(o=0,s=0;o<r;++o)u[n(l=e[o])]?u[n(l)]=0:a[s++]=l;return this._rem=[],this._add=a};qe.distinct=function(e){const t=this.values(),n={};let r=t.length,i=0,a;for(;--r>=0;)a=e(t[r])+"",G(n,a)||(n[a]=1,++i);return i};qe.extent=function(e){if(this._get!==e||!this._ext){const t=this.values(),n=px(t,e);this._ext=[t[n[0]],t[n[1]]],this._get=e}return this._ext};qe.argmin=function(e){return this.extent(e)[0]||{}};qe.argmax=function(e){return this.extent(e)[1]||{}};qe.min=function(e){const t=this.extent(e)[0];return t!=null?e(t):void 0};qe.max=function(e){const t=this.extent(e)[1];return t!=null?e(t):void 0};qe.quartile=function(e){return(this._get!==e||!this._q)&&(this._q=p0(this.values(),e),this._get=e),this._q};qe.q1=function(e){return this.quartile(e)[0]};qe.q2=function(e){return this.quartile(e)[1]};qe.q3=function(e){return this.quartile(e)[2]};qe.ci=function(e){return(this._get!==e||!this._ci)&&(this._ci=Zx(this.values(),1e3,.05,e),this._get=e),this._ci};qe.ci0=function(e){return this.ci(e)[0]};qe.ci1=function(e){return this.ci(e)[1]};function yr(e){$.call(this,null,e),this._adds=[],this._mods=[],this._alen=0,this._mlen=0,this._drop=!0,this._cross=!1,this._dims=[],this._dnames=[],this._measures=[],this._countOnly=!1,this._counts=null,this._prev=null,this._inputs=null,this._outputs=null}yr.Definition={type:"Aggregate",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"ops",type:"enum",array:!0,values:Lu},{name:"fields",type:"field",null:!0,array:!0},{name:"as",type:"string",null:!0,array:!0},{name:"drop",type:"boolean",default:!0},{name:"cross",type:"boolean",default:!1},{name:"key",type:"field"}]};T(yr,$,{transform(e,t){const n=this,r=t.fork(t.NO_SOURCE|t.NO_FIELDS),i=e.modified();return n.stamp=r.stamp,n.value&&(i||t.modified(n._inputs,!0))?(n._prev=n.value,n.value=i?n.init(e):Object.create(null),t.visit(t.SOURCE,a=>n.add(a))):(n.value=n.value||n.init(e),t.visit(t.REM,a=>n.rem(a)),t.visit(t.ADD,a=>n.add(a))),r.modifies(n._outputs),n._drop=e.drop!==!1,e.cross&&n._dims.length>1&&(n._drop=!1,n.cross()),t.clean()&&n._drop&&r.clean(!0).runAfter(()=>this.clean()),n.changes(r)},cross(){const e=this,t=e.value,n=e._dnames,r=n.map(()=>({})),i=n.length;function a(o){let s,l,f,c;for(s in o)for(f=o[s].tuple,l=0;l<i;++l)r[l][c=f[n[l]]]=c}a(e._prev),a(t);function u(o,s,l){const f=n[l],c=r[l++];for(const d in c){const h=o?o+"|"+d:d;s[f]=c[d],l<i?u(h,s,l):t[h]||e.cell(h,s)}}u("",{},0)},init(e){const t=this._inputs=[],n=this._outputs=[],r={};function i(m){const y=q(Xe(m)),v=y.length;let x=0,b;for(;x<v;++x)r[b=y[x]]||(r[b]=1,t.push(b))}this._dims=q(e.groupby),this._dnames=this._dims.map(m=>{const y=Ee(m);return i(m),n.push(y),y}),this.cellkey=e.key?e.key:ud(this._dims),this._countOnly=!0,this._counts=[],this._measures=[];const a=e.fields||[null],u=e.ops||["count"],o=e.as||[],s=a.length,l={};let f,c,d,h,g,p;for(s!==u.length&&S("Unmatched number of fields and aggregate ops."),p=0;p<s;++p){if(f=a[p],c=u[p],f==null&&c!=="count"&&S("Null aggregate field specified."),h=Ee(f),g=fb(c,h,o[p]),n.push(g),c==="count"){this._counts.push(g);continue}d=l[h],d||(i(f),d=l[h]=[],d.field=f,this._measures.push(d)),c!=="count"&&(this._countOnly=!1),d.push(cb(c,g))}return this._measures=this._measures.map(m=>hb(m,m.field)),Object.create(null)},cellkey:ud(),cell(e,t){let n=this.value[e];return n?n.num===0&&this._drop&&n.stamp<this.stamp?(n.stamp=this.stamp,this._adds[this._alen++]=n):n.stamp<this.stamp&&(n.stamp=this.stamp,this._mods[this._mlen++]=n):(n=this.value[e]=this.newcell(e,t),this._adds[this._alen++]=n),n},newcell(e,t){const n={key:e,num:0,agg:null,tuple:this.newtuple(t,this._prev&&this._prev[e]),stamp:this.stamp,store:!1};if(!this._countOnly){const r=this._measures,i=r.length;n.agg=Array(i);for(let a=0;a<i;++a)n.agg[a]=new r[a](n)}return n.store&&(n.data=new A0),n},newtuple(e,t){const n=this._dnames,r=this._dims,i=r.length,a={};for(let u=0;u<i;++u)a[n[u]]=r[u](e);return t?Wx(t.tuple,a):le(a)},clean(){const e=this.value;for(const t in e)e[t].num===0&&delete e[t]},add(e){const t=this.cellkey(e),n=this.cell(t,e);if(n.num+=1,this._countOnly)return;n.store&&n.data.add(e);const r=n.agg;for(let i=0,a=r.length;i<a;++i)r[i].add(r[i].get(e),e)},rem(e){const t=this.cellkey(e),n=this.cell(t,e);if(n.num-=1,this._countOnly)return;n.store&&n.data.rem(e);const r=n.agg;for(let i=0,a=r.length;i<a;++i)r[i].rem(r[i].get(e),e)},celltuple(e){const t=e.tuple,n=this._counts;e.store&&e.data.values();for(let r=0,i=n.length;r<i;++r)t[n[r]]=e.num;if(!this._countOnly){const r=e.agg;for(let i=0,a=r.length;i<a;++i)r[i].set(t)}return t},changes(e){const t=this._adds,n=this._mods,r=this._prev,i=this._drop,a=e.add,u=e.rem,o=e.mod;let s,l,f,c;if(r)for(l in r)s=r[l],(!i||s.num)&&u.push(s.tuple);for(f=0,c=this._alen;f<c;++f)a.push(this.celltuple(t[f])),t[f]=null;for(f=0,c=this._mlen;f<c;++f)s=n[f],(s.num===0&&i?u:o).push(this.celltuple(s)),n[f]=null;return this._alen=this._mlen=0,this._prev=null,e}});const nk=1e-14;function E0(e){$.call(this,null,e)}E0.Definition={type:"Bin",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"interval",type:"boolean",default:!0},{name:"anchor",type:"number"},{name:"maxbins",type:"number",default:20},{name:"base",type:"number",default:10},{name:"divide",type:"number",array:!0,default:[5,2]},{name:"extent",type:"number",array:!0,length:2,required:!0},{name:"span",type:"number"},{name:"step",type:"number"},{name:"steps",type:"number",array:!0},{name:"minstep",type:"number",default:0},{name:"nice",type:"boolean",default:!0},{name:"name",type:"string"},{name:"as",type:"string",array:!0,length:2,default:["bin0","bin1"]}]};T(E0,$,{transform(e,t){const n=e.interval!==!1,r=this._bins(e),i=r.start,a=r.step,u=e.as||["bin0","bin1"],o=u[0],s=u[1];let l;return e.modified()?(t=t.reflow(!0),l=t.SOURCE):l=t.modified(Xe(e.field))?t.ADD_MOD:t.ADD,t.visit(l,n?f=>{const c=r(f);f[o]=c,f[s]=c==null?null:i+a*(1+(c-i)/a)}:f=>f[o]=r(f)),t.modifies(n?u:o)},_bins(e){if(this.value&&!e.modified())return this.value;const t=e.field,n=Qx(e),r=n.step;let i=n.start,a=i+Math.ceil((n.stop-i)/r)*r,u,o;(u=e.anchor)!=null&&(o=u-(i+r*Math.floor((u-i)/r)),i+=o,a+=o);const s=function(l){let f=Ne(t(l));return f==null?null:f<i?-1/0:f>a?1/0:(f=Math.max(i,Math.min(f,a-r)),i+r*Math.floor(nk+(f-i)/r))};return s.start=i,s.stop=n.stop,s.step=r,this.value=Mt(s,Xe(t),e.name||"bin_"+Ee(t))}});function gb(e,t,n){const r=e;let i=t||[],a=n||[],u={},o=0;return{add:s=>a.push(s),remove:s=>u[r(s)]=++o,size:()=>i.length,data:(s,l)=>(o&&(i=i.filter(f=>!u[r(f)]),u={},o=0),l&&s&&i.sort(s),a.length&&(i=s?vx(s,i,a.sort(s)):i.concat(a),a=[]),i)}}function w0(e){$.call(this,[],e)}w0.Definition={type:"Collect",metadata:{source:!0},params:[{name:"sort",type:"compare"}]};T(w0,$,{transform(e,t){const n=t.fork(t.ALL),r=gb(Y,this.value,n.materialize(n.ADD).add),i=e.sort,a=t.changed()||i&&(e.modified("sort")||t.modified(i.fields));return n.visit(n.REM,r.remove),this.modified(a),this.value=n.source=r.data(ai(i),a),t.source&&t.source.root&&(this.value.root=t.source.root),n}});function pb(e){ge.call(this,null,rk,e)}T(pb,ge);function rk(e){return this.value&&!e.modified()?this.value:Ih(e.fields,e.orders)}function D0(e){$.call(this,null,e)}D0.Definition={type:"CountPattern",metadata:{generates:!0,changes:!0},params:[{name:"field",type:"field",required:!0},{name:"case",type:"enum",values:["upper","lower","mixed"],default:"mixed"},{name:"pattern",type:"string",default:'[\\w"]+'},{name:"stopwords",type:"string",default:""},{name:"as",type:"string",array:!0,length:2,default:["text","count"]}]};function ik(e,t,n){switch(t){case"upper":e=e.toUpperCase();break;case"lower":e=e.toLowerCase();break}return e.match(n)}T(D0,$,{transform(e,t){const n=c=>d=>{for(var h=ik(o(d),e.case,a)||[],g,p=0,m=h.length;p<m;++p)u.test(g=h[p])||c(g)},r=this._parameterCheck(e,t),i=this._counts,a=this._match,u=this._stop,o=e.field,s=e.as||["text","count"],l=n(c=>i[c]=1+(i[c]||0)),f=n(c=>i[c]-=1);return r?t.visit(t.SOURCE,l):(t.visit(t.ADD,l),t.visit(t.REM,f)),this._finish(t,s)},_parameterCheck(e,t){let n=!1;return(e.modified("stopwords")||!this._stop)&&(this._stop=new RegExp("^"+(e.stopwords||"")+"$","i"),n=!0),(e.modified("pattern")||!this._match)&&(this._match=new RegExp(e.pattern||"[\\w']+","g"),n=!0),(e.modified("field")||t.modified(e.field.fields))&&(n=!0),n&&(this._counts={}),n},_finish(e,t){const n=this._counts,r=this._tuples||(this._tuples={}),i=t[0],a=t[1],u=e.fork(e.NO_SOURCE|e.NO_FIELDS);let o,s,l;for(o in n)s=r[o],l=n[o]||0,!s&&l?(r[o]=s=le({}),s[i]=o,s[a]=l,u.add.push(s)):l===0?(s&&u.rem.push(s),n[o]=null,r[o]=null):s[a]!==l&&(s[a]=l,u.mod.push(s));return u.modifies(t)}});function C0(e){$.call(this,null,e)}C0.Definition={type:"Cross",metadata:{generates:!0},params:[{name:"filter",type:"expr"},{name:"as",type:"string",array:!0,length:2,default:["a","b"]}]};T(C0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE),r=e.as||["a","b"],i=r[0],a=r[1],u=!this.value||t.changed(t.ADD_REM)||e.modified("as")||e.modified("filter");let o=this.value;return u?(o&&(n.rem=o),o=t.materialize(t.SOURCE).source,n.add=this.value=ak(o,i,a,e.filter||Ct)):n.mod=o,n.source=this.value,n.modifies(r)}});function ak(e,t,n,r){for(var i=[],a={},u=e.length,o=0,s,l;o<u;++o)for(a[t]=l=e[o],s=0;s<u;++s)a[n]=e[s],r(a)&&(i.push(le(a)),a={},a[t]=l);return i}const Em={kde:v0,mixture:rb,normal:y0,lognormal:nb,uniform:ib},uk="distributions",wm="function",ok="field";function mb(e,t){const n=e[wm];G(Em,n)||S("Unknown distribution function: "+n);const r=Em[n]();for(const i in e)i===ok?r.data((e.from||t()).map(e[i])):i===uk?r[i](e[i].map(a=>mb(a,t))):typeof r[i]===wm&&r[i](e[i]);return r}function F0(e){$.call(this,null,e)}const yb=[{key:{function:"normal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"lognormal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"uniform"},params:[{name:"min",type:"number",default:0},{name:"max",type:"number",default:1}]},{key:{function:"kde"},params:[{name:"field",type:"field",required:!0},{name:"from",type:"data"},{name:"bandwidth",type:"number",default:0}]}],sk={key:{function:"mixture"},params:[{name:"distributions",type:"param",array:!0,params:yb},{name:"weights",type:"number",array:!0}]};F0.Definition={type:"Density",metadata:{generates:!0},params:[{name:"extent",type:"number",array:!0,length:2},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"method",type:"string",default:"pdf",values:["pdf","cdf"]},{name:"distribution",type:"param",params:yb.concat(sk)},{name:"as",type:"string",array:!0,default:["value","density"]}]};T(F0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE|t.NO_FIELDS);if(!this.value||t.changed()||e.modified()){const r=mb(e.distribution,lk(t)),i=e.steps||e.minsteps||25,a=e.steps||e.maxsteps||200;let u=e.method||"pdf";u!=="pdf"&&u!=="cdf"&&S("Invalid density method: "+u),!e.extent&&!r.data&&S("Missing density extent parameter."),u=r[u];const o=e.as||["value","density"],s=e.extent||ln(r.data()),l=Pl(u,s,i,a).map(f=>{const c={};return c[o[0]]=f[0],c[o[1]]=f[1],le(c)});this.value&&(n.rem=this.value),this.value=n.add=n.source=l}return n}});function lk(e){return()=>e.materialize(e.SOURCE).source}function vb(e,t){return e?e.map((n,r)=>t[r]||Ee(n)):null}function k0(e,t,n){const r=[],i=c=>c(s);let a,u,o,s,l,f;if(t==null)r.push(e.map(n));else for(a={},u=0,o=e.length;u<o;++u)s=e[u],l=t.map(i),f=a[l],f||(a[l]=f=[],f.dims=l,r.push(f)),f.push(n(s));return r}const xb="bin";function M0(e){$.call(this,null,e)}M0.Definition={type:"DotBin",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"step",type:"number"},{name:"smooth",type:"boolean",default:!1},{name:"as",type:"string",default:xb}]};const fk=(e,t)=>Ji(ln(e,t))/30;T(M0,$,{transform(e,t){if(this.value&&!(e.modified()||t.changed()))return t;const n=t.materialize(t.SOURCE).source,r=k0(t.source,e.groupby,et),i=e.smooth||!1,a=e.field,u=e.step||fk(n,a),o=ai((g,p)=>a(g)-a(p)),s=e.as||xb,l=r.length;let f=1/0,c=-1/0,d=0,h;for(;d<l;++d){const g=r[d].sort(o);h=-1;for(const p of eb(g,u,i,a))p<f&&(f=p),p>c&&(c=p),g[++h][s]=p}return this.value={start:f,stop:c,step:u},t.reflow(!0).modifies(s)}});function bb(e){ge.call(this,null,ck,e),this.modified(!0)}T(bb,ge);function ck(e){const t=e.expr;return this.value&&!e.modified("expr")?this.value:Mt(n=>t(n,e),Xe(t),Ee(t))}function S0(e){$.call(this,[void 0,void 0],e)}S0.Definition={type:"Extent",metadata:{},params:[{name:"field",type:"field",required:!0}]};T(S0,$,{transform(e,t){const n=this.value,r=e.field,i=t.changed()||t.modified(r.fields)||e.modified("field");let a=n[0],u=n[1];if((i||a==null)&&(a=1/0,u=-1/0),t.visit(i?t.SOURCE:t.ADD,o=>{const s=Ne(r(o));s!=null&&(s<a&&(a=s),s>u&&(u=s))}),!Number.isFinite(a)||!Number.isFinite(u)){let o=Ee(r);o&&(o=` for field "${o}"`),t.dataflow.warn(`Infinite extent${o}: [${a}, ${u}]`),a=u=void 0}this.value=[a,u]}});function $0(e,t){ge.call(this,e),this.parent=t,this.count=0}T($0,ge,{connect(e){return this.detachSubflow=e.detachSubflow,this.targets().add(e),e.source=this},add(e){this.count+=1,this.value.add.push(e)},rem(e){this.count-=1,this.value.rem.push(e)},mod(e){this.value.mod.push(e)},init(e){this.value.init(e,e.NO_SOURCE)},evaluate(){return this.value}});function Il(e){$.call(this,{},e),this._keys=Ki();const t=this._targets=[];t.active=0,t.forEach=n=>{for(let r=0,i=t.active;r<i;++r)n(t[r],r,t)}}T(Il,$,{activate(e){this._targets[this._targets.active++]=e},subflow(e,t,n,r){const i=this.value;let a=G(i,e)&&i[e],u,o;return a?a.value.stamp<n.stamp&&(a.init(n),this.activate(a)):(o=r||(o=this._group[e])&&o.tuple,u=n.dataflow,a=new $0(n.fork(n.NO_SOURCE),this),u.add(a).connect(t(u,e,o)),i[e]=a,this.activate(a)),a},clean(){const e=this.value;let t=0;for(const n in e)if(e[n].count===0){const r=e[n].detachSubflow;r&&r(),delete e[n],++t}if(t){const n=this._targets.filter(r=>r&&r.count>0);this.initTargets(n)}},initTargets(e){const t=this._targets,n=t.length,r=e?e.length:0;let i=0;for(;i<r;++i)t[i]=e[i];for(;i<n&&t[i]!=null;++i)t[i]=null;t.active=r},transform(e,t){const n=t.dataflow,r=e.key,i=e.subflow,a=this._keys,u=e.modified("key"),o=s=>this.subflow(s,i,t);return this._group=e.group||{},this.initTargets(),t.visit(t.REM,s=>{const l=Y(s),f=a.get(l);f!==void 0&&(a.delete(l),o(f).rem(s))}),t.visit(t.ADD,s=>{const l=r(s);a.set(Y(s),l),o(l).add(s)}),u||t.modified(r.fields)?t.visit(t.MOD,s=>{const l=Y(s),f=a.get(l),c=r(s);f===c?o(c).mod(s):(a.set(l,c),o(f).rem(s),o(c).add(s))}):t.changed(t.MOD)&&t.visit(t.MOD,s=>{o(a.get(Y(s))).mod(s)}),u&&t.visit(t.REFLOW,s=>{const l=Y(s),f=a.get(l),c=r(s);f!==c&&(a.set(l,c),o(f).rem(s),o(c).add(s))}),t.clean()?n.runAfter(()=>{this.clean(),a.clean()}):a.empty>n.cleanThreshold&&n.runAfter(a.clean),t}});function Ab(e){ge.call(this,null,dk,e)}T(Ab,ge);function dk(e){return this.value&&!e.modified()?this.value:z(e.name)?q(e.name).map(t=>Ot(t)):Ot(e.name,e.as)}function B0(e){$.call(this,Ki(),e)}B0.Definition={type:"Filter",metadata:{changes:!0},params:[{name:"expr",type:"expr",required:!0}]};T(B0,$,{transform(e,t){const n=t.dataflow,r=this.value,i=t.fork(),a=i.add,u=i.rem,o=i.mod,s=e.expr;let l=!0;t.visit(t.REM,c=>{const d=Y(c);r.has(d)?r.delete(d):u.push(c)}),t.visit(t.ADD,c=>{s(c,e)?a.push(c):r.set(Y(c),1)});function f(c){const d=Y(c),h=s(c,e),g=r.get(d);h&&g?(r.delete(d),a.push(c)):!h&&!g?(r.set(d,1),u.push(c)):l&&h&&!g&&o.push(c)}return t.visit(t.MOD,f),e.modified()&&(l=!1,t.visit(t.REFLOW,f)),r.empty>n.cleanThreshold&&n.runAfter(r.clean),i}});function _0(e){$.call(this,[],e)}_0.Definition={type:"Flatten",metadata:{generates:!0},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"index",type:"string"},{name:"as",type:"string",array:!0}]};T(_0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE),r=e.fields,i=vb(r,e.as||[]),a=e.index||null,u=i.length;return n.rem=this.value,t.visit(t.SOURCE,o=>{const s=r.map(g=>g(o)),l=s.reduce((g,p)=>Math.max(g,p.length),0);let f=0,c,d,h;for(;f<l;++f){for(d=c0(o),c=0;c<u;++c)d[i[c]]=(h=s[c][f])==null?null:h;a&&(d[a]=f),n.add.push(d)}}),this.value=n.source=n.add,a&&n.modifies(a),n.modifies(i)}});function R0(e){$.call(this,[],e)}R0.Definition={type:"Fold",metadata:{generates:!0},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"as",type:"string",array:!0,length:2,default:["key","value"]}]};T(R0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE),r=e.fields,i=r.map(Ee),a=e.as||["key","value"],u=a[0],o=a[1],s=r.length;return n.rem=this.value,t.visit(t.SOURCE,l=>{for(let f=0,c;f<s;++f)c=c0(l),c[u]=i[f],c[o]=r[f](l),n.add.push(c)}),this.value=n.source=n.add,n.modifies(a)}});function O0(e){$.call(this,null,e)}O0.Definition={type:"Formula",metadata:{modifies:!0},params:[{name:"expr",type:"expr",required:!0},{name:"as",type:"string",required:!0},{name:"initonly",type:"boolean"}]};T(O0,$,{transform(e,t){const n=e.expr,r=e.as,i=e.modified(),a=e.initonly?t.ADD:i?t.SOURCE:t.modified(n.fields)||t.modified(r)?t.ADD_MOD:t.ADD;return i&&(t=t.materialize().reflow(!0)),e.initonly||t.modifies(r),t.visit(a,u=>u[r]=n(u,e))}});function Eb(e){$.call(this,[],e)}T(Eb,$,{transform(e,t){const n=t.fork(t.ALL),r=e.generator;let i=this.value,a=e.size-i.length,u,o,s;if(a>0){for(u=[];--a>=0;)u.push(s=le(r(e))),i.push(s);n.add=n.add.length?n.materialize(n.ADD).add.concat(u):u}else o=i.slice(0,-a),n.rem=n.rem.length?n.materialize(n.REM).rem.concat(o):o,i=i.slice(-a);return n.source=this.value=i,n}});const bo={value:"value",median:z2,mean:ZD,min:xc,max:qr},hk=[];function T0(e){$.call(this,[],e)}T0.Definition={type:"Impute",metadata:{changes:!0},params:[{name:"field",type:"field",required:!0},{name:"key",type:"field",required:!0},{name:"keyvals",array:!0},{name:"groupby",type:"field",array:!0},{name:"method",type:"enum",default:"value",values:["value","mean","median","max","min"]},{name:"value",default:0}]};function gk(e){var t=e.method||bo.value,n;if(bo[t]==null)S("Unrecognized imputation method: "+t);else return t===bo.value?(n=e.value!==void 0?e.value:0,()=>n):bo[t]}function pk(e){const t=e.field;return n=>n?t(n):NaN}T(T0,$,{transform(e,t){var n=t.fork(t.ALL),r=gk(e),i=pk(e),a=Ee(e.field),u=Ee(e.key),o=(e.groupby||[]).map(Ee),s=mk(t.source,e.groupby,e.key,e.keyvals),l=[],f=this.value,c=s.domain.length,d,h,g,p,m,y,v,x,b,E;for(m=0,x=s.length;m<x;++m)for(d=s[m],g=d.values,h=NaN,v=0;v<c;++v)if(d[v]==null){for(p=s.domain[v],E={_impute:!0},y=0,b=g.length;y<b;++y)E[o[y]]=g[y];E[u]=p,E[a]=Number.isNaN(h)?h=r(d,i):h,l.push(le(E))}return l.length&&(n.add=n.materialize(n.ADD).add.concat(l)),f.length&&(n.rem=n.materialize(n.REM).rem.concat(f)),this.value=l,n}});function mk(e,t,n,r){var i=y=>y(m),a=[],u=r?r.slice():[],o={},s={},l,f,c,d,h,g,p,m;for(u.forEach((y,v)=>o[y]=v+1),d=0,p=e.length;d<p;++d)m=e[d],g=n(m),h=o[g]||(o[g]=u.push(g)),f=(l=t?t.map(i):hk)+"",(c=s[f])||(c=s[f]=[],a.push(c),c.values=l),c[h-1]=m;return a.domain=u,a}function L0(e){yr.call(this,e)}L0.Definition={type:"JoinAggregate",metadata:{modifies:!0},params:[{name:"groupby",type:"field",array:!0},{name:"fields",type:"field",null:!0,array:!0},{name:"ops",type:"enum",array:!0,values:Lu},{name:"as",type:"string",null:!0,array:!0},{name:"key",type:"field"}]};T(L0,yr,{transform(e,t){const n=this,r=e.modified();let i;return n.value&&(r||t.modified(n._inputs,!0))?(i=n.value=r?n.init(e):{},t.visit(t.SOURCE,a=>n.add(a))):(i=n.value=n.value||this.init(e),t.visit(t.REM,a=>n.rem(a)),t.visit(t.ADD,a=>n.add(a))),n.changes(),t.visit(t.SOURCE,a=>{Z(a,i[n.cellkey(a)].tuple)}),t.reflow(r).modifies(this._outputs)},changes(){const e=this._adds,t=this._mods;let n,r;for(n=0,r=this._alen;n<r;++n)this.celltuple(e[n]),e[n]=null;for(n=0,r=this._mlen;n<r;++n)this.celltuple(t[n]),t[n]=null;this._alen=this._mlen=0}});function N0(e){$.call(this,null,e)}N0.Definition={type:"KDE",metadata:{generates:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"cumulative",type:"boolean",default:!1},{name:"counts",type:"boolean",default:!1},{name:"bandwidth",type:"number",default:0},{name:"extent",type:"number",array:!0,length:2},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"as",type:"string",array:!0,default:["value","density"]}]};T(N0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE|t.NO_FIELDS);if(!this.value||t.changed()||e.modified()){const r=t.materialize(t.SOURCE).source,i=k0(r,e.groupby,e.field),a=(e.groupby||[]).map(Ee),u=e.bandwidth,o=e.cumulative?"cdf":"pdf",s=e.as||["value","density"],l=[];let f=e.extent,c=e.steps||e.minsteps||25,d=e.steps||e.maxsteps||200;o!=="pdf"&&o!=="cdf"&&S("Invalid density method: "+o),e.resolve==="shared"&&(f||(f=ln(r,e.field)),c=d=e.steps||d),i.forEach(h=>{const g=v0(h,u)[o],p=e.counts?h.length:1,m=f||ln(h);Pl(g,m,c,d).forEach(y=>{const v={};for(let x=0;x<a.length;++x)v[a[x]]=h.dims[x];v[s[0]]=y[0],v[s[1]]=y[1]*p,l.push(le(v))})}),this.value&&(n.rem=this.value),this.value=n.add=n.source=l}return n}});function wb(e){ge.call(this,null,yk,e)}T(wb,ge);function yk(e){return this.value&&!e.modified()?this.value:Wh(e.fields,e.flat)}function Db(e){$.call(this,[],e),this._pending=null}T(Db,$,{transform(e,t){const n=t.dataflow;return this._pending?Uf(this,t,this._pending):vk(e)?t.StopPropagation:e.values?Uf(this,t,n.parse(e.values,e.format)):e.async?{async:n.request(e.url,e.format).then(i=>(this._pending=q(i.data),a=>a.touch(this)))}:n.request(e.url,e.format).then(r=>Uf(this,t,q(r.data)))}});function vk(e){return e.modified("async")&&!(e.modified("values")||e.modified("url")||e.modified("format"))}function Uf(e,t,n){n.forEach(le);const r=t.fork(t.NO_FIELDS&t.NO_SOURCE);return r.rem=e.value,e.value=r.source=r.add=n,e._pending=null,r.rem.length&&r.clean(!0),r}function z0(e){$.call(this,{},e)}z0.Definition={type:"Lookup",metadata:{modifies:!0},params:[{name:"index",type:"index",params:[{name:"from",type:"data",required:!0},{name:"key",type:"field",required:!0}]},{name:"values",type:"field",array:!0},{name:"fields",type:"field",array:!0,required:!0},{name:"as",type:"string",array:!0},{name:"default",default:null}]};T(z0,$,{transform(e,t){const n=e.fields,r=e.index,i=e.values,a=e.default==null?null:e.default,u=e.modified(),o=n.length;let s=u?t.SOURCE:t.ADD,l=t,f=e.as,c,d,h;return i?(d=i.length,o>1&&!f&&S('Multi-field lookup requires explicit "as" parameter.'),f&&f.length!==o*d&&S('The "as" parameter has too few output field names.'),f=f||i.map(Ee),c=function(g){for(var p=0,m=0,y,v;p<o;++p)if(v=r.get(n[p](g)),v==null)for(y=0;y<d;++y,++m)g[f[m]]=a;else for(y=0;y<d;++y,++m)g[f[m]]=i[y](v)}):(f||S("Missing output field names."),c=function(g){for(var p=0,m;p<o;++p)m=r.get(n[p](g)),g[f[p]]=m??a}),u?l=t.reflow(!0):(h=n.some(g=>t.modified(g.fields)),s|=h?t.MOD:0),t.visit(s,c),l.modifies(f)}});function Cb(e){ge.call(this,null,xk,e)}T(Cb,ge);function xk(e){if(this.value&&!e.modified())return this.value;const t=e.extents,n=t.length;let r=1/0,i=-1/0,a,u;for(a=0;a<n;++a)u=t[a],u[0]<r&&(r=u[0]),u[1]>i&&(i=u[1]);return[r,i]}function Fb(e){ge.call(this,null,bk,e)}T(Fb,ge);function bk(e){return this.value&&!e.modified()?this.value:e.values.reduce((t,n)=>t.concat(n),[])}function kb(e){$.call(this,null,e)}T(kb,$,{transform(e,t){return this.modified(e.modified()),this.value=e,t.fork(t.NO_SOURCE|t.NO_FIELDS)}});function P0(e){yr.call(this,e)}P0.Definition={type:"Pivot",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"value",type:"field",required:!0},{name:"op",type:"enum",values:Lu,default:"sum"},{name:"limit",type:"number",default:0},{name:"key",type:"field"}]};T(P0,yr,{_transform:yr.prototype.transform,transform(e,t){return this._transform(Ak(e,t),t)}});function Ak(e,t){const n=e.field,r=e.value,i=(e.op==="count"?"__count__":e.op)||"sum",a=Xe(n).concat(Xe(r)),u=wk(n,e.limit||0,t);return t.changed()&&e.set("__pivot__",null,null,!0),{key:e.key,groupby:e.groupby,ops:u.map(()=>i),fields:u.map(o=>Ek(o,n,r,a)),as:u.map(o=>o+""),modified:e.modified.bind(e)}}function Ek(e,t,n,r){return Mt(i=>t(i)===e?n(i):NaN,r,e+"")}function wk(e,t,n){const r={},i=[];return n.visit(n.SOURCE,a=>{const u=e(a);r[u]||(r[u]=1,i.push(u))}),i.sort(xl),t?i.slice(0,t):i}function Mb(e){Il.call(this,e)}T(Mb,Il,{transform(e,t){const n=e.subflow,r=e.field,i=a=>this.subflow(Y(a),n,t,a);return(e.modified("field")||r&&t.modified(Xe(r)))&&S("PreFacet does not support field modification."),this.initTargets(),r?(t.visit(t.MOD,a=>{const u=i(a);r(a).forEach(o=>u.mod(o))}),t.visit(t.ADD,a=>{const u=i(a);r(a).forEach(o=>u.add(le(o)))}),t.visit(t.REM,a=>{const u=i(a);r(a).forEach(o=>u.rem(o))})):(t.visit(t.MOD,a=>i(a).mod(a)),t.visit(t.ADD,a=>i(a).add(a)),t.visit(t.REM,a=>i(a).rem(a))),t.clean()&&t.runAfter(()=>this.clean()),t}});function I0(e){$.call(this,null,e)}I0.Definition={type:"Project",metadata:{generates:!0,changes:!0},params:[{name:"fields",type:"field",array:!0},{name:"as",type:"string",null:!0,array:!0}]};T(I0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE),r=e.fields,i=vb(e.fields,e.as||[]),a=r?(o,s)=>Dk(o,s,r,i):Fl;let u;return this.value?u=this.value:(t=t.addAll(),u=this.value={}),t.visit(t.REM,o=>{const s=Y(o);n.rem.push(u[s]),u[s]=null}),t.visit(t.ADD,o=>{const s=a(o,le({}));u[Y(o)]=s,n.add.push(s)}),t.visit(t.MOD,o=>{n.mod.push(a(o,u[Y(o)]))}),n}});function Dk(e,t,n,r){for(let i=0,a=n.length;i<a;++i)t[r[i]]=n[i](e);return t}function Sb(e){$.call(this,null,e)}T(Sb,$,{transform(e,t){return this.value=e.value,e.modified("value")?t.fork(t.NO_SOURCE|t.NO_FIELDS):t.StopPropagation}});function U0(e){$.call(this,null,e)}U0.Definition={type:"Quantile",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"probs",type:"number",array:!0},{name:"step",type:"number",default:.01},{name:"as",type:"string",array:!0,default:["prob","value"]}]};const Ck=1e-14;T(U0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE|t.NO_FIELDS),r=e.as||["prob","value"];if(this.value&&!e.modified()&&!t.changed())return n.source=this.value,n;const i=t.materialize(t.SOURCE).source,a=k0(i,e.groupby,e.field),u=(e.groupby||[]).map(Ee),o=[],s=e.step||.01,l=e.probs||Et(s/2,1-Ck,s),f=l.length;return a.forEach(c=>{const d=g0(c,l);for(let h=0;h<f;++h){const g={};for(let p=0;p<u.length;++p)g[u[p]]=c.dims[p];g[r[0]]=l[h],g[r[1]]=d[h],o.push(le(g))}}),this.value&&(n.rem=this.value),this.value=n.add=n.source=o,n}});function $b(e){$.call(this,null,e)}T($b,$,{transform(e,t){let n,r;return this.value?r=this.value:(n=t=t.addAll(),r=this.value={}),e.derive&&(n=t.fork(t.NO_SOURCE),t.visit(t.REM,i=>{const a=Y(i);n.rem.push(r[a]),r[a]=null}),t.visit(t.ADD,i=>{const a=c0(i);r[Y(i)]=a,n.add.push(a)}),t.visit(t.MOD,i=>{const a=r[Y(i)];for(const u in i)a[u]=i[u],n.modifies(u);n.mod.push(a)})),n}});function q0(e){$.call(this,[],e),this.count=0}q0.Definition={type:"Sample",metadata:{},params:[{name:"size",type:"number",default:1e3}]};T(q0,$,{transform(e,t){const n=t.fork(t.NO_SOURCE),r=e.modified("size"),i=e.size,a=this.value.reduce((f,c)=>(f[Y(c)]=1,f),{});let u=this.value,o=this.count,s=0;function l(f){let c,d;u.length<i?u.push(f):(d=~~((o+1)*zt()),d<u.length&&d>=s&&(c=u[d],a[Y(c)]&&n.rem.push(c),u[d]=f)),++o}if(t.rem.length&&(t.visit(t.REM,f=>{const c=Y(f);a[c]&&(a[c]=-1,n.rem.push(f)),--o}),u=u.filter(f=>a[Y(f)]!==-1)),(t.rem.length||r)&&u.length<i&&t.source&&(s=o=u.length,t.visit(t.SOURCE,f=>{a[Y(f)]||l(f)}),s=-1),r&&u.length>i){const f=u.length-i;for(let c=0;c<f;++c)a[Y(u[c])]=-1,n.rem.push(u[c]);u=u.slice(f)}return t.mod.length&&t.visit(t.MOD,f=>{a[Y(f)]&&n.mod.push(f)}),t.add.length&&t.visit(t.ADD,l),(t.add.length||s<0)&&(n.add=u.filter(f=>!a[Y(f)])),this.count=o,this.value=n.source=u,n}});function j0(e){$.call(this,null,e)}j0.Definition={type:"Sequence",metadata:{generates:!0,changes:!0},params:[{name:"start",type:"number",required:!0},{name:"stop",type:"number",required:!0},{name:"step",type:"number",default:1},{name:"as",type:"string",default:"data"}]};T(j0,$,{transform(e,t){if(this.value&&!e.modified())return;const n=t.materialize().fork(t.MOD),r=e.as||"data";return n.rem=this.value?t.rem.concat(this.value):t.rem,this.value=Et(e.start,e.stop,e.step||1).map(i=>{const a={};return a[r]=i,le(a)}),n.add=t.add.concat(this.value),n}});function Bb(e){$.call(this,null,e),this.modified(!0)}T(Bb,$,{transform(e,t){return this.value=t.source,t.changed()?t.fork(t.NO_SOURCE|t.NO_FIELDS):t.StopPropagation}});function G0(e){$.call(this,null,e)}const _b=["unit0","unit1"];G0.Definition={type:"TimeUnit",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"interval",type:"boolean",default:!0},{name:"units",type:"enum",values:Vh,array:!0},{name:"step",type:"number",default:1},{name:"maxbins",type:"number",default:40},{name:"extent",type:"date",array:!0},{name:"timezone",type:"enum",default:"local",values:["local","utc"]},{name:"as",type:"string",array:!0,length:2,default:_b}]};T(G0,$,{transform(e,t){const n=e.field,r=e.interval!==!1,i=e.timezone==="utc",a=this._floor(e,t),u=(i?Zi:Qi)(a.unit).offset,o=e.as||_b,s=o[0],l=o[1],f=a.step;let c=a.start||1/0,d=a.stop||-1/0,h=t.ADD;return(e.modified()||t.changed(t.REM)||t.modified(Xe(n)))&&(t=t.reflow(!0),h=t.SOURCE,c=1/0,d=-1/0),t.visit(h,g=>{const p=n(g);let m,y;p==null?(g[s]=null,r&&(g[l]=null)):(g[s]=m=y=a(p),r&&(g[l]=y=u(m,f)),m<c&&(c=m),y>d&&(d=y))}),a.start=c,a.stop=d,t.modifies(r?o:s)},_floor(e,t){const n=e.timezone==="utc",{units:r,step:i}=e.units?{units:e.units,step:e.step||1}:Sx({extent:e.extent||ln(t.materialize(t.SOURCE).source,e.field),maxbins:e.maxbins}),a=Kh(r),u=this.value||{},o=(n?Dx:wx)(a,i);return o.unit=re(a),o.units=a,o.step=i,o.start=u.start,o.stop=u.stop,this.value=o}});function Rb(e){$.call(this,Ki(),e)}T(Rb,$,{transform(e,t){const n=t.dataflow,r=e.field,i=this.value,a=o=>i.set(r(o),o);let u=!0;return e.modified("field")||t.modified(r.fields)?(i.clear(),t.visit(t.SOURCE,a)):t.changed()?(t.visit(t.REM,o=>i.delete(r(o))),t.visit(t.ADD,a)):u=!1,this.modified(u),i.empty>n.cleanThreshold&&n.runAfter(i.clean),t.fork()}});function Ob(e){$.call(this,null,e)}T(Ob,$,{transform(e,t){(!this.value||e.modified("field")||e.modified("sort")||t.changed()||e.sort&&t.modified(e.sort.fields))&&(this.value=(e.sort?t.source.slice().sort(ai(e.sort)):t.source).map(e.field))}});function Fk(e,t,n,r){const i=cu[e](t,n);return{init:i.init||ir,update:function(a,u){u[r]=i.next(a)}}}const cu={row_number:function(){return{next:e=>e.index+1}},rank:function(){let e;return{init:()=>e=1,next:t=>{const n=t.index,r=t.data;return n&&t.compare(r[n-1],r[n])?e=n+1:e}}},dense_rank:function(){let e;return{init:()=>e=1,next:t=>{const n=t.index,r=t.data;return n&&t.compare(r[n-1],r[n])?++e:e}}},percent_rank:function(){const e=cu.rank(),t=e.next;return{init:e.init,next:n=>(t(n)-1)/(n.data.length-1)}},cume_dist:function(){let e;return{init:()=>e=0,next:t=>{const n=t.data,r=t.compare;let i=t.index;if(e<i){for(;i+1<n.length&&!r(n[i],n[i+1]);)++i;e=i}return(1+e)/n.length}}},ntile:function(e,t){t=+t,t>0||S("ntile num must be greater than zero.");const n=cu.cume_dist(),r=n.next;return{init:n.init,next:i=>Math.ceil(t*r(i))}},lag:function(e,t){return t=+t||1,{next:n=>{const r=n.index-t;return r>=0?e(n.data[r]):null}}},lead:function(e,t){return t=+t||1,{next:n=>{const r=n.index+t,i=n.data;return r<i.length?e(i[r]):null}}},first_value:function(e){return{next:t=>e(t.data[t.i0])}},last_value:function(e){return{next:t=>e(t.data[t.i1-1])}},nth_value:function(e,t){return t=+t,t>0||S("nth_value nth must be greater than zero."),{next:n=>{const r=n.i0+(t-1);return r<n.i1?e(n.data[r]):null}}},prev_value:function(e){let t;return{init:()=>t=null,next:n=>{const r=e(n.data[n.index]);return r!=null?t=r:t}}},next_value:function(e){let t,n;return{init:()=>(t=null,n=-1),next:r=>{const i=r.data;return r.index<=n?t:(n=kk(e,i,r.index))<0?(n=i.length,t=null):t=e(i[n])}}}};function kk(e,t,n){for(let r=t.length;n<r;++n)if(e(t[n])!=null)return n;return-1}const Mk=Object.keys(cu);function Tb(e){const t=q(e.ops),n=q(e.fields),r=q(e.params),i=q(e.as),a=this.outputs=[],u=this.windows=[],o={},s={},l=[],f=[];let c=!0;function d(h){q(Xe(h)).forEach(g=>o[g]=1)}d(e.sort),t.forEach((h,g)=>{const p=n[g],m=Ee(p),y=fb(h,m,i[g]);if(d(p),a.push(y),G(cu,h))u.push(Fk(h,n[g],r[g],y));else{if(p==null&&h!=="count"&&S("Null aggregate field specified."),h==="count"){l.push(y);return}c=!1;let v=s[m];v||(v=s[m]=[],v.field=p,f.push(v)),v.push(cb(h,y))}}),(l.length||f.length)&&(this.cell=Sk(f,l,c)),this.inputs=Object.keys(o)}const Lb=Tb.prototype;Lb.init=function(){this.windows.forEach(e=>e.init()),this.cell&&this.cell.init()};Lb.update=function(e,t){const n=this.cell,r=this.windows,i=e.data,a=r&&r.length;let u;if(n){for(u=e.p0;u<e.i0;++u)n.rem(i[u]);for(u=e.p1;u<e.i1;++u)n.add(i[u]);n.set(t)}for(u=0;u<a;++u)r[u].update(e,t)};function Sk(e,t,n){e=e.map(s=>hb(s,s.field));const r={num:0,agg:null,store:!1,count:t};if(!n)for(var i=e.length,a=r.agg=Array(i),u=0;u<i;++u)a[u]=new e[u](r);if(r.store)var o=r.data=new A0;return r.add=function(s){if(r.num+=1,!n){o&&o.add(s);for(let l=0;l<i;++l)a[l].add(a[l].get(s),s)}},r.rem=function(s){if(r.num-=1,!n){o&&o.rem(s);for(let l=0;l<i;++l)a[l].rem(a[l].get(s),s)}},r.set=function(s){let l,f;for(o&&o.values(),l=0,f=t.length;l<f;++l)s[t[l]]=r.num;if(!n)for(l=0,f=a.length;l<f;++l)a[l].set(s)},r.init=function(){r.num=0,o&&o.reset();for(let s=0;s<i;++s)a[s].init()},r}function W0(e){$.call(this,{},e),this._mlen=0,this._mods=[]}W0.Definition={type:"Window",metadata:{modifies:!0},params:[{name:"sort",type:"compare"},{name:"groupby",type:"field",array:!0},{name:"ops",type:"enum",array:!0,values:Mk.concat(Lu)},{name:"params",type:"number",null:!0,array:!0},{name:"fields",type:"field",null:!0,array:!0},{name:"as",type:"string",null:!0,array:!0},{name:"frame",type:"number",null:!0,array:!0,length:2,default:[null,0]},{name:"ignorePeers",type:"boolean",default:!1}]};T(W0,$,{transform(e,t){this.stamp=t.stamp;const n=e.modified(),r=ai(e.sort),i=ud(e.groupby),a=o=>this.group(i(o));let u=this.state;(!u||n)&&(u=this.state=new Tb(e)),n||t.modified(u.inputs)?(this.value={},t.visit(t.SOURCE,o=>a(o).add(o))):(t.visit(t.REM,o=>a(o).remove(o)),t.visit(t.ADD,o=>a(o).add(o)));for(let o=0,s=this._mlen;o<s;++o)$k(this._mods[o],u,r,e);return this._mlen=0,this._mods=[],t.reflow(n).modifies(u.outputs)},group(e){let t=this.value[e];return t||(t=this.value[e]=gb(Y),t.stamp=-1),t.stamp<this.stamp&&(t.stamp=this.stamp,this._mods[this._mlen++]=t),t}});function $k(e,t,n,r){const i=r.sort,a=i&&!r.ignorePeers,u=r.frame||[null,0],o=e.data(n),s=o.length,l=a?ih(i):null,f={i0:0,i1:0,p0:0,p1:0,index:0,data:o,compare:i||tt(-1)};t.init();for(let c=0;c<s;++c)Bk(f,u,c,s),a&&_k(f,l),t.update(f,o[c])}function Bk(e,t,n,r){e.p0=e.i0,e.p1=e.i1,e.i0=t[0]==null?0:Math.max(0,n-Math.abs(t[0])),e.i1=t[1]==null?r:Math.min(r,n+Math.abs(t[1])+1),e.index=n}function _k(e,t){const n=e.i0,r=e.i1-1,i=e.compare,a=e.data,u=a.length-1;n>0&&!i(a[n],a[n-1])&&(e.i0=t.left(a,a[n])),r<u&&!i(a[r],a[r+1])&&(e.i1=t.right(a,a[r]))}const Rk=Object.freeze(Object.defineProperty({__proto__:null,aggregate:yr,bin:E0,collect:w0,compare:pb,countpattern:D0,cross:C0,density:F0,dotbin:M0,expression:bb,extent:S0,facet:Il,field:Ab,filter:B0,flatten:_0,fold:R0,formula:O0,generate:Eb,impute:T0,joinaggregate:L0,kde:N0,key:wb,load:Db,lookup:z0,multiextent:Cb,multivalues:Fb,params:kb,pivot:P0,prefacet:Mb,project:I0,proxy:Sb,quantile:U0,relay:$b,sample:q0,sequence:j0,sieve:Bb,subflow:$0,timeunit:G0,tupleindex:Rb,values:Ob,window:W0},Symbol.toStringTag,{value:"Module"}));function pr(e,t){if(typeof document<"u"&&document.createElement){const n=document.createElement("canvas");if(n&&n.getContext)return n.width=e,n.height=t,n}return null}const Ok=()=>typeof Image<"u"?Image:null;function Ul(e,t,n){const r=e-t+n*2;return e?r>0?r:1:0}const Tk="identity",zi="linear",Hn="log",Nu="pow",zu="sqrt",ql="symlog",Kr="time",Jr="utc",En="sequential",na="diverging",Pi="quantile",jl="quantize",Gl="threshold",Y0="ordinal",od="point",Nb="band",H0="bin-ordinal",Be="continuous",Pu="discrete",Iu="discretizing",Jt="interpolating",X0="temporal";function Lk(e){return function(t){let n=t[0],r=t[1],i;return r<n&&(i=n,n=r,r=i),[e.invert(n),e.invert(r)]}}function Nk(e){return function(t){const n=e.range();let r=t[0],i=t[1],a=-1,u,o,s,l;for(i<r&&(o=r,r=i,i=o),s=0,l=n.length;s<l;++s)n[s]>=r&&n[s]<=i&&(a<0&&(a=s),u=s);if(!(a<0))return r=e.invertExtent(n[a]),i=e.invertExtent(n[u]),[r[0]===void 0?r[1]:r[0],i[1]===void 0?i[0]:i[1]]}}function V0(){const e=O2().unknown(void 0),t=e.domain,n=e.range;let r=[0,1],i,a,u=!1,o=0,s=0,l=.5;delete e.unknown;function f(){const c=t().length,d=r[1]<r[0],h=r[1-d],g=Ul(c,o,s);let p=r[d-0];i=(h-p)/(g||1),u&&(i=Math.floor(i)),p+=(h-p-i*(c-o))*l,a=i*(1-o),u&&(p=Math.round(p),a=Math.round(a));const m=Et(c).map(y=>p+i*y);return n(d?m.reverse():m)}return e.domain=function(c){return arguments.length?(t(c),f()):t()},e.range=function(c){return arguments.length?(r=[+c[0],+c[1]],f()):r.slice()},e.rangeRound=function(c){return r=[+c[0],+c[1]],u=!0,f()},e.bandwidth=function(){return a},e.step=function(){return i},e.round=function(c){return arguments.length?(u=!!c,f()):u},e.padding=function(c){return arguments.length?(s=Math.max(0,Math.min(1,c)),o=s,f()):o},e.paddingInner=function(c){return arguments.length?(o=Math.max(0,Math.min(1,c)),f()):o},e.paddingOuter=function(c){return arguments.length?(s=Math.max(0,Math.min(1,c)),f()):s},e.align=function(c){return arguments.length?(l=Math.max(0,Math.min(1,c)),f()):l},e.invertRange=function(c){if(c[0]==null||c[1]==null)return;const d=r[1]<r[0],h=d?n().reverse():n(),g=h.length-1;let p=+c[0],m=+c[1],y,v,x;if(!(p!==p||m!==m)&&(m<p&&(x=p,p=m,m=x),!(m<h[0]||p>r[1-d])))return y=Math.max(0,Hr(h,p)-1),v=p===m?y:Hr(h,m)-1,p-h[y]>a+1e-10&&++y,d&&(x=y,y=g-v,v=g-x),y>v?void 0:t().slice(y,v+1)},e.invert=function(c){const d=e.invertRange([c,c]);return d&&d[0]},e.copy=function(){return V0().domain(t()).range(r).round(u).paddingInner(o).paddingOuter(s).align(l)},f()}function zb(e){const t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,e.copy=function(){return zb(t())},e}function zk(){return zb(V0().paddingInner(1))}var Pk=Array.prototype.map;function Ik(e){return Pk.call(e,Ne)}const Uk=Array.prototype.slice;function Pb(){let e=[],t=[];function n(r){return r==null||r!==r?void 0:t[(Hr(e,r)-1)%t.length]}return n.domain=function(r){return arguments.length?(e=Ik(r),n):e.slice()},n.range=function(r){return arguments.length?(t=Uk.call(r),n):t.slice()},n.tickFormat=function(r,i){return iD(e[0],re(e),r??10,i)},n.copy=function(){return Pb().domain(n.domain()).range(n.range())},n}const Ts=new Map,Ib=Symbol("vega_scale");function Ub(e){return e[Ib]=!0,e}function qb(e){return e&&e[Ib]===!0}function qk(e,t,n){const r=function(){const a=t();return a.invertRange||(a.invertRange=a.invert?Lk(a):a.invertExtent?Nk(a):void 0),a.type=e,Ub(a)};return r.metadata=Tt(q(n)),r}function ue(e,t,n){return arguments.length>1?(Ts.set(e,qk(e,t,n)),this):jb(e)?Ts.get(e):void 0}ue(Tk,jv);ue(zi,rD,Be);ue(Hn,Gv,[Be,Hn]);ue(Nu,Dh,Be);ue(zu,$6,Be);ue(ql,Wv,Be);ue(Kr,W3,[Be,X0]);ue(Jr,B6,[Be,X0]);ue(En,Ch,[Be,Jt]);ue(`${En}-${zi}`,Ch,[Be,Jt]);ue(`${En}-${Hn}`,Vv,[Be,Jt,Hn]);ue(`${En}-${Nu}`,Fh,[Be,Jt]);ue(`${En}-${zu}`,_6,[Be,Jt]);ue(`${En}-${ql}`,Kv,[Be,Jt]);ue(`${na}-${zi}`,Jv,[Be,Jt]);ue(`${na}-${Hn}`,Qv,[Be,Jt,Hn]);ue(`${na}-${Nu}`,kh,[Be,Jt]);ue(`${na}-${zu}`,R6,[Be,Jt]);ue(`${na}-${ql}`,Zv,[Be,Jt]);ue(Pi,Yv,[Iu,Pi]);ue(jl,Hv,Iu);ue(Gl,Xv,Iu);ue(H0,Pb,[Pu,Iu]);ue(Y0,O2,Pu);ue(Nb,V0,Pu);ue(od,zk,Pu);function jb(e){return Ts.has(e)}function oi(e,t){const n=Ts.get(e);return n&&n.metadata[t]}function K0(e){return oi(e,Be)}function Ii(e){return oi(e,Pu)}function sd(e){return oi(e,Iu)}function Gb(e){return oi(e,Hn)}function jk(e){return oi(e,X0)}function Wb(e){return oi(e,Jt)}function Yb(e){return oi(e,Pi)}const Gk=["clamp","base","constant","exponent"];function Hb(e,t){const n=t[0],r=re(t)-n;return function(i){return e(n+i*r)}}function Wl(e,t,n){return lh(Q0(t||"rgb",n),e)}function Xb(e,t){const n=new Array(t),r=t+1;for(let i=0;i<t;)n[i]=e(++i/r);return n}function J0(e,t,n){const r=n-t;let i,a,u;return!r||!Number.isFinite(r)?tt(.5):(i=(a=e.type).indexOf("-"),a=i<0?a:a.slice(i+1),u=ue(a)().domain([t,n]).range([0,1]),Gk.forEach(o=>e[o]?u[o](e[o]()):0),u)}function Q0(e,t){const n=yC[Wk(e)];return t!=null&&n&&n.gamma?n.gamma(t):n}function Wk(e){return"interpolate"+e.toLowerCase().split("-").map(t=>t[0].toUpperCase()+t.slice(1)).join("")}const Yk={blues:"cfe1f2bed8eca8cee58fc1de74b2d75ba3cf4592c63181bd206fb2125ca40a4a90",greens:"d3eecdc0e6baabdda594d3917bc77d60ba6c46ab5e329a512089430e7735036429",greys:"e2e2e2d4d4d4c4c4c4b1b1b19d9d9d8888887575756262624d4d4d3535351e1e1e",oranges:"fdd8b3fdc998fdb87bfda55efc9244f87f2cf06b18e4580bd14904b93d029f3303",purples:"e2e1efd4d4e8c4c5e0b4b3d6a3a0cc928ec3827cb97566ae684ea25c3696501f8c",reds:"fdc9b4fcb49afc9e80fc8767fa7051f6573fec3f2fdc2a25c81b1db21218970b13",blueGreen:"d5efedc1e8e0a7ddd18bd2be70c6a958ba9144ad77319c5d2089460e7736036429",bluePurple:"ccddecbad0e4a8c2dd9ab0d4919cc98d85be8b6db28a55a6873c99822287730f71",greenBlue:"d3eecec5e8c3b1e1bb9bd8bb82cec269c2ca51b2cd3c9fc7288abd1675b10b60a1",orangeRed:"fddcaffdcf9bfdc18afdad77fb9562f67d53ee6545e24932d32d1ebf130da70403",purpleBlue:"dbdaebc8cee4b1c3de97b7d87bacd15b9fc93a90c01e7fb70b70ab056199045281",purpleBlueGreen:"dbd8eac8cee4b0c3de93b7d872acd1549fc83892bb1c88a3097f8702736b016353",purpleRed:"dcc9e2d3b3d7ce9eccd186c0da6bb2e14da0e23189d91e6fc61159ab07498f023a",redPurple:"fccfccfcbec0faa9b8f98faff571a5ec539ddb3695c41b8aa908808d0179700174",yellowGreen:"e4f4acd1eca0b9e2949ed68880c97c62bb6e47aa5e3297502083440e723b036034",yellowOrangeBrown:"feeaa1fedd84fecc63feb746fca031f68921eb7215db5e0bc54c05ab3d038f3204",yellowOrangeRed:"fee087fed16ffebd59fea849fd903efc7335f9522bee3423de1b20ca0b22af0225",blueOrange:"134b852f78b35da2cb9dcae1d2e5eff2f0ebfce0bafbbf74e8932fc5690d994a07",brownBlueGreen:"704108a0651ac79548e3c78af3e6c6eef1eac9e9e48ed1c74da79e187a72025147",purpleGreen:"5b1667834792a67fb6c9aed3e6d6e8eff0efd9efd5aedda971bb75368e490e5e29",purpleOrange:"4114696647968f83b7b9b4d6dadbebf3eeeafce0bafbbf74e8932fc5690d994a07",redBlue:"8c0d25bf363adf745ef4ae91fbdbc9f2efeed2e5ef9dcae15da2cb2f78b3134b85",redGrey:"8c0d25bf363adf745ef4ae91fcdccbfaf4f1e2e2e2c0c0c0969696646464343434",yellowGreenBlue:"eff9bddbf1b4bde5b594d5b969c5be45b4c22c9ec02182b82163aa23479c1c3185",redYellowBlue:"a50026d4322cf16e43fcac64fedd90faf8c1dcf1ecabd6e875abd04a74b4313695",redYellowGreen:"a50026d4322cf16e43fcac63fedd8df9f7aed7ee8ea4d86e64bc6122964f006837",pinkYellowGreen:"8e0152c0267edd72adf0b3d6faddedf5f3efe1f2cab6de8780bb474f9125276419",spectral:"9e0142d13c4bf0704afcac63fedd8dfbf8b0e0f3a1a9dda269bda94288b55e4fa2",viridis:"440154470e61481a6c482575472f7d443a834144873d4e8a39568c35608d31688e2d708e2a788e27818e23888e21918d1f988b1fa08822a8842ab07f35b77943bf7154c56866cc5d7ad1518fd744a5db36bcdf27d2e21be9e51afde725",magma:"0000040404130b0924150e3720114b2c11603b0f704a107957157e651a80721f817f24828c29819a2e80a8327db6377ac43c75d1426fde4968e95462f1605df76f5cfa7f5efc8f65fe9f6dfeaf78febf84fece91fddea0fcedaffcfdbf",inferno:"0000040403130c0826170c3b240c4f330a5f420a68500d6c5d126e6b176e781c6d86216b932667a12b62ae305cbb3755c73e4cd24644dd513ae65c30ed6925f3771af8850ffb9506fca50afcb519fac62df6d645f2e661f3f484fcffa4",plasma:"0d088723069033059742039d5002a25d01a66a00a87801a88405a7900da49c179ea72198b12a90ba3488c33d80cb4779d35171da5a69e16462e76e5bed7953f2834cf68f44fa9a3dfca636fdb32ffec029fcce25f9dc24f5ea27f0f921",cividis:"00205100235800265d002961012b65042e670831690d346b11366c16396d1c3c6e213f6e26426e2c456e31476e374a6e3c4d6e42506e47536d4c566d51586e555b6e5a5e6e5e616e62646f66676f6a6a706e6d717270717573727976737c79747f7c75827f758682768985778c8877908b78938e789691789a94789e9778a19b78a59e77a9a177aea575b2a874b6ab73bbaf71c0b26fc5b66dc9b96acebd68d3c065d8c462ddc85fe2cb5ce7cf58ebd355f0d652f3da4ff7de4cfae249fce647",rainbow:"6e40aa883eb1a43db3bf3cafd83fa4ee4395fe4b83ff576eff6659ff7847ff8c38f3a130e2b72fcfcc36bee044aff05b8ff4576ff65b52f6673af27828ea8d1ddfa319d0b81cbecb23abd82f96e03d82e14c6edb5a5dd0664dbf6e40aa",sinebow:"ff4040fc582af47218e78d0bd5a703bfbf00a7d5038de70b72f41858fc2a40ff402afc5818f4720be78d03d5a700bfbf03a7d50b8de71872f42a58fc4040ff582afc7218f48d0be7a703d5bf00bfd503a7e70b8df41872fc2a58ff4040",turbo:"23171b32204a3e2a71453493493eae4b49c54a53d7485ee44569ee4074f53c7ff8378af93295f72e9ff42ba9ef28b3e926bce125c5d925cdcf27d5c629dcbc2de3b232e9a738ee9d3ff39347f68950f9805afc7765fd6e70fe667cfd5e88fc5795fb51a1f84badf545b9f140c5ec3cd0e637dae034e4d931ecd12ef4c92bfac029ffb626ffad24ffa223ff9821ff8d1fff821dff771cfd6c1af76118f05616e84b14df4111d5380fcb2f0dc0260ab61f07ac1805a313029b0f00950c00910b00",browns:"eedbbdecca96e9b97ae4a865dc9856d18954c7784cc0673fb85536ad44339f3632",tealBlues:"bce4d89dd3d181c3cb65b3c245a2b9368fae347da0306a932c5985",teals:"bbdfdfa2d4d58ac9c975bcbb61b0af4da5a43799982b8b8c1e7f7f127273006667",warmGreys:"dcd4d0cec5c1c0b8b4b3aaa7a59c9998908c8b827f7e7673726866665c5a59504e",goldGreen:"f4d166d5ca60b6c35c98bb597cb25760a6564b9c533f8f4f33834a257740146c36",goldOrange:"f4d166f8be5cf8aa4cf5983bf3852aef701be2621fd65322c54923b142239e3a26",goldRed:"f4d166f6be59f9aa51fc964ef6834bee734ae56249db5247cf4244c43141b71d3e",lightGreyRed:"efe9e6e1dad7d5cbc8c8bdb9bbaea9cd967ddc7b43e15f19df4011dc000b",lightGreyTeal:"e4eaead6dcddc8ced2b7c2c7a6b4bc64b0bf22a6c32295c11f85be1876bc",lightMulti:"e0f1f2c4e9d0b0de9fd0e181f6e072f6c053f3993ef77440ef4a3c",lightOrange:"f2e7daf7d5baf9c499fab184fa9c73f68967ef7860e8645bde515bd43d5b",lightTealBlue:"e3e9e0c0dccf9aceca7abfc859afc0389fb9328dad2f7ca0276b95255988",darkBlue:"3232322d46681a5c930074af008cbf05a7ce25c0dd38daed50f3faffffff",darkGold:"3c3c3c584b37725e348c7631ae8b2bcfa424ecc31ef9de30fff184ffffff",darkGreen:"3a3a3a215748006f4d048942489e4276b340a6c63dd2d836ffeb2cffffaa",darkMulti:"3737371f5287197d8c29a86995ce3fffe800ffffff",darkRed:"3434347036339e3c38cc4037e75d1eec8620eeab29f0ce32ffeb2c"},Hk={category10:"1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf",category20:"1f77b4aec7e8ff7f0effbb782ca02c98df8ad62728ff98969467bdc5b0d58c564bc49c94e377c2f7b6d27f7f7fc7c7c7bcbd22dbdb8d17becf9edae5",category20b:"393b795254a36b6ecf9c9ede6379398ca252b5cf6bcedb9c8c6d31bd9e39e7ba52e7cb94843c39ad494ad6616be7969c7b4173a55194ce6dbdde9ed6",category20c:"3182bd6baed69ecae1c6dbefe6550dfd8d3cfdae6bfdd0a231a35474c476a1d99bc7e9c0756bb19e9ac8bcbddcdadaeb636363969696bdbdbdd9d9d9",tableau10:"4c78a8f58518e4575672b7b254a24beeca3bb279a2ff9da69d755dbab0ac",tableau20:"4c78a89ecae9f58518ffbf7954a24b88d27ab79a20f2cf5b43989483bcb6e45756ff9d9879706ebab0acd67195fcbfd2b279a2d6a5c99e765fd8b5a5",accent:"7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666",dark2:"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666",paired:"a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928",pastel1:"fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2",pastel2:"b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc",set1:"e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999",set2:"66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3",set3:"8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"};function Vb(e){const t=e.length/6|0,n=new Array(t);for(let r=0;r<t;)n[r]="#"+e.slice(r*6,++r*6);return n}function Kb(e,t){for(const n in e)Z0(n,t(e[n]))}const Dm={};Kb(Hk,Vb);Kb(Yk,e=>Wl(Vb(e)));function Z0(e,t){return e=e&&e.toLowerCase(),arguments.length>1?(Dm[e]=t,this):Dm[e]}const Po="symbol",Xk="discrete",Vk="gradient",Kk=e=>z(e)?e.map(t=>String(t)):String(e),Jk=(e,t)=>e[1]-t[1],Qk=(e,t)=>t[1]-e[1];function e1(e,t,n){let r;return Yn(t)&&(e.bins&&(t=Math.max(t,e.bins.length)),n!=null&&(t=Math.min(t,Math.floor(Ji(e.domain())/n||1)+1))),K(t)&&(r=t.step,t=t.interval),fe(t)&&(t=e.type===Kr?Qi(t):e.type==Jr?Zi(t):S("Only time and utc scales accept interval strings."),r&&(t=t.every(r))),t}function Jb(e,t,n){let r=e.range(),i=r[0],a=re(r),u=Jk;if(i>a&&(r=a,a=i,i=r,u=Qk),i=Math.floor(i),a=Math.ceil(a),t=t.map(o=>[o,e(o)]).filter(o=>i<=o[1]&&o[1]<=a).sort(u).map(o=>o[0]),n>0&&t.length>1){const o=[t[0],re(t)];for(;t.length>n&&t.length>=3;)t=t.filter((s,l)=>!(l%2));t.length<3&&(t=o)}return t}function t1(e,t){return e.bins?Jb(e,e.bins):e.ticks?e.ticks(t):e.domain()}function Qb(e,t,n,r,i,a){const u=t.type;let o=Kk;if(u===Kr||i===Kr)o=e.timeFormat(r);else if(u===Jr||i===Jr)o=e.utcFormat(r);else if(Gb(u)){const s=e.formatFloat(r);if(a||t.bins)o=s;else{const l=Zb(t,n,!1);o=f=>l(f)?s(f):""}}else if(t.tickFormat){const s=t.domain();o=e.formatSpan(s[0],s[s.length-1],n,r)}else r&&(o=e.format(r));return o}function Zb(e,t,n){const r=t1(e,t),i=e.base(),a=Math.log(i),u=Math.max(1,i*t/r.length),o=s=>{let l=s/Math.pow(i,Math.round(Math.log(s)/a));return l*i<i-.5&&(l*=i),l<=u};return n?r.filter(o):o}const ld={[Pi]:"quantiles",[jl]:"thresholds",[Gl]:"domain"},eA={[Pi]:"quantiles",[jl]:"domain"};function tA(e,t){return e.bins?t7(e.bins):e.type===Hn?Zb(e,t,!0):ld[e.type]?e7(e[ld[e.type]]()):t1(e,t)}function Zk(e,t,n){const r=t[eA[t.type]](),i=r.length;let a=i>1?r[1]-r[0]:r[0],u;for(u=1;u<i;++u)a=Math.min(a,r[u]-r[u-1]);return e.formatSpan(0,a,3*10,n)}function e7(e){const t=[-1/0].concat(e);return t.max=1/0,t}function t7(e){const t=e.slice(0,-1);return t.max=re(e),t}const n7=e=>ld[e.type]||e.bins;function nA(e,t,n,r,i,a,u){const o=eA[t.type]&&a!==Kr&&a!==Jr?Zk(e,t,i):Qb(e,t,n,i,a,u);return r===Po&&n7(t)?r7(o):r===Xk?i7(o):a7(o)}const r7=e=>(t,n,r)=>{const i=Cm(r[n+1],Cm(r.max,1/0)),a=Fm(t,e),u=Fm(i,e);return a&&u?a+" – "+u:u?"< "+u:"≥ "+a},Cm=(e,t)=>e??t,i7=e=>(t,n)=>n?e(t):null,a7=e=>t=>e(t),Fm=(e,t)=>Number.isFinite(e)?t(e):null;function u7(e){const t=e.domain(),n=t.length-1;let r=+t[0],i=+re(t),a=i-r;if(e.type===Gl){const u=n?a/n:.1;r-=u,i+=u,a=i-r}return u=>(u-r)/a}function o7(e,t,n,r){const i=r||t.type;return fe(n)&&jk(i)&&(n=n.replace(/%a/g,"%A").replace(/%b/g,"%B")),!n&&i===Kr?e.timeFormat("%A, %d %B %Y, %X"):!n&&i===Jr?e.utcFormat("%A, %d %B %Y, %X UTC"):nA(e,t,5,null,n,r,!0)}function rA(e,t,n){n=n||{};const r=Math.max(3,n.maxlen||7),i=o7(e,t,n.format,n.formatType);if(sd(t.type)){const a=tA(t).slice(1).map(i),u=a.length;return`${u} boundar${u===1?"y":"ies"}: ${a.join(", ")}`}else if(Ii(t.type)){const a=t.domain(),u=a.length,o=u>r?a.slice(0,r-2).map(i).join(", ")+", ending with "+a.slice(-1).map(i):a.map(i).join(", ");return`${u} value${u===1?"":"s"}: ${o}`}else{const a=t.domain();return`values from ${i(a[0])} to ${i(re(a))}`}}let iA=0;function s7(){iA=0}const Ls="p_";function n1(e){return e&&e.gradient}function aA(e,t,n){const r=e.gradient;let i=e.id,a=r==="radial"?Ls:"";return i||(i=e.id="gradient_"+iA++,r==="radial"?(e.x1=mn(e.x1,.5),e.y1=mn(e.y1,.5),e.r1=mn(e.r1,0),e.x2=mn(e.x2,.5),e.y2=mn(e.y2,.5),e.r2=mn(e.r2,.5),a=Ls):(e.x1=mn(e.x1,0),e.y1=mn(e.y1,0),e.x2=mn(e.x2,1),e.y2=mn(e.y2,0))),t[i]=e,"url("+(n||"")+"#"+a+i+")"}function mn(e,t){return e??t}function r1(e,t){var n=[],r;return r={gradient:"linear",x1:e?e[0]:0,y1:e?e[1]:0,x2:t?t[0]:1,y2:t?t[1]:0,stops:n,stop:function(i,a){return n.push({offset:i,color:a}),r}}}const km={basis:{curve:SD},"basis-closed":{curve:$D},"basis-open":{curve:BD},bundle:{curve:_D,tension:"beta",value:.85},cardinal:{curve:RD,tension:"tension",value:0},"cardinal-open":{curve:OD,tension:"tension",value:0},"cardinal-closed":{curve:TD,tension:"tension",value:0},"catmull-rom":{curve:LD,tension:"alpha",value:.5},"catmull-rom-closed":{curve:ND,tension:"alpha",value:.5},"catmull-rom-open":{curve:zD,tension:"alpha",value:.5},linear:{curve:_2},"linear-closed":{curve:PD},monotone:{horizontal:ID,vertical:UD},natural:{curve:qD},step:{curve:jD},"step-after":{curve:GD},"step-before":{curve:WD}};function i1(e,t,n){var r=G(km,e)&&km[e],i=null;return r&&(i=r.curve||r[t||"vertical"],r.tension&&n!=null&&(i=i[r.tension](n))),i}const l7={m:2,l:2,h:1,v:1,z:0,c:6,s:4,q:4,t:2,a:7},f7=/[mlhvzcsqta]([^mlhvzcsqta]+|$)/gi,c7=/^[+-]?(([0-9]*\.[0-9]+)|([0-9]+\.)|([0-9]+))([eE][+-]?[0-9]+)?/,d7=/^((\s+,?\s*)|(,\s*))/,h7=/^[01]/;function Qr(e){const t=[];return(e.match(f7)||[]).forEach(r=>{let i=r[0];const a=i.toLowerCase(),u=l7[a],o=g7(a,u,r.slice(1).trim()),s=o.length;if(s<u||s&&s%u!==0)throw Error("Invalid SVG path, incorrect parameter count");if(t.push([i,...o.slice(0,u)]),s!==u){a==="m"&&(i=i==="M"?"L":"l");for(let l=u;l<s;l+=u)t.push([i,...o.slice(l,l+u)])}}),t}function g7(e,t,n){const r=[];for(let i=0;t&&i<n.length;)for(let a=0;a<t;++a){const u=e==="a"&&(a===3||a===4)?h7:c7,o=n.slice(i).match(u);if(o===null)throw Error("Invalid SVG path, incorrect parameter type");i+=o[0].length,r.push(+o[0]);const s=n.slice(i).match(d7);s!==null&&(i+=s[0].length)}return r}const vr=Math.PI/180,p7=1e-14,Lr=Math.PI/2,An=Math.PI*2,mi=Math.sqrt(3)/2;var qf={},jf={},uA=[].join;function m7(e,t,n,r,i,a,u,o,s){const l=uA.call(arguments);if(qf[l])return qf[l];const f=u*vr,c=Math.sin(f),d=Math.cos(f);n=Math.abs(n),r=Math.abs(r);const h=d*(o-e)*.5+c*(s-t)*.5,g=d*(s-t)*.5-c*(o-e)*.5;let p=h*h/(n*n)+g*g/(r*r);p>1&&(p=Math.sqrt(p),n*=p,r*=p);const m=d/n,y=c/n,v=-c/r,x=d/r,b=m*o+y*s,E=v*o+x*s,w=m*e+y*t,A=v*e+x*t;let C=1/((w-b)*(w-b)+(A-E)*(A-E))-.25;C<0&&(C=0);let F=Math.sqrt(C);a==i&&(F=-F);const M=.5*(b+w)-F*(A-E),O=.5*(E+A)+F*(w-b),L=Math.atan2(E-O,b-M);let k=Math.atan2(A-O,w-M)-L;k<0&&a===1?k+=An:k>0&&a===0&&(k-=An);const R=Math.ceil(Math.abs(k/(Lr+.001))),j=[];for(let ee=0;ee<R;++ee){const Q=L+ee*k/R,Ce=L+(ee+1)*k/R;j[ee]=[M,O,Q,Ce,n,r,c,d]}return qf[l]=j}function y7(e){const t=uA.call(e);if(jf[t])return jf[t];var n=e[0],r=e[1],i=e[2],a=e[3],u=e[4],o=e[5],s=e[6],l=e[7];const f=l*u,c=-s*o,d=s*u,h=l*o,g=Math.cos(i),p=Math.sin(i),m=Math.cos(a),y=Math.sin(a),v=.5*(a-i),x=Math.sin(v*.5),b=8/3*x*x/Math.sin(v),E=n+g-b*p,w=r+p+b*g,A=n+m,D=r+y,C=A+b*y,F=D-b*m;return jf[t]=[f*E+c*w,d*E+h*w,f*C+c*F,d*C+h*F,f*A+c*D,d*A+h*D]}const Ut=["l",0,0,0,0,0,0,0];function v7(e,t,n){const r=Ut[0]=e[0];if(r==="a"||r==="A")Ut[1]=t*e[1],Ut[2]=n*e[2],Ut[3]=e[3],Ut[4]=e[4],Ut[5]=e[5],Ut[6]=t*e[6],Ut[7]=n*e[7];else if(r==="h"||r==="H")Ut[1]=t*e[1];else if(r==="v"||r==="V")Ut[1]=n*e[1];else for(var i=1,a=e.length;i<a;++i)Ut[i]=(i%2==1?t:n)*e[i];return Ut}function Ui(e,t,n,r,i,a){var u,o=null,s=0,l=0,f=0,c=0,d,h,g,p,m=0,y=0;n==null&&(n=0),r==null&&(r=0),i==null&&(i=1),a==null&&(a=i),e.beginPath&&e.beginPath();for(var v=0,x=t.length;v<x;++v){switch(u=t[v],(i!==1||a!==1)&&(u=v7(u,i,a)),u[0]){case"l":s+=u[1],l+=u[2],e.lineTo(s+n,l+r);break;case"L":s=u[1],l=u[2],e.lineTo(s+n,l+r);break;case"h":s+=u[1],e.lineTo(s+n,l+r);break;case"H":s=u[1],e.lineTo(s+n,l+r);break;case"v":l+=u[1],e.lineTo(s+n,l+r);break;case"V":l=u[1],e.lineTo(s+n,l+r);break;case"m":s+=u[1],l+=u[2],m=s,y=l,e.moveTo(s+n,l+r);break;case"M":s=u[1],l=u[2],m=s,y=l,e.moveTo(s+n,l+r);break;case"c":d=s+u[5],h=l+u[6],f=s+u[3],c=l+u[4],e.bezierCurveTo(s+u[1]+n,l+u[2]+r,f+n,c+r,d+n,h+r),s=d,l=h;break;case"C":s=u[5],l=u[6],f=u[3],c=u[4],e.bezierCurveTo(u[1]+n,u[2]+r,f+n,c+r,s+n,l+r);break;case"s":d=s+u[3],h=l+u[4],f=2*s-f,c=2*l-c,e.bezierCurveTo(f+n,c+r,s+u[1]+n,l+u[2]+r,d+n,h+r),f=s+u[1],c=l+u[2],s=d,l=h;break;case"S":d=u[3],h=u[4],f=2*s-f,c=2*l-c,e.bezierCurveTo(f+n,c+r,u[1]+n,u[2]+r,d+n,h+r),s=d,l=h,f=u[1],c=u[2];break;case"q":d=s+u[3],h=l+u[4],f=s+u[1],c=l+u[2],e.quadraticCurveTo(f+n,c+r,d+n,h+r),s=d,l=h;break;case"Q":d=u[3],h=u[4],e.quadraticCurveTo(u[1]+n,u[2]+r,d+n,h+r),s=d,l=h,f=u[1],c=u[2];break;case"t":d=s+u[1],h=l+u[2],o[0].match(/[QqTt]/)===null?(f=s,c=l):o[0]==="t"?(f=2*s-g,c=2*l-p):o[0]==="q"&&(f=2*s-f,c=2*l-c),g=f,p=c,e.quadraticCurveTo(f+n,c+r,d+n,h+r),s=d,l=h,f=s+u[1],c=l+u[2];break;case"T":d=u[1],h=u[2],f=2*s-f,c=2*l-c,e.quadraticCurveTo(f+n,c+r,d+n,h+r),s=d,l=h;break;case"a":Mm(e,s+n,l+r,[u[1],u[2],u[3],u[4],u[5],u[6]+s+n,u[7]+l+r]),s+=u[6],l+=u[7];break;case"A":Mm(e,s+n,l+r,[u[1],u[2],u[3],u[4],u[5],u[6]+n,u[7]+r]),s=u[6],l=u[7];break;case"z":case"Z":s=m,l=y,e.closePath();break}o=u}}function Mm(e,t,n,r){const i=m7(r[5],r[6],r[0],r[1],r[3],r[4],r[2],t,n);for(let a=0;a<i.length;++a){const u=y7(i[a]);e.bezierCurveTo(u[0],u[1],u[2],u[3],u[4],u[5])}}const Sm=.5773502691896257,$m={circle:{draw:function(e,t){const n=Math.sqrt(t)/2;e.moveTo(n,0),e.arc(0,0,n,0,An)}},cross:{draw:function(e,t){var n=Math.sqrt(t)/2,r=n/2.5;e.moveTo(-n,-r),e.lineTo(-n,r),e.lineTo(-r,r),e.lineTo(-r,n),e.lineTo(r,n),e.lineTo(r,r),e.lineTo(n,r),e.lineTo(n,-r),e.lineTo(r,-r),e.lineTo(r,-n),e.lineTo(-r,-n),e.lineTo(-r,-r),e.closePath()}},diamond:{draw:function(e,t){const n=Math.sqrt(t)/2;e.moveTo(-n,0),e.lineTo(0,-n),e.lineTo(n,0),e.lineTo(0,n),e.closePath()}},square:{draw:function(e,t){var n=Math.sqrt(t),r=-n/2;e.rect(r,r,n,n)}},arrow:{draw:function(e,t){var n=Math.sqrt(t)/2,r=n/7,i=n/2.5,a=n/8;e.moveTo(-r,n),e.lineTo(r,n),e.lineTo(r,-a),e.lineTo(i,-a),e.lineTo(0,-n),e.lineTo(-i,-a),e.lineTo(-r,-a),e.closePath()}},wedge:{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n,i=r-n*Sm,a=n/4;e.moveTo(0,-r-i),e.lineTo(-a,r-i),e.lineTo(a,r-i),e.closePath()}},triangle:{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n,i=r-n*Sm;e.moveTo(0,-r-i),e.lineTo(-n,r-i),e.lineTo(n,r-i),e.closePath()}},"triangle-up":{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n;e.moveTo(0,-r),e.lineTo(-n,r),e.lineTo(n,r),e.closePath()}},"triangle-down":{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n;e.moveTo(0,r),e.lineTo(-n,-r),e.lineTo(n,-r),e.closePath()}},"triangle-right":{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n;e.moveTo(r,0),e.lineTo(-r,-n),e.lineTo(-r,n),e.closePath()}},"triangle-left":{draw:function(e,t){var n=Math.sqrt(t)/2,r=mi*n;e.moveTo(-r,0),e.lineTo(r,-n),e.lineTo(r,n),e.closePath()}},stroke:{draw:function(e,t){const n=Math.sqrt(t)/2;e.moveTo(-n,0),e.lineTo(n,0)}}};function oA(e){return G($m,e)?$m[e]:x7(e)}var Gf={};function x7(e){if(!G(Gf,e)){const t=Qr(e);Gf[e]={draw:function(n,r){Ui(n,t,0,0,Math.sqrt(r)/2)}}}return Gf[e]}const Jn=.448084975506;function b7(e){return e.x}function A7(e){return e.y}function E7(e){return e.width}function w7(e){return e.height}function Rn(e){return typeof e=="function"?e:()=>+e}function Ao(e,t,n){return Math.max(t,Math.min(e,n))}function sA(){var e=b7,t=A7,n=E7,r=w7,i=Rn(0),a=i,u=i,o=i,s=null;function l(f,c,d){var h,g=c??+e.call(this,f),p=d??+t.call(this,f),m=+n.call(this,f),y=+r.call(this,f),v=Math.min(m,y)/2,x=Ao(+i.call(this,f),0,v),b=Ao(+a.call(this,f),0,v),E=Ao(+u.call(this,f),0,v),w=Ao(+o.call(this,f),0,v);if(s||(s=h=uh()),x<=0&&b<=0&&E<=0&&w<=0)s.rect(g,p,m,y);else{var A=g+m,D=p+y;s.moveTo(g+x,p),s.lineTo(A-b,p),s.bezierCurveTo(A-Jn*b,p,A,p+Jn*b,A,p+b),s.lineTo(A,D-w),s.bezierCurveTo(A,D-Jn*w,A-Jn*w,D,A-w,D),s.lineTo(g+E,D),s.bezierCurveTo(g+Jn*E,D,g,D-Jn*E,g,D-E),s.lineTo(g,p+x),s.bezierCurveTo(g,p+Jn*x,g+Jn*x,p,g+x,p),s.closePath()}if(h)return s=null,h+""||null}return l.x=function(f){return arguments.length?(e=Rn(f),l):e},l.y=function(f){return arguments.length?(t=Rn(f),l):t},l.width=function(f){return arguments.length?(n=Rn(f),l):n},l.height=function(f){return arguments.length?(r=Rn(f),l):r},l.cornerRadius=function(f,c,d,h){return arguments.length?(i=Rn(f),a=c!=null?Rn(c):i,o=d!=null?Rn(d):i,u=h!=null?Rn(h):a,l):i},l.context=function(f){return arguments.length?(s=f??null,l):s},l}function lA(){var e,t,n,r,i=null,a,u,o,s;function l(c,d,h){const g=h/2;if(a){var p=o-d,m=c-u;if(p||m){var y=Math.sqrt(p*p+m*m),v=(p/=y)*s,x=(m/=y)*s,b=Math.atan2(m,p);i.moveTo(u-v,o-x),i.lineTo(c-p*g,d-m*g),i.arc(c,d,g,b-Math.PI,b),i.lineTo(u+v,o+x),i.arc(u,o,s,b,b+Math.PI)}else i.arc(c,d,g,0,An);i.closePath()}else a=1;u=c,o=d,s=g}function f(c){var d,h=c.length,g,p=!1,m;for(i==null&&(i=m=uh()),d=0;d<=h;++d)!(d<h&&r(g=c[d],d,c))===p&&(p=!p)&&(a=0),p&&l(+e(g,d,c),+t(g,d,c),+n(g,d,c));if(m)return i=null,m+""||null}return f.x=function(c){return arguments.length?(e=c,f):e},f.y=function(c){return arguments.length?(t=c,f):t},f.size=function(c){return arguments.length?(n=c,f):n},f.defined=function(c){return arguments.length?(r=c,f):r},f.context=function(c){return arguments.length?(c==null?i=null:i=c,f):i},f}function Uu(e,t){return e??t}const qu=e=>e.x||0,ju=e=>e.y||0,D7=e=>e.width||0,C7=e=>e.height||0,F7=e=>(e.x||0)+(e.width||0),k7=e=>(e.y||0)+(e.height||0),M7=e=>e.startAngle||0,S7=e=>e.endAngle||0,$7=e=>e.padAngle||0,B7=e=>e.innerRadius||0,_7=e=>e.outerRadius||0,R7=e=>e.cornerRadius||0,O7=e=>Uu(e.cornerRadiusTopLeft,e.cornerRadius)||0,T7=e=>Uu(e.cornerRadiusTopRight,e.cornerRadius)||0,L7=e=>Uu(e.cornerRadiusBottomRight,e.cornerRadius)||0,N7=e=>Uu(e.cornerRadiusBottomLeft,e.cornerRadius)||0,z7=e=>Uu(e.size,64),P7=e=>e.size||1,Yl=e=>e.defined!==!1,I7=e=>oA(e.shape||"circle"),U7=HD().startAngle(M7).endAngle(S7).padAngle($7).innerRadius(B7).outerRadius(_7).cornerRadius(R7),q7=ex().x(qu).y1(ju).y0(k7).defined(Yl),j7=ex().y(ju).x1(qu).x0(F7).defined(Yl),G7=R2().x(qu).y(ju).defined(Yl),W7=sA().x(qu).y(ju).width(D7).height(C7).cornerRadius(O7,T7,L7,N7),Y7=T6().type(I7).size(z7),H7=lA().x(qu).y(ju).defined(Yl).size(P7);function a1(e){return e.cornerRadius||e.cornerRadiusTopLeft||e.cornerRadiusTopRight||e.cornerRadiusBottomRight||e.cornerRadiusBottomLeft}function X7(e,t){return U7.context(e)(t)}function V7(e,t){const n=t[0],r=n.interpolate||"linear";return(n.orient==="horizontal"?j7:q7).curve(i1(r,n.orient,n.tension)).context(e)(t)}function K7(e,t){const n=t[0],r=n.interpolate||"linear";return G7.curve(i1(r,n.orient,n.tension)).context(e)(t)}function ra(e,t,n,r){return W7.context(e)(t,n,r)}function J7(e,t){return(t.mark.shape||t.shape).context(e)(t)}function Q7(e,t){return Y7.context(e)(t)}function Z7(e,t){return H7.context(e)(t)}var fA=1;function cA(){fA=1}function u1(e,t,n){var r=t.clip,i=e._defs,a=t.clip_id||(t.clip_id="clip"+fA++),u=i.clipping[a]||(i.clipping[a]={id:a});return J(r)?u.path=r(null):a1(n)?u.path=ra(null,n,0,0):(u.width=n.width||0,u.height=n.height||0),"url(#"+a+")"}function we(e){this.clear(),e&&this.union(e)}we.prototype={clone(){return new we(this)},clear(){return this.x1=+Number.MAX_VALUE,this.y1=+Number.MAX_VALUE,this.x2=-Number.MAX_VALUE,this.y2=-Number.MAX_VALUE,this},empty(){return this.x1===+Number.MAX_VALUE&&this.y1===+Number.MAX_VALUE&&this.x2===-Number.MAX_VALUE&&this.y2===-Number.MAX_VALUE},equals(e){return this.x1===e.x1&&this.y1===e.y1&&this.x2===e.x2&&this.y2===e.y2},set(e,t,n,r){return n<e?(this.x2=e,this.x1=n):(this.x1=e,this.x2=n),r<t?(this.y2=t,this.y1=r):(this.y1=t,this.y2=r),this},add(e,t){return e<this.x1&&(this.x1=e),t<this.y1&&(this.y1=t),e>this.x2&&(this.x2=e),t>this.y2&&(this.y2=t),this},expand(e){return this.x1-=e,this.y1-=e,this.x2+=e,this.y2+=e,this},round(){return this.x1=Math.floor(this.x1),this.y1=Math.floor(this.y1),this.x2=Math.ceil(this.x2),this.y2=Math.ceil(this.y2),this},scale(e){return this.x1*=e,this.y1*=e,this.x2*=e,this.y2*=e,this},translate(e,t){return this.x1+=e,this.x2+=e,this.y1+=t,this.y2+=t,this},rotate(e,t,n){const r=this.rotatedPoints(e,t,n);return this.clear().add(r[0],r[1]).add(r[2],r[3]).add(r[4],r[5]).add(r[6],r[7])},rotatedPoints(e,t,n){var{x1:r,y1:i,x2:a,y2:u}=this,o=Math.cos(e),s=Math.sin(e),l=t-t*o+n*s,f=n-t*s-n*o;return[o*r-s*i+l,s*r+o*i+f,o*r-s*u+l,s*r+o*u+f,o*a-s*i+l,s*a+o*i+f,o*a-s*u+l,s*a+o*u+f]},union(e){return e.x1<this.x1&&(this.x1=e.x1),e.y1<this.y1&&(this.y1=e.y1),e.x2>this.x2&&(this.x2=e.x2),e.y2>this.y2&&(this.y2=e.y2),this},intersect(e){return e.x1>this.x1&&(this.x1=e.x1),e.y1>this.y1&&(this.y1=e.y1),e.x2<this.x2&&(this.x2=e.x2),e.y2<this.y2&&(this.y2=e.y2),this},encloses(e){return e&&this.x1<=e.x1&&this.x2>=e.x2&&this.y1<=e.y1&&this.y2>=e.y2},alignsWith(e){return e&&(this.x1==e.x1||this.x2==e.x2||this.y1==e.y1||this.y2==e.y2)},intersects(e){return e&&!(this.x2<e.x1||this.x1>e.x2||this.y2<e.y1||this.y1>e.y2)},contains(e,t){return!(e<this.x1||e>this.x2||t<this.y1||t>this.y2)},width(){return this.x2-this.x1},height(){return this.y2-this.y1}};function Hl(e){this.mark=e,this.bounds=this.bounds||new we}function Xl(e){Hl.call(this,e),this.items=this.items||[]}T(Xl,Hl);function o1(e){this._pending=0,this._loader=e||Dl()}function Bm(e){e._pending+=1}function ga(e){e._pending-=1}o1.prototype={pending(){return this._pending},sanitizeURL(e){const t=this;return Bm(t),t._loader.sanitize(e,{context:"href"}).then(n=>(ga(t),n)).catch(()=>(ga(t),null))},loadImage(e){const t=this,n=Ok();return Bm(t),t._loader.sanitize(e,{context:"image"}).then(r=>{const i=r.href;if(!i||!n)throw{url:i};const a=new n,u=G(r,"crossOrigin")?r.crossOrigin:"anonymous";return u!=null&&(a.crossOrigin=u),a.onload=()=>ga(t),a.onerror=()=>ga(t),a.src=i,a}).catch(r=>(ga(t),{complete:!1,width:0,height:0,src:r&&r.url||""}))},ready(){const e=this;return new Promise(t=>{function n(r){e.pending()?setTimeout(()=>{n(!0)},10):t(r)}n(!1)})}};function Xn(e,t,n){if(t.stroke&&t.opacity!==0&&t.strokeOpacity!==0){const r=t.strokeWidth!=null?+t.strokeWidth:1;e.expand(r+(n?eM(t,r):0))}return e}function eM(e,t){return e.strokeJoin&&e.strokeJoin!=="miter"?0:t}const tM=An-1e-8;let Vl,Io,Uo,Ur,fd,qo,cd,dd;const or=(e,t)=>Vl.add(e,t),jo=(e,t)=>or(Io=e,Uo=t),_m=e=>or(e,Vl.y1),Rm=e=>or(Vl.x1,e),Nr=(e,t)=>fd*e+cd*t,zr=(e,t)=>qo*e+dd*t,Wf=(e,t)=>or(Nr(e,t),zr(e,t)),Yf=(e,t)=>jo(Nr(e,t),zr(e,t));function Gu(e,t){return Vl=e,t?(Ur=t*vr,fd=dd=Math.cos(Ur),qo=Math.sin(Ur),cd=-qo):(fd=dd=1,Ur=qo=cd=0),nM}const nM={beginPath(){},closePath(){},moveTo:Yf,lineTo:Yf,rect(e,t,n,r){Ur?(Wf(e+n,t),Wf(e+n,t+r),Wf(e,t+r),Yf(e,t)):(or(e+n,t+r),jo(e,t))},quadraticCurveTo(e,t,n,r){const i=Nr(e,t),a=zr(e,t),u=Nr(n,r),o=zr(n,r);Om(Io,i,u,_m),Om(Uo,a,o,Rm),jo(u,o)},bezierCurveTo(e,t,n,r,i,a){const u=Nr(e,t),o=zr(e,t),s=Nr(n,r),l=zr(n,r),f=Nr(i,a),c=zr(i,a);Tm(Io,u,s,f,_m),Tm(Uo,o,l,c,Rm),jo(f,c)},arc(e,t,n,r,i,a){if(r+=Ur,i+=Ur,Io=n*Math.cos(i)+e,Uo=n*Math.sin(i)+t,Math.abs(i-r)>tM)or(e-n,t-n),or(e+n,t+n);else{const u=l=>or(n*Math.cos(l)+e,n*Math.sin(l)+t);let o,s;if(u(r),u(i),i!==r)if(r=r%An,r<0&&(r+=An),i=i%An,i<0&&(i+=An),i<r&&(a=!a,o=r,r=i,i=o),a)for(i-=An,o=r-r%Lr,s=0;s<4&&o>i;++s,o-=Lr)u(o);else for(o=r-r%Lr+Lr,s=0;s<4&&o<i;++s,o=o+Lr)u(o)}}};function Om(e,t,n,r){const i=(e-t)/(e+n-2*t);0<i&&i<1&&r(e+(t-e)*i)}function Tm(e,t,n,r,i){const a=r-e+3*t-3*n,u=e+n-2*t,o=e-t;let s=0,l=0,f;Math.abs(a)>p7?(f=u*u+o*a,f>=0&&(f=Math.sqrt(f),s=(-u+f)/a,l=(-u-f)/a)):s=.5*o/u,0<s&&s<1&&i(Lm(s,e,t,n,r)),0<l&&l<1&&i(Lm(l,e,t,n,r))}function Lm(e,t,n,r,i){const a=1-e,u=a*a,o=e*e;return u*a*t+3*u*e*n+3*a*o*r+o*e*i}var mr=(mr=pr(1,1))?mr.getContext("2d"):null;const hd=new we;function s1(e){return function(t,n){if(!mr)return!0;e(mr,t),hd.clear().union(t.bounds).intersect(n).round();const{x1:r,y1:i,x2:a,y2:u}=hd;for(let o=i;o<=u;++o)for(let s=r;s<=a;++s)if(mr.isPointInPath(s,o))return!0;return!1}}function l1(e,t){return t.contains(e.x||0,e.y||0)}function dA(e,t){const n=e.x||0,r=e.y||0,i=e.width||0,a=e.height||0;return t.intersects(hd.set(n,r,n+i,r+a))}function hA(e,t){const n=e.x||0,r=e.y||0,i=e.x2!=null?e.x2:n,a=e.y2!=null?e.y2:r;return Fi(t,n,r,i,a)}function Fi(e,t,n,r,i){const{x1:a,y1:u,x2:o,y2:s}=e,l=r-t,f=i-n;let c=0,d=1,h,g,p,m;for(m=0;m<4;++m){if(m===0&&(h=-l,g=-(a-t)),m===1&&(h=l,g=o-t),m===2&&(h=-f,g=-(u-n)),m===3&&(h=f,g=s-n),Math.abs(h)<1e-10&&g<0)return!1;if(p=g/h,h<0){if(p>d)return!1;p>c&&(c=p)}else if(h>0){if(p<c)return!1;p<d&&(d=p)}}return!0}function qi(e,t){e.globalCompositeOperation=t.blend||"source-over"}function Zt(e,t){return e??t}function Nm(e,t){const n=t.length;for(let r=0;r<n;++r)e.addColorStop(t[r].offset,t[r].color);return e}function rM(e,t,n){const r=n.width(),i=n.height();let a;if(t.gradient==="radial")a=e.createRadialGradient(n.x1+Zt(t.x1,.5)*r,n.y1+Zt(t.y1,.5)*i,Math.max(r,i)*Zt(t.r1,0),n.x1+Zt(t.x2,.5)*r,n.y1+Zt(t.y2,.5)*i,Math.max(r,i)*Zt(t.r2,.5));else{const u=Zt(t.x1,0),o=Zt(t.y1,0),s=Zt(t.x2,1),l=Zt(t.y2,0);if(u===s||o===l||r===i)a=e.createLinearGradient(n.x1+u*r,n.y1+o*i,n.x1+s*r,n.y1+l*i);else{const f=pr(Math.ceil(r),Math.ceil(i)),c=f.getContext("2d");return c.scale(r,i),c.fillStyle=Nm(c.createLinearGradient(u,o,s,l),t.stops),c.fillRect(0,0,r,i),e.createPattern(f,"no-repeat")}}return Nm(a,t.stops)}function gA(e,t,n){return n1(n)?rM(e,n,t.bounds):n}function Ns(e,t,n){return n*=t.fillOpacity==null?1:t.fillOpacity,n>0?(e.globalAlpha=n,e.fillStyle=gA(e,t,t.fill),!0):!1}var iM=[];function ji(e,t,n){var r=(r=t.strokeWidth)!=null?r:1;return r<=0?!1:(n*=t.strokeOpacity==null?1:t.strokeOpacity,n>0?(e.globalAlpha=n,e.strokeStyle=gA(e,t,t.stroke),e.lineWidth=r,e.lineCap=t.strokeCap||"butt",e.lineJoin=t.strokeJoin||"miter",e.miterLimit=t.strokeMiterLimit||10,e.setLineDash&&(e.setLineDash(t.strokeDash||iM),e.lineDashOffset=t.strokeDashOffset||0),!0):!1)}function aM(e,t){return e.zindex-t.zindex||e.index-t.index}function f1(e){if(!e.zdirty)return e.zitems;var t=e.items,n=[],r,i,a;for(i=0,a=t.length;i<a;++i)r=t[i],r.index=i,r.zindex&&n.push(r);return e.zdirty=!1,e.zitems=n.sort(aM)}function cn(e,t){var n=e.items,r,i;if(!n||!n.length)return;const a=f1(e);if(a&&a.length){for(r=0,i=n.length;r<i;++r)n[r].zindex||t(n[r]);n=a}for(r=0,i=n.length;r<i;++r)t(n[r])}function zs(e,t){var n=e.items,r,i;if(!n||!n.length)return null;const a=f1(e);for(a&&a.length&&(n=a),i=n.length;--i>=0;)if(r=t(n[i]))return r;if(n===a){for(n=e.items,i=n.length;--i>=0;)if(!n[i].zindex&&(r=t(n[i])))return r}return null}function c1(e){return function(t,n,r){cn(n,i=>{(!r||r.intersects(i.bounds))&&pA(e,t,i,i)})}}function uM(e){return function(t,n,r){n.items.length&&(!r||r.intersects(n.bounds))&&pA(e,t,n.items[0],n.items)}}function pA(e,t,n,r){var i=n.opacity==null?1:n.opacity;i!==0&&(e(t,r)||(qi(t,n),n.fill&&Ns(t,n,i)&&t.fill(),n.stroke&&ji(t,n,i)&&t.stroke()))}function Kl(e){return e=e||Ct,function(t,n,r,i,a,u){return r*=t.pixelRatio,i*=t.pixelRatio,zs(n,o=>{const s=o.bounds;if(!(s&&!s.contains(a,u)||!s)&&e(t,o,r,i,a,u))return o})}}function Wu(e,t){return function(n,r,i,a){var u=Array.isArray(r)?r[0]:r,o=t??u.fill,s=u.stroke&&n.isPointInStroke,l,f;return s&&(l=u.strokeWidth,f=u.strokeCap,n.lineWidth=l??1,n.lineCap=f??"butt"),e(n,r)?!1:o&&n.isPointInPath(i,a)||s&&n.isPointInStroke(i,a)}}function d1(e){return Kl(Wu(e))}function Wr(e,t){return"translate("+e+","+t+")"}function h1(e){return"rotate("+e+")"}function oM(e,t){return"scale("+e+","+t+")"}function mA(e){return Wr(e.x||0,e.y||0)}function sM(e){return Wr(e.x||0,e.y||0)+(e.angle?" "+h1(e.angle):"")}function lM(e){return Wr(e.x||0,e.y||0)+(e.angle?" "+h1(e.angle):"")+(e.scaleX||e.scaleY?" "+oM(e.scaleX||1,e.scaleY||1):"")}function g1(e,t,n){function r(u,o){u("transform",sM(o)),u("d",t(null,o))}function i(u,o){return t(Gu(u,o.angle),o),Xn(u,o).translate(o.x||0,o.y||0)}function a(u,o){var s=o.x||0,l=o.y||0,f=o.angle||0;u.translate(s,l),f&&u.rotate(f*=vr),u.beginPath(),t(u,o),f&&u.rotate(-f),u.translate(-s,-l)}return{type:e,tag:"path",nested:!1,attr:r,bound:i,draw:c1(a),pick:d1(a),isect:n||s1(a)}}var fM=g1("arc",X7);function cM(e,t){for(var n=e[0].orient==="horizontal"?t[1]:t[0],r=e[0].orient==="horizontal"?"y":"x",i=e.length,a=1/0,u,o;--i>=0;)e[i].defined!==!1&&(o=Math.abs(e[i][r]-n),o<a&&(a=o,u=e[i]));return u}function dM(e,t){for(var n=Math.pow(e[0].strokeWidth||1,2),r=e.length,i,a,u;--r>=0;)if(e[r].defined!==!1&&(i=e[r].x-t[0],a=e[r].y-t[1],u=i*i+a*a,u<n))return e[r];return null}function hM(e,t){for(var n=e.length,r,i,a;--n>=0;)if(e[n].defined!==!1&&(r=e[n].x-t[0],i=e[n].y-t[1],a=r*r+i*i,r=e[n].size||1,a<r*r))return e[n];return null}function p1(e,t,n){function r(s,l){var f=l.mark.items;f.length&&s("d",t(null,f))}function i(s,l){var f=l.items;return f.length===0?s:(t(Gu(s),f),Xn(s,f[0]))}function a(s,l){s.beginPath(),t(s,l)}const u=Wu(a);function o(s,l,f,c,d,h){var g=l.items,p=l.bounds;return!g||!g.length||p&&!p.contains(d,h)?null:(f*=s.pixelRatio,c*=s.pixelRatio,u(s,g,f,c)?g[0]:null)}return{type:e,tag:"path",nested:!0,attr:r,bound:i,draw:uM(a),pick:o,isect:l1,tip:n}}var gM=p1("area",V7,cM);function pM(e,t){var n=t.clip;e.save(),J(n)?(e.beginPath(),n(e),e.clip()):yA(e,t.group)}function yA(e,t){e.beginPath(),a1(t)?ra(e,t,0,0):e.rect(0,0,t.width||0,t.height||0),e.clip()}function vA(e){const t=Zt(e.strokeWidth,1);return e.strokeOffset!=null?e.strokeOffset:e.stroke&&t>.5&&t<1.5?.5-Math.abs(t-1):0}function mM(e,t){e("transform",mA(t))}function xA(e,t){const n=vA(t);e("d",ra(null,t,n,n))}function yM(e,t){e("class","background"),e("aria-hidden",!0),xA(e,t)}function vM(e,t){e("class","foreground"),e("aria-hidden",!0),t.strokeForeground?xA(e,t):e("d","")}function xM(e,t,n){const r=t.clip?u1(n,t,t):null;e("clip-path",r)}function bM(e,t){if(!t.clip&&t.items){const n=t.items,r=n.length;for(let i=0;i<r;++i)e.union(n[i].bounds)}return(t.clip||t.width||t.height)&&!t.noBound&&e.add(0,0).add(t.width||0,t.height||0),Xn(e,t),e.translate(t.x||0,t.y||0)}function du(e,t,n,r){const i=vA(t);e.beginPath(),ra(e,t,(n||0)+i,(r||0)+i)}const AM=Wu(du),EM=Wu(du,!1),wM=Wu(du,!0);function DM(e,t,n){cn(t,r=>{const i=r.x||0,a=r.y||0,u=r.strokeForeground,o=r.opacity==null?1:r.opacity;(r.stroke||r.fill)&&o&&(du(e,r,i,a),qi(e,r),r.fill&&Ns(e,r,o)&&e.fill(),r.stroke&&!u&&ji(e,r,o)&&e.stroke()),e.save(),e.translate(i,a),r.clip&&yA(e,r),n&&n.translate(-i,-a),cn(r,s=>{this.draw(e,s,n)}),n&&n.translate(i,a),e.restore(),u&&r.stroke&&o&&(du(e,r,i,a),qi(e,r),ji(e,r,o)&&e.stroke())})}function CM(e,t,n,r,i,a){if(t.bounds&&!t.bounds.contains(i,a)||!t.items)return null;const u=n*e.pixelRatio,o=r*e.pixelRatio;return zs(t,s=>{let l,f,c;const d=s.bounds;if(d&&!d.contains(i,a))return;f=s.x||0,c=s.y||0;const h=f+(s.width||0),g=c+(s.height||0),p=s.clip;if(p&&(i<f||i>h||a<c||a>g))return;if(e.save(),e.translate(f,c),f=i-f,c=a-c,p&&a1(s)&&!wM(e,s,u,o))return e.restore(),null;const m=s.strokeForeground,y=t.interactive!==!1;return y&&m&&s.stroke&&EM(e,s,u,o)?(e.restore(),s):(l=zs(s,v=>FM(v,f,c)?this.pick(v,n,r,f,c):null),!l&&y&&(s.fill||!m&&s.stroke)&&AM(e,s,u,o)&&(l=s),e.restore(),l||null)})}function FM(e,t,n){return(e.interactive!==!1||e.marktype==="group")&&e.bounds&&e.bounds.contains(t,n)}var kM={type:"group",tag:"g",nested:!1,attr:mM,bound:bM,draw:DM,pick:CM,isect:dA,content:xM,background:yM,foreground:vM},hu={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",version:"1.1"};function m1(e,t){var n=e.image;return(!n||e.url&&e.url!==n.url)&&(n={complete:!1,width:0,height:0},t.loadImage(e.url).then(r=>{e.image=r,e.image.url=e.url})),n}function y1(e,t){return e.width!=null?e.width:!t||!t.width?0:e.aspect!==!1&&e.height?e.height*t.width/t.height:t.width}function v1(e,t){return e.height!=null?e.height:!t||!t.height?0:e.aspect!==!1&&e.width?e.width*t.height/t.width:t.height}function Jl(e,t){return e==="center"?t/2:e==="right"?t:0}function Ql(e,t){return e==="middle"?t/2:e==="bottom"?t:0}function MM(e,t,n){const r=m1(t,n),i=y1(t,r),a=v1(t,r),u=(t.x||0)-Jl(t.align,i),o=(t.y||0)-Ql(t.baseline,a),s=!r.src&&r.toDataURL?r.toDataURL():r.src||"";e("href",s,hu["xmlns:xlink"],"xlink:href"),e("transform",Wr(u,o)),e("width",i),e("height",a),e("preserveAspectRatio",t.aspect===!1?"none":"xMidYMid")}function SM(e,t){const n=t.image,r=y1(t,n),i=v1(t,n),a=(t.x||0)-Jl(t.align,r),u=(t.y||0)-Ql(t.baseline,i);return e.set(a,u,a+r,u+i)}function $M(e,t,n){cn(t,r=>{if(n&&!n.intersects(r.bounds))return;const i=m1(r,this);let a=y1(r,i),u=v1(r,i);if(a===0||u===0)return;let o=(r.x||0)-Jl(r.align,a),s=(r.y||0)-Ql(r.baseline,u),l,f,c,d;r.aspect!==!1&&(f=i.width/i.height,c=r.width/r.height,f===f&&c===c&&f!==c&&(c<f?(d=a/f,s+=(u-d)/2,u=d):(d=u*f,o+=(a-d)/2,a=d))),(i.complete||i.toDataURL)&&(qi(e,r),e.globalAlpha=(l=r.opacity)!=null?l:1,e.imageSmoothingEnabled=r.smooth!==!1,e.drawImage(i,o,s,a,u))})}var BM={type:"image",tag:"image",nested:!1,attr:MM,bound:SM,draw:$M,pick:Kl(),isect:Ct,get:m1,xOffset:Jl,yOffset:Ql},_M=p1("line",K7,dM);function RM(e,t){var n=t.scaleX||1,r=t.scaleY||1;(n!==1||r!==1)&&e("vector-effect","non-scaling-stroke"),e("transform",lM(t)),e("d",t.path)}function Go(e,t){var n=t.path;if(n==null)return!0;var r=t.x||0,i=t.y||0,a=t.scaleX||1,u=t.scaleY||1,o=(t.angle||0)*vr,s=t.pathCache;(!s||s.path!==n)&&((t.pathCache=s=Qr(n)).path=n),o&&e.rotate&&e.translate?(e.translate(r,i),e.rotate(o),Ui(e,s,0,0,a,u),e.rotate(-o),e.translate(-r,-i)):Ui(e,s,r,i,a,u)}function OM(e,t){return Go(Gu(e,t.angle),t)?e.set(0,0,0,0):Xn(e,t,!0)}var TM={type:"path",tag:"path",nested:!1,attr:RM,bound:OM,draw:c1(Go),pick:d1(Go),isect:s1(Go)};function LM(e,t){e("d",ra(null,t))}function NM(e,t){var n,r;return Xn(e.set(n=t.x||0,r=t.y||0,n+t.width||0,r+t.height||0),t)}function zm(e,t){e.beginPath(),ra(e,t)}var zM={type:"rect",tag:"path",nested:!1,attr:LM,bound:NM,draw:c1(zm),pick:d1(zm),isect:dA};function PM(e,t){e("transform",mA(t)),e("x2",t.x2!=null?t.x2-(t.x||0):0),e("y2",t.y2!=null?t.y2-(t.y||0):0)}function IM(e,t){var n,r;return Xn(e.set(n=t.x||0,r=t.y||0,t.x2!=null?t.x2:n,t.y2!=null?t.y2:r),t)}function bA(e,t,n){var r,i,a,u;return t.stroke&&ji(e,t,n)?(r=t.x||0,i=t.y||0,a=t.x2!=null?t.x2:r,u=t.y2!=null?t.y2:i,e.beginPath(),e.moveTo(r,i),e.lineTo(a,u),!0):!1}function UM(e,t,n){cn(t,r=>{if(!(n&&!n.intersects(r.bounds))){var i=r.opacity==null?1:r.opacity;i&&bA(e,r,i)&&(qi(e,r),e.stroke())}})}function qM(e,t,n,r){return e.isPointInStroke?bA(e,t,1)&&e.isPointInStroke(n,r):!1}var jM={type:"rule",tag:"line",nested:!1,attr:PM,bound:IM,draw:UM,pick:Kl(qM),isect:hA},GM=g1("shape",J7),WM=g1("symbol",Q7,l1);const Pm=yx();var on={height:kn,measureWidth:x1,estimateWidth:gd,width:gd,canvas:AA};AA(!0);function AA(e){on.width=e&&mr?x1:gd}function gd(e,t){return EA(br(e,t),kn(e))}function EA(e,t){return~~(.8*e.length*t)}function x1(e,t){return kn(e)<=0||!(t=br(e,t))?0:wA(t,Zl(e))}function wA(e,t){const n=`(${t}) ${e}`;let r=Pm.get(n);return r===void 0&&(mr.font=t,r=mr.measureText(e).width,Pm.set(n,r)),r}function kn(e){return e.fontSize!=null?+e.fontSize||0:11}function xr(e){return e.lineHeight!=null?e.lineHeight:kn(e)+2}function YM(e){return z(e)?e.length>1?e:e[0]:e}function Yu(e){return YM(e.lineBreak&&e.text&&!z(e.text)?e.text.split(e.lineBreak):e.text)}function b1(e){const t=Yu(e);return(z(t)?t.length-1:0)*xr(e)}function br(e,t){const n=t==null?"":(t+"").trim();return e.limit>0&&n.length?XM(e,n):n}function HM(e){if(on.width===x1){const t=Zl(e);return n=>wA(n,t)}else{const t=kn(e);return n=>EA(n,t)}}function XM(e,t){var n=+e.limit,r=HM(e);if(r(t)<n)return t;var i=e.ellipsis||"…",a=e.dir==="rtl",u=0,o=t.length,s;if(n-=r(i),a){for(;u<o;)s=u+o>>>1,r(t.slice(s))>n?u=s+1:o=s;return i+t.slice(u)}else{for(;u<o;)s=1+(u+o>>>1),r(t.slice(0,s))<n?u=s:o=s-1;return t.slice(0,u)+i}}function Hu(e,t){var n=e.font;return(t&&n?String(n).replace(/"/g,"'"):n)||"sans-serif"}function Zl(e,t){return(e.fontStyle?e.fontStyle+" ":"")+(e.fontVariant?e.fontVariant+" ":"")+(e.fontWeight?e.fontWeight+" ":"")+kn(e)+"px "+Hu(e,t)}function A1(e){var t=e.baseline,n=kn(e);return Math.round(t==="top"?.79*n:t==="middle"?.3*n:t==="bottom"?-.21*n:t==="line-top"?.29*n+.5*xr(e):t==="line-bottom"?.29*n-.5*xr(e):0)}const VM={left:"start",center:"middle",right:"end"},Xa=new we;function ef(e){var t=e.x||0,n=e.y||0,r=e.radius||0,i;return r&&(i=(e.theta||0)-Lr,t+=r*Math.cos(i),n+=r*Math.sin(i)),Xa.x1=t,Xa.y1=n,Xa}function KM(e,t){var n=t.dx||0,r=(t.dy||0)+A1(t),i=ef(t),a=i.x1,u=i.y1,o=t.angle||0,s;e("text-anchor",VM[t.align]||"start"),o?(s=Wr(a,u)+" "+h1(o),(n||r)&&(s+=" "+Wr(n,r))):s=Wr(a+n,u+r),e("transform",s)}function E1(e,t,n){var r=on.height(t),i=t.align,a=ef(t),u=a.x1,o=a.y1,s=t.dx||0,l=(t.dy||0)+A1(t)-Math.round(.8*r),f=Yu(t),c;if(z(f)?(r+=xr(t)*(f.length-1),c=f.reduce((d,h)=>Math.max(d,on.width(t,h)),0)):c=on.width(t,f),i==="center"?s-=c/2:i==="right"&&(s-=c),e.set(s+=u,l+=o,s+c,l+r),t.angle&&!n)e.rotate(t.angle*vr,u,o);else if(n===2)return e.rotatedPoints(t.angle*vr,u,o);return e}function JM(e,t,n){cn(t,r=>{var i=r.opacity==null?1:r.opacity,a,u,o,s,l,f,c;if(!(n&&!n.intersects(r.bounds)||i===0||r.fontSize<=0||r.text==null||r.text.length===0)){if(e.font=Zl(r),e.textAlign=r.align||"left",a=ef(r),u=a.x1,o=a.y1,r.angle&&(e.save(),e.translate(u,o),e.rotate(r.angle*vr),u=o=0),u+=r.dx||0,o+=(r.dy||0)+A1(r),f=Yu(r),qi(e,r),z(f))for(l=xr(r),s=0;s<f.length;++s)c=br(r,f[s]),r.fill&&Ns(e,r,i)&&e.fillText(c,u,o),r.stroke&&ji(e,r,i)&&e.strokeText(c,u,o),o+=l;else c=br(r,f),r.fill&&Ns(e,r,i)&&e.fillText(c,u,o),r.stroke&&ji(e,r,i)&&e.strokeText(c,u,o);r.angle&&e.restore()}})}function QM(e,t,n,r,i,a){if(t.fontSize<=0)return!1;if(!t.angle)return!0;var u=ef(t),o=u.x1,s=u.y1,l=E1(Xa,t,1),f=-t.angle*vr,c=Math.cos(f),d=Math.sin(f),h=c*i-d*a+(o-c*o+d*s),g=d*i+c*a+(s-d*o-c*s);return l.contains(h,g)}function ZM(e,t){const n=E1(Xa,e,2);return Fi(t,n[0],n[1],n[2],n[3])||Fi(t,n[0],n[1],n[4],n[5])||Fi(t,n[4],n[5],n[6],n[7])||Fi(t,n[2],n[3],n[6],n[7])}var e4={type:"text",tag:"text",nested:!1,attr:KM,bound:E1,draw:JM,pick:Kl(QM),isect:ZM},t4=p1("trail",Z7,hM),Pt={arc:fM,area:gM,group:kM,image:BM,line:_M,path:TM,rect:zM,rule:jM,shape:GM,symbol:WM,text:e4,trail:t4};function pd(e,t,n){var r=Pt[e.mark.marktype],i=t||r.bound;return r.nested&&(e=e.mark),i(e.bounds||(e.bounds=new we),e,n)}var Im={mark:null};function DA(e,t,n){var r=Pt[e.marktype],i=r.bound,a=e.items,u=a&&a.length,o,s,l,f;if(r.nested)return u?l=a[0]:(Im.mark=e,l=Im),f=pd(l,i,n),t=t&&t.union(f)||f,t;if(t=t||e.bounds&&e.bounds.clear()||new we,u)for(o=0,s=a.length;o<s;++o)t.union(pd(a[o],i,n));return e.bounds=t}const n4=["marktype","name","role","interactive","clip","items","zindex","x","y","width","height","align","baseline","fill","fillOpacity","opacity","blend","stroke","strokeOpacity","strokeWidth","strokeCap","strokeDash","strokeDashOffset","strokeForeground","strokeOffset","startAngle","endAngle","innerRadius","outerRadius","cornerRadius","padAngle","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight","interpolate","tension","orient","defined","url","aspect","smooth","path","scaleX","scaleY","x2","y2","size","shape","text","angle","theta","radius","dir","dx","dy","ellipsis","limit","lineBreak","lineHeight","font","fontSize","fontWeight","fontStyle","fontVariant","description","aria","ariaRole","ariaRoleDescription"];function CA(e,t){return JSON.stringify(e,n4,t)}function FA(e){const t=typeof e=="string"?JSON.parse(e):e;return kA(t)}function kA(e){var t=e.marktype,n=e.items,r,i,a;if(n)for(i=0,a=n.length;i<a;++i)r=t?"mark":"group",n[i][r]=e,n[i].zindex&&(n[i][r].zdirty=!0),(t||r)==="group"&&kA(n[i]);return t&&DA(e),e}function w1(e){arguments.length?this.root=FA(e):(this.root=MA({marktype:"group",name:"root",role:"frame"}),this.root.items=[new Xl(this.root)])}w1.prototype={toJSON(e){return CA(this.root,e||0)},mark(e,t,n){t=t||this.root.items[0];const r=MA(e,t);return t.items[n]=r,r.zindex&&(r.group.zdirty=!0),r}};function MA(e,t){const n={bounds:new we,clip:!!e.clip,group:t,interactive:e.interactive!==!1,items:[],marktype:e.marktype,name:e.name||void 0,role:e.role||void 0,zindex:e.zindex||0};return e.aria!=null&&(n.aria=e.aria),e.description&&(n.description=e.description),n}function sr(e,t,n){return!e&&typeof document<"u"&&document.createElement&&(e=document),e?n?e.createElementNS(n,t):e.createElement(t):null}function D1(e,t){t=t.toLowerCase();for(var n=e.childNodes,r=0,i=n.length;r<i;++r)if(n[r].tagName.toLowerCase()===t)return n[r]}function xt(e,t,n,r){var i=e.childNodes[t],a;return(!i||i.tagName.toLowerCase()!==n.toLowerCase())&&(a=i||null,i=sr(e.ownerDocument,n,r),e.insertBefore(i,a)),i}function nn(e,t){for(var n=e.childNodes,r=n.length;r>t;)e.removeChild(n[--r]);return e}function SA(e){return"mark-"+e.marktype+(e.role?" role-"+e.role:"")+(e.name?" "+e.name:"")}function tf(e,t){const n=t.getBoundingClientRect();return[e.clientX-n.left-(t.clientLeft||0),e.clientY-n.top-(t.clientTop||0)]}function r4(e,t,n,r){var i=e&&e.mark,a,u;if(i&&(a=Pt[i.marktype]).tip){for(u=tf(t,n),u[0]-=r[0],u[1]-=r[1];e=e.mark.group;)u[0]-=e.x||0,u[1]-=e.y||0;e=a.tip(i.items,u)}return e}function Ar(e,t){this._active=null,this._handlers={},this._loader=e||Dl(),this._tooltip=t||i4}function i4(e,t,n,r){e.element().setAttribute("title",r||"")}Ar.prototype={initialize(e,t,n){return this._el=e,this._obj=n||null,this.origin(t)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},origin(e){return arguments.length?(this._origin=e||[0,0],this):this._origin.slice()},scene(e){return arguments.length?(this._scene=e,this):this._scene},on(){},off(){},_handlerIndex(e,t,n){for(let r=e?e.length:0;--r>=0;)if(e[r].type===t&&(!n||e[r].handler===n))return r;return-1},handlers(e){const t=this._handlers,n=[];if(e)n.push(...t[this.eventName(e)]);else for(const r in t)n.push(...t[r]);return n},eventName(e){const t=e.indexOf(".");return t<0?e:e.slice(0,t)},handleHref(e,t,n){this._loader.sanitize(n,{context:"href"}).then(r=>{const i=new MouseEvent(e.type,e),a=sr(null,"a");for(const u in r)a.setAttribute(u,r[u]);a.dispatchEvent(i)}).catch(()=>{})},handleTooltip(e,t,n){if(t&&t.tooltip!=null){t=r4(t,e,this.canvas(),this._origin);const r=n&&t&&t.tooltip||null;this._tooltip.call(this._obj,this,e,t,r)}},getItemBoundingClientRect(e){const t=this.canvas();if(!t)return;const n=t.getBoundingClientRect(),r=this._origin,i=e.bounds,a=i.width(),u=i.height();let o=i.x1+r[0]+n.left,s=i.y1+r[1]+n.top;for(;e.mark&&(e=e.mark.group);)o+=e.x||0,s+=e.y||0;return{x:o,y:s,width:a,height:u,left:o,top:s,right:o+a,bottom:s+u}}};function Mn(e){this._el=null,this._bgcolor=null,this._loader=new o1(e)}Mn.prototype={initialize(e,t,n,r,i){return this._el=e,this.resize(t,n,r,i)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},background(e){return arguments.length===0?this._bgcolor:(this._bgcolor=e,this)},resize(e,t,n,r){return this._width=e,this._height=t,this._origin=n||[0,0],this._scale=r||1,this},dirty(){},render(e){const t=this;return t._call=function(){t._render(e)},t._call(),t._call=null,t},_render(){},renderAsync(e){const t=this.render(e);return this._ready?this._ready.then(()=>t):Promise.resolve(t)},_load(e,t){var n=this,r=n._loader[e](t);if(!n._ready){const i=n._call;n._ready=n._loader.ready().then(a=>{a&&i(),n._ready=null})}return r},sanitizeURL(e){return this._load("sanitizeURL",e)},loadImage(e){return this._load("loadImage",e)}};const a4="keydown",u4="keypress",o4="keyup",$A="dragenter",Wo="dragleave",BA="dragover",md="mousedown",s4="mouseup",Ps="mousemove",Va="mouseout",_A="mouseover",Is="click",l4="dblclick",f4="wheel",RA="mousewheel",Us="touchstart",qs="touchmove",js="touchend",c4=[a4,u4,o4,$A,Wo,BA,md,s4,Ps,Va,_A,Is,l4,f4,RA,Us,qs,js],yd=Ps,gu=Va,vd=Is;function Xu(e,t){Ar.call(this,e,t),this._down=null,this._touch=null,this._first=!0,this._events={}}const d4=e=>e===Us||e===qs||e===js?[Us,qs,js]:[e];function Um(e,t){d4(t).forEach(n=>h4(e,n))}function h4(e,t){const n=e.canvas();n&&!e._events[t]&&(e._events[t]=1,n.addEventListener(t,e[t]?r=>e[t](r):r=>e.fire(t,r)))}function qm(e,t,n){return function(r){const i=this._active,a=this.pickEvent(r);a===i?this.fire(e,r):((!i||!i.exit)&&this.fire(n,r),this._active=a,this.fire(t,r),this.fire(e,r))}}function jm(e){return function(t){this.fire(e,t),this._active=null}}T(Xu,Ar,{initialize(e,t,n){return this._canvas=e&&D1(e,"canvas"),[Is,md,Ps,Va,Wo].forEach(r=>Um(this,r)),Ar.prototype.initialize.call(this,e,t,n)},canvas(){return this._canvas},context(){return this._canvas.getContext("2d")},events:c4,DOMMouseScroll(e){this.fire(RA,e)},mousemove:qm(Ps,_A,Va),dragover:qm(BA,$A,Wo),mouseout:jm(Va),dragleave:jm(Wo),mousedown(e){this._down=this._active,this.fire(md,e)},click(e){this._down===this._active&&(this.fire(Is,e),this._down=null)},touchstart(e){this._touch=this.pickEvent(e.changedTouches[0]),this._first&&(this._active=this._touch,this._first=!1),this.fire(Us,e,!0)},touchmove(e){this.fire(qs,e,!0)},touchend(e){this.fire(js,e,!0),this._touch=null},fire(e,t,n){const r=n?this._touch:this._active,i=this._handlers[e];if(t.vegaType=e,e===vd&&r&&r.href?this.handleHref(t,r,r.href):(e===yd||e===gu)&&this.handleTooltip(t,r,e!==gu),i)for(let a=0,u=i.length;a<u;++a)i[a].handler.call(this._obj,t,r)},on(e,t){const n=this.eventName(e),r=this._handlers;return this._handlerIndex(r[n],e,t)<0&&(Um(this,e),(r[n]||(r[n]=[])).push({type:e,handler:t})),this},off(e,t){const n=this.eventName(e),r=this._handlers[n],i=this._handlerIndex(r,e,t);return i>=0&&r.splice(i,1),this},pickEvent(e){const t=tf(e,this._canvas),n=this._origin;return this.pick(this._scene,t[0],t[1],t[0]-n[0],t[1]-n[1])},pick(e,t,n,r,i){const a=this.context();return Pt[e.marktype].pick.call(this,a,e,t,n,r,i)}});function g4(){return typeof window<"u"&&window.devicePixelRatio||1}var p4=g4();function m4(e,t,n,r,i,a){const u=typeof HTMLElement<"u"&&e instanceof HTMLElement&&e.parentNode!=null,o=e.getContext("2d"),s=u?p4:i;e.width=t*s,e.height=n*s;for(const l in a)o[l]=a[l];return u&&s!==1&&(e.style.width=t+"px",e.style.height=n+"px"),o.pixelRatio=s,o.setTransform(s,0,0,s,s*r[0],s*r[1]),e}function Gs(e){Mn.call(this,e),this._options={},this._redraw=!1,this._dirty=new we,this._tempb=new we}const Gm=Mn.prototype,y4=(e,t,n)=>new we().set(0,0,t,n).translate(-e[0],-e[1]);function v4(e,t,n){return t.expand(1).round(),e.pixelRatio%1&&t.scale(e.pixelRatio).round().scale(1/e.pixelRatio),t.translate(-(n[0]%1),-(n[1]%1)),e.beginPath(),e.rect(t.x1,t.y1,t.width(),t.height()),e.clip(),t}T(Gs,Mn,{initialize(e,t,n,r,i,a){return this._options=a||{},this._canvas=this._options.externalContext?null:pr(1,1,this._options.type),e&&this._canvas&&(nn(e,0).appendChild(this._canvas),this._canvas.setAttribute("class","marks")),Gm.initialize.call(this,e,t,n,r,i)},resize(e,t,n,r){if(Gm.resize.call(this,e,t,n,r),this._canvas)m4(this._canvas,this._width,this._height,this._origin,this._scale,this._options.context);else{const i=this._options.externalContext;i||S("CanvasRenderer is missing a valid canvas or context"),i.scale(this._scale,this._scale),i.translate(this._origin[0],this._origin[1])}return this._redraw=!0,this},canvas(){return this._canvas},context(){return this._options.externalContext||(this._canvas?this._canvas.getContext("2d"):null)},dirty(e){const t=this._tempb.clear().union(e.bounds);let n=e.mark.group;for(;n;)t.translate(n.x||0,n.y||0),n=n.mark.group;this._dirty.union(t)},_render(e){const t=this.context(),n=this._origin,r=this._width,i=this._height,a=this._dirty,u=y4(n,r,i);t.save();const o=this._redraw||a.empty()?(this._redraw=!1,u.expand(1)):v4(t,u.intersect(a),n);return this.clear(-n[0],-n[1],r,i),this.draw(t,e,o),t.restore(),a.clear(),this},draw(e,t,n){const r=Pt[t.marktype];t.clip&&pM(e,t),r.draw.call(this,e,t,n),t.clip&&e.restore()},clear(e,t,n,r){const i=this._options,a=this.context();i.type!=="pdf"&&!i.externalContext&&a.clearRect(e,t,n,r),this._bgcolor!=null&&(a.fillStyle=this._bgcolor,a.fillRect(e,t,n,r))}});function C1(e,t){Ar.call(this,e,t);const n=this;n._hrefHandler=xd(n,(r,i)=>{i&&i.href&&n.handleHref(r,i,i.href)}),n._tooltipHandler=xd(n,(r,i)=>{n.handleTooltip(r,i,r.type!==gu)})}const xd=(e,t)=>n=>{let r=n.target.__data__;r=Array.isArray(r)?r[0]:r,n.vegaType=n.type,t.call(e._obj,n,r)};T(C1,Ar,{initialize(e,t,n){let r=this._svg;return r&&(r.removeEventListener(vd,this._hrefHandler),r.removeEventListener(yd,this._tooltipHandler),r.removeEventListener(gu,this._tooltipHandler)),this._svg=r=e&&D1(e,"svg"),r&&(r.addEventListener(vd,this._hrefHandler),r.addEventListener(yd,this._tooltipHandler),r.addEventListener(gu,this._tooltipHandler)),Ar.prototype.initialize.call(this,e,t,n)},canvas(){return this._svg},on(e,t){const n=this.eventName(e),r=this._handlers;if(this._handlerIndex(r[n],e,t)<0){const a={type:e,handler:t,listener:xd(this,t)};(r[n]||(r[n]=[])).push(a),this._svg&&this._svg.addEventListener(n,a.listener)}return this},off(e,t){const n=this.eventName(e),r=this._handlers[n],i=this._handlerIndex(r,e,t);return i>=0&&(this._svg&&this._svg.removeEventListener(n,r[i].listener),r.splice(i,1)),this}});const OA="aria-hidden",F1="aria-label",k1="role",M1="aria-roledescription",TA="graphics-object",S1="graphics-symbol",LA=(e,t,n)=>({[k1]:e,[M1]:t,[F1]:n||void 0}),x4=Tt(["axis-domain","axis-grid","axis-label","axis-tick","axis-title","legend-band","legend-entry","legend-gradient","legend-label","legend-title","legend-symbol","title"]),Wm={axis:{desc:"axis",caption:E4},legend:{desc:"legend",caption:w4},"title-text":{desc:"title",caption:e=>`Title text '${Hm(e)}'`},"title-subtitle":{desc:"subtitle",caption:e=>`Subtitle text '${Hm(e)}'`}},Ym={ariaRole:k1,ariaRoleDescription:M1,description:F1};function NA(e,t){const n=t.aria===!1;if(e(OA,n||void 0),n||t.description==null)for(const r in Ym)e(Ym[r],void 0);else{const r=t.mark.marktype;e(F1,t.description),e(k1,t.ariaRole||(r==="group"?TA:S1)),e(M1,t.ariaRoleDescription||`${r} mark`)}}function zA(e){return e.aria===!1?{[OA]:!0}:x4[e.role]?null:Wm[e.role]?A4(e,Wm[e.role]):b4(e)}function b4(e){const t=e.marktype,n=t==="group"||t==="text"||e.items.some(r=>r.description!=null&&r.aria!==!1);return LA(n?TA:S1,`${t} mark container`,e.description)}function A4(e,t){try{const n=e.items[0],r=t.caption||(()=>"");return LA(t.role||S1,t.desc,n.description||r(n))}catch{return null}}function Hm(e){return q(e.text).join(" ")}function E4(e){const t=e.datum,n=e.orient,r=t.title?PA(e):null,i=e.context,a=i.scales[t.scale].value,u=i.dataflow.locale(),o=a.type;return`${n==="left"||n==="right"?"Y":"X"}-axis`+(r?` titled '${r}'`:"")+` for a ${Ii(o)?"discrete":o} scale with ${rA(u,a,e)}`}function w4(e){const t=e.datum,n=t.title?PA(e):null,r=`${t.type||""} legend`.trim(),i=t.scales,a=Object.keys(i),u=e.context,o=u.scales[i[a[0]]].value,s=u.dataflow.locale();return C4(r)+(n?` titled '${n}'`:"")+` for ${D4(a)} with ${rA(s,o,e)}`}function PA(e){try{return q(re(e.items).items[0].text).join(" ")}catch{return null}}function D4(e){return e=e.map(t=>t+(t==="fill"||t==="stroke"?" color":"")),e.length<2?e[0]:e.slice(0,-1).join(", ")+" and "+re(e)}function C4(e){return e.length?e[0].toUpperCase()+e.slice(1):e}const IA=e=>(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),F4=e=>IA(e).replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;");function $1(){let e="",t="",n="";const r=[],i=()=>t=n="",a=s=>{t&&(e+=`${t}>${n}`,i()),r.push(s)},u=(s,l)=>(l!=null&&(t+=` ${s}="${F4(l)}"`),o),o={open(s){a(s),t="<"+s;for(var l=arguments.length,f=new Array(l>1?l-1:0),c=1;c<l;c++)f[c-1]=arguments[c];for(const d of f)for(const h in d)u(h,d[h]);return o},close(){const s=r.pop();return t?e+=t+(n?`>${n}</${s}>`:"/>"):e+=`</${s}>`,i(),o},attr:u,text:s=>(n+=IA(s),o),toString:()=>e};return o}const UA=e=>qA($1(),e)+"";function qA(e,t){if(e.open(t.tagName),t.hasAttributes()){const n=t.attributes,r=n.length;for(let i=0;i<r;++i)e.attr(n[i].name,n[i].value)}if(t.hasChildNodes()){const n=t.childNodes;for(const r of n)r.nodeType===3?e.text(r.nodeValue):qA(e,r)}return e.close()}const Ws={fill:"fill",fillOpacity:"fill-opacity",stroke:"stroke",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",strokeCap:"stroke-linecap",strokeJoin:"stroke-linejoin",strokeDash:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeMiterLimit:"stroke-miterlimit",opacity:"opacity"},Ys={blend:"mix-blend-mode"},jA={fill:"none","stroke-miterlimit":10},pa=0,Xm="http://www.w3.org/2000/xmlns/",Me=hu.xmlns;function B1(e){Mn.call(this,e),this._dirtyID=0,this._dirty=[],this._svg=null,this._root=null,this._defs=null}const Hf=Mn.prototype;T(B1,Mn,{initialize(e,t,n,r,i){return this._defs={},this._clearDefs(),e&&(this._svg=xt(e,0,"svg",Me),this._svg.setAttributeNS(Xm,"xmlns",Me),this._svg.setAttributeNS(Xm,"xmlns:xlink",hu["xmlns:xlink"]),this._svg.setAttribute("version",hu.version),this._svg.setAttribute("class","marks"),nn(e,1),this._root=xt(this._svg,pa,"g",Me),lr(this._root,jA),nn(this._svg,pa+1)),this.background(this._bgcolor),Hf.initialize.call(this,e,t,n,r,i)},background(e){return arguments.length&&this._svg&&this._svg.style.setProperty("background-color",e),Hf.background.apply(this,arguments)},resize(e,t,n,r){return Hf.resize.call(this,e,t,n,r),this._svg&&(lr(this._svg,{width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`}),this._root.setAttribute("transform",`translate(${this._origin})`)),this._dirty=[],this},canvas(){return this._svg},svg(){const e=this._svg,t=this._bgcolor;if(!e)return null;let n;t&&(e.removeAttribute("style"),n=xt(e,pa,"rect",Me),lr(n,{width:this._width,height:this._height,fill:t}));const r=UA(e);return t&&(e.removeChild(n),this._svg.style.setProperty("background-color",t)),r},_render(e){return this._dirtyCheck()&&(this._dirtyAll&&this._clearDefs(),this.mark(this._root,e),nn(this._root,1)),this.defs(),this._dirty=[],++this._dirtyID,this},dirty(e){e.dirty!==this._dirtyID&&(e.dirty=this._dirtyID,this._dirty.push(e))},isDirty(e){return this._dirtyAll||!e._svg||!e._svg.ownerSVGElement||e.dirty===this._dirtyID},_dirtyCheck(){this._dirtyAll=!0;const e=this._dirty;if(!e.length||!this._dirtyID)return!0;const t=++this._dirtyID;let n,r,i,a,u,o,s;for(u=0,o=e.length;u<o;++u)if(n=e[u],r=n.mark,r.marktype!==i&&(i=r.marktype,a=Pt[i]),r.zdirty&&r.dirty!==t&&(this._dirtyAll=!1,Vm(n,t),r.items.forEach(l=>{l.dirty=t})),!r.zdirty){if(n.exit){a.nested&&r.items.length?(s=r.items[0],s._svg&&this._update(a,s._svg,s)):n._svg&&(s=n._svg.parentNode,s&&s.removeChild(n._svg)),n._svg=null;continue}n=a.nested?r.items[0]:n,n._update!==t&&(!n._svg||!n._svg.ownerSVGElement?(this._dirtyAll=!1,Vm(n,t)):this._update(a,n._svg,n),n._update=t)}return!this._dirtyAll},mark(e,t,n){if(!this.isDirty(t))return t._svg;const r=this._svg,i=Pt[t.marktype],a=t.interactive===!1?"none":null,u=i.tag==="g",o=Km(t,e,n,"g",r);o.setAttribute("class",SA(t));const s=zA(t);for(const d in s)it(o,d,s[d]);u||it(o,"pointer-events",a),it(o,"clip-path",t.clip?u1(this,t,t.group):null);let l=null,f=0;const c=d=>{const h=this.isDirty(d),g=Km(d,o,l,i.tag,r);h&&(this._update(i,g,d),u&&S4(this,g,d)),l=g,++f};return i.nested?t.items.length&&c(t.items[0]):cn(t,c),nn(o,f),o},_update(e,t,n){qn=t,Ye=t.__values__,NA(Ka,n),e.attr(Ka,n,this);const r=B4[e.type];r&&r.call(this,e,t,n),qn&&this.style(qn,n)},style(e,t){if(t!=null){for(const n in Ws){let r=n==="font"?Hu(t):t[n];if(r===Ye[n])continue;const i=Ws[n];r==null?e.removeAttribute(i):(n1(r)&&(r=aA(r,this._defs.gradient,GA())),e.setAttribute(i,r+"")),Ye[n]=r}for(const n in Ys)Yo(e,Ys[n],t[n])}},defs(){const e=this._svg,t=this._defs;let n=t.el,r=0;for(const i in t.gradient)n||(t.el=n=xt(e,pa+1,"defs",Me)),r=k4(n,t.gradient[i],r);for(const i in t.clipping)n||(t.el=n=xt(e,pa+1,"defs",Me)),r=M4(n,t.clipping[i],r);n&&(r===0?(e.removeChild(n),t.el=null):nn(n,r))},_clearDefs(){const e=this._defs;e.gradient={},e.clipping={}}});function Vm(e,t){for(;e&&e.dirty!==t;e=e.mark.group)if(e.dirty=t,e.mark&&e.mark.dirty!==t)e.mark.dirty=t;else return}function k4(e,t,n){let r,i,a;if(t.gradient==="radial"){let u=xt(e,n++,"pattern",Me);lr(u,{id:Ls+t.id,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"}),u=xt(u,0,"rect",Me),lr(u,{width:1,height:1,fill:`url(${GA()}#${t.id})`}),e=xt(e,n++,"radialGradient",Me),lr(e,{id:t.id,fx:t.x1,fy:t.y1,fr:t.r1,cx:t.x2,cy:t.y2,r:t.r2})}else e=xt(e,n++,"linearGradient",Me),lr(e,{id:t.id,x1:t.x1,x2:t.x2,y1:t.y1,y2:t.y2});for(r=0,i=t.stops.length;r<i;++r)a=xt(e,r,"stop",Me),a.setAttribute("offset",t.stops[r].offset),a.setAttribute("stop-color",t.stops[r].color);return nn(e,r),n}function M4(e,t,n){let r;return e=xt(e,n,"clipPath",Me),e.setAttribute("id",t.id),t.path?(r=xt(e,0,"path",Me),r.setAttribute("d",t.path)):(r=xt(e,0,"rect",Me),lr(r,{x:0,y:0,width:t.width,height:t.height})),nn(e,1),n+1}function S4(e,t,n){t=t.lastChild.previousSibling;let r,i=0;cn(n,a=>{r=e.mark(t,a,r),++i}),nn(t,1+i)}function Km(e,t,n,r,i){let a=e._svg,u;if(!a&&(u=t.ownerDocument,a=sr(u,r,Me),e._svg=a,e.mark&&(a.__data__=e,a.__values__={fill:"default"},r==="g"))){const o=sr(u,"path",Me);a.appendChild(o),o.__data__=e;const s=sr(u,"g",Me);a.appendChild(s),s.__data__=e;const l=sr(u,"path",Me);a.appendChild(l),l.__data__=e,l.__values__={fill:"default"}}return(a.ownerSVGElement!==i||$4(a,n))&&t.insertBefore(a,n?n.nextSibling:t.firstChild),a}function $4(e,t){return e.parentNode&&e.parentNode.childNodes.length>1&&e.previousSibling!=t}let qn=null,Ye=null;const B4={group(e,t,n){const r=qn=t.childNodes[2];Ye=r.__values__,e.foreground(Ka,n,this),Ye=t.__values__,qn=t.childNodes[1],e.content(Ka,n,this);const i=qn=t.childNodes[0];e.background(Ka,n,this);const a=n.mark.interactive===!1?"none":null;if(a!==Ye.events&&(it(r,"pointer-events",a),it(i,"pointer-events",a),Ye.events=a),n.strokeForeground&&n.stroke){const u=n.fill;it(r,"display",null),this.style(i,n),it(i,"stroke",null),u&&(n.fill=null),Ye=r.__values__,this.style(r,n),u&&(n.fill=u),qn=null}else it(r,"display","none")},image(e,t,n){n.smooth===!1?(Yo(t,"image-rendering","optimizeSpeed"),Yo(t,"image-rendering","pixelated")):Yo(t,"image-rendering",null)},text(e,t,n){const r=Yu(n);let i,a,u,o;z(r)?(a=r.map(s=>br(n,s)),i=a.join(`
`),i!==Ye.text&&(nn(t,0),u=t.ownerDocument,o=xr(n),a.forEach((s,l)=>{const f=sr(u,"tspan",Me);f.__data__=n,f.textContent=s,l&&(f.setAttribute("x",0),f.setAttribute("dy",o)),t.appendChild(f)}),Ye.text=i)):(a=br(n,r),a!==Ye.text&&(t.textContent=a,Ye.text=a)),it(t,"font-family",Hu(n)),it(t,"font-size",kn(n)+"px"),it(t,"font-style",n.fontStyle),it(t,"font-variant",n.fontVariant),it(t,"font-weight",n.fontWeight)}};function Ka(e,t,n){t!==Ye[e]&&(n?_4(qn,e,t,n):it(qn,e,t),Ye[e]=t)}function Yo(e,t,n){n!==Ye[t]&&(n==null?e.style.removeProperty(t):e.style.setProperty(t,n+""),Ye[t]=n)}function lr(e,t){for(const n in t)it(e,n,t[n])}function it(e,t,n){n!=null?e.setAttribute(t,n):e.removeAttribute(t)}function _4(e,t,n,r){n!=null?e.setAttributeNS(r,t,n):e.removeAttributeNS(r,t)}function GA(){let e;return typeof window>"u"?"":(e=window.location).hash?e.href.slice(0,-e.hash.length):e.href}function _1(e){Mn.call(this,e),this._text=null,this._defs={gradient:{},clipping:{}}}T(_1,Mn,{svg(){return this._text},_render(e){const t=$1();t.open("svg",Z({},hu,{class:"marks",width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`}));const n=this._bgcolor;return n&&n!=="transparent"&&n!=="none"&&t.open("rect",{width:this._width,height:this._height,fill:n}).close(),t.open("g",jA,{transform:"translate("+this._origin+")"}),this.mark(t,e),t.close(),this.defs(t),this._text=t.close()+"",this},mark(e,t){const n=Pt[t.marktype],r=n.tag,i=[NA,n.attr];e.open("g",{class:SA(t),"clip-path":t.clip?u1(this,t,t.group):null},zA(t),{"pointer-events":r!=="g"&&t.interactive===!1?"none":null});const a=u=>{const o=this.href(u);if(o&&e.open("a",o),e.open(r,this.attr(t,u,i,r!=="g"?r:null)),r==="text"){const s=Yu(u);if(z(s)){const l={x:0,dy:xr(u)};for(let f=0;f<s.length;++f)e.open("tspan",f?l:null).text(br(u,s[f])).close()}else e.text(br(u,s))}else if(r==="g"){const s=u.strokeForeground,l=u.fill,f=u.stroke;s&&f&&(u.stroke=null),e.open("path",this.attr(t,u,n.background,"bgrect")).close(),e.open("g",this.attr(t,u,n.content)),cn(u,c=>this.mark(e,c)),e.close(),s&&f?(l&&(u.fill=null),u.stroke=f,e.open("path",this.attr(t,u,n.foreground,"bgrect")).close(),l&&(u.fill=l)):e.open("path",this.attr(t,u,n.foreground,"bgfore")).close()}e.close(),o&&e.close()};return n.nested?t.items&&t.items.length&&a(t.items[0]):cn(t,a),e.close()},href(e){const t=e.href;let n;if(t){if(n=this._hrefs&&this._hrefs[t])return n;this.sanitizeURL(t).then(r=>{r["xlink:href"]=r.href,r.href=null,(this._hrefs||(this._hrefs={}))[t]=r})}return null},attr(e,t,n,r){const i={},a=(u,o,s,l)=>{i[l||u]=o};return Array.isArray(n)?n.forEach(u=>u(a,t,this)):n(a,t,this),r&&R4(i,t,e,r,this._defs),i},defs(e){const t=this._defs.gradient,n=this._defs.clipping;if(Object.keys(t).length+Object.keys(n).length!==0){e.open("defs");for(const i in t){const a=t[i],u=a.stops;a.gradient==="radial"?(e.open("pattern",{id:Ls+i,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"}),e.open("rect",{width:"1",height:"1",fill:"url(#"+i+")"}).close(),e.close(),e.open("radialGradient",{id:i,fx:a.x1,fy:a.y1,fr:a.r1,cx:a.x2,cy:a.y2,r:a.r2})):e.open("linearGradient",{id:i,x1:a.x1,x2:a.x2,y1:a.y1,y2:a.y2});for(let o=0;o<u.length;++o)e.open("stop",{offset:u[o].offset,"stop-color":u[o].color}).close();e.close()}for(const i in n){const a=n[i];e.open("clipPath",{id:i}),a.path?e.open("path",{d:a.path}).close():e.open("rect",{x:0,y:0,width:a.width,height:a.height}).close(),e.close()}e.close()}}});function R4(e,t,n,r,i){let a;if(t==null||(r==="bgrect"&&n.interactive===!1&&(e["pointer-events"]="none"),r==="bgfore"&&(n.interactive===!1&&(e["pointer-events"]="none"),e.display="none",t.fill!==null)))return e;r==="image"&&t.smooth===!1&&(a=["image-rendering: optimizeSpeed;","image-rendering: pixelated;"]),r==="text"&&(e["font-family"]=Hu(t),e["font-size"]=kn(t)+"px",e["font-style"]=t.fontStyle,e["font-variant"]=t.fontVariant,e["font-weight"]=t.fontWeight);for(const u in Ws){let o=t[u];const s=Ws[u];o==="transparent"&&(s==="fill"||s==="stroke")||o!=null&&(n1(o)&&(o=aA(o,i.gradient,"")),e[s]=o)}for(const u in Ys){const o=t[u];o!=null&&(a=a||[],a.push(`${Ys[u]}: ${o};`))}return a&&(e.style=a.join(" ")),e}const WA="canvas",YA="png",HA="svg",XA="none",fr={Canvas:WA,PNG:YA,SVG:HA,None:XA},Gi={};Gi[WA]=Gi[YA]={renderer:Gs,headless:Gs,handler:Xu};Gi[HA]={renderer:B1,headless:_1,handler:C1};Gi[XA]={};function nf(e,t){return e=String(e||"").toLowerCase(),arguments.length>1?(Gi[e]=t,this):Gi[e]}function R1(e,t,n){const r=[],i=new we().union(t),a=e.marktype;return a?VA(e,i,n,r):a==="group"?KA(e,i,n,r):S("Intersect scene must be mark node or group item.")}function VA(e,t,n,r){if(O4(e,t,n)){const i=e.items,a=e.marktype,u=i.length;let o=0;if(a==="group")for(;o<u;++o)KA(i[o],t,n,r);else for(const s=Pt[a].isect;o<u;++o){const l=i[o];JA(l,t,s)&&r.push(l)}}return r}function O4(e,t,n){return e.bounds&&t.intersects(e.bounds)&&(e.marktype==="group"||e.interactive!==!1&&(!n||n(e)))}function KA(e,t,n,r){n&&n(e.mark)&&JA(e,t,Pt.group.isect)&&r.push(e);const i=e.items,a=i&&i.length;if(a){const u=e.x||0,o=e.y||0;t.translate(-u,-o);for(let s=0;s<a;++s)VA(i[s],t,n,r);t.translate(u,o)}return r}function JA(e,t,n){const r=e.bounds;return t.encloses(r)||t.intersects(r)&&n(e,t)}const Xf=new we;function QA(e){const t=e.clip;if(J(t))t(Gu(Xf.clear()));else if(t)Xf.set(0,0,e.group.width,e.group.height);else return;e.bounds.intersect(Xf)}const T4=1e-9;function O1(e,t,n){return e===t?!0:n==="path"?ZA(e,t):e instanceof Date&&t instanceof Date?+e==+t:Yn(e)&&Yn(t)?Math.abs(e-t)<=T4:!e||!t||!K(e)&&!K(t)?e==t:L4(e,t)}function ZA(e,t){return O1(Qr(e),Qr(t))}function L4(e,t){var n=Object.keys(e),r=Object.keys(t),i,a;if(n.length!==r.length)return!1;for(n.sort(),r.sort(),a=n.length-1;a>=0;a--)if(n[a]!=r[a])return!1;for(a=n.length-1;a>=0;a--)if(i=n[a],!O1(e[i],t[i],i))return!1;return typeof e==typeof t}function N4(){cA(),s7()}const Wi="top",rn="left",un="right",Er="bottom",z4="top-left",P4="top-right",I4="bottom-left",U4="bottom-right",T1="start",bd="middle",at="end",q4="x",j4="y",rf="group",L1="axis",N1="title",G4="frame",W4="scope",z1="legend",eE="row-header",tE="row-footer",nE="row-title",rE="column-header",iE="column-footer",aE="column-title",Y4="padding",H4="symbol",uE="fit",oE="fit-x",sE="fit-y",X4="pad",P1="none",Eo="all",Ad="each",I1="flush",cr="column",dr="row";function lE(e){$.call(this,null,e)}T(lE,$,{transform(e,t){const n=t.dataflow,r=e.mark,i=r.marktype,a=Pt[i],u=a.bound;let o=r.bounds,s;if(a.nested)r.items.length&&n.dirty(r.items[0]),o=wo(r,u),r.items.forEach(l=>{l.bounds.clear().union(o)});else if(i===rf||e.modified())switch(t.visit(t.MOD,l=>n.dirty(l)),o.clear(),r.items.forEach(l=>o.union(wo(l,u))),r.role){case L1:case z1:case N1:t.reflow()}else s=t.changed(t.REM),t.visit(t.ADD,l=>{o.union(wo(l,u))}),t.visit(t.MOD,l=>{s=s||o.alignsWith(l.bounds),n.dirty(l),o.union(wo(l,u))}),s&&(o.clear(),r.items.forEach(l=>o.union(l.bounds)));return QA(r),t.modifies("bounds")}});function wo(e,t,n){return t(e.bounds.clear(),e,n)}const Jm=":vega_identifier:";function U1(e){$.call(this,0,e)}U1.Definition={type:"Identifier",metadata:{modifies:!0},params:[{name:"as",type:"string",required:!0}]};T(U1,$,{transform(e,t){const n=V4(t.dataflow),r=e.as;let i=n.value;return t.visit(t.ADD,a=>a[r]=a[r]||++i),n.set(this.value=i),t}});function V4(e){return e._signals[Jm]||(e._signals[Jm]=e.add(0))}function fE(e){$.call(this,null,e)}T(fE,$,{transform(e,t){let n=this.value;n||(n=t.dataflow.scenegraph().mark(e.markdef,K4(e),e.index),n.group.context=e.context,e.context.group||(e.context.group=n.group),n.source=this.source,n.clip=e.clip,n.interactive=e.interactive,this.value=n);const r=n.marktype===rf?Xl:Hl;return t.visit(t.ADD,i=>r.call(i,n)),(e.modified("clip")||e.modified("interactive"))&&(n.clip=e.clip,n.interactive=!!e.interactive,n.zdirty=!0,t.reflow()),n.items=t.source,t}});function K4(e){const t=e.groups,n=e.parent;return t&&t.size===1?t.get(Object.keys(t.object)[0]):t&&n?t.lookup(n):null}function cE(e){$.call(this,null,e)}const Qm={parity:e=>e.filter((t,n)=>n%2?t.opacity=0:1),greedy:(e,t)=>{let n;return e.filter((r,i)=>!i||!dE(n.bounds,r.bounds,t)?(n=r,1):r.opacity=0)}},dE=(e,t,n)=>n>Math.max(t.x1-e.x2,e.x1-t.x2,t.y1-e.y2,e.y1-t.y2),Zm=(e,t)=>{for(var n=1,r=e.length,i=e[0].bounds,a;n<r;i=a,++n)if(dE(i,a=e[n].bounds,t))return!0},J4=e=>{const t=e.bounds;return t.width()>1&&t.height()>1},Q4=(e,t,n)=>{var r=e.range(),i=new we;return t===Wi||t===Er?i.set(r[0],-1/0,r[1],1/0):i.set(-1/0,r[0],1/0,r[1]),i.expand(n||1),a=>i.encloses(a.bounds)},ey=e=>(e.forEach(t=>t.opacity=1),e),ty=(e,t)=>e.reflow(t.modified()).modifies("opacity");T(cE,$,{transform(e,t){const n=Qm[e.method]||Qm.parity,r=e.separation||0;let i=t.materialize(t.SOURCE).source,a,u;if(!i||!i.length)return;if(!e.method)return e.modified("method")&&(ey(i),t=ty(t,e)),t;if(i=i.filter(J4),!i.length)return;if(e.sort&&(i=i.slice().sort(e.sort)),a=ey(i),t=ty(t,e),a.length>=3&&Zm(a,r)){do a=n(a,r);while(a.length>=3&&Zm(a,r));a.length<3&&!re(i).opacity&&(a.length>1&&(re(a).opacity=0),re(i).opacity=1)}e.boundScale&&e.boundTolerance>=0&&(u=Q4(e.boundScale,e.boundOrient,+e.boundTolerance),i.forEach(s=>{u(s)||(s.opacity=0)}));const o=a[0].mark.bounds.clear();return i.forEach(s=>{s.opacity&&o.union(s.bounds)}),t}});function hE(e){$.call(this,null,e)}T(hE,$,{transform(e,t){const n=t.dataflow;if(t.visit(t.ALL,r=>n.dirty(r)),t.fields&&t.fields.zindex){const r=t.source&&t.source[0];r&&(r.mark.zdirty=!0)}}});const Ge=new we;function ki(e,t,n){return e[t]===n?0:(e[t]=n,1)}function Z4(e){var t=e.items[0].orient;return t===rn||t===un}function eS(e){let t=+e.grid;return[e.ticks?t++:-1,e.labels?t++:-1,t+ +e.domain]}function tS(e,t,n,r){var i=t.items[0],a=i.datum,u=i.translate!=null?i.translate:.5,o=i.orient,s=eS(a),l=i.range,f=i.offset,c=i.position,d=i.minExtent,h=i.maxExtent,g=a.title&&i.items[s[2]].items[0],p=i.titlePadding,m=i.bounds,y=g&&b1(g),v=0,x=0,b,E;switch(Ge.clear().union(m),m.clear(),(b=s[0])>-1&&m.union(i.items[b].bounds),(b=s[1])>-1&&m.union(i.items[b].bounds),o){case Wi:v=c||0,x=-f,E=Math.max(d,Math.min(h,-m.y1)),m.add(0,-E).add(l,0),g&&Do(e,g,E,p,y,0,-1,m);break;case rn:v=-f,x=c||0,E=Math.max(d,Math.min(h,-m.x1)),m.add(-E,0).add(0,l),g&&Do(e,g,E,p,y,1,-1,m);break;case un:v=n+f,x=c||0,E=Math.max(d,Math.min(h,m.x2)),m.add(0,0).add(E,l),g&&Do(e,g,E,p,y,1,1,m);break;case Er:v=c||0,x=r+f,E=Math.max(d,Math.min(h,m.y2)),m.add(0,0).add(l,E),g&&Do(e,g,E,p,0,0,1,m);break;default:v=i.x,x=i.y}return Xn(m.translate(v,x),i),ki(i,"x",v+u)|ki(i,"y",x+u)&&(i.bounds=Ge,e.dirty(i),i.bounds=m,e.dirty(i)),i.mark.bounds.clear().union(m)}function Do(e,t,n,r,i,a,u,o){const s=t.bounds;if(t.auto){const l=u*(n+i+r);let f=0,c=0;e.dirty(t),a?f=(t.x||0)-(t.x=l):c=(t.y||0)-(t.y=l),t.mark.bounds.clear().union(s.translate(-f,-c)),e.dirty(t)}o.union(s)}const ny=(e,t)=>Math.floor(Math.min(e,t)),ry=(e,t)=>Math.ceil(Math.max(e,t));function nS(e){var t=e.items,n=t.length,r=0,i,a;const u={marks:[],rowheaders:[],rowfooters:[],colheaders:[],colfooters:[],rowtitle:null,coltitle:null};for(;r<n;++r)if(i=t[r],a=i.items,i.marktype===rf)switch(i.role){case L1:case z1:case N1:break;case eE:u.rowheaders.push(...a);break;case tE:u.rowfooters.push(...a);break;case rE:u.colheaders.push(...a);break;case iE:u.colfooters.push(...a);break;case nE:u.rowtitle=a[0];break;case aE:u.coltitle=a[0];break;default:u.marks.push(...a)}return u}function rS(e){return new we().set(0,0,e.width||0,e.height||0)}function iS(e){const t=e.bounds.clone();return t.empty()?t.set(0,0,0,0):t.translate(-(e.x||0),-(e.y||0))}function ye(e,t,n){const r=K(e)?e[t]:e;return r??(n!==void 0?n:0)}function iy(e){return e<0?Math.ceil(-e):0}function gE(e,t,n){var r=!n.nodirty,i=n.bounds===I1?rS:iS,a=Ge.set(0,0,0,0),u=ye(n.align,cr),o=ye(n.align,dr),s=ye(n.padding,cr),l=ye(n.padding,dr),f=n.columns||t.length,c=f<=0?1:Math.ceil(t.length/f),d=t.length,h=Array(d),g=Array(f),p=0,m=Array(d),y=Array(c),v=0,x=Array(d),b=Array(d),E=Array(d),w,A,D,C,F,M,O,L,N,k,R;for(A=0;A<f;++A)g[A]=0;for(A=0;A<c;++A)y[A]=0;for(A=0;A<d;++A)M=t[A],F=E[A]=i(M),M.x=M.x||0,x[A]=0,M.y=M.y||0,b[A]=0,D=A%f,C=~~(A/f),p=Math.max(p,O=Math.ceil(F.x2)),v=Math.max(v,L=Math.ceil(F.y2)),g[D]=Math.max(g[D],O),y[C]=Math.max(y[C],L),h[A]=s+iy(F.x1),m[A]=l+iy(F.y1),r&&e.dirty(t[A]);for(A=0;A<d;++A)A%f===0&&(h[A]=0),A<f&&(m[A]=0);if(u===Ad)for(D=1;D<f;++D){for(R=0,A=D;A<d;A+=f)R<h[A]&&(R=h[A]);for(A=D;A<d;A+=f)h[A]=R+g[D-1]}else if(u===Eo){for(R=0,A=0;A<d;++A)A%f&&R<h[A]&&(R=h[A]);for(A=0;A<d;++A)A%f&&(h[A]=R+p)}else for(u=!1,D=1;D<f;++D)for(A=D;A<d;A+=f)h[A]+=g[D-1];if(o===Ad)for(C=1;C<c;++C){for(R=0,A=C*f,w=A+f;A<w;++A)R<m[A]&&(R=m[A]);for(A=C*f;A<w;++A)m[A]=R+y[C-1]}else if(o===Eo){for(R=0,A=f;A<d;++A)R<m[A]&&(R=m[A]);for(A=f;A<d;++A)m[A]=R+v}else for(o=!1,C=1;C<c;++C)for(A=C*f,w=A+f;A<w;++A)m[A]+=y[C-1];for(N=0,A=0;A<d;++A)N=h[A]+(A%f?N:0),x[A]+=N-t[A].x;for(D=0;D<f;++D)for(k=0,A=D;A<d;A+=f)k+=m[A],b[A]+=k-t[A].y;if(u&&ye(n.center,cr)&&c>1)for(A=0;A<d;++A)F=u===Eo?p:g[A%f],N=F-E[A].x2-t[A].x-x[A],N>0&&(x[A]+=N/2);if(o&&ye(n.center,dr)&&f!==1)for(A=0;A<d;++A)F=o===Eo?v:y[~~(A/f)],k=F-E[A].y2-t[A].y-b[A],k>0&&(b[A]+=k/2);for(A=0;A<d;++A)a.union(E[A].translate(x[A],b[A]));switch(N=ye(n.anchor,q4),k=ye(n.anchor,j4),ye(n.anchor,cr)){case at:N-=a.width();break;case bd:N-=a.width()/2}switch(ye(n.anchor,dr)){case at:k-=a.height();break;case bd:k-=a.height()/2}for(N=Math.round(N),k=Math.round(k),a.clear(),A=0;A<d;++A)t[A].mark.bounds.clear();for(A=0;A<d;++A)M=t[A],M.x+=x[A]+=N,M.y+=b[A]+=k,a.union(M.mark.bounds.union(M.bounds.translate(x[A],b[A]))),r&&e.dirty(M);return a}function aS(e,t,n){var r=nS(t),i=r.marks,a=n.bounds===I1?uS:oS,u=n.offset,o=n.columns||i.length,s=o<=0?1:Math.ceil(i.length/o),l=s*o,f,c,d,h,g,p,m;const y=gE(e,i,n);y.empty()&&y.set(0,0,0,0),r.rowheaders&&(p=ye(n.headerBand,dr,null),f=Co(e,r.rowheaders,i,o,s,-ye(u,"rowHeader"),ny,0,a,"x1",0,o,1,p)),r.colheaders&&(p=ye(n.headerBand,cr,null),c=Co(e,r.colheaders,i,o,o,-ye(u,"columnHeader"),ny,1,a,"y1",0,1,o,p)),r.rowfooters&&(p=ye(n.footerBand,dr,null),d=Co(e,r.rowfooters,i,o,s,ye(u,"rowFooter"),ry,0,a,"x2",o-1,o,1,p)),r.colfooters&&(p=ye(n.footerBand,cr,null),h=Co(e,r.colfooters,i,o,o,ye(u,"columnFooter"),ry,1,a,"y2",l-o,1,o,p)),r.rowtitle&&(g=ye(n.titleAnchor,dr),m=ye(u,"rowTitle"),m=g===at?d+m:f-m,p=ye(n.titleBand,dr,.5),ay(e,r.rowtitle,m,0,y,p)),r.coltitle&&(g=ye(n.titleAnchor,cr),m=ye(u,"columnTitle"),m=g===at?h+m:c-m,p=ye(n.titleBand,cr,.5),ay(e,r.coltitle,m,1,y,p))}function uS(e,t){return t==="x1"?e.x||0:t==="y1"?e.y||0:t==="x2"?(e.x||0)+(e.width||0):t==="y2"?(e.y||0)+(e.height||0):void 0}function oS(e,t){return e.bounds[t]}function Co(e,t,n,r,i,a,u,o,s,l,f,c,d,h){var g=n.length,p=0,m=0,y,v,x,b,E,w,A,D,C;if(!g)return p;for(y=f;y<g;y+=c)n[y]&&(p=u(p,s(n[y],l)));if(!t.length)return p;for(t.length>i&&(e.warn("Grid headers exceed limit: "+i),t=t.slice(0,i)),p+=a,v=0,b=t.length;v<b;++v)e.dirty(t[v]),t[v].mark.bounds.clear();for(y=f,v=0,b=t.length;v<b;++v,y+=c){for(w=t[v],E=w.mark.bounds,x=y;x>=0&&(A=n[x])==null;x-=d);o?(D=h==null?A.x:Math.round(A.bounds.x1+h*A.bounds.width()),C=p):(D=p,C=h==null?A.y:Math.round(A.bounds.y1+h*A.bounds.height())),E.union(w.bounds.translate(D-(w.x||0),C-(w.y||0))),w.x=D,w.y=C,e.dirty(w),m=u(m,E[l])}return m}function ay(e,t,n,r,i,a){if(t){e.dirty(t);var u=n,o=n;r?u=Math.round(i.x1+a*i.width()):o=Math.round(i.y1+a*i.height()),t.bounds.translate(u-(t.x||0),o-(t.y||0)),t.mark.bounds.clear().union(t.bounds),t.x=u,t.y=o,e.dirty(t)}}function sS(e,t){const n=e[t]||{};return(r,i)=>n[r]!=null?n[r]:e[r]!=null?e[r]:i}function lS(e,t){let n=-1/0;return e.forEach(r=>{r.offset!=null&&(n=Math.max(n,r.offset))}),n>-1/0?n:t}function fS(e,t,n,r,i,a,u){const o=sS(n,t),s=lS(e,o("offset",0)),l=o("anchor",T1),f=l===at?1:l===bd?.5:0,c={align:Ad,bounds:o("bounds",I1),columns:o("direction")==="vertical"?1:e.length,padding:o("margin",8),center:o("center"),nodirty:!0};switch(t){case rn:c.anchor={x:Math.floor(r.x1)-s,column:at,y:f*(u||r.height()+2*r.y1),row:l};break;case un:c.anchor={x:Math.ceil(r.x2)+s,y:f*(u||r.height()+2*r.y1),row:l};break;case Wi:c.anchor={y:Math.floor(i.y1)-s,row:at,x:f*(a||i.width()+2*i.x1),column:l};break;case Er:c.anchor={y:Math.ceil(i.y2)+s,x:f*(a||i.width()+2*i.x1),column:l};break;case z4:c.anchor={x:s,y:s};break;case P4:c.anchor={x:a-s,y:s,column:at};break;case I4:c.anchor={x:s,y:u-s,row:at};break;case U4:c.anchor={x:a-s,y:u-s,column:at,row:at};break}return c}function cS(e,t){var n=t.items[0],r=n.datum,i=n.orient,a=n.bounds,u=n.x,o=n.y,s,l;return n._bounds?n._bounds.clear().union(a):n._bounds=a.clone(),a.clear(),hS(e,n,n.items[0].items[0]),a=dS(n,a),s=2*n.padding,l=2*n.padding,a.empty()||(s=Math.ceil(a.width()+s),l=Math.ceil(a.height()+l)),r.type===H4&&gS(n.items[0].items[0].items[0].items),i!==P1&&(n.x=u=0,n.y=o=0),n.width=s,n.height=l,Xn(a.set(u,o,u+s,o+l),n),n.mark.bounds.clear().union(a),n}function dS(e,t){return e.items.forEach(n=>t.union(n.bounds)),t.x1=e.padding,t.y1=e.padding,t}function hS(e,t,n){var r=t.padding,i=r-n.x,a=r-n.y;if(!t.datum.title)(i||a)&&ma(e,n,i,a);else{var u=t.items[1].items[0],o=u.anchor,s=t.titlePadding||0,l=r-u.x,f=r-u.y;switch(u.orient){case rn:i+=Math.ceil(u.bounds.width())+s;break;case un:case Er:break;default:a+=u.bounds.height()+s}switch((i||a)&&ma(e,n,i,a),u.orient){case rn:f+=yi(t,n,u,o,1,1);break;case un:l+=yi(t,n,u,at,0,0)+s,f+=yi(t,n,u,o,1,1);break;case Er:l+=yi(t,n,u,o,0,0),f+=yi(t,n,u,at,-1,0,1)+s;break;default:l+=yi(t,n,u,o,0,0)}(l||f)&&ma(e,u,l,f),(l=Math.round(u.bounds.x1-r))<0&&(ma(e,n,-l,0),ma(e,u,-l,0))}}function yi(e,t,n,r,i,a,u){const o=e.datum.type!=="symbol",s=n.datum.vgrad,l=o&&(a||!s)&&!u?t.items[0]:t,f=l.bounds[i?"y2":"x2"]-e.padding,c=s&&a?f:0,d=s&&a?0:f,h=i<=0?0:b1(n);return Math.round(r===T1?c:r===at?d-h:.5*(f-h))}function ma(e,t,n,r){t.x+=n,t.y+=r,t.bounds.translate(n,r),t.mark.bounds.translate(n,r),e.dirty(t)}function gS(e){const t=e.reduce((n,r)=>(n[r.column]=Math.max(r.bounds.x2-r.x,n[r.column]||0),n),{});e.forEach(n=>{n.width=t[n.column],n.height=n.bounds.y2-n.y})}function pS(e,t,n,r,i){var a=t.items[0],u=a.frame,o=a.orient,s=a.anchor,l=a.offset,f=a.padding,c=a.items[0].items[0],d=a.items[1]&&a.items[1].items[0],h=o===rn||o===un?r:n,g=0,p=0,m=0,y=0,v=0,x;if(u!==rf?o===rn?(g=i.y2,h=i.y1):o===un?(g=i.y1,h=i.y2):(g=i.x1,h=i.x2):o===rn&&(g=r,h=0),x=s===T1?g:s===at?h:(g+h)/2,d&&d.text){switch(o){case Wi:case Er:v=c.bounds.height()+f;break;case rn:y=c.bounds.width()+f;break;case un:y=-c.bounds.width()-f;break}Ge.clear().union(d.bounds),Ge.translate(y-(d.x||0),v-(d.y||0)),ki(d,"x",y)|ki(d,"y",v)&&(e.dirty(d),d.bounds.clear().union(Ge),d.mark.bounds.clear().union(Ge),e.dirty(d)),Ge.clear().union(d.bounds)}else Ge.clear();switch(Ge.union(c.bounds),o){case Wi:p=x,m=i.y1-Ge.height()-l;break;case rn:p=i.x1-Ge.width()-l,m=x;break;case un:p=i.x2+Ge.width()+l,m=x;break;case Er:p=x,m=i.y2+l;break;default:p=a.x,m=a.y}return ki(a,"x",p)|ki(a,"y",m)&&(Ge.translate(p,m),e.dirty(a),a.bounds.clear().union(Ge),t.bounds.clear().union(Ge),e.dirty(a)),a.bounds}function pE(e){$.call(this,null,e)}T(pE,$,{transform(e,t){const n=t.dataflow;return e.mark.items.forEach(r=>{e.layout&&aS(n,r,e.layout),yS(n,r,e)}),mS(e.mark.group)?t.reflow():t}});function mS(e){return e&&e.mark.role!=="legend-entry"}function yS(e,t,n){var r=t.items,i=Math.max(0,t.width||0),a=Math.max(0,t.height||0),u=new we().set(0,0,i,a),o=u.clone(),s=u.clone(),l=[],f,c,d,h,g,p;for(g=0,p=r.length;g<p;++g)switch(c=r[g],c.role){case L1:h=Z4(c)?o:s,h.union(tS(e,c,i,a));break;case N1:f=c;break;case z1:l.push(cS(e,c));break;case G4:case W4:case eE:case tE:case nE:case rE:case iE:case aE:o.union(c.bounds),s.union(c.bounds);break;default:u.union(c.bounds)}if(l.length){const m={};l.forEach(y=>{d=y.orient||un,d!==P1&&(m[d]||(m[d]=[])).push(y)});for(const y in m){const v=m[y];gE(e,v,fS(v,y,n.legends,o,s,i,a))}l.forEach(y=>{const v=y.bounds;if(v.equals(y._bounds)||(y.bounds=y._bounds,e.dirty(y),y.bounds=v,e.dirty(y)),n.autosize&&(n.autosize.type===uE||n.autosize.type===oE||n.autosize.type===sE))switch(y.orient){case rn:case un:u.add(v.x1,0).add(v.x2,0);break;case Wi:case Er:u.add(0,v.y1).add(0,v.y2)}else u.union(v)})}u.union(o).union(s),f&&u.union(pS(e,f,i,a,u)),t.clip&&u.set(0,0,t.width||0,t.height||0),vS(e,t,u,n)}function vS(e,t,n,r){const i=r.autosize||{},a=i.type;if(e._autosize<1||!a)return;let u=e._width,o=e._height,s=Math.max(0,t.width||0),l=Math.max(0,Math.ceil(-n.x1)),f=Math.max(0,t.height||0),c=Math.max(0,Math.ceil(-n.y1));const d=Math.max(0,Math.ceil(n.x2-s)),h=Math.max(0,Math.ceil(n.y2-f));if(i.contains===Y4){const g=e.padding();u-=g.left+g.right,o-=g.top+g.bottom}a===P1?(l=0,c=0,s=u,f=o):a===uE?(s=Math.max(0,u-l-d),f=Math.max(0,o-c-h)):a===oE?(s=Math.max(0,u-l-d),o=f+c+h):a===sE?(u=s+l+d,f=Math.max(0,o-c-h)):a===X4&&(u=s+l+d,o=f+c+h),e._resizeView(u,o,s,f,[l,c],i.resize)}const xS=Object.freeze(Object.defineProperty({__proto__:null,bound:lE,identifier:U1,mark:fE,overlap:cE,render:hE,viewlayout:pE},Symbol.toStringTag,{value:"Module"}));function mE(e){$.call(this,null,e)}T(mE,$,{transform(e,t){if(this.value&&!e.modified())return t.StopPropagation;var n=t.dataflow.locale(),r=t.fork(t.NO_SOURCE|t.NO_FIELDS),i=this.value,a=e.scale,u=e.count==null?e.values?e.values.length:10:e.count,o=e1(a,u,e.minstep),s=e.format||Qb(n,a,o,e.formatSpecifier,e.formatType,!!e.values),l=e.values?Jb(a,e.values,o):t1(a,o);return i&&(r.rem=i),i=l.map((f,c)=>le({index:c/(l.length-1||1),value:f,label:s(f)})),e.extra&&i.length&&i.push(le({index:-1,extra:{value:i[0].value},label:""})),r.source=i,r.add=i,this.value=i,r}});function yE(e){$.call(this,null,e)}function bS(){return le({})}function AS(e){const t=Ki().test(n=>n.exit);return t.lookup=n=>t.get(e(n)),t}T(yE,$,{transform(e,t){var n=t.dataflow,r=t.fork(t.NO_SOURCE|t.NO_FIELDS),i=e.item||bS,a=e.key||Y,u=this.value;return z(r.encode)&&(r.encode=null),u&&(e.modified("key")||t.modified(a))&&S("DataJoin does not support modified key function or fields."),u||(t=t.addAll(),this.value=u=AS(a)),t.visit(t.ADD,o=>{const s=a(o);let l=u.get(s);l?l.exit?(u.empty--,r.add.push(l)):r.mod.push(l):(l=i(o),u.set(s,l),r.add.push(l)),l.datum=o,l.exit=!1}),t.visit(t.MOD,o=>{const s=a(o),l=u.get(s);l&&(l.datum=o,r.mod.push(l))}),t.visit(t.REM,o=>{const s=a(o),l=u.get(s);o===l.datum&&!l.exit&&(r.rem.push(l),l.exit=!0,++u.empty)}),t.changed(t.ADD_MOD)&&r.modifies("datum"),(t.clean()||e.clean&&u.empty>n.cleanThreshold)&&n.runAfter(u.clean),r}});function vE(e){$.call(this,null,e)}T(vE,$,{transform(e,t){var n=t.fork(t.ADD_REM),r=e.mod||!1,i=e.encoders,a=t.encode;if(z(a))if(n.changed()||a.every(c=>i[c]))a=a[0],n.encode=null;else return t.StopPropagation;var u=a==="enter",o=i.update||Qn,s=i.enter||Qn,l=i.exit||Qn,f=(a&&!u?i[a]:o)||Qn;if(t.changed(t.ADD)&&(t.visit(t.ADD,c=>{s(c,e),o(c,e)}),n.modifies(s.output),n.modifies(o.output),f!==Qn&&f!==o&&(t.visit(t.ADD,c=>{f(c,e)}),n.modifies(f.output))),t.changed(t.REM)&&l!==Qn&&(t.visit(t.REM,c=>{l(c,e)}),n.modifies(l.output)),u||f!==Qn){const c=t.MOD|(e.modified()?t.REFLOW:0);u?(t.visit(c,d=>{const h=s(d,e)||r;(f(d,e)||h)&&n.mod.push(d)}),n.mod.length&&n.modifies(s.output)):t.visit(c,d=>{(f(d,e)||r)&&n.mod.push(d)}),n.mod.length&&n.modifies(f.output)}return n.changed()?n:t.StopPropagation}});function xE(e){$.call(this,[],e)}T(xE,$,{transform(e,t){if(this.value!=null&&!e.modified())return t.StopPropagation;var n=t.dataflow.locale(),r=t.fork(t.NO_SOURCE|t.NO_FIELDS),i=this.value,a=e.type||Po,u=e.scale,o=+e.limit,s=e1(u,e.count==null?5:e.count,e.minstep),l=!!e.values||a===Po,f=e.format||nA(n,u,s,a,e.formatSpecifier,e.formatType,l),c=e.values||tA(u,s),d,h,g,p,m;return i&&(r.rem=i),a===Po?(o&&c.length>o?(t.dataflow.warn("Symbol legend count exceeds limit, filtering items."),i=c.slice(0,o-1),m=!0):i=c,J(g=e.size)?(!e.values&&u(i[0])===0&&(i=i.slice(1)),p=i.reduce((y,v)=>Math.max(y,g(v,e)),0)):g=tt(p=g||8),i=i.map((y,v)=>le({index:v,label:f(y,v,i),value:y,offset:p,size:g(y,e)})),m&&(m=c[i.length],i.push(le({index:i.length,label:`…${c.length-i.length} entries`,value:m,offset:p,size:g(m,e)})))):a===Vk?(d=u.domain(),h=J0(u,d[0],re(d)),c.length<3&&!e.values&&d[0]!==re(d)&&(c=[d[0],re(d)]),i=c.map((y,v)=>le({index:v,label:f(y,v,c),value:y,perc:h(y)}))):(g=c.length-1,h=u7(u),i=c.map((y,v)=>le({index:v,label:f(y,v,c),value:y,perc:v?h(y):0,perc2:v===g?1:h(c[v+1])}))),r.source=i,r.add=i,this.value=i,r}});const ES=e=>e.source.x,wS=e=>e.source.y,DS=e=>e.target.x,CS=e=>e.target.y;function q1(e){$.call(this,{},e)}q1.Definition={type:"LinkPath",metadata:{modifies:!0},params:[{name:"sourceX",type:"field",default:"source.x"},{name:"sourceY",type:"field",default:"source.y"},{name:"targetX",type:"field",default:"target.x"},{name:"targetY",type:"field",default:"target.y"},{name:"orient",type:"enum",default:"vertical",values:["horizontal","vertical","radial"]},{name:"shape",type:"enum",default:"line",values:["line","arc","curve","diagonal","orthogonal"]},{name:"require",type:"signal"},{name:"as",type:"string",default:"path"}]};T(q1,$,{transform(e,t){var n=e.sourceX||ES,r=e.sourceY||wS,i=e.targetX||DS,a=e.targetY||CS,u=e.as||"path",o=e.orient||"vertical",s=e.shape||"line",l=uy.get(s+"-"+o)||uy.get(s);return l||S("LinkPath unsupported type: "+e.shape+(e.orient?"-"+e.orient:"")),t.visit(t.SOURCE,f=>{f[u]=l(n(f),r(f),i(f),a(f))}),t.reflow(e.modified()).modifies(u)}});const bE=(e,t,n,r)=>"M"+e+","+t+"L"+n+","+r,FS=(e,t,n,r)=>bE(t*Math.cos(e),t*Math.sin(e),r*Math.cos(n),r*Math.sin(n)),AE=(e,t,n,r)=>{var i=n-e,a=r-t,u=Math.hypot(i,a)/2,o=180*Math.atan2(a,i)/Math.PI;return"M"+e+","+t+"A"+u+","+u+" "+o+" 0 1 "+n+","+r},kS=(e,t,n,r)=>AE(t*Math.cos(e),t*Math.sin(e),r*Math.cos(n),r*Math.sin(n)),EE=(e,t,n,r)=>{const i=n-e,a=r-t,u=.2*(i+a),o=.2*(a-i);return"M"+e+","+t+"C"+(e+u)+","+(t+o)+" "+(n+o)+","+(r-u)+" "+n+","+r},MS=(e,t,n,r)=>EE(t*Math.cos(e),t*Math.sin(e),r*Math.cos(n),r*Math.sin(n)),SS=(e,t,n,r)=>"M"+e+","+t+"V"+r+"H"+n,$S=(e,t,n,r)=>"M"+e+","+t+"H"+n+"V"+r,BS=(e,t,n,r)=>{const i=Math.cos(e),a=Math.sin(e),u=Math.cos(n),o=Math.sin(n),s=Math.abs(n-e)>Math.PI?n<=e:n>e;return"M"+t*i+","+t*a+"A"+t+","+t+" 0 0,"+(s?1:0)+" "+t*u+","+t*o+"L"+r*u+","+r*o},_S=(e,t,n,r)=>{const i=(e+n)/2;return"M"+e+","+t+"C"+i+","+t+" "+i+","+r+" "+n+","+r},RS=(e,t,n,r)=>{const i=(t+r)/2;return"M"+e+","+t+"C"+e+","+i+" "+n+","+i+" "+n+","+r},OS=(e,t,n,r)=>{const i=Math.cos(e),a=Math.sin(e),u=Math.cos(n),o=Math.sin(n),s=(t+r)/2;return"M"+t*i+","+t*a+"C"+s*i+","+s*a+" "+s*u+","+s*o+" "+r*u+","+r*o},uy=Ki({line:bE,"line-radial":FS,arc:AE,"arc-radial":kS,curve:EE,"curve-radial":MS,"orthogonal-horizontal":SS,"orthogonal-vertical":$S,"orthogonal-radial":BS,"diagonal-horizontal":_S,"diagonal-vertical":RS,"diagonal-radial":OS});function j1(e){$.call(this,null,e)}j1.Definition={type:"Pie",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"startAngle",type:"number",default:0},{name:"endAngle",type:"number",default:6.283185307179586},{name:"sort",type:"boolean",default:!1},{name:"as",type:"string",array:!0,length:2,default:["startAngle","endAngle"]}]};T(j1,$,{transform(e,t){var n=e.as||["startAngle","endAngle"],r=n[0],i=n[1],a=e.field||Vi,u=e.startAngle||0,o=e.endAngle!=null?e.endAngle:2*Math.PI,s=t.source,l=s.map(a),f=l.length,c=u,d=(o-u)/I2(l),h=Et(f),g,p,m;for(e.sort&&h.sort((y,v)=>l[y]-l[v]),g=0;g<f;++g)m=l[h[g]],p=s[h[g]],p[r]=c,p[i]=c+=m*d;return this.value=l,t.reflow(e.modified()).modifies(n)}});const TS=5;function LS(e){const t=e.type;return!e.bins&&(t===zi||t===Nu||t===zu)}function wE(e){return K0(e)&&e!==En}const NS=Tt(["set","modified","clear","type","scheme","schemeExtent","schemeCount","domain","domainMin","domainMid","domainMax","domainRaw","domainImplicit","nice","zero","bins","range","rangeStep","round","reverse","interpolate","interpolateGamma"]);function DE(e){$.call(this,null,e),this.modified(!0)}T(DE,$,{transform(e,t){var n=t.dataflow,r=this.value,i=zS(e);(!r||i!==r.type)&&(this.value=r=ue(i)());for(i in e)if(!NS[i]){if(i==="padding"&&wE(r.type))continue;J(r[i])?r[i](e[i]):n.warn("Unsupported scale property: "+i)}return GS(r,e,jS(r,e,IS(r,e,n))),t.fork(t.NO_SOURCE|t.NO_FIELDS)}});function zS(e){var t=e.type,n="",r;return t===En?En+"-"+zi:(PS(e)&&(r=e.rawDomain?e.rawDomain.length:e.domain?e.domain.length+ +(e.domainMid!=null):0,n=r===2?En+"-":r===3?na+"-":""),(n+t||zi).toLowerCase())}function PS(e){const t=e.type;return K0(t)&&t!==Kr&&t!==Jr&&(e.scheme||e.range&&e.range.length&&e.range.every(fe))}function IS(e,t,n){const r=US(e,t.domainRaw,n);if(r>-1)return r;var i=t.domain,a=e.type,u=t.zero||t.zero===void 0&&LS(e),o,s;if(!i)return 0;if(wE(a)&&t.padding&&i[0]!==re(i)&&(i=qS(a,i,t.range,t.padding,t.exponent,t.constant)),(u||t.domainMin!=null||t.domainMax!=null||t.domainMid!=null)&&(o=(i=i.slice()).length-1||1,u&&(i[0]>0&&(i[0]=0),i[o]<0&&(i[o]=0)),t.domainMin!=null&&(i[0]=t.domainMin),t.domainMax!=null&&(i[o]=t.domainMax),t.domainMid!=null)){s=t.domainMid;const l=s>i[o]?o+1:s<i[0]?0:o;l!==o&&n.warn("Scale domainMid exceeds domain min or max.",s),i.splice(l,0,s)}return e.domain(CE(a,i,n)),a===Y0&&e.unknown(t.domainImplicit?YD:void 0),t.nice&&e.nice&&e.nice(t.nice!==!0&&e1(e,t.nice)||null),i.length}function US(e,t,n){return t?(e.domain(CE(e.type,t,n)),t.length):-1}function qS(e,t,n,r,i,a){var u=Math.abs(re(n)-n[0]),o=u/(u-2*r),s=e===Hn?yl(t,null,o):e===zu?su(t,null,o,.5):e===Nu?su(t,null,o,i||1):e===ql?vl(t,null,o,a||1):ml(t,null,o);return t=t.slice(),t[0]=s[0],t[t.length-1]=s[1],t}function CE(e,t,n){if(Gb(e)){var r=Math.abs(t.reduce((i,a)=>i+(a<0?-1:a>0?1:0),0));r!==t.length&&n.warn("Log scale domain includes zero: "+U(t))}return t}function jS(e,t,n){let r=t.bins;if(r&&!z(r)){const i=e.domain(),a=i[0],u=re(i),o=r.step;let s=r.start==null?a:r.start,l=r.stop==null?u:r.stop;o||S("Scale bins parameter missing step property."),s<a&&(s=o*Math.ceil(a/o)),l>u&&(l=o*Math.floor(u/o)),r=Et(s,l+o/2,o)}return r?e.bins=r:e.bins&&delete e.bins,e.type===H0&&(r?!t.domain&&!t.domainRaw&&(e.domain(r),n=r.length):e.bins=e.domain()),n}function GS(e,t,n){var r=e.type,i=t.round||!1,a=t.range;if(t.rangeStep!=null)a=WS(r,t,n);else if(t.scheme&&(a=YS(r,t,n),J(a))){if(e.interpolator)return e.interpolator(a);S(`Scale type ${r} does not support interpolating color schemes.`)}if(a&&Wb(r))return e.interpolator(Wl(Ed(a,t.reverse),t.interpolate,t.interpolateGamma));a&&t.interpolate&&e.interpolate?e.interpolate(Q0(t.interpolate,t.interpolateGamma)):J(e.round)?e.round(i):J(e.rangeRound)&&e.interpolate(i?al:Cu),a&&e.range(Ed(a,t.reverse))}function WS(e,t,n){e!==Nb&&e!==od&&S("Only band and point scales support rangeStep.");var r=(t.paddingOuter!=null?t.paddingOuter:t.padding)||0,i=e===od?1:(t.paddingInner!=null?t.paddingInner:t.padding)||0;return[0,t.rangeStep*Ul(n,i,r)]}function YS(e,t,n){var r=t.schemeExtent,i,a;return z(t.scheme)?a=Wl(t.scheme,t.interpolate,t.interpolateGamma):(i=t.scheme.toLowerCase(),a=Z0(i),a||S(`Unrecognized scheme name: ${t.scheme}`)),n=e===Gl?n+1:e===H0?n-1:e===Pi||e===jl?+t.schemeCount||TS:n,Wb(e)?oy(a,r,t.reverse):J(a)?Xb(oy(a,r),n):e===Y0?a:a.slice(0,n)}function oy(e,t,n){return J(e)&&(t||n)?Hb(e,Ed(t||[0,1],n)):e}function Ed(e,t){return t?e.slice().reverse():e}function FE(e){$.call(this,null,e)}T(FE,$,{transform(e,t){const n=e.modified("sort")||t.changed(t.ADD)||t.modified(e.sort.fields)||t.modified("datum");return n&&t.source.sort(ai(e.sort)),this.modified(n),t}});const sy="zero",kE="center",ME="normalize",SE=["y0","y1"];function G1(e){$.call(this,null,e)}G1.Definition={type:"Stack",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"groupby",type:"field",array:!0},{name:"sort",type:"compare"},{name:"offset",type:"enum",default:sy,values:[sy,kE,ME]},{name:"as",type:"string",array:!0,length:2,default:SE}]};T(G1,$,{transform(e,t){var n=e.as||SE,r=n[0],i=n[1],a=ai(e.sort),u=e.field||Vi,o=e.offset===kE?HS:e.offset===ME?XS:VS,s,l,f,c;for(s=KS(t.source,e.groupby,a,u),l=0,f=s.length,c=s.max;l<f;++l)o(s[l],c,u,r,i);return t.reflow(e.modified()).modifies(n)}});function HS(e,t,n,r,i){for(var a=(t-e.sum)/2,u=e.length,o=0,s;o<u;++o)s=e[o],s[r]=a,s[i]=a+=Math.abs(n(s))}function XS(e,t,n,r,i){for(var a=1/e.sum,u=0,o=e.length,s=0,l=0,f;s<o;++s)f=e[s],f[r]=u,f[i]=u=a*(l+=Math.abs(n(f)))}function VS(e,t,n,r,i){for(var a=0,u=0,o=e.length,s=0,l,f;s<o;++s)f=e[s],l=+n(f),l<0?(f[r]=u,f[i]=u+=l):(f[r]=a,f[i]=a+=l)}function KS(e,t,n,r){var i=[],a=p=>p(f),u,o,s,l,f,c,d,h,g;if(t==null)i.push(e.slice());else for(u={},o=0,s=e.length;o<s;++o)f=e[o],c=t.map(a),d=u[c],d||(u[c]=d=[],i.push(d)),d.push(f);for(c=0,g=0,l=i.length;c<l;++c){for(d=i[c],o=0,h=0,s=d.length;o<s;++o)h+=Math.abs(r(d[o]));d.sum=h,h>g&&(g=h),n&&d.sort(n)}return i.max=g,i}const JS=Object.freeze(Object.defineProperty({__proto__:null,axisticks:mE,datajoin:yE,encode:vE,legendentries:xE,linkpath:q1,pie:j1,scale:DE,sortitems:FE,stack:G1},Symbol.toStringTag,{value:"Module"}));var QS=Math.abs,wd=Math.cos,Hs=Math.sin,ZS=1e-6,$E=Math.PI,Dd=$E/2,ly=e$(2);function fy(e){return e>1?Dd:e<-1?-Dd:Math.asin(e)}function e$(e){return e>0?Math.sqrt(e):0}function t$(e,t){var n=e*Hs(t),r=30,i;do t-=i=(t+Hs(t)-n)/(1+wd(t));while(QS(i)>ZS&&--r>0);return t/2}function n$(e,t,n){function r(i,a){return[e*i*wd(a=t$(n,a)),t*Hs(a)]}return r.invert=function(i,a){return a=fy(a/t),[i/(e*wd(a)),fy((2*a+Hs(2*a))/n)]},r}var r$=n$(ly/Dd,ly,$E);function i$(){return Fn(r$).scale(169.529)}const a$=Ev(),Cd=["clipAngle","clipExtent","scale","translate","center","rotate","parallels","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];function u$(e,t){return function n(){const r=t();return r.type=e,r.path=Ev().projection(r),r.copy=r.copy||function(){const i=n();return Cd.forEach(a=>{r[a]&&i[a](r[a]())}),i.path.pointRadius(r.path.pointRadius()),i},Ub(r)}}function W1(e,t){if(!e||typeof e!="string")throw new Error("Projection type must be a name string.");return e=e.toLowerCase(),arguments.length>1?(Xs[e]=u$(e,t),this):Xs[e]||null}function BE(e){return e&&e.path||a$}const Xs={albers:Dv,albersusa:u5,azimuthalequalarea:o5,azimuthalequidistant:s5,conicconformal:c5,conicequalarea:ks,conicequidistant:g5,equalEarth:m5,equirectangular:d5,gnomonic:y5,identity:v5,mercator:l5,mollweide:i$,naturalEarth1:x5,orthographic:b5,stereographic:A5,transversemercator:E5};for(const e in Xs)W1(e,Xs[e]);function o$(){}const On=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function _E(){var e=1,t=1,n=o;function r(s,l){return l.map(f=>i(s,f))}function i(s,l){var f=[],c=[];return a(s,l,d=>{n(d,s,l),s$(d)>0?f.push([d]):c.push(d)}),c.forEach(d=>{for(var h=0,g=f.length,p;h<g;++h)if(l$((p=f[h])[0],d)!==-1){p.push(d);return}}),{type:"MultiPolygon",value:l,coordinates:f}}function a(s,l,f){var c=new Array,d=new Array,h,g,p,m,y,v;for(h=g=-1,m=s[0]>=l,On[m<<1].forEach(x);++h<e-1;)p=m,m=s[h+1]>=l,On[p|m<<1].forEach(x);for(On[m<<0].forEach(x);++g<t-1;){for(h=-1,m=s[g*e+e]>=l,y=s[g*e]>=l,On[m<<1|y<<2].forEach(x);++h<e-1;)p=m,m=s[g*e+e+h+1]>=l,v=y,y=s[g*e+h+1]>=l,On[p|m<<1|y<<2|v<<3].forEach(x);On[m|y<<3].forEach(x)}for(h=-1,y=s[g*e]>=l,On[y<<2].forEach(x);++h<e-1;)v=y,y=s[g*e+h+1]>=l,On[y<<2|v<<3].forEach(x);On[y<<3].forEach(x);function x(b){var E=[b[0][0]+h,b[0][1]+g],w=[b[1][0]+h,b[1][1]+g],A=u(E),D=u(w),C,F;(C=d[A])?(F=c[D])?(delete d[C.end],delete c[F.start],C===F?(C.ring.push(w),f(C.ring)):c[C.start]=d[F.end]={start:C.start,end:F.end,ring:C.ring.concat(F.ring)}):(delete d[C.end],C.ring.push(w),d[C.end=D]=C):(C=c[D])?(F=d[A])?(delete c[C.start],delete d[F.end],C===F?(C.ring.push(w),f(C.ring)):c[F.start]=d[C.end]={start:F.start,end:C.end,ring:F.ring.concat(C.ring)}):(delete c[C.start],C.ring.unshift(E),c[C.start=A]=C):c[A]=d[D]={start:A,end:D,ring:[E,w]}}}function u(s){return s[0]*2+s[1]*(e+1)*4}function o(s,l,f){s.forEach(c=>{var d=c[0],h=c[1],g=d|0,p=h|0,m,y=l[p*e+g];d>0&&d<e&&g===d&&(m=l[p*e+g-1],c[0]=d+(f-m)/(y-m)-.5),h>0&&h<t&&p===h&&(m=l[(p-1)*e+g],c[1]=h+(f-m)/(y-m)-.5)})}return r.contour=i,r.size=function(s){if(!arguments.length)return[e,t];var l=Math.floor(s[0]),f=Math.floor(s[1]);return l>=0&&f>=0||S("invalid size"),e=l,t=f,r},r.smooth=function(s){return arguments.length?(n=s?o:o$,r):n===o},r}function s$(e){for(var t=0,n=e.length,r=e[n-1][1]*e[0][0]-e[n-1][0]*e[0][1];++t<n;)r+=e[t-1][1]*e[t][0]-e[t-1][0]*e[t][1];return r}function l$(e,t){for(var n=-1,r=t.length,i;++n<r;)if(i=f$(e,t[n]))return i;return 0}function f$(e,t){for(var n=t[0],r=t[1],i=-1,a=0,u=e.length,o=u-1;a<u;o=a++){var s=e[a],l=s[0],f=s[1],c=e[o],d=c[0],h=c[1];if(c$(s,c,t))return 0;f>r!=h>r&&n<(d-l)*(r-f)/(h-f)+l&&(i=-i)}return i}function c$(e,t,n){var r;return d$(e,t,n)&&h$(e[r=+(e[0]===t[0])],n[r],t[r])}function d$(e,t,n){return(t[0]-e[0])*(n[1]-e[1])===(n[0]-e[0])*(t[1]-e[1])}function h$(e,t,n){return e<=t&&t<=n||n<=t&&t<=e}function RE(e,t,n){return function(r){var i=ln(r),a=n?Math.min(i[0],0):i[0],u=i[1],o=u-a,s=t?nu(a,u,e):o/(e+1);return Et(a+s,u,s)}}function Y1(e){$.call(this,null,e)}Y1.Definition={type:"Isocontour",metadata:{generates:!0},params:[{name:"field",type:"field"},{name:"thresholds",type:"number",array:!0},{name:"levels",type:"number"},{name:"nice",type:"boolean",default:!1},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"zero",type:"boolean",default:!0},{name:"smooth",type:"boolean",default:!0},{name:"scale",type:"number",expr:!0},{name:"translate",type:"number",array:!0,expr:!0},{name:"as",type:"string",null:!0,default:"contour"}]};T(Y1,$,{transform(e,t){if(this.value&&!t.changed()&&!e.modified())return t.StopPropagation;var n=t.fork(t.NO_SOURCE|t.NO_FIELDS),r=t.materialize(t.SOURCE).source,i=e.field||et,a=_E().smooth(e.smooth!==!1),u=e.thresholds||g$(r,i,e),o=e.as===null?null:e.as||"contour",s=[];return r.forEach(l=>{const f=i(l),c=a.size([f.width,f.height])(f.values,z(u)?u:u(f.values));p$(c,f,l,e),c.forEach(d=>{s.push(Fl(l,le(o!=null?{[o]:d}:d)))})}),this.value&&(n.rem=this.value),this.value=n.source=n.add=s,n}});function g$(e,t,n){const r=RE(n.levels||10,n.nice,n.zero!==!1);return n.resolve!=="shared"?r:r(e.map(i=>qr(t(i).values)))}function p$(e,t,n,r){let i=r.scale||t.scale,a=r.translate||t.translate;if(J(i)&&(i=i(n,r)),J(a)&&(a=a(n,r)),(i===1||i==null)&&!a)return;const u=(Yn(i)?i:i[0])||1,o=(Yn(i)?i:i[1])||1,s=a&&a[0]||0,l=a&&a[1]||0;e.forEach(OE(t,u,o,s,l))}function OE(e,t,n,r,i){const a=e.x1||0,u=e.y1||0,o=t*n<0;function s(c){c.forEach(l)}function l(c){o&&c.reverse(),c.forEach(f)}function f(c){c[0]=(c[0]-a)*t+r,c[1]=(c[1]-u)*n+i}return function(c){return c.coordinates.forEach(s),c}}function cy(e,t,n){const r=e>=0?e:m0(t,n);return Math.round((Math.sqrt(4*r*r+1)-1)/2)}function Vf(e){return J(e)?e:tt(+e)}function TE(){var e=s=>s[0],t=s=>s[1],n=Vi,r=[-1,-1],i=960,a=500,u=2;function o(s,l){const f=cy(r[0],s,e)>>u,c=cy(r[1],s,t)>>u,d=f?f+2:0,h=c?c+2:0,g=2*d+(i>>u),p=2*h+(a>>u),m=new Float32Array(g*p),y=new Float32Array(g*p);let v=m;s.forEach(b=>{const E=d+(+e(b)>>u),w=h+(+t(b)>>u);E>=0&&E<g&&w>=0&&w<p&&(m[E+w*g]+=+n(b))}),f>0&&c>0?(vi(g,p,m,y,f),xi(g,p,y,m,c),vi(g,p,m,y,f),xi(g,p,y,m,c),vi(g,p,m,y,f),xi(g,p,y,m,c)):f>0?(vi(g,p,m,y,f),vi(g,p,y,m,f),vi(g,p,m,y,f),v=y):c>0&&(xi(g,p,m,y,c),xi(g,p,y,m,c),xi(g,p,m,y,c),v=y);const x=l?Math.pow(2,-2*u):1/I2(v);for(let b=0,E=g*p;b<E;++b)v[b]*=x;return{values:v,scale:1<<u,width:g,height:p,x1:d,y1:h,x2:d+(i>>u),y2:h+(a>>u)}}return o.x=function(s){return arguments.length?(e=Vf(s),o):e},o.y=function(s){return arguments.length?(t=Vf(s),o):t},o.weight=function(s){return arguments.length?(n=Vf(s),o):n},o.size=function(s){if(!arguments.length)return[i,a];var l=+s[0],f=+s[1];return l>=0&&f>=0||S("invalid size"),i=l,a=f,o},o.cellSize=function(s){return arguments.length?((s=+s)>=1||S("invalid cell size"),u=Math.floor(Math.log(s)/Math.LN2),o):1<<u},o.bandwidth=function(s){return arguments.length?(s=q(s),s.length===1&&(s=[+s[0],+s[0]]),s.length!==2&&S("invalid bandwidth"),r=s,o):r},o}function vi(e,t,n,r,i){const a=(i<<1)+1;for(let u=0;u<t;++u)for(let o=0,s=0;o<e+i;++o)o<e&&(s+=n[o+u*e]),o>=i&&(o>=a&&(s-=n[o-a+u*e]),r[o-i+u*e]=s/Math.min(o+1,e-1+a-o,a))}function xi(e,t,n,r,i){const a=(i<<1)+1;for(let u=0;u<e;++u)for(let o=0,s=0;o<t+i;++o)o<t&&(s+=n[u+o*e]),o>=i&&(o>=a&&(s-=n[u+(o-a)*e]),r[u+(o-i)*e]=s/Math.min(o+1,t-1+a-o,a))}function H1(e){$.call(this,null,e)}H1.Definition={type:"KDE2D",metadata:{generates:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"weight",type:"field"},{name:"groupby",type:"field",array:!0},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number",array:!0,length:2},{name:"counts",type:"boolean",default:!1},{name:"as",type:"string",default:"grid"}]};const m$=["x","y","weight","size","cellSize","bandwidth"];function LE(e,t){return m$.forEach(n=>t[n]!=null?e[n](t[n]):0),e}T(H1,$,{transform(e,t){if(this.value&&!t.changed()&&!e.modified())return t.StopPropagation;var n=t.fork(t.NO_SOURCE|t.NO_FIELDS),r=t.materialize(t.SOURCE).source,i=y$(r,e.groupby),a=(e.groupby||[]).map(Ee),u=LE(TE(),e),o=e.as||"grid",s=[];function l(f,c){for(let d=0;d<a.length;++d)f[a[d]]=c[d];return f}return s=i.map(f=>le(l({[o]:u(f,e.counts)},f.dims))),this.value&&(n.rem=this.value),this.value=n.source=n.add=s,n}});function y$(e,t){var n=[],r=f=>f(o),i,a,u,o,s,l;if(t==null)n.push(e);else for(i={},a=0,u=e.length;a<u;++a)o=e[a],s=t.map(r),l=i[s],l||(i[s]=l=[],l.dims=s,n.push(l)),l.push(o);return n}function X1(e){$.call(this,null,e)}X1.Definition={type:"Contour",metadata:{generates:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"values",type:"number",array:!0},{name:"x",type:"field"},{name:"y",type:"field"},{name:"weight",type:"field"},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number"},{name:"count",type:"number"},{name:"nice",type:"boolean",default:!1},{name:"thresholds",type:"number",array:!0},{name:"smooth",type:"boolean",default:!0}]};T(X1,$,{transform(e,t){if(this.value&&!t.changed()&&!e.modified())return t.StopPropagation;var n=t.fork(t.NO_SOURCE|t.NO_FIELDS),r=_E().smooth(e.smooth!==!1),i=e.values,a=e.thresholds||RE(e.count||10,e.nice,!!i),u=e.size,o,s;return i||(i=t.materialize(t.SOURCE).source,o=LE(TE(),e)(i,!0),s=OE(o,o.scale||1,o.scale||1,0,0),u=[o.width,o.height],i=o.values),a=z(a)?a:a(i),i=r.size(u)(i,a),s&&i.forEach(s),this.value&&(n.rem=this.value),this.value=n.source=n.add=(i||[]).map(le),n}});const Fd="Feature",V1="FeatureCollection",v$="MultiPoint";function K1(e){$.call(this,null,e)}K1.Definition={type:"GeoJSON",metadata:{},params:[{name:"fields",type:"field",array:!0,length:2},{name:"geojson",type:"field"}]};T(K1,$,{transform(e,t){var n=this._features,r=this._points,i=e.fields,a=i&&i[0],u=i&&i[1],o=e.geojson||!i&&et,s=t.ADD,l;l=e.modified()||t.changed(t.REM)||t.modified(Xe(o))||a&&t.modified(Xe(a))||u&&t.modified(Xe(u)),(!this.value||l)&&(s=t.SOURCE,this._features=n=[],this._points=r=[]),o&&t.visit(s,f=>n.push(o(f))),a&&u&&(t.visit(s,f=>{var c=a(f),d=u(f);c!=null&&d!=null&&(c=+c)===c&&(d=+d)===d&&r.push([c,d])}),n=n.concat({type:Fd,geometry:{type:v$,coordinates:r}})),this.value={type:V1,features:n}}});function J1(e){$.call(this,null,e)}J1.Definition={type:"GeoPath",metadata:{modifies:!0},params:[{name:"projection",type:"projection"},{name:"field",type:"field"},{name:"pointRadius",type:"number",expr:!0},{name:"as",type:"string",default:"path"}]};T(J1,$,{transform(e,t){var n=t.fork(t.ALL),r=this.value,i=e.field||et,a=e.as||"path",u=n.SOURCE;!r||e.modified()?(this.value=r=BE(e.projection),n.materialize().reflow()):u=i===et||t.modified(i.fields)?n.ADD_MOD:n.ADD;const o=x$(r,e.pointRadius);return n.visit(u,s=>s[a]=r(i(s))),r.pointRadius(o),n.modifies(a)}});function x$(e,t){const n=e.pointRadius();return e.context(null),t!=null&&e.pointRadius(t),n}function Q1(e){$.call(this,null,e)}Q1.Definition={type:"GeoPoint",metadata:{modifies:!0},params:[{name:"projection",type:"projection",required:!0},{name:"fields",type:"field",array:!0,required:!0,length:2},{name:"as",type:"string",array:!0,length:2,default:["x","y"]}]};T(Q1,$,{transform(e,t){var n=e.projection,r=e.fields[0],i=e.fields[1],a=e.as||["x","y"],u=a[0],o=a[1],s;function l(f){const c=n([r(f),i(f)]);c?(f[u]=c[0],f[o]=c[1]):(f[u]=void 0,f[o]=void 0)}return e.modified()?t=t.materialize().reflow(!0).visit(t.SOURCE,l):(s=t.modified(r.fields)||t.modified(i.fields),t.visit(s?t.ADD_MOD:t.ADD,l)),t.modifies(a)}});function Z1(e){$.call(this,null,e)}Z1.Definition={type:"GeoShape",metadata:{modifies:!0,nomod:!0},params:[{name:"projection",type:"projection"},{name:"field",type:"field",default:"datum"},{name:"pointRadius",type:"number",expr:!0},{name:"as",type:"string",default:"shape"}]};T(Z1,$,{transform(e,t){var n=t.fork(t.ALL),r=this.value,i=e.as||"shape",a=n.ADD;return(!r||e.modified())&&(this.value=r=b$(BE(e.projection),e.field||Ot("datum"),e.pointRadius),n.materialize().reflow(),a=n.SOURCE),n.visit(a,u=>u[i]=r),n.modifies(i)}});function b$(e,t,n){const r=n==null?i=>e(t(i)):i=>{var a=e.pointRadius(),u=e.pointRadius(n)(t(i));return e.pointRadius(a),u};return r.context=i=>(e.context(i),r),r}function eg(e){$.call(this,[],e),this.generator=PF()}eg.Definition={type:"Graticule",metadata:{changes:!0,generates:!0},params:[{name:"extent",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"extentMajor",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"extentMinor",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"step",type:"number",array:!0,length:2},{name:"stepMajor",type:"number",array:!0,length:2,default:[90,360]},{name:"stepMinor",type:"number",array:!0,length:2,default:[10,10]},{name:"precision",type:"number",default:2.5}]};T(eg,$,{transform(e,t){var n=this.value,r=this.generator,i;if(!n.length||e.modified())for(const a in e)J(r[a])&&r[a](e[a]);return i=r(),n.length?t.mod.push(Wx(n[0],i)):t.add.push(le(i)),n[0]=i,t}});function tg(e){$.call(this,null,e)}tg.Definition={type:"heatmap",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"color",type:"string",expr:!0},{name:"opacity",type:"number",expr:!0},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"as",type:"string",default:"image"}]};T(tg,$,{transform(e,t){if(!t.changed()&&!e.modified())return t.StopPropagation;var n=t.materialize(t.SOURCE).source,r=e.resolve==="shared",i=e.field||et,a=E$(e.opacity,e),u=A$(e.color,e),o=e.as||"image",s={$x:0,$y:0,$value:0,$max:r?qr(n.map(l=>qr(i(l).values))):0};return n.forEach(l=>{const f=i(l),c=Z({},l,s);r||(c.$max=qr(f.values||[])),l[o]=w$(f,c,u.dep?u:tt(u(c)),a.dep?a:tt(a(c)))}),t.reflow(!0).modifies(o)}});function A$(e,t){let n;return J(e)?(n=r=>_i(e(r,t)),n.dep=NE(e)):n=tt(_i(e||"#888")),n}function E$(e,t){let n;return J(e)?(n=r=>e(r,t),n.dep=NE(e)):e?n=tt(e):(n=r=>r.$value/r.$max||0,n.dep=!0),n}function NE(e){if(!J(e))return!1;const t=Tt(Xe(e));return t.$x||t.$y||t.$value||t.$max}function w$(e,t,n,r){const i=e.width,a=e.height,u=e.x1||0,o=e.y1||0,s=e.x2||i,l=e.y2||a,f=e.values,c=f?m=>f[m]:ir,d=pr(s-u,l-o),h=d.getContext("2d"),g=h.getImageData(0,0,s-u,l-o),p=g.data;for(let m=o,y=0;m<l;++m){t.$y=m-o;for(let v=u,x=m*i;v<s;++v,y+=4){t.$x=v-u,t.$value=c(v+x);const b=n(t);p[y+0]=b.r,p[y+1]=b.g,p[y+2]=b.b,p[y+3]=~~(255*r(t))}}return h.putImageData(g,0,0),d}function zE(e){$.call(this,null,e),this.modified(!0)}T(zE,$,{transform(e,t){let n=this.value;return!n||e.modified("type")?(this.value=n=C$(e.type),Cd.forEach(r=>{e[r]!=null&&dy(n,r,e[r])})):Cd.forEach(r=>{e.modified(r)&&dy(n,r,e[r])}),e.pointRadius!=null&&n.path.pointRadius(e.pointRadius),e.fit&&D$(n,e),t.fork(t.NO_SOURCE|t.NO_FIELDS)}});function D$(e,t){const n=F$(t.fit);t.extent?e.fitExtent(t.extent,n):t.size&&e.fitSize(t.size,n)}function C$(e){const t=W1((e||"mercator").toLowerCase());return t||S("Unrecognized projection type: "+e),t()}function dy(e,t,n){J(e[t])&&e[t](n)}function F$(e){return e=q(e),e.length===1?e[0]:{type:V1,features:e.reduce((t,n)=>t.concat(k$(n)),[])}}function k$(e){return e.type===V1?e.features:q(e).filter(t=>t!=null).map(t=>t.type===Fd?t:{type:Fd,geometry:t})}const M$=Object.freeze(Object.defineProperty({__proto__:null,contour:X1,geojson:K1,geopath:J1,geopoint:Q1,geoshape:Z1,graticule:eg,heatmap:tg,isocontour:Y1,kde2d:H1,projection:zE},Symbol.toStringTag,{value:"Module"})),hy={center:PC,collide:iF,nbody:pF,link:uF,x:mF,y:yF},za="forces",kd=["alpha","alphaMin","alphaTarget","velocityDecay","forces"],S$=["static","iterations"],PE=["x","y","vx","vy"];function ng(e){$.call(this,null,e)}ng.Definition={type:"Force",metadata:{modifies:!0},params:[{name:"static",type:"boolean",default:!1},{name:"restart",type:"boolean",default:!1},{name:"iterations",type:"number",default:300},{name:"alpha",type:"number",default:1},{name:"alphaMin",type:"number",default:.001},{name:"alphaTarget",type:"number",default:0},{name:"velocityDecay",type:"number",default:.4},{name:"forces",type:"param",array:!0,params:[{key:{force:"center"},params:[{name:"x",type:"number",default:0},{name:"y",type:"number",default:0}]},{key:{force:"collide"},params:[{name:"radius",type:"number",expr:!0},{name:"strength",type:"number",default:.7},{name:"iterations",type:"number",default:1}]},{key:{force:"nbody"},params:[{name:"strength",type:"number",default:-30},{name:"theta",type:"number",default:.9},{name:"distanceMin",type:"number",default:1},{name:"distanceMax",type:"number"}]},{key:{force:"link"},params:[{name:"links",type:"data"},{name:"id",type:"field"},{name:"distance",type:"number",default:30,expr:!0},{name:"strength",type:"number",expr:!0},{name:"iterations",type:"number",default:1}]},{key:{force:"x"},params:[{name:"strength",type:"number",default:.1},{name:"x",type:"field"}]},{key:{force:"y"},params:[{name:"strength",type:"number",default:.1},{name:"y",type:"field"}]}]},{name:"as",type:"string",array:!0,modify:!1,default:PE}]};T(ng,$,{transform(e,t){var n=this.value,r=t.changed(t.ADD_REM),i=e.modified(kd),a=e.iterations||300;if(n?(r&&(t.modifies("index"),n.nodes(t.source)),(i||t.changed(t.MOD))&&IE(n,e,0,t)):(this.value=n=B$(t.source,e),n.on("tick",$$(t.dataflow,this)),e.static||(r=!0,n.tick()),t.modifies("index")),i||r||e.modified(S$)||t.changed()&&e.restart){if(n.alpha(Math.max(n.alpha(),e.alpha||1)).alphaDecay(1-Math.pow(n.alphaMin(),1/a)),e.static)for(n.stop();--a>=0;)n.tick();else if(n.stopped()&&n.restart(),!r)return t.StopPropagation}return this.finish(e,t)},finish(e,t){const n=t.dataflow;for(let o=this._argops,s=0,l=o.length,f;s<l;++s)if(f=o[s],!(f.name!==za||f.op._argval.force!=="link")){for(var r=f.op._argops,i=0,a=r.length,u;i<a;++i)if(r[i].name==="links"&&(u=r[i].op.source)){n.pulse(u,n.changeset().reflow());break}}return t.reflow(e.modified()).modifies(PE)}});function $$(e,t){return()=>e.touch(t).run()}function B$(e,t){const n=gF(e),r=n.stop,i=n.restart;let a=!1;return n.stopped=()=>a,n.restart=()=>(a=!1,i()),n.stop=()=>(a=!0,r()),IE(n,t,!0).on("end",()=>a=!0)}function IE(e,t,n,r){var i=q(t.forces),a,u,o,s;for(a=0,u=kd.length;a<u;++a)o=kd[a],o!==za&&t.modified(o)&&e[o](t[o]);for(a=0,u=i.length;a<u;++a)s=za+a,o=n||t.modified(za,a)?R$(i[a]):r&&_$(i[a],r)?e.force(s):null,o&&e.force(s,o);for(u=e.numForces||0;a<u;++a)e.force(za+a,null);return e.numForces=i.length,e}function _$(e,t){var n,r;for(n in e)if(J(r=e[n])&&t.modified(Xe(r)))return 1;return 0}function R$(e){var t,n;G(hy,e.force)||S("Unrecognized force: "+e.force),t=hy[e.force]();for(n in e)J(t[n])&&O$(t[n],e[n],e);return t}function O$(e,t,n){e(J(t)?r=>t(r,n):t)}const T$=Object.freeze(Object.defineProperty({__proto__:null,force:ng},Symbol.toStringTag,{value:"Module"}));function Md(e,t,n){const r={};return e.each(i=>{const a=i.data;n(a)&&(r[t(a)]=i)}),e.lookup=r,e}function rg(e){$.call(this,null,e)}rg.Definition={type:"Nest",metadata:{treesource:!0,changes:!0},params:[{name:"keys",type:"field",array:!0},{name:"generate",type:"boolean"}]};const L$=e=>e.values;T(rg,$,{transform(e,t){t.source||S("Nest transform requires an upstream data source.");var n=e.generate,r=e.modified(),i=t.clone(),a=this.value;return(!a||r||t.changed())&&(a&&a.each(u=>{u.children&&ea(u.data)&&i.rem.push(u.data)}),this.value=a=bh({values:q(e.keys).reduce((u,o)=>(u.key(o),u),N$()).entries(i.source)},L$),n&&a.each(u=>{u.children&&(u=le(u.data),i.add.push(u),i.source.push(u))}),Md(a,Y,Y)),i.source.root=a,i}});function N$(){const e=[],t={entries:i=>r(n(i,0),0),key:i=>(e.push(i),t)};function n(i,a){if(a>=e.length)return i;const u=i.length,o=e[a++],s={},l={};let f=-1,c,d,h;for(;++f<u;)c=o(d=i[f])+"",(h=s[c])?h.push(d):s[c]=[d];for(c in s)l[c]=n(s[c],a);return l}function r(i,a){if(++a>e.length)return i;const u=[];for(const o in i)u.push({key:o,values:r(i[o],a)});return u}return t}function Vn(e){$.call(this,null,e)}const z$=(e,t)=>e.parent===t.parent?1:2;T(Vn,$,{transform(e,t){(!t.source||!t.source.root)&&S(this.constructor.name+" transform requires a backing tree data source.");const n=this.layout(e.method),r=this.fields,i=t.source.root,a=e.as||r;e.field?i.sum(e.field):i.count(),e.sort&&i.sort(ai(e.sort,u=>u.data)),P$(n,this.params,e),n.separation&&n.separation(e.separation!==!1?z$:Vi);try{this.value=n(i)}catch(u){S(u)}return i.each(u=>I$(u,r,a)),t.reflow(e.modified()).modifies(a).modifies("leaf")}});function P$(e,t,n){for(let r,i=0,a=t.length;i<a;++i)r=t[i],r in n&&e[r](n[r])}function I$(e,t,n){const r=e.data,i=t.length-1;for(let a=0;a<i;++a)r[n[a]]=e[t[a]];r[n[i]]=e.children?e.children.length:0}const Sd=["x","y","r","depth","children"];function ig(e){Vn.call(this,e)}ig.Definition={type:"Pack",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"radius",type:"field",default:null},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:Sd.length,default:Sd}]};T(ig,Vn,{layout:o6,params:["radius","size","padding"],fields:Sd});const $d=["x0","y0","x1","y1","depth","children"];function ag(e){Vn.call(this,e)}ag.Definition={type:"Partition",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"round",type:"boolean",default:!1},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:$d.length,default:$d}]};T(ag,Vn,{layout:s6,params:["size","round","padding"],fields:$d});function ug(e){$.call(this,null,e)}ug.Definition={type:"Stratify",metadata:{treesource:!0},params:[{name:"key",type:"field",required:!0},{name:"parentKey",type:"field",required:!0}]};T(ug,$,{transform(e,t){t.source||S("Stratify transform requires an upstream data source.");let n=this.value;const r=e.modified(),i=t.fork(t.ALL).materialize(t.SOURCE),a=!n||r||t.changed(t.ADD_REM)||t.modified(e.key.fields)||t.modified(e.parentKey.fields);return i.source=i.source.slice(),a&&(n=i.source.length?Md(Qp().id(e.key).parentId(e.parentKey)(i.source),e.key,Ct):Md(Qp()([{}]),e.key,e.key)),i.source.root=this.value=n,i}});const gy={tidy:v6,cluster:$5},Bd=["x","y","depth","children"];function og(e){Vn.call(this,e)}og.Definition={type:"Tree",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"tidy",values:["tidy","cluster"]},{name:"size",type:"number",array:!0,length:2},{name:"nodeSize",type:"number",array:!0,length:2},{name:"separation",type:"boolean",default:!0},{name:"as",type:"string",array:!0,length:Bd.length,default:Bd}]};T(og,Vn,{layout(e){const t=e||"tidy";if(G(gy,t))return gy[t]();S("Unrecognized Tree layout method: "+t)},params:["size","nodeSize"],fields:Bd});function sg(e){$.call(this,[],e)}sg.Definition={type:"TreeLinks",metadata:{tree:!0,generates:!0,changes:!0},params:[]};T(sg,$,{transform(e,t){const n=this.value,r=t.source&&t.source.root,i=t.fork(t.NO_SOURCE),a={};return r||S("TreeLinks transform requires a tree data source."),t.changed(t.ADD_REM)?(i.rem=n,t.visit(t.SOURCE,u=>a[Y(u)]=1),r.each(u=>{const o=u.data,s=u.parent&&u.parent.data;s&&a[Y(o)]&&a[Y(s)]&&i.add.push(le({source:s,target:o}))}),this.value=i.add):t.changed(t.MOD)&&(t.visit(t.MOD,u=>a[Y(u)]=1),n.forEach(u=>{(a[Y(u.source)]||a[Y(u.target)])&&i.mod.push(u)})),i}});const py={binary:b6,dice:Su,slice:fl,slicedice:A6,squarify:qv,resquarify:E6},_d=["x0","y0","x1","y1","depth","children"];function lg(e){Vn.call(this,e)}lg.Definition={type:"Treemap",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"squarify",values:["squarify","resquarify","binary","dice","slice","slicedice"]},{name:"padding",type:"number",default:0},{name:"paddingInner",type:"number",default:0},{name:"paddingOuter",type:"number",default:0},{name:"paddingTop",type:"number",default:0},{name:"paddingRight",type:"number",default:0},{name:"paddingBottom",type:"number",default:0},{name:"paddingLeft",type:"number",default:0},{name:"ratio",type:"number",default:1.618033988749895},{name:"round",type:"boolean",default:!1},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:_d.length,default:_d}]};T(lg,Vn,{layout(){const e=x6();return e.ratio=t=>{const n=e.tile();n.ratio&&e.tile(n.ratio(t))},e.method=t=>{G(py,t)?e.tile(py[t]):S("Unrecognized Treemap layout method: "+t)},e},params:["method","ratio","size","round","padding","paddingInner","paddingOuter","paddingTop","paddingRight","paddingBottom","paddingLeft"],fields:_d});const U$=Object.freeze(Object.defineProperty({__proto__:null,nest:rg,pack:ig,partition:ag,stratify:ug,tree:og,treelinks:sg,treemap:lg},Symbol.toStringTag,{value:"Module"})),Kf=4278190080;function q$(e,t){const n=e.bitmap();return(t||[]).forEach(r=>n.set(e(r.boundary[0]),e(r.boundary[3]))),[n,void 0]}function j$(e,t,n,r,i){const a=e.width,u=e.height,o=r||i,s=pr(a,u).getContext("2d"),l=pr(a,u).getContext("2d"),f=o&&pr(a,u).getContext("2d");n.forEach(D=>Ho(s,D,!1)),Ho(l,t,!1),o&&Ho(f,t,!0);const c=Jf(s,a,u),d=Jf(l,a,u),h=o&&Jf(f,a,u),g=e.bitmap(),p=o&&e.bitmap();let m,y,v,x,b,E,w,A;for(y=0;y<u;++y)for(m=0;m<a;++m)b=y*a+m,E=c[b]&Kf,A=d[b]&Kf,w=o&&h[b]&Kf,(E||w||A)&&(v=e(m),x=e(y),!i&&(E||A)&&g.set(v,x),o&&(E||w)&&p.set(v,x));return[g,p]}function Jf(e,t,n){return new Uint32Array(e.getImageData(0,0,t,n).data.buffer)}function Ho(e,t,n){if(!t.length)return;const r=t[0].mark.marktype;r==="group"?t.forEach(i=>{i.items.forEach(a=>Ho(e,a.items,n))}):Pt[r].draw(e,{items:n?t.map(G$):t})}function G$(e){const t=Fl(e,{});return t.stroke&&t.strokeOpacity!==0||t.fill&&t.fillOpacity!==0?{...t,strokeOpacity:1,stroke:"#000",fillOpacity:0}:t}const Tn=5,rt=31,pu=32,rr=new Uint32Array(pu+1),tn=new Uint32Array(pu+1);tn[0]=0;rr[0]=~tn[0];for(let e=1;e<=pu;++e)tn[e]=tn[e-1]<<1|1,rr[e]=~tn[e];function W$(e,t){const n=new Uint32Array(~~((e*t+pu)/pu));function r(a,u){n[a]|=u}function i(a,u){n[a]&=u}return{array:n,get:(a,u)=>{const o=u*e+a;return n[o>>>Tn]&1<<(o&rt)},set:(a,u)=>{const o=u*e+a;r(o>>>Tn,1<<(o&rt))},clear:(a,u)=>{const o=u*e+a;i(o>>>Tn,~(1<<(o&rt)))},getRange:(a,u,o,s)=>{let l=s,f,c,d,h;for(;l>=u;--l)if(f=l*e+a,c=l*e+o,d=f>>>Tn,h=c>>>Tn,d===h){if(n[d]&rr[f&rt]&tn[(c&rt)+1])return!0}else{if(n[d]&rr[f&rt]||n[h]&tn[(c&rt)+1])return!0;for(let g=d+1;g<h;++g)if(n[g])return!0}return!1},setRange:(a,u,o,s)=>{let l,f,c,d,h;for(;u<=s;++u)if(l=u*e+a,f=u*e+o,c=l>>>Tn,d=f>>>Tn,c===d)r(c,rr[l&rt]&tn[(f&rt)+1]);else for(r(c,rr[l&rt]),r(d,tn[(f&rt)+1]),h=c+1;h<d;++h)r(h,4294967295)},clearRange:(a,u,o,s)=>{let l,f,c,d,h;for(;u<=s;++u)if(l=u*e+a,f=u*e+o,c=l>>>Tn,d=f>>>Tn,c===d)i(c,tn[l&rt]|rr[(f&rt)+1]);else for(i(c,tn[l&rt]),i(d,rr[(f&rt)+1]),h=c+1;h<d;++h)i(h,0)},outOfBounds:(a,u,o,s)=>a<0||u<0||s>=t||o>=e}}function Y$(e,t,n){const r=Math.max(1,Math.sqrt(e*t/1e6)),i=~~((e+2*n+r)/r),a=~~((t+2*n+r)/r),u=o=>~~((o+n)/r);return u.invert=o=>o*r-n,u.bitmap=()=>W$(i,a),u.ratio=r,u.padding=n,u.width=e,u.height=t,u}function H$(e,t,n,r){const i=e.width,a=e.height;return function(u){const o=u.datum.datum.items[r].items,s=o.length,l=u.datum.fontSize,f=on.width(u.datum,u.datum.text);let c=0,d,h,g,p,m,y,v;for(let x=0;x<s;++x)d=o[x].x,g=o[x].y,h=o[x].x2===void 0?d:o[x].x2,p=o[x].y2===void 0?g:o[x].y2,m=(d+h)/2,y=(g+p)/2,v=Math.abs(h-d+p-g),v>=c&&(c=v,u.x=m,u.y=y);return m=f/2,y=l/2,d=u.x-m,h=u.x+m,g=u.y-y,p=u.y+y,u.align="center",d<0&&h<=i?u.align="left":0<=d&&i<h&&(u.align="right"),u.baseline="middle",g<0&&p<=a?u.baseline="top":0<=g&&a<p&&(u.baseline="bottom"),!0}}function Vs(e,t,n,r,i,a){let u=n/2;return e-u<0||e+u>i||t-(u=r/2)<0||t+u>a}function hr(e,t,n,r,i,a,u,o){const s=i*a/(r*2),l=e(t-s),f=e(t+s),c=e(n-(a=a/2)),d=e(n+a);return u.outOfBounds(l,c,f,d)||u.getRange(l,c,f,d)||o&&o.getRange(l,c,f,d)}function X$(e,t,n,r){const i=e.width,a=e.height,u=t[0],o=t[1];function s(l,f,c,d,h){const g=e.invert(l),p=e.invert(f);let m=c,y=a,v;if(!Vs(g,p,d,h,i,a)&&!hr(e,g,p,h,d,m,u,o)&&!hr(e,g,p,h,d,h,u,null)){for(;y-m>=1;)v=(m+y)/2,hr(e,g,p,h,d,v,u,o)?y=v:m=v;if(m>c)return[g,p,m,!0]}}return function(l){const f=l.datum.datum.items[r].items,c=f.length,d=l.datum.fontSize,h=on.width(l.datum,l.datum.text);let g=n?d:0,p=!1,m=!1,y=0,v,x,b,E,w,A,D,C,F,M,O,L,N,k,R,j,ee;for(let Q=0;Q<c;++Q){for(v=f[Q].x,b=f[Q].y,x=f[Q].x2===void 0?v:f[Q].x2,E=f[Q].y2===void 0?b:f[Q].y2,v>x&&(ee=v,v=x,x=ee),b>E&&(ee=b,b=E,E=ee),F=e(v),O=e(x),M=~~((F+O)/2),L=e(b),k=e(E),N=~~((L+k)/2),D=M;D>=F;--D)for(C=N;C>=L;--C)j=s(D,C,g,h,d),j&&([l.x,l.y,g,p]=j);for(D=M;D<=O;++D)for(C=N;C<=k;++C)j=s(D,C,g,h,d),j&&([l.x,l.y,g,p]=j);!p&&!n&&(R=Math.abs(x-v+E-b),w=(v+x)/2,A=(b+E)/2,R>=y&&!Vs(w,A,h,d,i,a)&&!hr(e,w,A,d,h,d,u,null)&&(y=R,l.x=w,l.y=A,m=!0))}return p||m?(w=h/2,A=d/2,u.setRange(e(l.x-w),e(l.y-A),e(l.x+w),e(l.y+A)),l.align="center",l.baseline="middle",!0):!1}}const V$=[-1,-1,1,1],K$=[-1,1,-1,1];function J$(e,t,n,r){const i=e.width,a=e.height,u=t[0],o=t[1],s=e.bitmap();return function(l){const f=l.datum.datum.items[r].items,c=f.length,d=l.datum.fontSize,h=on.width(l.datum,l.datum.text),g=[];let p=n?d:0,m=!1,y=!1,v=0,x,b,E,w,A,D,C,F,M,O,L,N;for(let k=0;k<c;++k){for(x=f[k].x,E=f[k].y,b=f[k].x2===void 0?x:f[k].x2,w=f[k].y2===void 0?E:f[k].y2,g.push([e((x+b)/2),e((E+w)/2)]);g.length;)if([C,F]=g.pop(),!(u.get(C,F)||o.get(C,F)||s.get(C,F))){s.set(C,F);for(let R=0;R<4;++R)A=C+V$[R],D=F+K$[R],s.outOfBounds(A,D,A,D)||g.push([A,D]);if(A=e.invert(C),D=e.invert(F),M=p,O=a,!Vs(A,D,h,d,i,a)&&!hr(e,A,D,d,h,M,u,o)&&!hr(e,A,D,d,h,d,u,null)){for(;O-M>=1;)L=(M+O)/2,hr(e,A,D,d,h,L,u,o)?O=L:M=L;M>p&&(l.x=A,l.y=D,p=M,m=!0)}}!m&&!n&&(N=Math.abs(b-x+w-E),A=(x+b)/2,D=(E+w)/2,N>=v&&!Vs(A,D,h,d,i,a)&&!hr(e,A,D,d,h,d,u,null)&&(v=N,l.x=A,l.y=D,y=!0))}return m||y?(A=h/2,D=d/2,u.setRange(e(l.x-A),e(l.y-D),e(l.x+A),e(l.y+D)),l.align="center",l.baseline="middle",!0):!1}}const Q$=["right","center","left"],Z$=["bottom","middle","top"];function eB(e,t,n,r){const i=e.width,a=e.height,u=t[0],o=t[1],s=r.length;return function(l){const f=l.boundary,c=l.datum.fontSize;if(f[2]<0||f[5]<0||f[0]>i||f[3]>a)return!1;let d=l.textWidth??0,h,g,p,m,y,v,x,b,E,w,A,D,C,F,M;for(let O=0;O<s;++O){if(h=(n[O]&3)-1,g=(n[O]>>>2&3)-1,p=h===0&&g===0||r[O]<0,m=h&&g?Math.SQRT1_2:1,y=r[O]<0?-1:1,v=f[1+h]+r[O]*h*m,A=f[4+g]+y*c*g/2+r[O]*g*m,b=A-c/2,E=A+c/2,D=e(v),F=e(b),M=e(E),!d)if(my(D,D,F,M,u,o,v,v,b,E,f,p))d=on.width(l.datum,l.datum.text);else continue;if(w=v+y*d*h/2,v=w-d/2,x=w+d/2,D=e(v),C=e(x),my(D,C,F,M,u,o,v,x,b,E,f,p))return l.x=h?h*y<0?x:v:w,l.y=g?g*y<0?E:b:A,l.align=Q$[h*y+1],l.baseline=Z$[g*y+1],u.setRange(D,F,C,M),!0}return!1}}function my(e,t,n,r,i,a,u,o,s,l,f,c){return!(i.outOfBounds(e,n,t,r)||(c&&a||i).getRange(e,n,t,r))}const Qf=0,Zf=4,ec=8,tc=0,nc=1,rc=2,tB={"top-left":Qf+tc,top:Qf+nc,"top-right":Qf+rc,left:Zf+tc,middle:Zf+nc,right:Zf+rc,"bottom-left":ec+tc,bottom:ec+nc,"bottom-right":ec+rc},nB={naive:H$,"reduced-search":X$,floodfill:J$};function rB(e,t,n,r,i,a,u,o,s,l,f){if(!e.length)return e;const c=Math.max(r.length,i.length),d=iB(r,c),h=aB(i,c),g=uB(e[0].datum),p=g==="group"&&e[0].datum.items[s].marktype,m=p==="area",y=oB(g,p,o,s),v=l===null||l===1/0,x=m&&f==="naive";let b=-1,E=-1;const w=e.map(F=>{const M=v?on.width(F,F.text):void 0;return b=Math.max(b,M),E=Math.max(E,F.fontSize),{datum:F,opacity:0,x:void 0,y:void 0,align:void 0,baseline:void 0,boundary:y(F),textWidth:M}});l=l===null||l===1/0?Math.max(b,E)+Math.max(...r):l;const A=Y$(t[0],t[1],l);let D;if(!x){n&&w.sort((O,L)=>n(O.datum,L.datum));let F=!1;for(let O=0;O<h.length&&!F;++O)F=h[O]===5||d[O]<0;const M=(g&&u||m)&&e.map(O=>O.datum);D=a.length||M?j$(A,M||[],a,F,m):q$(A,u&&w)}const C=m?nB[f](A,D,u,s):eB(A,D,h,d);return w.forEach(F=>F.opacity=+C(F)),w}function iB(e,t){const n=new Float64Array(t),r=e.length;for(let i=0;i<r;++i)n[i]=e[i]||0;for(let i=r;i<t;++i)n[i]=n[r-1];return n}function aB(e,t){const n=new Int8Array(t),r=e.length;for(let i=0;i<r;++i)n[i]|=tB[e[i]];for(let i=r;i<t;++i)n[i]=n[r-1];return n}function uB(e){return e&&e.mark&&e.mark.marktype}function oB(e,t,n,r){const i=a=>[a.x,a.x,a.x,a.y,a.y,a.y];return e?e==="line"||e==="area"?a=>i(a.datum):t==="line"?a=>{const u=a.datum.items[r].items;return i(u.length?u[n==="start"?0:u.length-1]:{x:NaN,y:NaN})}:a=>{const u=a.datum.bounds;return[u.x1,(u.x1+u.x2)/2,u.x2,u.y1,(u.y1+u.y2)/2,u.y2]}:i}const Rd=["x","y","opacity","align","baseline"],UE=["top-left","left","bottom-left","top","bottom","top-right","right","bottom-right"];function fg(e){$.call(this,null,e)}fg.Definition={type:"Label",metadata:{modifies:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"sort",type:"compare"},{name:"anchor",type:"string",array:!0,default:UE},{name:"offset",type:"number",array:!0,default:[1]},{name:"padding",type:"number",default:0,null:!0},{name:"lineAnchor",type:"string",values:["start","end"],default:"end"},{name:"markIndex",type:"number",default:0},{name:"avoidBaseMark",type:"boolean",default:!0},{name:"avoidMarks",type:"data",array:!0},{name:"method",type:"string",default:"naive"},{name:"as",type:"string",array:!0,length:Rd.length,default:Rd}]};T(fg,$,{transform(e,t){function n(a){const u=e[a];return J(u)&&t.modified(u.fields)}const r=e.modified();if(!(r||t.changed(t.ADD_REM)||n("sort")))return;(!e.size||e.size.length!==2)&&S("Size parameter should be specified as a [width, height] array.");const i=e.as||Rd;return rB(t.materialize(t.SOURCE).source||[],e.size,e.sort,q(e.offset==null?1:e.offset),q(e.anchor||UE),e.avoidMarks||[],e.avoidBaseMark!==!1,e.lineAnchor||"end",e.markIndex||0,e.padding===void 0?0:e.padding,e.method||"naive").forEach(a=>{const u=a.datum;u[i[0]]=a.x,u[i[1]]=a.y,u[i[2]]=a.opacity,u[i[3]]=a.align,u[i[4]]=a.baseline}),t.reflow(r).modifies(i)}});const sB=Object.freeze(Object.defineProperty({__proto__:null,label:fg},Symbol.toStringTag,{value:"Module"}));function qE(e,t){var n=[],r=function(f){return f(o)},i,a,u,o,s,l;if(t==null)n.push(e);else for(i={},a=0,u=e.length;a<u;++a)o=e[a],s=t.map(r),l=i[s],l||(i[s]=l=[],l.dims=s,n.push(l)),l.push(o);return n}function cg(e){$.call(this,null,e)}cg.Definition={type:"Loess",metadata:{generates:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"bandwidth",type:"number",default:.3},{name:"as",type:"string",array:!0}]};T(cg,$,{transform(e,t){const n=t.fork(t.NO_SOURCE|t.NO_FIELDS);if(!this.value||t.changed()||e.modified()){const r=t.materialize(t.SOURCE).source,i=qE(r,e.groupby),a=(e.groupby||[]).map(Ee),u=a.length,o=e.as||[Ee(e.x),Ee(e.y)],s=[];i.forEach(l=>{lb(l,e.x,e.y,e.bandwidth||.3).forEach(f=>{const c={};for(let d=0;d<u;++d)c[a[d]]=l.dims[d];c[o[0]]=f[0],c[o[1]]=f[1],s.push(le(c))})}),this.value&&(n.rem=this.value),this.value=n.add=n.source=s}return n}});const Od={linear:x0,log:ab,exp:ub,pow:ob,quad:b0,poly:sb},lB=(e,t)=>e==="poly"?t:e==="quad"?2:1;function dg(e){$.call(this,null,e)}dg.Definition={type:"Regression",metadata:{generates:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"method",type:"string",default:"linear",values:Object.keys(Od)},{name:"order",type:"number",default:3},{name:"extent",type:"number",array:!0,length:2},{name:"params",type:"boolean",default:!1},{name:"as",type:"string",array:!0}]};T(dg,$,{transform(e,t){const n=t.fork(t.NO_SOURCE|t.NO_FIELDS);if(!this.value||t.changed()||e.modified()){const r=t.materialize(t.SOURCE).source,i=qE(r,e.groupby),a=(e.groupby||[]).map(Ee),u=e.method||"linear",o=e.order||3,s=lB(u,o),l=e.as||[Ee(e.x),Ee(e.y)],f=Od[u],c=[];let d=e.extent;G(Od,u)||S("Invalid regression method: "+u),d!=null&&u==="log"&&d[0]<=0&&(t.dataflow.warn("Ignoring extent with values <= 0 for log regression."),d=null),i.forEach(h=>{if(h.length<=s){t.dataflow.warn("Skipping regression with more parameters than data points.");return}const p=f(h,e.x,e.y,o);if(e.params){c.push(le({keys:h.dims,coef:p.coef,rSquared:p.rSquared}));return}const m=d||ln(h,e.x),y=v=>{const x={};for(let b=0;b<a.length;++b)x[a[b]]=h.dims[b];x[l[0]]=v[0],x[l[1]]=v[1],c.push(le(x))};u==="linear"?m.forEach(v=>y([v,p.predict(v)])):Pl(p.predict,m,25,200).forEach(y)}),this.value&&(n.rem=this.value),this.value=n.add=n.source=c}return n}});const fB=Object.freeze(Object.defineProperty({__proto__:null,loess:cg,regression:dg},Symbol.toStringTag,{value:"Module"}));function hg(e){$.call(this,null,e)}hg.Definition={type:"Voronoi",metadata:{modifies:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"size",type:"number",array:!0,length:2},{name:"extent",type:"array",array:!0,length:2,default:[[-1e5,-1e5],[1e5,1e5]],content:{type:"number",array:!0,length:2}},{name:"as",type:"string",default:"path"}]};const cB=[-1e5,-1e5,1e5,1e5];T(hg,$,{transform(e,t){const n=e.as||"path",r=t.source;if(!r||!r.length)return t;let i=e.size;i=i?[0,0,i[0],i[1]]:(i=e.extent)?[i[0][0],i[0][1],i[1][0],i[1][1]]:cB;const a=this.value=fh.from(r,e.x,e.y).voronoi(i);for(let u=0,o=r.length;u<o;++u){const s=a.cellPolygon(u);r[u][n]=s&&!hB(s)?dB(s):null}return t.reflow(e.modified()).modifies(n)}});function dB(e){const t=e[0][0],n=e[0][1];let r=e.length-1;for(;e[r][0]===t&&e[r][1]===n;--r);return"M"+e.slice(0,r+1).join("L")+"Z"}function hB(e){return e.length===2&&e[0][0]===e[1][0]&&e[0][1]===e[1][1]}const gB=Object.freeze(Object.defineProperty({__proto__:null,voronoi:hg},Symbol.toStringTag,{value:"Module"}));var ic=Math.PI/180,Pa=64,Xo=2048;function pB(){var e=[256,256],t,n,r,i,a,u,o,s=jE,l=[],f=Math.random,c={};c.layout=function(){for(var g=d(pr()),p=AB((e[0]>>5)*e[1]),m=null,y=l.length,v=-1,x=[],b=l.map(w=>({text:t(w),font:n(w),style:i(w),weight:a(w),rotate:u(w),size:~~(r(w)+1e-14),padding:o(w),xoff:0,yoff:0,x1:0,y1:0,x0:0,y0:0,hasText:!1,sprite:null,datum:w})).sort((w,A)=>A.size-w.size);++v<y;){var E=b[v];E.x=e[0]*(f()+.5)>>1,E.y=e[1]*(f()+.5)>>1,mB(g,E,b,v),E.hasText&&h(p,E,m)&&(x.push(E),m?vB(m,E):m=[{x:E.x+E.x0,y:E.y+E.y0},{x:E.x+E.x1,y:E.y+E.y1}],E.x-=e[0]>>1,E.y-=e[1]>>1)}return x};function d(g){g.width=g.height=1;var p=Math.sqrt(g.getContext("2d").getImageData(0,0,1,1).data.length>>2);g.width=(Pa<<5)/p,g.height=Xo/p;var m=g.getContext("2d");return m.fillStyle=m.strokeStyle="red",m.textAlign="center",{context:m,ratio:p}}function h(g,p,m){for(var y=p.x,v=p.y,x=Math.sqrt(e[0]*e[0]+e[1]*e[1]),b=s(e),E=f()<.5?1:-1,w=-E,A,D,C;(A=b(w+=E))&&(D=~~A[0],C=~~A[1],!(Math.min(Math.abs(D),Math.abs(C))>=x));)if(p.x=y+D,p.y=v+C,!(p.x+p.x0<0||p.y+p.y0<0||p.x+p.x1>e[0]||p.y+p.y1>e[1])&&(!m||!yB(p,g,e[0]))&&(!m||xB(p,m))){for(var F=p.sprite,M=p.width>>5,O=e[0]>>5,L=p.x-(M<<4),N=L&127,k=32-N,R=p.y1-p.y0,j=(p.y+p.y0)*O+(L>>5),ee,Q=0;Q<R;Q++){ee=0;for(var Ce=0;Ce<=M;Ce++)g[j+Ce]|=ee<<k|(Ce<M?(ee=F[Q*M+Ce])>>>N:0);j+=O}return p.sprite=null,!0}return!1}return c.words=function(g){return arguments.length?(l=g,c):l},c.size=function(g){return arguments.length?(e=[+g[0],+g[1]],c):e},c.font=function(g){return arguments.length?(n=Sr(g),c):n},c.fontStyle=function(g){return arguments.length?(i=Sr(g),c):i},c.fontWeight=function(g){return arguments.length?(a=Sr(g),c):a},c.rotate=function(g){return arguments.length?(u=Sr(g),c):u},c.text=function(g){return arguments.length?(t=Sr(g),c):t},c.spiral=function(g){return arguments.length?(s=EB[g]||g,c):s},c.fontSize=function(g){return arguments.length?(r=Sr(g),c):r},c.padding=function(g){return arguments.length?(o=Sr(g),c):o},c.random=function(g){return arguments.length?(f=g,c):f},c}function mB(e,t,n,r){if(!t.sprite){var i=e.context,a=e.ratio;i.clearRect(0,0,(Pa<<5)/a,Xo/a);var u=0,o=0,s=0,l=n.length,f,c,d,h,g;for(--r;++r<l;){if(t=n[r],i.save(),i.font=t.style+" "+t.weight+" "+~~((t.size+1)/a)+"px "+t.font,f=i.measureText(t.text+"m").width*a,d=t.size<<1,t.rotate){var p=Math.sin(t.rotate*ic),m=Math.cos(t.rotate*ic),y=f*m,v=f*p,x=d*m,b=d*p;f=Math.max(Math.abs(y+b),Math.abs(y-b))+31>>5<<5,d=~~Math.max(Math.abs(v+x),Math.abs(v-x))}else f=f+31>>5<<5;if(d>s&&(s=d),u+f>=Pa<<5&&(u=0,o+=s,s=0),o+d>=Xo)break;i.translate((u+(f>>1))/a,(o+(d>>1))/a),t.rotate&&i.rotate(t.rotate*ic),i.fillText(t.text,0,0),t.padding&&(i.lineWidth=2*t.padding,i.strokeText(t.text,0,0)),i.restore(),t.width=f,t.height=d,t.xoff=u,t.yoff=o,t.x1=f>>1,t.y1=d>>1,t.x0=-t.x1,t.y0=-t.y1,t.hasText=!0,u+=f}for(var E=i.getImageData(0,0,(Pa<<5)/a,Xo/a).data,w=[];--r>=0;)if(t=n[r],!!t.hasText){for(f=t.width,c=f>>5,d=t.y1-t.y0,h=0;h<d*c;h++)w[h]=0;if(u=t.xoff,u==null)return;o=t.yoff;var A=0,D=-1;for(g=0;g<d;g++){for(h=0;h<f;h++){var C=c*g+(h>>5),F=E[(o+g)*(Pa<<5)+(u+h)<<2]?1<<31-h%32:0;w[C]|=F,A|=F}A?D=g:(t.y0++,d--,g--,o++)}t.y1=t.y0+D,t.sprite=w.slice(0,(t.y1-t.y0)*c)}}}function yB(e,t,n){n>>=5;for(var r=e.sprite,i=e.width>>5,a=e.x-(i<<4),u=a&127,o=32-u,s=e.y1-e.y0,l=(e.y+e.y0)*n+(a>>5),f,c=0;c<s;c++){f=0;for(var d=0;d<=i;d++)if((f<<o|(d<i?(f=r[c*i+d])>>>u:0))&t[l+d])return!0;l+=n}return!1}function vB(e,t){var n=e[0],r=e[1];t.x+t.x0<n.x&&(n.x=t.x+t.x0),t.y+t.y0<n.y&&(n.y=t.y+t.y0),t.x+t.x1>r.x&&(r.x=t.x+t.x1),t.y+t.y1>r.y&&(r.y=t.y+t.y1)}function xB(e,t){return e.x+e.x1>t[0].x&&e.x+e.x0<t[1].x&&e.y+e.y1>t[0].y&&e.y+e.y0<t[1].y}function jE(e){var t=e[0]/e[1];return function(n){return[t*(n*=.1)*Math.cos(n),n*Math.sin(n)]}}function bB(e){var t=4,n=t*e[0]/e[1],r=0,i=0;return function(a){var u=a<0?-1:1;switch(Math.sqrt(1+4*u*a)-u&3){case 0:r+=n;break;case 1:i+=t;break;case 2:r-=n;break;default:i-=t;break}return[r,i]}}function AB(e){for(var t=[],n=-1;++n<e;)t[n]=0;return t}function Sr(e){return typeof e=="function"?e:function(){return e}}var EB={archimedean:jE,rectangular:bB};const GE=["x","y","font","fontSize","fontStyle","fontWeight","angle"],wB=["text","font","rotate","fontSize","fontStyle","fontWeight"];function gg(e){$.call(this,pB(),e)}gg.Definition={type:"Wordcloud",metadata:{modifies:!0},params:[{name:"size",type:"number",array:!0,length:2},{name:"font",type:"string",expr:!0,default:"sans-serif"},{name:"fontStyle",type:"string",expr:!0,default:"normal"},{name:"fontWeight",type:"string",expr:!0,default:"normal"},{name:"fontSize",type:"number",expr:!0,default:14},{name:"fontSizeRange",type:"number",array:"nullable",default:[10,50]},{name:"rotate",type:"number",expr:!0,default:0},{name:"text",type:"field"},{name:"spiral",type:"string",values:["archimedean","rectangular"]},{name:"padding",type:"number",expr:!0},{name:"as",type:"string",array:!0,length:7,default:GE}]};T(gg,$,{transform(e,t){e.size&&!(e.size[0]&&e.size[1])&&S("Wordcloud size dimensions must be non-zero.");function n(g){const p=e[g];return J(p)&&t.modified(p.fields)}const r=e.modified();if(!(r||t.changed(t.ADD_REM)||wB.some(n)))return;const i=t.materialize(t.SOURCE).source,a=this.value,u=e.as||GE;let o=e.fontSize||14,s;if(J(o)?s=e.fontSizeRange:o=tt(o),s){const g=o,p=ue("sqrt")().domain(ln(i,g)).range(s);o=m=>p(g(m))}i.forEach(g=>{g[u[0]]=NaN,g[u[1]]=NaN,g[u[3]]=0});const l=a.words(i).text(e.text).size(e.size||[500,500]).padding(e.padding||1).spiral(e.spiral||"archimedean").rotate(e.rotate||0).font(e.font||"sans-serif").fontStyle(e.fontStyle||"normal").fontWeight(e.fontWeight||"normal").fontSize(o).random(zt).layout(),f=a.size(),c=f[0]>>1,d=f[1]>>1,h=l.length;for(let g=0,p,m;g<h;++g)p=l[g],m=p.datum,m[u[0]]=p.x+c,m[u[1]]=p.y+d,m[u[2]]=p.font,m[u[3]]=p.size,m[u[4]]=p.style,m[u[5]]=p.weight,m[u[6]]=p.rotate;return t.reflow(r).modifies(u)}});const DB=Object.freeze(Object.defineProperty({__proto__:null,wordcloud:gg},Symbol.toStringTag,{value:"Module"})),CB=e=>new Uint8Array(e),FB=e=>new Uint16Array(e),Ja=e=>new Uint32Array(e);function kB(){let e=8,t=[],n=Ja(0),r=Fo(0,e),i=Fo(0,e);return{data:()=>t,seen:()=>n=MB(n,t.length),add(a){for(let u=0,o=t.length,s=a.length,l;u<s;++u)l=a[u],l._index=o++,t.push(l)},remove(a,u){const o=t.length,s=Array(o-a),l=t;let f,c,d;for(c=0;!u[c]&&c<o;++c)s[c]=t[c],l[c]=c;for(d=c;c<o;++c)f=t[c],u[c]?l[c]=-1:(l[c]=d,r[d]=r[c],i[d]=i[c],s[d]=f,f._index=d++),r[c]=0;return t=s,l},size:()=>t.length,curr:()=>r,prev:()=>i,reset:a=>i[a]=r[a],all:()=>e<257?255:e<65537?65535:4294967295,set(a,u){r[a]|=u},clear(a,u){r[a]&=~u},resize(a,u){const o=r.length;(a>o||u>e)&&(e=Math.max(u,e),r=Fo(a,e,r),i=Fo(a,e))}}}function MB(e,t,n){return e.length>=t?e:(n=n||new e.constructor(t),n.set(e),n)}function Fo(e,t,n){const r=(t<257?CB:t<65537?FB:Ja)(e);return n&&r.set(n),r}function yy(e,t,n){const r=1<<t;return{one:r,zero:~r,range:n.slice(),bisect:e.bisect,index:e.index,size:e.size,onAdd(i,a){const u=this,o=u.bisect(u.range,i.value),s=i.index,l=o[0],f=o[1],c=s.length;let d;for(d=0;d<l;++d)a[s[d]]|=r;for(d=f;d<c;++d)a[s[d]]|=r;return u}}}function vy(){let e=Ja(0),t=[],n=0;function r(o,s,l){if(!s.length)return[];const f=n,c=s.length,d=Ja(c);let h=Array(c),g,p,m;for(m=0;m<c;++m)h[m]=o(s[m]),d[m]=m;if(h=SB(h,d),f)g=t,p=e,t=Array(f+c),e=Ja(f+c),$B(l,g,p,f,h,d,c,t,e);else{if(l>0)for(m=0;m<c;++m)d[m]+=l;t=h,e=d}return n=f+c,{index:d,value:h}}function i(o,s){const l=n;let f,c,d;for(c=0;!s[e[c]]&&c<l;++c);for(d=c;c<l;++c)s[f=e[c]]||(e[d]=f,t[d]=t[c],++d);n=l-o}function a(o){for(let s=0,l=n;s<l;++s)e[s]=o[e[s]]}function u(o,s){let l;return s?l=s.length:(s=t,l=n),[aD(s,o[0],0,l),Hr(s,o[1],0,l)]}return{insert:r,remove:i,bisect:u,reindex:a,index:()=>e,size:()=>n}}function SB(e,t){return e.sort.call(t,(n,r)=>{const i=e[n],a=e[r];return i<a?-1:i>a?1:0}),JD(e,t)}function $B(e,t,n,r,i,a,u,o,s){let l=0,f=0,c;for(c=0;l<r&&f<u;++c)t[l]<i[f]?(o[c]=t[l],s[c]=n[l++]):(o[c]=i[f],s[c]=a[f++]+e);for(;l<r;++l,++c)o[c]=t[l],s[c]=n[l];for(;f<u;++f,++c)o[c]=i[f],s[c]=a[f]+e}function pg(e){$.call(this,kB(),e),this._indices=null,this._dims=null}pg.Definition={type:"CrossFilter",metadata:{},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"query",type:"array",array:!0,required:!0,content:{type:"number",array:!0,length:2}}]};T(pg,$,{transform(e,t){if(this._dims){var n=e.modified("fields")||e.fields.some(r=>t.modified(r.fields));return n?this.reinit(e,t):this.eval(e,t)}else return this.init(e,t)},init(e,t){const n=e.fields,r=e.query,i=this._indices={},a=this._dims=[],u=r.length;let o=0,s,l;for(;o<u;++o)s=n[o].fname,l=i[s]||(i[s]=vy()),a.push(yy(l,o,r[o]));return this.eval(e,t)},reinit(e,t){const n=t.materialize().fork(),r=e.fields,i=e.query,a=this._indices,u=this._dims,o=this.value,s=o.curr(),l=o.prev(),f=o.all(),c=n.rem=n.add,d=n.mod,h=i.length,g={};let p,m,y,v,x,b,E,w,A;if(l.set(s),t.rem.length&&(x=this.remove(e,t,n)),t.add.length&&o.add(t.add),t.mod.length)for(b={},v=t.mod,E=0,w=v.length;E<w;++E)b[v[E]._index]=1;for(E=0;E<h;++E)A=r[E],(!u[E]||e.modified("fields",E)||t.modified(A.fields))&&(y=A.fname,(p=g[y])||(a[y]=m=vy(),g[y]=p=m.insert(A,t.source,0)),u[E]=yy(m,E,i[E]).onAdd(p,s));for(E=0,w=o.data().length;E<w;++E)x[E]||(l[E]!==s[E]?c.push(E):b[E]&&s[E]!==f&&d.push(E));return o.mask=(1<<h)-1,n},eval(e,t){const n=t.materialize().fork(),r=this._dims.length;let i=0;return t.rem.length&&(this.remove(e,t,n),i|=(1<<r)-1),e.modified("query")&&!e.modified("fields")&&(i|=this.update(e,t,n)),t.add.length&&(this.insert(e,t,n),i|=(1<<r)-1),t.mod.length&&(this.modify(t,n),i|=(1<<r)-1),this.value.mask=i,n},insert(e,t,n){const r=t.add,i=this.value,a=this._dims,u=this._indices,o=e.fields,s={},l=n.add,f=i.size()+r.length,c=a.length;let d=i.size(),h,g,p;i.resize(f,c),i.add(r);const m=i.curr(),y=i.prev(),v=i.all();for(h=0;h<c;++h)g=o[h].fname,p=s[g]||(s[g]=u[g].insert(o[h],r,d)),a[h].onAdd(p,m);for(;d<f;++d)y[d]=v,m[d]!==v&&l.push(d)},modify(e,t){const n=t.mod,r=this.value,i=r.curr(),a=r.all(),u=e.mod;let o,s,l;for(o=0,s=u.length;o<s;++o)l=u[o]._index,i[l]!==a&&n.push(l)},remove(e,t,n){const r=this._indices,i=this.value,a=i.curr(),u=i.prev(),o=i.all(),s={},l=n.rem,f=t.rem;let c,d,h,g;for(c=0,d=f.length;c<d;++c)h=f[c]._index,s[h]=1,u[h]=g=a[h],a[h]=o,g!==o&&l.push(h);for(h in r)r[h].remove(d,s);return this.reindex(t,d,s),s},reindex(e,t,n){const r=this._indices,i=this.value;e.runAfter(()=>{const a=i.remove(t,n);for(const u in r)r[u].reindex(a)})},update(e,t,n){const r=this._dims,i=e.query,a=t.stamp,u=r.length;let o=0,s,l;for(n.filters=0,l=0;l<u;++l)e.modified("query",l)&&(s=l,++o);if(o===1)o=r[s].one,this.incrementOne(r[s],i[s],n.add,n.rem);else for(l=0,o=0;l<u;++l)e.modified("query",l)&&(o|=r[l].one,this.incrementAll(r[l],i[l],a,n.add),n.rem=n.add);return o},incrementAll(e,t,n,r){const i=this.value,a=i.seen(),u=i.curr(),o=i.prev(),s=e.index(),l=e.bisect(e.range),f=e.bisect(t),c=f[0],d=f[1],h=l[0],g=l[1],p=e.one;let m,y,v;if(c<h)for(m=c,y=Math.min(h,d);m<y;++m)v=s[m],a[v]!==n&&(o[v]=u[v],a[v]=n,r.push(v)),u[v]^=p;else if(c>h)for(m=h,y=Math.min(c,g);m<y;++m)v=s[m],a[v]!==n&&(o[v]=u[v],a[v]=n,r.push(v)),u[v]^=p;if(d>g)for(m=Math.max(c,g),y=d;m<y;++m)v=s[m],a[v]!==n&&(o[v]=u[v],a[v]=n,r.push(v)),u[v]^=p;else if(d<g)for(m=Math.max(h,d),y=g;m<y;++m)v=s[m],a[v]!==n&&(o[v]=u[v],a[v]=n,r.push(v)),u[v]^=p;e.range=t.slice()},incrementOne(e,t,n,r){const i=this.value,a=i.curr(),u=e.index(),o=e.bisect(e.range),s=e.bisect(t),l=s[0],f=s[1],c=o[0],d=o[1],h=e.one;let g,p,m;if(l<c)for(g=l,p=Math.min(c,f);g<p;++g)m=u[g],a[m]^=h,n.push(m);else if(l>c)for(g=c,p=Math.min(l,d);g<p;++g)m=u[g],a[m]^=h,r.push(m);if(f>d)for(g=Math.max(l,d),p=f;g<p;++g)m=u[g],a[m]^=h,n.push(m);else if(f<d)for(g=Math.max(c,f),p=d;g<p;++g)m=u[g],a[m]^=h,r.push(m);e.range=t.slice()}});function mg(e){$.call(this,null,e)}mg.Definition={type:"ResolveFilter",metadata:{},params:[{name:"ignore",type:"number",required:!0,description:"A bit mask indicating which filters to ignore."},{name:"filter",type:"object",required:!0,description:"Per-tuple filter bitmaps from a CrossFilter transform."}]};T(mg,$,{transform(e,t){const n=~(e.ignore||0),r=e.filter,i=r.mask;if(!(i&n))return t.StopPropagation;const a=t.fork(t.ALL),u=r.data(),o=r.curr(),s=r.prev(),l=f=>o[f]&n?null:u[f];return a.filter(a.MOD,l),i&i-1?(a.filter(a.ADD,f=>{const c=o[f]&n;return!c&&c^s[f]&n?u[f]:null}),a.filter(a.REM,f=>{const c=o[f]&n;return c&&!(c^(c^s[f]&n))?u[f]:null})):(a.filter(a.ADD,l),a.filter(a.REM,f=>(o[f]&n)===i?u[f]:null)),a.filter(a.SOURCE,f=>l(f._index))}});const BB=Object.freeze(Object.defineProperty({__proto__:null,crossfilter:pg,resolvefilter:mg},Symbol.toStringTag,{value:"Module"})),_B="RawCode",Kt="Literal",RB="Property",OB="Identifier",TB="ArrayExpression",LB="BinaryExpression",WE="CallExpression",NB="ConditionalExpression",zB="LogicalExpression",PB="MemberExpression",IB="ObjectExpression",UB="UnaryExpression";function dn(e){this.type=e}dn.prototype.visit=function(e){let t,n,r;if(e(this))return 1;for(t=qB(this),n=0,r=t.length;n<r;++n)if(t[n].visit(e))return 1};function qB(e){switch(e.type){case TB:return e.elements;case LB:case zB:return[e.left,e.right];case WE:return[e.callee].concat(e.arguments);case NB:return[e.test,e.consequent,e.alternate];case PB:return[e.object,e.property];case IB:return e.properties;case RB:return[e.key,e.value];case UB:return[e.argument];case OB:case Kt:case _B:default:return[]}}var Sn,P,B,Ke,ce,af=1,Vu=2,Zr=3,Fr=4,uf=5,si=6,mt=7,Ku=8,jB=9;Sn={};Sn[af]="Boolean";Sn[Vu]="<end>";Sn[Zr]="Identifier";Sn[Fr]="Keyword";Sn[uf]="Null";Sn[si]="Numeric";Sn[mt]="Punctuator";Sn[Ku]="String";Sn[jB]="RegularExpression";var GB="ArrayExpression",WB="BinaryExpression",YB="CallExpression",HB="ConditionalExpression",YE="Identifier",XB="Literal",VB="LogicalExpression",KB="MemberExpression",JB="ObjectExpression",QB="Property",ZB="UnaryExpression",_e="Unexpected token %0",e_="Unexpected number",t_="Unexpected string",n_="Unexpected identifier",r_="Unexpected reserved word",i_="Unexpected end of input",Td="Invalid regular expression",ac="Invalid regular expression: missing /",HE="Octal literals are not allowed in strict mode.",a_="Duplicate data property in object literal not allowed in strict mode",ze="ILLEGAL",mu="Disabled.",u_=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]"),o_=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B2\\u08E4-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58\\u0C59\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C81-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D57\\u0D60-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFC-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA69D\\uA69F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C4\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2D\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]");function of(e,t){if(!e)throw new Error("ASSERT: "+t)}function zn(e){return e>=48&&e<=57}function yg(e){return"0123456789abcdefABCDEF".indexOf(e)>=0}function Qa(e){return"01234567".indexOf(e)>=0}function s_(e){return e===32||e===9||e===11||e===12||e===160||e>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0}function yu(e){return e===10||e===13||e===8232||e===8233}function Ju(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e===92||e>=128&&u_.test(String.fromCharCode(e))}function Ks(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||e===92||e>=128&&o_.test(String.fromCharCode(e))}const l_={if:1,in:1,do:1,var:1,for:1,new:1,try:1,let:1,this:1,else:1,case:1,void:1,with:1,enum:1,while:1,break:1,catch:1,throw:1,const:1,yield:1,class:1,super:1,return:1,typeof:1,delete:1,switch:1,export:1,import:1,public:1,static:1,default:1,finally:1,extends:1,package:1,private:1,function:1,continue:1,debugger:1,interface:1,protected:1,instanceof:1,implements:1};function XE(){for(;B<Ke;){const e=P.charCodeAt(B);if(s_(e)||yu(e))++B;else break}}function Ld(e){var t,n,r,i=0;for(n=e==="u"?4:2,t=0;t<n;++t)B<Ke&&yg(P[B])?(r=P[B++],i=i*16+"0123456789abcdef".indexOf(r.toLowerCase())):oe({},_e,ze);return String.fromCharCode(i)}function f_(){var e,t,n,r;for(e=P[B],t=0,e==="}"&&oe({},_e,ze);B<Ke&&(e=P[B++],!!yg(e));)t=t*16+"0123456789abcdef".indexOf(e.toLowerCase());return(t>1114111||e!=="}")&&oe({},_e,ze),t<=65535?String.fromCharCode(t):(n=(t-65536>>10)+55296,r=(t-65536&1023)+56320,String.fromCharCode(n,r))}function VE(){var e,t;for(e=P.charCodeAt(B++),t=String.fromCharCode(e),e===92&&(P.charCodeAt(B)!==117&&oe({},_e,ze),++B,e=Ld("u"),(!e||e==="\\"||!Ju(e.charCodeAt(0)))&&oe({},_e,ze),t=e);B<Ke&&(e=P.charCodeAt(B),!!Ks(e));)++B,t+=String.fromCharCode(e),e===92&&(t=t.substr(0,t.length-1),P.charCodeAt(B)!==117&&oe({},_e,ze),++B,e=Ld("u"),(!e||e==="\\"||!Ks(e.charCodeAt(0)))&&oe({},_e,ze),t+=e);return t}function c_(){var e,t;for(e=B++;B<Ke;){if(t=P.charCodeAt(B),t===92)return B=e,VE();if(Ks(t))++B;else break}return P.slice(e,B)}function d_(){var e,t,n;return e=B,t=P.charCodeAt(B)===92?VE():c_(),t.length===1?n=Zr:l_.hasOwnProperty(t)?n=Fr:t==="null"?n=uf:t==="true"||t==="false"?n=af:n=Zr,{type:n,value:t,start:e,end:B}}function uc(){var e=B,t=P.charCodeAt(B),n,r=P[B],i,a,u;switch(t){case 46:case 40:case 41:case 59:case 44:case 123:case 125:case 91:case 93:case 58:case 63:case 126:return++B,{type:mt,value:String.fromCharCode(t),start:e,end:B};default:if(n=P.charCodeAt(B+1),n===61)switch(t){case 43:case 45:case 47:case 60:case 62:case 94:case 124:case 37:case 38:case 42:return B+=2,{type:mt,value:String.fromCharCode(t)+String.fromCharCode(n),start:e,end:B};case 33:case 61:return B+=2,P.charCodeAt(B)===61&&++B,{type:mt,value:P.slice(e,B),start:e,end:B}}}if(u=P.substr(B,4),u===">>>=")return B+=4,{type:mt,value:u,start:e,end:B};if(a=u.substr(0,3),a===">>>"||a==="<<="||a===">>=")return B+=3,{type:mt,value:a,start:e,end:B};if(i=a.substr(0,2),r===i[1]&&"+-<>&|".indexOf(r)>=0||i==="=>")return B+=2,{type:mt,value:i,start:e,end:B};if(i==="//"&&oe({},_e,ze),"<>=!+-*%&|^/".indexOf(r)>=0)return++B,{type:mt,value:r,start:e,end:B};oe({},_e,ze)}function h_(e){let t="";for(;B<Ke&&yg(P[B]);)t+=P[B++];return t.length===0&&oe({},_e,ze),Ju(P.charCodeAt(B))&&oe({},_e,ze),{type:si,value:parseInt("0x"+t,16),start:e,end:B}}function g_(e){let t="0"+P[B++];for(;B<Ke&&Qa(P[B]);)t+=P[B++];return(Ju(P.charCodeAt(B))||zn(P.charCodeAt(B)))&&oe({},_e,ze),{type:si,value:parseInt(t,8),octal:!0,start:e,end:B}}function xy(){var e,t,n;if(n=P[B],of(zn(n.charCodeAt(0))||n===".","Numeric literal must start with a decimal digit or a decimal point"),t=B,e="",n!=="."){if(e=P[B++],n=P[B],e==="0"){if(n==="x"||n==="X")return++B,h_(t);if(Qa(n))return g_(t);n&&zn(n.charCodeAt(0))&&oe({},_e,ze)}for(;zn(P.charCodeAt(B));)e+=P[B++];n=P[B]}if(n==="."){for(e+=P[B++];zn(P.charCodeAt(B));)e+=P[B++];n=P[B]}if(n==="e"||n==="E")if(e+=P[B++],n=P[B],(n==="+"||n==="-")&&(e+=P[B++]),zn(P.charCodeAt(B)))for(;zn(P.charCodeAt(B));)e+=P[B++];else oe({},_e,ze);return Ju(P.charCodeAt(B))&&oe({},_e,ze),{type:si,value:parseFloat(e),start:t,end:B}}function p_(){var e="",t,n,r,i,a=!1;for(t=P[B],of(t==="'"||t==='"',"String literal must starts with a quote"),n=B,++B;B<Ke;)if(r=P[B++],r===t){t="";break}else if(r==="\\")if(r=P[B++],!r||!yu(r.charCodeAt(0)))switch(r){case"u":case"x":P[B]==="{"?(++B,e+=f_()):e+=Ld(r);break;case"n":e+=`
`;break;case"r":e+="\r";break;case"t":e+="	";break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:Qa(r)?(i="01234567".indexOf(r),i!==0&&(a=!0),B<Ke&&Qa(P[B])&&(a=!0,i=i*8+"01234567".indexOf(P[B++]),"0123".indexOf(r)>=0&&B<Ke&&Qa(P[B])&&(i=i*8+"01234567".indexOf(P[B++]))),e+=String.fromCharCode(i)):e+=r;break}else r==="\r"&&P[B]===`
`&&++B;else{if(yu(r.charCodeAt(0)))break;e+=r}return t!==""&&oe({},_e,ze),{type:Ku,value:e,octal:a,start:n,end:B}}function m_(e,t){let n=e;t.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}/g,(r,i)=>{if(parseInt(i,16)<=1114111)return"x";oe({},Td)}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x"));try{new RegExp(n)}catch{oe({},Td)}try{return new RegExp(e,t)}catch{return null}}function y_(){var e,t,n,r,i;for(e=P[B],of(e==="/","Regular expression literal must start with a slash"),t=P[B++],n=!1,r=!1;B<Ke;)if(e=P[B++],t+=e,e==="\\")e=P[B++],yu(e.charCodeAt(0))&&oe({},ac),t+=e;else if(yu(e.charCodeAt(0)))oe({},ac);else if(n)e==="]"&&(n=!1);else if(e==="/"){r=!0;break}else e==="["&&(n=!0);return r||oe({},ac),i=t.substr(1,t.length-2),{value:i,literal:t}}function v_(){var e,t,n;for(t="",n="";B<Ke&&(e=P[B],!!Ks(e.charCodeAt(0)));)++B,e==="\\"&&B<Ke?oe({},_e,ze):(n+=e,t+=e);return n.search(/[^gimuy]/g)>=0&&oe({},Td,n),{value:n,literal:t}}function x_(){var e,t,n,r;return ce=null,XE(),e=B,t=y_(),n=v_(),r=m_(t.value,n.value),{literal:t.literal+n.literal,value:r,regex:{pattern:t.value,flags:n.value},start:e,end:B}}function b_(e){return e.type===Zr||e.type===Fr||e.type===af||e.type===uf}function KE(){if(XE(),B>=Ke)return{type:Vu,start:B,end:B};const e=P.charCodeAt(B);return Ju(e)?d_():e===40||e===41||e===59?uc():e===39||e===34?p_():e===46?zn(P.charCodeAt(B+1))?xy():uc():zn(e)?xy():uc()}function bt(){const e=ce;return B=e.end,ce=KE(),B=e.end,e}function JE(){const e=B;ce=KE(),B=e}function A_(e){const t=new dn(GB);return t.elements=e,t}function by(e,t,n){const r=new dn(e==="||"||e==="&&"?VB:WB);return r.operator=e,r.left=t,r.right=n,r}function E_(e,t){const n=new dn(YB);return n.callee=e,n.arguments=t,n}function w_(e,t,n){const r=new dn(HB);return r.test=e,r.consequent=t,r.alternate=n,r}function vg(e){const t=new dn(YE);return t.name=e,t}function Ia(e){const t=new dn(XB);return t.value=e.value,t.raw=P.slice(e.start,e.end),e.regex&&(t.raw==="//"&&(t.raw="/(?:)/"),t.regex=e.regex),t}function Ay(e,t,n){const r=new dn(KB);return r.computed=e==="[",r.object=t,r.property=n,r.computed||(n.member=!0),r}function D_(e){const t=new dn(JB);return t.properties=e,t}function Ey(e,t,n){const r=new dn(QB);return r.key=t,r.value=n,r.kind=e,r}function C_(e,t){const n=new dn(ZB);return n.operator=e,n.argument=t,n.prefix=!0,n}function oe(e,t){var n,r=Array.prototype.slice.call(arguments,2),i=t.replace(/%(\d)/g,(a,u)=>(of(u<r.length,"Message reference must be in range"),r[u]));throw n=new Error(i),n.index=B,n.description=i,n}function sf(e){e.type===Vu&&oe(e,i_),e.type===si&&oe(e,e_),e.type===Ku&&oe(e,t_),e.type===Zr&&oe(e,n_),e.type===Fr&&oe(e,r_),oe(e,_e,e.value)}function Je(e){const t=bt();(t.type!==mt||t.value!==e)&&sf(t)}function pe(e){return ce.type===mt&&ce.value===e}function oc(e){return ce.type===Fr&&ce.value===e}function F_(){const e=[];for(B=ce.start,Je("[");!pe("]");)pe(",")?(bt(),e.push(null)):(e.push(ei()),pe("]")||Je(","));return bt(),A_(e)}function wy(){B=ce.start;const e=bt();return e.type===Ku||e.type===si?(e.octal&&oe(e,HE),Ia(e)):vg(e.value)}function k_(){var e,t,n,r;if(B=ce.start,e=ce,e.type===Zr)return n=wy(),Je(":"),r=ei(),Ey("init",n,r);if(e.type===Vu||e.type===mt)sf(e);else return t=wy(),Je(":"),r=ei(),Ey("init",t,r)}function M_(){var e=[],t,n,r,i={},a=String;for(B=ce.start,Je("{");!pe("}");)t=k_(),t.key.type===YE?n=t.key.name:n=a(t.key.value),r="$"+n,Object.prototype.hasOwnProperty.call(i,r)?oe({},a_):i[r]=!0,e.push(t),pe("}")||Je(",");return Je("}"),D_(e)}function S_(){Je("(");const e=xg();return Je(")"),e}const $_={if:1};function B_(){var e,t,n;if(pe("("))return S_();if(pe("["))return F_();if(pe("{"))return M_();if(e=ce.type,B=ce.start,e===Zr||$_[ce.value])n=vg(bt().value);else if(e===Ku||e===si)ce.octal&&oe(ce,HE),n=Ia(bt());else{if(e===Fr)throw new Error(mu);e===af?(t=bt(),t.value=t.value==="true",n=Ia(t)):e===uf?(t=bt(),t.value=null,n=Ia(t)):pe("/")||pe("/=")?(n=Ia(x_()),JE()):sf(bt())}return n}function __(){const e=[];if(Je("("),!pe(")"))for(;B<Ke&&(e.push(ei()),!pe(")"));)Je(",");return Je(")"),e}function R_(){B=ce.start;const e=bt();return b_(e)||sf(e),vg(e.value)}function O_(){return Je("."),R_()}function T_(){Je("[");const e=xg();return Je("]"),e}function L_(){var e,t,n;for(e=B_();;)if(pe("."))n=O_(),e=Ay(".",e,n);else if(pe("("))t=__(),e=E_(e,t);else if(pe("["))n=T_(),e=Ay("[",e,n);else break;return e}function Dy(){const e=L_();if(ce.type===mt&&(pe("++")||pe("--")))throw new Error(mu);return e}function Vo(){var e,t;if(ce.type!==mt&&ce.type!==Fr)t=Dy();else{if(pe("++")||pe("--"))throw new Error(mu);if(pe("+")||pe("-")||pe("~")||pe("!"))e=bt(),t=Vo(),t=C_(e.value,t);else{if(oc("delete")||oc("void")||oc("typeof"))throw new Error(mu);t=Dy()}}return t}function Cy(e){let t=0;if(e.type!==mt&&e.type!==Fr)return 0;switch(e.value){case"||":t=1;break;case"&&":t=2;break;case"|":t=3;break;case"^":t=4;break;case"&":t=5;break;case"==":case"!=":case"===":case"!==":t=6;break;case"<":case">":case"<=":case">=":case"instanceof":case"in":t=7;break;case"<<":case">>":case">>>":t=8;break;case"+":case"-":t=9;break;case"*":case"/":case"%":t=11;break}return t}function N_(){var e,t,n,r,i,a,u,o,s,l;if(e=ce,s=Vo(),r=ce,i=Cy(r),i===0)return s;for(r.prec=i,bt(),t=[e,ce],u=Vo(),a=[s,r,u];(i=Cy(ce))>0;){for(;a.length>2&&i<=a[a.length-2].prec;)u=a.pop(),o=a.pop().value,s=a.pop(),t.pop(),n=by(o,s,u),a.push(n);r=bt(),r.prec=i,a.push(r),t.push(ce),n=Vo(),a.push(n)}for(l=a.length-1,n=a[l],t.pop();l>1;)t.pop(),n=by(a[l-1].value,a[l-2],n),l-=2;return n}function ei(){var e,t,n;return e=N_(),pe("?")&&(bt(),t=ei(),Je(":"),n=ei(),e=w_(e,t,n)),e}function xg(){const e=ei();if(pe(","))throw new Error(mu);return e}function z_(e){P=e,B=0,Ke=P.length,ce=null,JE();const t=xg();if(ce.type!==Vu)throw new Error("Unexpect token after expression.");return t}var bg={NaN:"NaN",E:"Math.E",LN2:"Math.LN2",LN10:"Math.LN10",LOG2E:"Math.LOG2E",LOG10E:"Math.LOG10E",PI:"Math.PI",SQRT1_2:"Math.SQRT1_2",SQRT2:"Math.SQRT2",MIN_VALUE:"Number.MIN_VALUE",MAX_VALUE:"Number.MAX_VALUE"};function Ag(e){function t(u,o,s,l){let f=e(o[0]);return s&&(f=s+"("+f+")",s.lastIndexOf("new ",0)===0&&(f="("+f+")")),f+"."+u+(l<0?"":l===0?"()":"("+o.slice(1).map(e).join(",")+")")}function n(u,o,s){return l=>t(u,l,o,s)}const r="new Date",i="String",a="RegExp";return{isNaN:"Number.isNaN",isFinite:"Number.isFinite",abs:"Math.abs",acos:"Math.acos",asin:"Math.asin",atan:"Math.atan",atan2:"Math.atan2",ceil:"Math.ceil",cos:"Math.cos",exp:"Math.exp",floor:"Math.floor",hypot:"Math.hypot",log:"Math.log",max:"Math.max",min:"Math.min",pow:"Math.pow",random:"Math.random",round:"Math.round",sin:"Math.sin",sqrt:"Math.sqrt",tan:"Math.tan",clamp:function(u){u.length<3&&S("Missing arguments to clamp function."),u.length>3&&S("Too many arguments to clamp function.");const o=u.map(e);return"Math.max("+o[1]+", Math.min("+o[2]+","+o[0]+"))"},now:"Date.now",utc:"Date.UTC",datetime:r,date:n("getDate",r,0),day:n("getDay",r,0),year:n("getFullYear",r,0),month:n("getMonth",r,0),hours:n("getHours",r,0),minutes:n("getMinutes",r,0),seconds:n("getSeconds",r,0),milliseconds:n("getMilliseconds",r,0),time:n("getTime",r,0),timezoneoffset:n("getTimezoneOffset",r,0),utcdate:n("getUTCDate",r,0),utcday:n("getUTCDay",r,0),utcyear:n("getUTCFullYear",r,0),utcmonth:n("getUTCMonth",r,0),utchours:n("getUTCHours",r,0),utcminutes:n("getUTCMinutes",r,0),utcseconds:n("getUTCSeconds",r,0),utcmilliseconds:n("getUTCMilliseconds",r,0),length:n("length",null,-1),parseFloat:"parseFloat",parseInt:"parseInt",upper:n("toUpperCase",i,0),lower:n("toLowerCase",i,0),substring:n("substring",i),split:n("split",i),trim:n("trim",i,0),regexp:a,test:n("test",a),if:function(u){u.length<3&&S("Missing arguments to if function."),u.length>3&&S("Too many arguments to if function.");const o=u.map(e);return"("+o[0]+"?"+o[1]+":"+o[2]+")"}}}function P_(e){const t=e&&e.length-1;return t&&(e[0]==='"'&&e[t]==='"'||e[0]==="'"&&e[t]==="'")?e.slice(1,-1):e}function QE(e){e=e||{};const t=e.allowed?Tt(e.allowed):{},n=e.forbidden?Tt(e.forbidden):{},r=e.constants||bg,i=(e.functions||Ag)(c),a=e.globalvar,u=e.fieldvar,o=J(a)?a:g=>`${a}["${g}"]`;let s={},l={},f=0;function c(g){if(fe(g))return g;const p=d[g.type];return p==null&&S("Unsupported type: "+g.type),p(g)}const d={Literal:g=>g.raw,Identifier:g=>{const p=g.name;return f>0?p:G(n,p)?S("Illegal identifier: "+p):G(r,p)?r[p]:G(t,p)?p:(s[p]=1,o(p))},MemberExpression:g=>{const p=!g.computed,m=c(g.object);p&&(f+=1);const y=c(g.property);return m===u&&(l[P_(y)]=1),p&&(f-=1),m+(p?"."+y:"["+y+"]")},CallExpression:g=>{g.callee.type!=="Identifier"&&S("Illegal callee type: "+g.callee.type);const p=g.callee.name,m=g.arguments,y=G(i,p)&&i[p];return y||S("Unrecognized function: "+p),J(y)?y(m):y+"("+m.map(c).join(",")+")"},ArrayExpression:g=>"["+g.elements.map(c).join(",")+"]",BinaryExpression:g=>"("+c(g.left)+" "+g.operator+" "+c(g.right)+")",UnaryExpression:g=>"("+g.operator+c(g.argument)+")",ConditionalExpression:g=>"("+c(g.test)+"?"+c(g.consequent)+":"+c(g.alternate)+")",LogicalExpression:g=>"("+c(g.left)+g.operator+c(g.right)+")",ObjectExpression:g=>"{"+g.properties.map(c).join(",")+"}",Property:g=>{f+=1;const p=c(g.key);return f-=1,p+":"+c(g.value)}};function h(g){const p={code:c(g),globals:Object.keys(s),fields:Object.keys(l)};return s={},l={},p}return h.functions=i,h.constants=r,h}const Fy=Symbol("vega_selection_getter");function ZE(e){return(!e.getter||!e.getter[Fy])&&(e.getter=Ot(e.field),e.getter[Fy]=!0),e.getter}const Eg="intersect",ky="union",I_="vlMulti",U_="vlPoint",My="or",q_="and",vn="_vgsid_",vu=Ot(vn),j_="E",G_="R",W_="R-E",Y_="R-LE",H_="R-RE",Js="index:unit";function Sy(e,t){for(var n=t.fields,r=t.values,i=n.length,a=0,u,o;a<i;++a)if(o=n[a],u=ZE(o)(e),Gn(u)&&(u=Ne(u)),Gn(r[a])&&(r[a]=Ne(r[a])),z(r[a])&&Gn(r[a][0])&&(r[a]=r[a].map(Ne)),o.type===j_){if(z(r[a])?r[a].indexOf(u)<0:u!==r[a])return!1}else if(o.type===G_){if(!Ir(u,r[a]))return!1}else if(o.type===H_){if(!Ir(u,r[a],!0,!1))return!1}else if(o.type===W_){if(!Ir(u,r[a],!1,!1))return!1}else if(o.type===Y_&&!Ir(u,r[a],!1,!0))return!1;return!0}function ew(e,t,n){for(var r=this.context.data[e],i=r?r.values.value:[],a=r?r[Js]&&r[Js].value:void 0,u=n===Eg,o=i.length,s=0,l,f,c,d,h;s<o;++s)if(l=i[s],a&&u){if(f=f||{},c=f[d=l.unit]||0,c===-1)continue;if(h=Sy(t,l),f[d]=h?-1:++c,h&&a.size===1)return!0;if(!h&&c===a.get(d).count)return!1}else if(h=Sy(t,l),u^h)return h;return o&&u}const tw=ih(vu),X_=tw.left,V_=tw.right;function nw(e,t,n){const r=this.context.data[e],i=r?r.values.value:[],a=r?r[Js]&&r[Js].value:void 0,u=n===Eg,o=vu(t),s=X_(i,o);if(s===i.length||vu(i[s])!==o)return!1;if(a&&u){if(a.size===1)return!0;if(V_(i,o)-s<a.size)return!1}return!0}function rw(e,t){return e.map(n=>Z(t.fields?{values:t.fields.map(r=>ZE(r)(n.datum))}:{[vn]:vu(n.datum)},t))}function iw(e,t,n,r){for(var i=this.context.data[e],a=i?i.values.value:[],u={},o={},s={},l,f,c,d,h,g,p,m,y,v,x=a.length,b=0,E,w;b<x;++b)if(l=a[b],d=l.unit,f=l.fields,c=l.values,f&&c){for(E=0,w=f.length;E<w;++E)h=f[E],p=u[h.field]||(u[h.field]={}),m=p[d]||(p[d]=[]),s[h.field]=y=h.type.charAt(0),v=sc[`${y}_union`],p[d]=v(m,q(c[E]));n&&(m=o[d]||(o[d]=[]),m.push(q(c).reduce((A,D,C)=>(A[f[C].field]=D,A),{})))}else h=vn,g=vu(l),p=u[h]||(u[h]={}),m=p[d]||(p[d]=[]),m.push(g),n&&(m=o[d]||(o[d]=[]),m.push({[vn]:g}));if(t=t||ky,u[vn]?u[vn]=sc[`${vn}_${t}`](...Object.values(u[vn])):Object.keys(u).forEach(A=>{u[A]=Object.keys(u[A]).map(D=>u[A][D]).reduce((D,C)=>D===void 0?C:sc[`${s[A]}_${t}`](D,C))}),a=Object.keys(o),n&&a.length){const A=r?U_:I_;u[A]=t===ky?{[My]:a.reduce((D,C)=>(D.push(...o[C]),D),[])}:{[q_]:a.map(D=>({[My]:o[D]}))}}return u}var sc={[`${vn}_union`]:rC,[`${vn}_intersect`]:tC,E_union:function(e,t){if(!e.length)return t;for(var n=0,r=t.length;n<r;++n)e.indexOf(t[n])<0&&e.push(t[n]);return e},E_intersect:function(e,t){return e.length?e.filter(n=>t.indexOf(n)>=0):t},R_union:function(e,t){var n=Ne(t[0]),r=Ne(t[1]);return n>r&&(n=t[1],r=t[0]),e.length?(e[0]>n&&(e[0]=n),e[1]<r&&(e[1]=r),e):[n,r]},R_intersect:function(e,t){var n=Ne(t[0]),r=Ne(t[1]);return n>r&&(n=t[1],r=t[0]),e.length?r<e[0]||e[1]<n?[]:(e[0]<n&&(e[0]=n),e[1]>r&&(e[1]=r),e):[n,r]}};const K_=":",J_="@";function ia(e,t,n,r){t[0].type!==Kt&&S("First argument to selection functions must be a string literal.");const i=t[0].value,a=t.length>=2&&re(t).value,u="unit",o=J_+u,s=K_+i;a===Eg&&!G(r,o)&&(r[o]=n.getData(i).indataRef(n,u)),G(r,s)||(r[s]=n.getData(i).tuplesRef())}function aw(e){const t=this.context.data[e];return t?t.values.value:[]}function Q_(e,t,n){const r=this.context.data[e]["index:"+t],i=r?r.value.get(n):void 0;return i&&i.count}function Z_(e,t){const n=this.context.dataflow,r=this.context.data[e],i=r.input;return n.pulse(i,n.changeset().remove(Ct).insert(t)),1}function eR(e,t,n){if(e){const r=this.context.dataflow,i=e.mark.source;r.pulse(i,r.changeset().encode(e,t))}return n!==void 0?n:e}const Qu=e=>function(t,n){return this.context.dataflow.locale()[e](n)(t)},tR=Qu("format"),uw=Qu("timeFormat"),nR=Qu("utcFormat"),rR=Qu("timeParse"),iR=Qu("utcParse"),ko=new Date(2e3,0,1);function lf(e,t,n){return!Number.isInteger(e)||!Number.isInteger(t)?"":(ko.setYear(2e3),ko.setMonth(e),ko.setDate(t),uw.call(this,ko,n))}function aR(e){return lf.call(this,e,1,"%B")}function uR(e){return lf.call(this,e,1,"%b")}function oR(e){return lf.call(this,0,2+e,"%A")}function sR(e){return lf.call(this,0,2+e,"%a")}const lR=":",fR="@",Nd="%",ow="$";function wg(e,t,n,r){t[0].type!==Kt&&S("First argument to data functions must be a string literal.");const i=t[0].value,a=lR+i;if(!G(a,r))try{r[a]=n.getData(i).tuplesRef()}catch{}}function cR(e,t,n,r){t[0].type!==Kt&&S("First argument to indata must be a string literal."),t[1].type!==Kt&&S("Second argument to indata must be a string literal.");const i=t[0].value,a=t[1].value,u=fR+a;G(u,r)||(r[u]=n.getData(i).indataRef(n,a))}function dt(e,t,n,r){if(t[0].type===Kt)$y(n,r,t[0].value);else for(e in n.scales)$y(n,r,e)}function $y(e,t,n){const r=Nd+n;if(!G(t,r))try{t[r]=e.scaleRef(n)}catch{}}function $n(e,t){if(J(e))return e;if(fe(e)){const n=t.scales[e];return n&&qb(n.value)?n.value:void 0}}function dR(e,t,n){t.__bandwidth=i=>i&&i.bandwidth?i.bandwidth():0,n._bandwidth=dt,n._range=dt,n._scale=dt;const r=i=>"_["+(i.type===Kt?U(Nd+i.value):U(Nd)+"+"+e(i))+"]";return{_bandwidth:i=>`this.__bandwidth(${r(i[0])})`,_range:i=>`${r(i[0])}.range()`,_scale:i=>`${r(i[0])}(${e(i[1])})`}}function Dg(e,t){return function(n,r,i){if(n){const a=$n(n,(i||this).context);return a&&a.path[e](r)}else return t(r)}}const hR=Dg("area",K2),gR=Dg("bounds",tv),pR=Dg("centroid",av);function mR(e,t){const n=$n(e,(t||this).context);return n&&n.scale()}function yR(e){const t=this.context.group;let n=!1;if(t)for(;e;){if(e===t){n=!0;break}e=e.mark.group}return n}function Cg(e,t,n){try{e[t].apply(e,["EXPRESSION"].concat([].slice.call(n)))}catch(r){e.warn(r)}return n[n.length-1]}function vR(){return Cg(this.context.dataflow,"warn",arguments)}function xR(){return Cg(this.context.dataflow,"info",arguments)}function bR(){return Cg(this.context.dataflow,"debug",arguments)}function lc(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}function zd(e){const t=_i(e),n=lc(t.r),r=lc(t.g),i=lc(t.b);return .2126*n+.7152*r+.0722*i}function AR(e,t){const n=zd(e),r=zd(t),i=Math.max(n,r),a=Math.min(n,r);return(i+.05)/(a+.05)}function ER(){const e=[].slice.call(arguments);return e.unshift({}),Z(...e)}function sw(e,t){return e===t||e!==e&&t!==t?!0:z(e)?z(t)&&e.length===t.length?wR(e,t):!1:K(e)&&K(t)?lw(e,t):!1}function wR(e,t){for(let n=0,r=e.length;n<r;++n)if(!sw(e[n],t[n]))return!1;return!0}function lw(e,t){for(const n in e)if(!sw(e[n],t[n]))return!1;return!0}function By(e){return t=>lw(e,t)}function DR(e,t,n,r,i,a){const u=this.context.dataflow,o=this.context.data[e],s=o.input,l=u.stamp();let f=o.changes,c,d;if(u._trigger===!1||!(s.value.length||t||r))return 0;if((!f||f.stamp<l)&&(o.changes=f=u.changeset(),f.stamp=l,u.runAfter(()=>{o.modified=!0,u.pulse(s,f).run()},!0,1)),n&&(c=n===!0?Ct:z(n)||ea(n)?n:By(n),f.remove(c)),t&&f.insert(t),r&&(c=By(r),s.value.some(c)?f.remove(c):f.insert(r)),i)for(d in a)f.modify(i,d,a[d]);return 1}function CR(e){const t=e.touches,n=t[0].clientX-t[1].clientX,r=t[0].clientY-t[1].clientY;return Math.hypot(n,r)}function FR(e){const t=e.touches;return Math.atan2(t[0].clientY-t[1].clientY,t[0].clientX-t[1].clientX)}const _y={};function kR(e,t){const n=_y[t]||(_y[t]=Ot(t));return z(e)?e.map(n):n(e)}function Fg(e){return z(e)||ArrayBuffer.isView(e)?e:null}function kg(e){return Fg(e)||(fe(e)?e:null)}function MR(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Fg(e).join(...n)}function SR(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return kg(e).indexOf(...n)}function $R(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return kg(e).lastIndexOf(...n)}function BR(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return kg(e).slice(...n)}function _R(e,t,n){return J(n)&&S("Function argument passed to replace."),String(e).replace(t,n)}function RR(e){return Fg(e).slice().reverse()}function OR(e,t,n){return Ul(e||0,t||0,n||0)}function TR(e,t){const n=$n(e,(t||this).context);return n&&n.bandwidth?n.bandwidth():0}function LR(e,t){const n=$n(e,(t||this).context);return n?n.copy():void 0}function NR(e,t){const n=$n(e,(t||this).context);return n?n.domain():[]}function zR(e,t,n){const r=$n(e,(n||this).context);return r?z(t)?(r.invertRange||r.invert)(t):(r.invert||r.invertExtent)(t):void 0}function PR(e,t){const n=$n(e,(t||this).context);return n&&n.range?n.range():[]}function IR(e,t,n){const r=$n(e,(n||this).context);return r?r(t):void 0}function UR(e,t,n,r,i){e=$n(e,(i||this).context);const a=r1(t,n);let u=e.domain(),o=u[0],s=re(u),l=et;return s-o?l=J0(e,o,s):e=(e.interpolator?ue("sequential")().interpolator(e.interpolator()):ue("linear")().interpolate(e.interpolate()).range(e.range())).domain([o=0,s=1]),e.ticks&&(u=e.ticks(+r||15),o!==u[0]&&u.unshift(o),s!==re(u)&&u.push(s)),u.forEach(f=>a.stop(l(f),e(f))),a}function qR(e,t,n){const r=$n(e,(n||this).context);return function(i){return r?r.path.context(i)(t):""}}function jR(e){let t=null;return function(n){return n?Ui(n,t=t||Qr(e)):e}}const fw=e=>e.data;function cw(e,t){const n=aw.call(t,e);return n.root&&n.root.lookup||{}}function GR(e,t,n){const r=cw(e,this),i=r[t],a=r[n];return i&&a?i.path(a).map(fw):void 0}function WR(e,t){const n=cw(e,this)[t];return n?n.ancestors().map(fw):void 0}const dw=()=>typeof window<"u"&&window||null;function YR(){const e=dw();return e?e.screen:{}}function HR(){const e=dw();return e?[e.innerWidth,e.innerHeight]:[void 0,void 0]}function XR(){const e=this.context.dataflow,t=e.container&&e.container();return t?[t.clientWidth,t.clientHeight]:[void 0,void 0]}function hw(e,t,n){if(!e)return[];const[r,i]=e,a=new we().set(r[0],r[1],i[0],i[1]),u=n||this.context.dataflow.scenegraph().root;return R1(u,a,VR(t))}function VR(e){let t=null;if(e){const n=q(e.marktype),r=q(e.markname);t=i=>(!n.length||n.some(a=>i.marktype===a))&&(!r.length||r.some(a=>i.name===a))}return t}function KR(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:5;e=q(e);const i=e[e.length-1];return i===void 0||Math.hypot(i[0]-t,i[1]-n)>r?[...e,[t,n]]:e}function JR(e){return q(e).reduce((t,n,r)=>{let[i,a]=n;return t+=r==0?`M ${i},${a} `:r===e.length-1?" Z":`L ${i},${a} `},"")}function QR(e,t,n){const{x:r,y:i,mark:a}=n,u=new we().set(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER,Number.MIN_SAFE_INTEGER);for(const[s,l]of t)s<u.x1&&(u.x1=s),s>u.x2&&(u.x2=s),l<u.y1&&(u.y1=l),l>u.y2&&(u.y2=l);return u.translate(r,i),hw([[u.x1,u.y1],[u.x2,u.y2]],e,a).filter(s=>ZR(s.x,s.y,t))}function ZR(e,t,n){let r=0;for(let i=0,a=n.length-1;i<n.length;a=i++){const[u,o]=n[a],[s,l]=n[i];l>t!=o>t&&e<(u-s)*(t-l)/(o-l)+s&&r++}return r&1}const xu={random(){return zt()},cumulativeNormal:_u,cumulativeLogNormal:_l,cumulativeUniform:Ll,densityNormal:Sl,densityLogNormal:Bl,densityUniform:Tl,quantileNormal:Ru,quantileLogNormal:Rl,quantileUniform:Nl,sampleNormal:Bu,sampleLogNormal:$l,sampleUniform:Ol,isArray:z,isBoolean:jh,isDate:Gn,isDefined(e){return e!==void 0},isNumber:Yn,isObject:K,isRegExp:Gh,isString:fe,isTuple:ea,isValid(e){return e!=null&&e===e},toBoolean:bl,toDate(e){return Al(e)},toNumber:Ne,toString:El,indexof:SR,join:MR,lastindexof:$R,replace:_R,reverse:RR,slice:BR,flush:qh,lerp:Yh,merge:ER,pad:Hh,peek:re,pluck:kR,span:Ji,inrange:Ir,truncate:Xh,rgb:_i,lab:ns,hcl:M2,hsl:rs,luminance:zd,contrast:AR,sequence:Et,format:tR,utcFormat:nR,utcParse:iR,utcOffset:i0,utcSequence:u0,timeFormat:uw,timeParse:rR,timeOffset:r0,timeSequence:a0,timeUnitSpecifier:Jh,monthFormat:aR,monthAbbrevFormat:uR,dayFormat:oR,dayAbbrevFormat:sR,quarter:Nh,utcquarter:zh,week:e0,utcweek:n0,dayofyear:Zh,utcdayofyear:t0,warn:vR,info:xR,debug:bR,extent(e){return ln(e)},inScope:yR,intersect:hw,clampRange:Ph,pinchDistance:CR,pinchAngle:FR,screen:YR,containerSize:XR,windowSize:HR,bandspace:OR,setdata:Z_,pathShape:jR,panLinear:Rh,panLog:Oh,panPow:Th,panSymlog:Lh,zoomLinear:ml,zoomLog:yl,zoomPow:su,zoomSymlog:vl,encode:eR,modify:DR,lassoAppend:KR,lassoPath:JR,intersectLasso:QR},eO=["view","item","group","xy","x","y"],tO="event.vega.",gw="this.",Mg={},pw={forbidden:["_"],allowed:["datum","event","item"],fieldvar:"datum",globalvar:e=>`_[${U(ow+e)}]`,functions:nO,constants:bg,visitors:Mg},Pd=QE(pw);function nO(e){const t=Ag(e);eO.forEach(n=>t[n]=tO+n);for(const n in xu)t[n]=gw+n;return Z(t,dR(e,xu,Mg)),t}function Fe(e,t,n){return arguments.length===1?xu[e]:(xu[e]=t,n&&(Mg[e]=n),Pd&&(Pd.functions[e]=gw+e),this)}Fe("bandwidth",TR,dt);Fe("copy",LR,dt);Fe("domain",NR,dt);Fe("range",PR,dt);Fe("invert",zR,dt);Fe("scale",IR,dt);Fe("gradient",UR,dt);Fe("geoArea",hR,dt);Fe("geoBounds",gR,dt);Fe("geoCentroid",pR,dt);Fe("geoShape",qR,dt);Fe("geoScale",mR,dt);Fe("indata",Q_,cR);Fe("data",aw,wg);Fe("treePath",GR,wg);Fe("treeAncestors",WR,wg);Fe("vlSelectionTest",ew,ia);Fe("vlSelectionIdTest",nw,ia);Fe("vlSelectionResolve",iw,ia);Fe("vlSelectionTuples",rw);function Cn(e,t){const n={};let r;try{e=fe(e)?e:U(e)+"",r=z_(e)}catch{S("Expression parse error: "+e)}r.visit(a=>{if(a.type!==WE)return;const u=a.callee.name,o=pw.visitors[u];o&&o(u,a.arguments,t,n)});const i=Pd(r);return i.globals.forEach(a=>{const u=ow+a;!G(n,u)&&t.getSignal(a)&&(n[u]=t.signalRef(a))}),{$expr:Z({code:i.code},t.options.ast?{ast:r}:null),$fields:i.fields,$params:n}}function rO(e){const t=this,n=e.operators||[];return e.background&&(t.background=e.background),e.eventConfig&&(t.eventConfig=e.eventConfig),e.locale&&(t.locale=e.locale),n.forEach(r=>t.parseOperator(r)),n.forEach(r=>t.parseOperatorParameters(r)),(e.streams||[]).forEach(r=>t.parseStream(r)),(e.updates||[]).forEach(r=>t.parseUpdate(r)),t.resolve()}const iO=Tt(["rule"]),Ry=Tt(["group","image","rect"]);function aO(e,t){let n="";return iO[t]||(e.x2&&(e.x?(Ry[t]&&(n+="if(o.x>o.x2)$=o.x,o.x=o.x2,o.x2=$;"),n+="o.width=o.x2-o.x;"):n+="o.x=o.x2-(o.width||0);"),e.xc&&(n+="o.x=o.xc-(o.width||0)/2;"),e.y2&&(e.y?(Ry[t]&&(n+="if(o.y>o.y2)$=o.y,o.y=o.y2,o.y2=$;"),n+="o.height=o.y2-o.y;"):n+="o.y=o.y2-(o.height||0);"),e.yc&&(n+="o.y=o.yc-(o.height||0)/2;")),n}function Sg(e){return(e+"").toLowerCase()}function uO(e){return Sg(e)==="operator"}function oO(e){return Sg(e)==="collect"}function ya(e,t,n){n.endsWith(";")||(n="return("+n+");");const r=Function(...t.concat(n));return e&&e.functions?r.bind(e.functions):r}function sO(e,t,n,r){return`((u = ${e}) < (v = ${t}) || u == null) && v != null ? ${n}
  : (u > v || v == null) && u != null ? ${r}
  : ((v = v instanceof Date ? +v : v), (u = u instanceof Date ? +u : u)) !== u && v === v ? ${n}
  : v !== v && u === u ? ${r} : `}var lO={operator:(e,t)=>ya(e,["_"],t.code),parameter:(e,t)=>ya(e,["datum","_"],t.code),event:(e,t)=>ya(e,["event"],t.code),handler:(e,t)=>{const n=`var datum=event.item&&event.item.datum;return ${t.code};`;return ya(e,["_","event"],n)},encode:(e,t)=>{const{marktype:n,channels:r}=t;let i="var o=item,datum=o.datum,m=0,$;";for(const a in r){const u="o["+U(a)+"]";i+=`$=${r[a].code};if(${u}!==$)${u}=$,m=1;`}return i+=aO(r,n),i+="return m;",ya(e,["item","_"],i)},codegen:{get(e){const t=`[${e.map(U).join("][")}]`,n=Function("_",`return _${t};`);return n.path=t,n},comparator(e,t){let n;const r=(a,u)=>{const o=t[u];let s,l;return a.path?(s=`a${a.path}`,l=`b${a.path}`):((n=n||{})["f"+u]=a,s=`this.f${u}(a)`,l=`this.f${u}(b)`),sO(s,l,-o,o)},i=Function("a","b","var u, v; return "+e.map(r).join("")+"0;");return n?i.bind(n):i}}};function fO(e){const t=this;uO(e.type)||!e.type?t.operator(e,e.update?t.operatorExpression(e.update):null):t.transform(e,e.type)}function cO(e){const t=this;if(e.params){const n=t.get(e.id);n||S("Invalid operator id: "+e.id),t.dataflow.connect(n,n.parameters(t.parseParameters(e.params),e.react,e.initonly))}}function dO(e,t){t=t||{};const n=this;for(const r in e){const i=e[r];t[r]=z(i)?i.map(a=>Oy(a,n,t)):Oy(i,n,t)}return t}function Oy(e,t,n){if(!e||!K(e))return e;for(let r=0,i=Ty.length,a;r<i;++r)if(a=Ty[r],G(e,a.key))return a.parse(e,t,n);return e}var Ty=[{key:"$ref",parse:hO},{key:"$key",parse:pO},{key:"$expr",parse:gO},{key:"$field",parse:mO},{key:"$encode",parse:vO},{key:"$compare",parse:yO},{key:"$context",parse:xO},{key:"$subflow",parse:bO},{key:"$tupleid",parse:AO}];function hO(e,t){return t.get(e.$ref)||S("Operator not defined: "+e.$ref)}function gO(e,t,n){e.$params&&t.parseParameters(e.$params,n);const r="e:"+e.$expr.code;return t.fn[r]||(t.fn[r]=Mt(t.parameterExpression(e.$expr),e.$fields))}function pO(e,t){const n="k:"+e.$key+"_"+!!e.$flat;return t.fn[n]||(t.fn[n]=Wh(e.$key,e.$flat,t.expr.codegen))}function mO(e,t){if(!e.$field)return null;const n="f:"+e.$field+"_"+e.$name;return t.fn[n]||(t.fn[n]=Ot(e.$field,e.$name,t.expr.codegen))}function yO(e,t){const n="c:"+e.$compare+"_"+e.$order,r=q(e.$compare).map(i=>i&&i.$tupleid?Y:i);return t.fn[n]||(t.fn[n]=Ih(r,e.$order,t.expr.codegen))}function vO(e,t){const n=e.$encode,r={};for(const i in n){const a=n[i];r[i]=Mt(t.encodeExpression(a.$expr),a.$fields),r[i].output=a.$output}return r}function xO(e,t){return t}function bO(e,t){const n=e.$subflow;return function(r,i,a){const u=t.fork().parse(n),o=u.get(n.operators[0].id),s=u.signals.parent;return s&&s.set(a),o.detachSubflow=()=>t.detach(u),o}}function AO(){return Y}function EO(e){var t=this,n=e.filter!=null?t.eventExpression(e.filter):void 0,r=e.stream!=null?t.get(e.stream):void 0,i;e.source?r=t.events(e.source,e.type,n):e.merge&&(i=e.merge.map(a=>t.get(a)),r=i[0].merge.apply(i[0],i.slice(1))),e.between&&(i=e.between.map(a=>t.get(a)),r=r.between(i[0],i[1])),e.filter&&(r=r.filter(n)),e.throttle!=null&&(r=r.throttle(+e.throttle)),e.debounce!=null&&(r=r.debounce(+e.debounce)),r==null&&S("Invalid stream definition: "+JSON.stringify(e)),e.consume&&r.consume(!0),t.stream(e,r)}function wO(e){var t=this,n=K(n=e.source)?n.$ref:n,r=t.get(n),i=null,a=e.update,u=void 0;r||S("Source not defined: "+e.source),i=e.target&&e.target.$expr?t.eventExpression(e.target.$expr):t.get(e.target),a&&a.$expr&&(a.$params&&(u=t.parseParameters(a.$params)),a=t.handlerExpression(a.$expr)),t.update(e,r,i,a,u)}const DO={skip:!0};function CO(e){var t=this,n={};if(e.signals){var r=n.signals={};Object.keys(t.signals).forEach(a=>{const u=t.signals[a];e.signals(a,u)&&(r[a]=u.value)})}if(e.data){var i=n.data={};Object.keys(t.data).forEach(a=>{const u=t.data[a];e.data(a,u)&&(i[a]=u.input.value)})}return t.subcontext&&e.recurse!==!1&&(n.subcontext=t.subcontext.map(a=>a.getState(e))),n}function FO(e){var t=this,n=t.dataflow,r=e.data,i=e.signals;Object.keys(i||{}).forEach(a=>{n.update(t.signals[a],i[a],DO)}),Object.keys(r||{}).forEach(a=>{n.pulse(t.data[a].input,n.changeset().remove(Ct).insert(r[a]))}),(e.subcontext||[]).forEach((a,u)=>{const o=t.subcontext[u];o&&o.setState(a)})}function mw(e,t,n,r){return new yw(e,t,n,r)}function yw(e,t,n,r){this.dataflow=e,this.transforms=t,this.events=e.events.bind(e),this.expr=r||lO,this.signals={},this.scales={},this.nodes={},this.data={},this.fn={},n&&(this.functions=Object.create(n),this.functions.context=this)}function Ly(e){this.dataflow=e.dataflow,this.transforms=e.transforms,this.events=e.events,this.expr=e.expr,this.signals=Object.create(e.signals),this.scales=Object.create(e.scales),this.nodes=Object.create(e.nodes),this.data=Object.create(e.data),this.fn=Object.create(e.fn),e.functions&&(this.functions=Object.create(e.functions),this.functions.context=this)}yw.prototype=Ly.prototype={fork(){const e=new Ly(this);return(this.subcontext||(this.subcontext=[])).push(e),e},detach(e){this.subcontext=this.subcontext.filter(n=>n!==e);const t=Object.keys(e.nodes);for(const n of t)e.nodes[n]._targets=null;for(const n of t)e.nodes[n].detach();e.nodes=null},get(e){return this.nodes[e]},set(e,t){return this.nodes[e]=t},add(e,t){const n=this,r=n.dataflow,i=e.value;if(n.set(e.id,t),oO(e.type)&&i&&(i.$ingest?r.ingest(t,i.$ingest,i.$format):i.$request?r.preload(t,i.$request,i.$format):r.pulse(t,r.changeset().insert(i))),e.root&&(n.root=t),e.parent){let a=n.get(e.parent.$ref);a?(r.connect(a,[t]),t.targets().add(a)):(n.unresolved=n.unresolved||[]).push(()=>{a=n.get(e.parent.$ref),r.connect(a,[t]),t.targets().add(a)})}if(e.signal&&(n.signals[e.signal]=t),e.scale&&(n.scales[e.scale]=t),e.data)for(const a in e.data){const u=n.data[a]||(n.data[a]={});e.data[a].forEach(o=>u[o]=t)}},resolve(){return(this.unresolved||[]).forEach(e=>e()),delete this.unresolved,this},operator(e,t){this.add(e,this.dataflow.add(e.value,t))},transform(e,t){this.add(e,this.dataflow.add(this.transforms[Sg(t)]))},stream(e,t){this.set(e.id,t)},update(e,t,n,r,i){this.dataflow.on(t,n,r,i,e.options)},operatorExpression(e){return this.expr.operator(this,e)},parameterExpression(e){return this.expr.parameter(this,e)},eventExpression(e){return this.expr.event(this,e)},handlerExpression(e){return this.expr.handler(this,e)},encodeExpression(e){return this.expr.encode(this,e)},parse:rO,parseOperator:fO,parseOperatorParameters:cO,parseParameters:dO,parseStream:EO,parseUpdate:wO,getState:CO,setState:FO};function kO(e){const t=e.container();t&&(t.setAttribute("role","graphics-document"),t.setAttribute("aria-roleDescription","visualization"),vw(t,e.description()))}function vw(e,t){e&&(t==null?e.removeAttribute("aria-label"):e.setAttribute("aria-label",t))}function MO(e){e.add(null,t=>(e._background=t.bg,e._resize=1,t.bg),{bg:e._signals.background})}const fc="default";function SO(e){const t=e._signals.cursor||(e._signals.cursor=e.add({user:fc,item:null}));e.on(e.events("view","mousemove"),t,(n,r)=>{const i=t.value,a=i?fe(i)?i:i.user:fc,u=r.item&&r.item.cursor||null;return i&&a===i.user&&u==i.item?i:{user:a,item:u}}),e.add(null,function(n){let r=n.cursor,i=this.value;return fe(r)||(i=r.item,r=r.user),Id(e,r&&r!==fc?r:i||r),i},{cursor:t})}function Id(e,t){const n=e.globalCursor()?typeof document<"u"&&document.body:e.container();if(n)return t==null?n.style.removeProperty("cursor"):n.style.cursor=t}function Qs(e,t){var n=e._runtime.data;return G(n,t)||S("Unrecognized data set: "+t),n[t]}function $O(e,t){return arguments.length<2?Qs(this,e).values.value:ff.call(this,e,ui().remove(Ct).insert(t))}function ff(e,t){Yx(t)||S("Second argument to changes must be a changeset.");const n=Qs(this,e);return n.modified=!0,this.pulse(n.input,t)}function BO(e,t){return ff.call(this,e,ui().insert(t))}function _O(e,t){return ff.call(this,e,ui().remove(t))}function xw(e){var t=e.padding();return Math.max(0,e._viewWidth+t.left+t.right)}function bw(e){var t=e.padding();return Math.max(0,e._viewHeight+t.top+t.bottom)}function cf(e){var t=e.padding(),n=e._origin;return[t.left+n[0],t.top+n[1]]}function RO(e){var t=cf(e),n=xw(e),r=bw(e);e._renderer.background(e.background()),e._renderer.resize(n,r,t),e._handler.origin(t),e._resizeListeners.forEach(i=>{try{i(n,r)}catch(a){e.error(a)}})}function OO(e,t,n){var r=e._renderer,i=r&&r.canvas(),a,u,o;return i&&(o=cf(e),u=t.changedTouches?t.changedTouches[0]:t,a=tf(u,i),a[0]-=o[0],a[1]-=o[1]),t.dataflow=e,t.item=n,t.vega=TO(e,n,a),t}function TO(e,t,n){const r=t?t.mark.marktype==="group"?t:t.mark.group:null;function i(u){var o=r,s;if(u){for(s=t;s;s=s.mark.group)if(s.mark.name===u){o=s;break}}return o&&o.mark&&o.mark.interactive?o:{}}function a(u){if(!u)return n;fe(u)&&(u=i(u));const o=n.slice();for(;u;)o[0]-=u.x||0,o[1]-=u.y||0,u=u.mark&&u.mark.group;return o}return{view:tt(e),item:tt(t||{}),group:i,xy:a,x:u=>a(u)[0],y:u=>a(u)[1]}}const Ny="view",LO="timer",NO="window",zO={trap:!1};function PO(e){const t=Z({defaults:{}},e),n=(r,i)=>{i.forEach(a=>{z(r[a])&&(r[a]=Tt(r[a]))})};return n(t.defaults,["prevent","allow"]),n(t,["view","window","selector"]),t}function Aw(e,t,n,r){e._eventListeners.push({type:n,sources:q(t),handler:r})}function IO(e,t){var n=e._eventConfig.defaults,r=n.prevent,i=n.allow;return r===!1||i===!0?!1:r===!0||i===!1?!0:r?r[t]:i?!i[t]:e.preventDefault()}function Mo(e,t,n){const r=e._eventConfig&&e._eventConfig[t];return r===!1||K(r)&&!r[n]?(e.warn(`Blocked ${t} ${n} event listener.`),!1):!0}function UO(e,t,n){var r=this,i=new Ml(n),a=function(l,f){r.runAsync(null,()=>{e===Ny&&IO(r,t)&&l.preventDefault(),i.receive(OO(r,l,f))})},u;if(e===LO)Mo(r,"timer",t)&&r.timer(a,t);else if(e===Ny)Mo(r,"view",t)&&r.addEventListener(t,a,zO);else if(e===NO?Mo(r,"window",t)&&typeof window<"u"&&(u=[window]):typeof document<"u"&&Mo(r,"selector",t)&&(u=Array.from(document.querySelectorAll(e))),!u)r.warn("Can not resolve event source: "+e);else{for(var o=0,s=u.length;o<s;++o)u[o].addEventListener(t,a);Aw(r,u,t,a)}return i}function zy(e){return e.item}function Py(e){return e.item.mark.source}function Iy(e){return function(t,n){return n.vega.view().changeset().encode(n.item,e)}}function qO(e,t){return e=[e||"hover"],t=[t||"update",e[0]],this.on(this.events("view","mouseover",zy),Py,Iy(e)),this.on(this.events("view","mouseout",zy),Py,Iy(t)),this}function jO(){var e=this._tooltip,t=this._timers,n=this._eventListeners,r,i,a;for(r=t.length;--r>=0;)t[r].stop();for(r=n.length;--r>=0;)for(a=n[r],i=a.sources.length;--i>=0;)a.sources[i].removeEventListener(a.type,a.handler);return e&&e.call(this,this._handler,null,null,null),this}function Rt(e,t,n){const r=document.createElement(e);for(const i in t)r.setAttribute(i,t[i]);return n!=null&&(r.textContent=n),r}const GO="vega-bind",WO="vega-bind-name",YO="vega-bind-radio";function HO(e,t,n){if(!t)return;const r=n.param;let i=n.state;return i||(i=n.state={elements:null,active:!1,set:null,update:u=>{u!=e.signal(r.signal)&&e.runAsync(null,()=>{i.source=!0,e.signal(r.signal,u)})}},r.debounce&&(i.update=Uh(r.debounce,i.update))),(r.input==null&&r.element?XO:KO)(i,t,r,e),i.active||(e.on(e._signals[r.signal],null,()=>{i.source?i.source=!1:i.set(e.signal(r.signal))}),i.active=!0),i}function XO(e,t,n,r){const i=n.event||"input",a=()=>e.update(t.value);r.signal(n.signal,t.value),t.addEventListener(i,a),Aw(r,t,i,a),e.set=u=>{t.value=u,t.dispatchEvent(VO(i))}}function VO(e){return typeof Event<"u"?new Event(e):{type:e}}function KO(e,t,n,r){const i=r.signal(n.signal),a=Rt("div",{class:GO}),u=n.input==="radio"?a:a.appendChild(Rt("label"));u.appendChild(Rt("span",{class:WO},n.name||n.signal)),t.appendChild(a);let o=JO;switch(n.input){case"checkbox":o=QO;break;case"select":o=ZO;break;case"radio":o=eT;break;case"range":o=tT;break}o(e,u,n,i)}function JO(e,t,n,r){const i=Rt("input");for(const a in n)a!=="signal"&&a!=="element"&&i.setAttribute(a==="input"?"type":a,n[a]);i.setAttribute("name",n.signal),i.value=r,t.appendChild(i),i.addEventListener("input",()=>e.update(i.value)),e.elements=[i],e.set=a=>i.value=a}function QO(e,t,n,r){const i={type:"checkbox",name:n.signal};r&&(i.checked=!0);const a=Rt("input",i);t.appendChild(a),a.addEventListener("change",()=>e.update(a.checked)),e.elements=[a],e.set=u=>a.checked=!!u||null}function ZO(e,t,n,r){const i=Rt("select",{name:n.signal}),a=n.labels||[];n.options.forEach((u,o)=>{const s={value:u};Zs(u,r)&&(s.selected=!0),i.appendChild(Rt("option",s,(a[o]||u)+""))}),t.appendChild(i),i.addEventListener("change",()=>{e.update(n.options[i.selectedIndex])}),e.elements=[i],e.set=u=>{for(let o=0,s=n.options.length;o<s;++o)if(Zs(n.options[o],u)){i.selectedIndex=o;return}}}function eT(e,t,n,r){const i=Rt("span",{class:YO}),a=n.labels||[];t.appendChild(i),e.elements=n.options.map((u,o)=>{const s={type:"radio",name:n.signal,value:u};Zs(u,r)&&(s.checked=!0);const l=Rt("input",s);l.addEventListener("change",()=>e.update(u));const f=Rt("label",{},(a[o]||u)+"");return f.prepend(l),i.appendChild(f),l}),e.set=u=>{const o=e.elements,s=o.length;for(let l=0;l<s;++l)Zs(o[l].value,u)&&(o[l].checked=!0)}}function tT(e,t,n,r){r=r!==void 0?r:(+n.max+ +n.min)/2;const i=n.max!=null?n.max:Math.max(100,+r)||100,a=n.min||Math.min(0,i,+r)||0,u=n.step||nu(a,i,100),o=Rt("input",{type:"range",name:n.signal,min:a,max:i,step:u});o.value=r;const s=Rt("span",{},+r);t.appendChild(o),t.appendChild(s);const l=()=>{s.textContent=o.value,e.update(+o.value)};o.addEventListener("input",l),o.addEventListener("change",l),e.elements=[o],e.set=f=>{o.value=f,s.textContent=f}}function Zs(e,t){return e===t||e+""==t+""}function Ew(e,t,n,r,i,a){return t=t||new r(e.loader()),t.initialize(n,xw(e),bw(e),cf(e),i,a).background(e.background())}function $g(e,t){return t?function(){try{t.apply(this,arguments)}catch(n){e.error(n)}}:null}function nT(e,t,n,r){const i=new r(e.loader(),$g(e,e.tooltip())).scene(e.scenegraph().root).initialize(n,cf(e),e);return t&&t.handlers().forEach(a=>{i.on(a.type,a.handler)}),i}function rT(e,t){const n=this,r=n._renderType,i=n._eventConfig.bind,a=nf(r);e=n._el=e?cc(n,e,!0):null,kO(n),a||n.error("Unrecognized renderer type: "+r);const u=a.handler||Xu,o=e?a.renderer:a.headless;return n._renderer=o?Ew(n,n._renderer,e,o):null,n._handler=nT(n,n._handler,e,u),n._redraw=!0,e&&i!=="none"&&(t=t?n._elBind=cc(n,t,!0):e.appendChild(Rt("form",{class:"vega-bindings"})),n._bind.forEach(s=>{s.param.element&&i!=="container"&&(s.element=cc(n,s.param.element,!!s.param.input))}),n._bind.forEach(s=>{HO(n,s.element||t,s)})),n}function cc(e,t,n){if(typeof t=="string")if(typeof document<"u"){if(t=document.querySelector(t),!t)return e.error("Signal bind element not found: "+t),null}else return e.error("DOM document instance not found."),null;if(t&&n)try{t.textContent=""}catch(r){t=null,e.error(r)}return t}const va=e=>+e||0,iT=e=>({top:e,bottom:e,left:e,right:e});function Uy(e){return K(e)?{top:va(e.top),bottom:va(e.bottom),left:va(e.left),right:va(e.right)}:iT(va(e))}async function Bg(e,t,n,r){const i=nf(t),a=i&&i.headless;return a||S("Unrecognized renderer type: "+t),await e.runAsync(),Ew(e,null,null,a,n,r).renderAsync(e._scenegraph.root)}async function aT(e,t){e!==fr.Canvas&&e!==fr.SVG&&e!==fr.PNG&&S("Unrecognized image type: "+e);const n=await Bg(this,e,t);return e===fr.SVG?uT(n.svg(),"image/svg+xml"):n.canvas().toDataURL("image/png")}function uT(e,t){const n=new Blob([e],{type:t});return window.URL.createObjectURL(n)}async function oT(e,t){return(await Bg(this,fr.Canvas,e,t)).canvas()}async function sT(e){return(await Bg(this,fr.SVG,e)).svg()}function lT(e,t,n){return mw(e,Ni,xu,n).parse(t)}function fT(e){var t=this._runtime.scales;return G(t,e)||S("Unrecognized scale or projection: "+e),t[e].value}var ww="width",Dw="height",_g="padding",qy={skip:!0};function Cw(e,t){var n=e.autosize(),r=e.padding();return t-(n&&n.contains===_g?r.left+r.right:0)}function Fw(e,t){var n=e.autosize(),r=e.padding();return t-(n&&n.contains===_g?r.top+r.bottom:0)}function cT(e){var t=e._signals,n=t[ww],r=t[Dw],i=t[_g];function a(){e._autosize=e._resize=1}e._resizeWidth=e.add(null,o=>{e._width=o.size,e._viewWidth=Cw(e,o.size),a()},{size:n}),e._resizeHeight=e.add(null,o=>{e._height=o.size,e._viewHeight=Fw(e,o.size),a()},{size:r});const u=e.add(null,a,{pad:i});e._resizeWidth.rank=n.rank+1,e._resizeHeight.rank=r.rank+1,u.rank=i.rank+1}function dT(e,t,n,r,i,a){this.runAfter(u=>{let o=0;u._autosize=0,u.width()!==n&&(o=1,u.signal(ww,n,qy),u._resizeWidth.skip(!0)),u.height()!==r&&(o=1,u.signal(Dw,r,qy),u._resizeHeight.skip(!0)),u._viewWidth!==e&&(u._resize=1,u._viewWidth=e),u._viewHeight!==t&&(u._resize=1,u._viewHeight=t),(u._origin[0]!==i[0]||u._origin[1]!==i[1])&&(u._resize=1,u._origin=i),o&&u.run("enter"),a&&u.runAfter(s=>s.resize())},!1,1)}function hT(e){return this._runtime.getState(e||{data:gT,signals:pT,recurse:!0})}function gT(e,t){return t.modified&&z(t.input.value)&&e.indexOf("_:vega:_")}function pT(e,t){return!(e==="parent"||t instanceof Ni.proxy)}function mT(e){return this.runAsync(null,t=>{t._trigger=!1,t._runtime.setState(e)},t=>{t._trigger=!0}),this}function yT(e,t){function n(r){e({timestamp:Date.now(),elapsed:r})}this._timers.push(vC(n,t))}function vT(e,t,n,r){const i=e.element();i&&i.setAttribute("title",xT(r))}function xT(e){return e==null?"":z(e)?kw(e):K(e)&&!Gn(e)?bT(e):e+""}function bT(e){return Object.keys(e).map(t=>{const n=e[t];return t+": "+(z(n)?kw(n):Mw(n))}).join(`
`)}function kw(e){return"["+e.map(Mw).join(", ")+"]"}function Mw(e){return z(e)?"[…]":K(e)&&!Gn(e)?"{…}":e}function Sw(e,t){const n=this;if(t=t||{},$i.call(n),t.loader&&n.loader(t.loader),t.logger&&n.logger(t.logger),t.logLevel!=null&&n.logLevel(t.logLevel),t.locale||e.locale){const a=Z({},e.locale,t.locale);n.locale(Lx(a.number,a.time))}n._el=null,n._elBind=null,n._renderType=t.renderer||fr.Canvas,n._scenegraph=new w1;const r=n._scenegraph.root;n._renderer=null,n._tooltip=t.tooltip||vT,n._redraw=!0,n._handler=new Xu().scene(r),n._globalCursor=!1,n._preventDefault=!1,n._timers=[],n._eventListeners=[],n._resizeListeners=[],n._eventConfig=PO(e.eventConfig),n.globalCursor(n._eventConfig.globalCursor);const i=lT(n,e,t.expr);n._runtime=i,n._signals=i.signals,n._bind=(e.bindings||[]).map(a=>({state:null,param:Z({},a)})),i.root&&i.root.set(r),r.source=i.data.root.input,n.pulse(i.data.root.input,n.changeset().insert(r.items)),n._width=n.width(),n._height=n.height(),n._viewWidth=Cw(n,n._width),n._viewHeight=Fw(n,n._height),n._origin=[0,0],n._resize=0,n._autosize=1,cT(n),MO(n),SO(n),n.description(e.description),t.hover&&n.hover(),t.container&&n.initialize(t.container,t.bind)}function So(e,t){return G(e._signals,t)?e._signals[t]:S("Unrecognized signal name: "+U(t))}function $w(e,t){const n=(e._targets||[]).filter(r=>r._update&&r._update.handler===t);return n.length?n[0]:null}function jy(e,t,n,r){let i=$w(n,r);return i||(i=$g(e,()=>r(t,n.value)),i.handler=r,e.on(n,null,i)),e}function Gy(e,t,n){const r=$w(t,n);return r&&t._targets.remove(r),e}T(Sw,$i,{async evaluate(e,t,n){if(await $i.prototype.evaluate.call(this,e,t),this._redraw||this._resize)try{this._renderer&&(this._resize&&(this._resize=0,RO(this)),await this._renderer.renderAsync(this._scenegraph.root)),this._redraw=!1}catch(r){this.error(r)}return n&&No(this,n),this},dirty(e){this._redraw=!0,this._renderer&&this._renderer.dirty(e)},description(e){if(arguments.length){const t=e!=null?e+"":null;return t!==this._desc&&vw(this._el,this._desc=t),this}return this._desc},container(){return this._el},scenegraph(){return this._scenegraph},origin(){return this._origin.slice()},signal(e,t,n){const r=So(this,e);return arguments.length===1?r.value:this.update(r,t,n)},width(e){return arguments.length?this.signal("width",e):this.signal("width")},height(e){return arguments.length?this.signal("height",e):this.signal("height")},padding(e){return arguments.length?this.signal("padding",Uy(e)):Uy(this.signal("padding"))},autosize(e){return arguments.length?this.signal("autosize",e):this.signal("autosize")},background(e){return arguments.length?this.signal("background",e):this.signal("background")},renderer(e){return arguments.length?(nf(e)||S("Unrecognized renderer type: "+e),e!==this._renderType&&(this._renderType=e,this._resetRenderer()),this):this._renderType},tooltip(e){return arguments.length?(e!==this._tooltip&&(this._tooltip=e,this._resetRenderer()),this):this._tooltip},loader(e){return arguments.length?(e!==this._loader&&($i.prototype.loader.call(this,e),this._resetRenderer()),this):this._loader},resize(){return this._autosize=1,this.touch(So(this,"autosize"))},_resetRenderer(){this._renderer&&(this._renderer=null,this.initialize(this._el,this._elBind))},_resizeView:dT,addEventListener(e,t,n){let r=t;return n&&n.trap===!1||(r=$g(this,t),r.raw=t),this._handler.on(e,r),this},removeEventListener(e,t){for(var n=this._handler.handlers(e),r=n.length,i,a;--r>=0;)if(a=n[r].type,i=n[r].handler,e===a&&(t===i||t===i.raw)){this._handler.off(a,i);break}return this},addResizeListener(e){const t=this._resizeListeners;return t.indexOf(e)<0&&t.push(e),this},removeResizeListener(e){var t=this._resizeListeners,n=t.indexOf(e);return n>=0&&t.splice(n,1),this},addSignalListener(e,t){return jy(this,e,So(this,e),t)},removeSignalListener(e,t){return Gy(this,So(this,e),t)},addDataListener(e,t){return jy(this,e,Qs(this,e).values,t)},removeDataListener(e,t){return Gy(this,Qs(this,e).values,t)},globalCursor(e){if(arguments.length){if(this._globalCursor!==!!e){const t=Id(this,null);this._globalCursor=!!e,t&&Id(this,t)}return this}else return this._globalCursor},preventDefault(e){return arguments.length?(this._preventDefault=e,this):this._preventDefault},timer:yT,events:UO,finalize:jO,hover:qO,data:$O,change:ff,insert:BO,remove:_O,scale:fT,initialize:rT,toImageURL:aT,toCanvas:oT,toSVG:sT,getState:hT,setState:mT});function Bw(e){const t=this.context.data[e];return t?t.values.value:[]}function AT(e,t,n){const r=this.context.data[e]["index:"+t],i=r?r.value.get(n):void 0;return i&&i.count}function ET(e,t){const n=this.context.dataflow,r=this.context.data[e],i=r.input;return n.pulse(i,n.changeset().remove(Ct).insert(t)),1}function wT(e,t,n){if(e){const r=this.context.dataflow,i=e.mark.source;r.pulse(i,r.changeset().encode(e,t))}return n!==void 0?n:e}const Zu=e=>function(t,n){return this.context.dataflow.locale()[e](n)(t)},DT=Zu("format"),_w=Zu("timeFormat"),CT=Zu("utcFormat"),FT=Zu("timeParse"),kT=Zu("utcParse"),$o=new Date(2e3,0,1);function df(e,t,n){return!Number.isInteger(e)||!Number.isInteger(t)?"":($o.setYear(2e3),$o.setMonth(e),$o.setDate(t),_w.call(this,$o,n))}function MT(e){return df.call(this,e,1,"%B")}function ST(e){return df.call(this,e,1,"%b")}function $T(e){return df.call(this,0,2+e,"%A")}function BT(e){return df.call(this,0,2+e,"%a")}const _T=":",RT="@",Ud="%",OT="$";function Rg(e,t,n,r){t[0].type!==Kt&&S("First argument to data functions must be a string literal.");const i=t[0].value,a=_T+i;if(!G(a,r))try{r[a]=n.getData(i).tuplesRef()}catch{}}function TT(e,t,n,r){t[0].type!==Kt&&S("First argument to indata must be a string literal."),t[1].type!==Kt&&S("Second argument to indata must be a string literal.");const i=t[0].value,a=t[1].value,u=RT+a;G(u,r)||(r[u]=n.getData(i).indataRef(n,a))}function wt(e,t,n,r){if(t[0].type===Kt)Wy(n,r,t[0].value);else for(e in n.scales)Wy(n,r,e)}function Wy(e,t,n){const r=Ud+n;if(!G(t,r))try{t[r]=e.scaleRef(n)}catch{}}function Kn(e,t){if(J(e))return e;if(fe(e)){const n=t.scales[e];return n&&qb(n.value)?n.value:void 0}}function LT(e,t,n){t.__bandwidth=i=>i&&i.bandwidth?i.bandwidth():0,n._bandwidth=wt,n._range=wt,n._scale=wt;const r=i=>"_["+(i.type===Kt?U(Ud+i.value):U(Ud)+"+"+e(i))+"]";return{_bandwidth:i=>`this.__bandwidth(${r(i[0])})`,_range:i=>`${r(i[0])}.range()`,_scale:i=>`${r(i[0])}(${e(i[1])})`}}function Og(e,t){return function(n,r,i){if(n){const a=Kn(n,(i||this).context);return a&&a.path[e](r)}else return t(r)}}const NT=Og("area",K2),zT=Og("bounds",tv),PT=Og("centroid",av);function IT(e){const t=this.context.group;let n=!1;if(t)for(;e;){if(e===t){n=!0;break}e=e.mark.group}return n}function Tg(e,t,n){try{e[t].apply(e,["EXPRESSION"].concat([].slice.call(n)))}catch(r){e.warn(r)}return n[n.length-1]}function UT(){return Tg(this.context.dataflow,"warn",arguments)}function qT(){return Tg(this.context.dataflow,"info",arguments)}function jT(){return Tg(this.context.dataflow,"debug",arguments)}function dc(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}function qd(e){const t=_i(e),n=dc(t.r),r=dc(t.g),i=dc(t.b);return .2126*n+.7152*r+.0722*i}function GT(e,t){const n=qd(e),r=qd(t),i=Math.max(n,r),a=Math.min(n,r);return(i+.05)/(a+.05)}function WT(){const e=[].slice.call(arguments);return e.unshift({}),Z(...e)}function Rw(e,t){return e===t||e!==e&&t!==t?!0:z(e)?z(t)&&e.length===t.length?YT(e,t):!1:K(e)&&K(t)?Ow(e,t):!1}function YT(e,t){for(let n=0,r=e.length;n<r;++n)if(!Rw(e[n],t[n]))return!1;return!0}function Ow(e,t){for(const n in e)if(!Rw(e[n],t[n]))return!1;return!0}function Yy(e){return t=>Ow(e,t)}function HT(e,t,n,r,i,a){const u=this.context.dataflow,o=this.context.data[e],s=o.input,l=u.stamp();let f=o.changes,c,d;if(u._trigger===!1||!(s.value.length||t||r))return 0;if((!f||f.stamp<l)&&(o.changes=f=u.changeset(),f.stamp=l,u.runAfter(()=>{o.modified=!0,u.pulse(s,f).run()},!0,1)),n&&(c=n===!0?Ct:z(n)||ea(n)?n:Yy(n),f.remove(c)),t&&f.insert(t),r&&(c=Yy(r),s.value.some(c)?f.remove(c):f.insert(r)),i)for(d in a)f.modify(i,d,a[d]);return 1}function XT(e){const t=e.touches,n=t[0].clientX-t[1].clientX,r=t[0].clientY-t[1].clientY;return Math.hypot(n,r)}function VT(e){const t=e.touches;return Math.atan2(t[0].clientY-t[1].clientY,t[0].clientX-t[1].clientX)}const Hy={};function KT(e,t){const n=Hy[t]||(Hy[t]=Ot(t));return z(e)?e.map(n):n(e)}function Lg(e){return z(e)||ArrayBuffer.isView(e)?e:null}function Ng(e){return Lg(e)||(fe(e)?e:null)}function JT(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Lg(e).join(...n)}function QT(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ng(e).indexOf(...n)}function ZT(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ng(e).lastIndexOf(...n)}function eL(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ng(e).slice(...n)}function tL(e,t,n){return J(n)&&S("Function argument passed to replace."),String(e).replace(t,n)}function nL(e){return Lg(e).slice().reverse()}function rL(e,t,n){return Ul(e||0,t||0,n||0)}function iL(e,t){const n=Kn(e,(t||this).context);return n&&n.bandwidth?n.bandwidth():0}function aL(e,t){const n=Kn(e,(t||this).context);return n?n.copy():void 0}function uL(e,t){const n=Kn(e,(t||this).context);return n?n.domain():[]}function oL(e,t,n){const r=Kn(e,(n||this).context);return r?z(t)?(r.invertRange||r.invert)(t):(r.invert||r.invertExtent)(t):void 0}function sL(e,t){const n=Kn(e,(t||this).context);return n&&n.range?n.range():[]}function lL(e,t,n){const r=Kn(e,(n||this).context);return r?r(t):void 0}function fL(e,t,n,r,i){e=Kn(e,(i||this).context);const a=r1(t,n);let u=e.domain(),o=u[0],s=re(u),l=et;return s-o?l=J0(e,o,s):e=(e.interpolator?ue("sequential")().interpolator(e.interpolator()):ue("linear")().interpolate(e.interpolate()).range(e.range())).domain([o=0,s=1]),e.ticks&&(u=e.ticks(+r||15),o!==u[0]&&u.unshift(o),s!==re(u)&&u.push(s)),u.forEach(f=>a.stop(l(f),e(f))),a}function cL(e,t,n){const r=Kn(e,(n||this).context);return function(i){return r?r.path.context(i)(t):""}}function dL(e){let t=null;return function(n){return n?Ui(n,t=t||Qr(e)):e}}const Tw=e=>e.data;function Lw(e,t){const n=Bw.call(t,e);return n.root&&n.root.lookup||{}}function hL(e,t,n){const r=Lw(e,this),i=r[t],a=r[n];return i&&a?i.path(a).map(Tw):void 0}function gL(e,t){const n=Lw(e,this)[t];return n?n.ancestors().map(Tw):void 0}const Nw=()=>typeof window<"u"&&window||null;function pL(){const e=Nw();return e?e.screen:{}}function mL(){const e=Nw();return e?[e.innerWidth,e.innerHeight]:[void 0,void 0]}function yL(){const e=this.context.dataflow,t=e.container&&e.container();return t?[t.clientWidth,t.clientHeight]:[void 0,void 0]}function zw(e,t,n){if(!e)return[];const[r,i]=e,a=new we().set(r[0],r[1],i[0],i[1]),u=n||this.context.dataflow.scenegraph().root;return R1(u,a,vL(t))}function vL(e){let t=null;if(e){const n=q(e.marktype),r=q(e.markname);t=i=>(!n.length||n.some(a=>i.marktype===a))&&(!r.length||r.some(a=>i.name===a))}return t}function xL(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:5;e=q(e);const i=e[e.length-1];return i===void 0||Math.hypot(i[0]-t,i[1]-n)>r?[...e,[t,n]]:e}function bL(e){return q(e).reduce((t,n,r)=>{let[i,a]=n;return t+=r==0?`M ${i},${a} `:r===e.length-1?" Z":`L ${i},${a} `},"")}function AL(e,t,n){const{x:r,y:i,mark:a}=n,u=new we().set(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER,Number.MIN_SAFE_INTEGER);for(const[s,l]of t)s<u.x1&&(u.x1=s),s>u.x2&&(u.x2=s),l<u.y1&&(u.y1=l),l>u.y2&&(u.y2=l);return u.translate(r,i),zw([[u.x1,u.y1],[u.x2,u.y2]],e,a).filter(s=>EL(s.x,s.y,t))}function EL(e,t,n){let r=0;for(let i=0,a=n.length-1;i<n.length;a=i++){const[u,o]=n[a],[s,l]=n[i];l>t!=o>t&&e<(u-s)*(t-l)/(o-l)+s&&r++}return r&1}const el={random(){return zt()},cumulativeNormal:_u,cumulativeLogNormal:_l,cumulativeUniform:Ll,densityNormal:Sl,densityLogNormal:Bl,densityUniform:Tl,quantileNormal:Ru,quantileLogNormal:Rl,quantileUniform:Nl,sampleNormal:Bu,sampleLogNormal:$l,sampleUniform:Ol,isArray:z,isBoolean:jh,isDate:Gn,isDefined(e){return e!==void 0},isNumber:Yn,isObject:K,isRegExp:Gh,isString:fe,isTuple:ea,isValid(e){return e!=null&&e===e},toBoolean:bl,toDate(e){return Al(e)},toNumber:Ne,toString:El,indexof:QT,join:JT,lastindexof:ZT,replace:tL,reverse:nL,slice:eL,flush:qh,lerp:Yh,merge:WT,pad:Hh,peek:re,pluck:KT,span:Ji,inrange:Ir,truncate:Xh,rgb:_i,lab:ns,hcl:M2,hsl:rs,luminance:qd,contrast:GT,sequence:Et,format:DT,utcFormat:CT,utcParse:kT,utcOffset:i0,utcSequence:u0,timeFormat:_w,timeParse:FT,timeOffset:r0,timeSequence:a0,timeUnitSpecifier:Jh,monthFormat:MT,monthAbbrevFormat:ST,dayFormat:$T,dayAbbrevFormat:BT,quarter:Nh,utcquarter:zh,week:e0,utcweek:n0,dayofyear:Zh,utcdayofyear:t0,warn:UT,info:qT,debug:jT,extent(e){return ln(e)},inScope:IT,intersect:zw,clampRange:Ph,pinchDistance:XT,pinchAngle:VT,screen:pL,containerSize:yL,windowSize:mL,bandspace:rL,setdata:ET,pathShape:dL,panLinear:Rh,panLog:Oh,panPow:Th,panSymlog:Lh,zoomLinear:ml,zoomLog:yl,zoomPow:su,zoomSymlog:vl,encode:wT,modify:HT,lassoAppend:xL,lassoPath:bL,intersectLasso:AL},wL=["view","item","group","xy","x","y"],DL="event.vega.",Pw="this.",zg={},CL={forbidden:["_"],allowed:["datum","event","item"],fieldvar:"datum",globalvar:e=>`_[${U(OT+e)}]`,functions:FL,constants:bg,visitors:zg},Xy=QE(CL);function FL(e){const t=Ag(e);wL.forEach(n=>t[n]=DL+n);for(const n in el)t[n]=Pw+n;return Z(t,LT(e,el,zg)),t}function ke(e,t,n){return arguments.length===1?el[e]:(el[e]=t,n&&(zg[e]=n),Xy&&(Xy.functions[e]=Pw+e),this)}ke("bandwidth",iL,wt);ke("copy",aL,wt);ke("domain",uL,wt);ke("range",sL,wt);ke("invert",oL,wt);ke("scale",lL,wt);ke("gradient",fL,wt);ke("geoArea",NT,wt);ke("geoBounds",zT,wt);ke("geoCentroid",PT,wt);ke("geoShape",cL,wt);ke("indata",AT,TT);ke("data",Bw,Rg);ke("treePath",hL,Rg);ke("treeAncestors",gL,Rg);ke("vlSelectionTest",ew,ia);ke("vlSelectionIdTest",nw,ia);ke("vlSelectionResolve",iw,ia);ke("vlSelectionTuples",rw);const kL="view",tl="[",nl="]",Iw="{",Uw="}",ML=":",qw=",",SL="@",$L=">",BL=/[[\]{}]/,_L={"*":1,arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1};let jw,Gw;function Ww(e,t,n){return jw=t||kL,Gw=n||_L,Yw(e.trim()).map(jd)}function RL(e){return Gw[e]}function Za(e,t,n,r,i){const a=e.length;let u=0,o;for(;t<a;++t){if(o=e[t],!u&&o===n)return t;i&&i.indexOf(o)>=0?--u:r&&r.indexOf(o)>=0&&++u}return t}function Yw(e){const t=[],n=e.length;let r=0,i=0;for(;i<n;)i=Za(e,i,qw,tl+Iw,nl+Uw),t.push(e.substring(r,i).trim()),r=++i;if(t.length===0)throw"Empty event selector: "+e;return t}function jd(e){return e[0]==="["?OL(e):TL(e)}function OL(e){const t=e.length;let n=1,r;if(n=Za(e,n,nl,tl,nl),n===t)throw"Empty between selector: "+e;if(r=Yw(e.substring(1,n)),r.length!==2)throw"Between selector must have two elements: "+e;if(e=e.slice(n+1).trim(),e[0]!==$L)throw"Expected '>' after between selector: "+e;r=r.map(jd);const i=jd(e.slice(1).trim());return i.between?{between:r,stream:i}:(i.between=r,i)}function TL(e){const t={source:jw},n=[];let r=[0,0],i=0,a=0,u=e.length,o=0,s,l;if(e[u-1]===Uw){if(o=e.lastIndexOf(Iw),o>=0){try{r=LL(e.substring(o+1,u-1))}catch{throw"Invalid throttle specification: "+e}e=e.slice(0,o).trim(),u=e.length}else throw"Unmatched right brace: "+e;o=0}if(!u)throw e;if(e[0]===SL&&(i=++o),s=Za(e,o,ML),s<u&&(n.push(e.substring(a,s).trim()),a=o=++s),o=Za(e,o,tl),o===u)n.push(e.substring(a,u).trim());else if(n.push(e.substring(a,o).trim()),l=[],a=++o,a===u)throw"Unmatched left bracket: "+e;for(;o<u;){if(o=Za(e,o,nl),o===u)throw"Unmatched left bracket: "+e;if(l.push(e.substring(a,o).trim()),o<u-1&&e[++o]!==tl)throw"Expected left bracket: "+e;a=++o}if(!(u=n.length)||BL.test(n[u-1]))throw"Invalid event selector: "+e;return u>1?(t.type=n[1],i?t.markname=n[0].slice(1):RL(n[0])?t.marktype=n[0]:t.source=n[0]):t.type=n[0],t.type.slice(-1)==="!"&&(t.consume=!0,t.type=t.type.slice(0,-1)),l!=null&&(t.filter=l),r[0]&&(t.throttle=r[0]),r[1]&&(t.debounce=r[1]),t}function LL(e){const t=e.split(qw);if(!e.length||t.length>2)throw e;return t.map(n=>{const r=+n;if(r!==r)throw e;return r})}function NL(e){return K(e)?e:{type:e||"pad"}}const xa=e=>+e||0,zL=e=>({top:e,bottom:e,left:e,right:e});function PL(e){return K(e)?e.signal?e:{top:xa(e.top),bottom:xa(e.bottom),left:xa(e.left),right:xa(e.right)}:zL(xa(e))}const Re=e=>K(e)&&!z(e)?Z({},e):{value:e};function Vy(e,t,n,r){return n!=null?(K(n)&&!z(n)||z(n)&&n.length&&K(n[0])?e.update[t]=n:e[r||"enter"][t]={value:n},1):0}function Ue(e,t,n){for(const r in t)Vy(e,r,t[r]);for(const r in n)Vy(e,r,n[r],"update")}function aa(e,t,n){for(const r in t)n&&G(n,r)||(e[r]=Z(e[r]||{},t[r]));return e}function Ei(e,t){return t&&(t.enter&&t.enter[e]||t.update&&t.update[e])}const Pg="mark",Ig="frame",Ug="scope",IL="axis",UL="axis-domain",qL="axis-grid",jL="axis-label",GL="axis-tick",WL="axis-title",YL="legend",HL="legend-band",XL="legend-entry",VL="legend-gradient",Hw="legend-label",KL="legend-symbol",JL="legend-title",QL="title",ZL="title-text",eN="title-subtitle";function tN(e,t,n,r,i){const a={},u={};let o,s,l,f;s="lineBreak",t==="text"&&i[s]!=null&&!Ei(s,e)&&hc(a,s,i[s]),(n=="legend"||String(n).startsWith("axis"))&&(n=null),f=n===Ig?i.group:n===Pg?Z({},i.mark,i[t]):null;for(s in f)l=Ei(s,e)||(s==="fill"||s==="stroke")&&(Ei("fill",e)||Ei("stroke",e)),l||hc(a,s,f[s]);q(r).forEach(c=>{const d=i.style&&i.style[c];for(const h in d)Ei(h,e)||hc(a,h,d[h])}),e=Z({},e);for(s in a)f=a[s],f.signal?(o=o||{})[s]=f:u[s]=f;return e.enter=Z(u,e.enter),o&&(e.update=Z(o,e.update)),e}function hc(e,t,n){e[t]=n&&n.signal?{signal:n.signal}:{value:n}}const Xw=e=>fe(e)?U(e):e.signal?`(${e.signal})`:Vw(e);function hf(e){if(e.gradient!=null)return rN(e);let t=e.signal?`(${e.signal})`:e.color?nN(e.color):e.field!=null?Vw(e.field):e.value!==void 0?U(e.value):void 0;return e.scale!=null&&(t=iN(e,t)),t===void 0&&(t=null),e.exponent!=null&&(t=`pow(${t},${Ko(e.exponent)})`),e.mult!=null&&(t+=`*${Ko(e.mult)}`),e.offset!=null&&(t+=`+${Ko(e.offset)}`),e.round&&(t=`round(${t})`),t}const Bo=(e,t,n,r)=>`(${e}(${[t,n,r].map(hf).join(",")})+'')`;function nN(e){return e.c?Bo("hcl",e.h,e.c,e.l):e.h||e.s?Bo("hsl",e.h,e.s,e.l):e.l||e.a?Bo("lab",e.l,e.a,e.b):e.r||e.g||e.b?Bo("rgb",e.r,e.g,e.b):null}function rN(e){const t=[e.start,e.stop,e.count].map(n=>n==null?null:U(n));for(;t.length&&re(t)==null;)t.pop();return t.unshift(Xw(e.gradient)),`gradient(${t.join(",")})`}function Ko(e){return K(e)?"("+hf(e)+")":e}function Vw(e){return Kw(K(e)?e:{datum:e})}function Kw(e){let t,n,r;if(e.signal)t="datum",r=e.signal;else if(e.group||e.parent){for(n=Math.max(1,e.level||1),t="item";n-- >0;)t+=".mark.group";e.parent?(r=e.parent,t+=".datum"):r=e.group}else e.datum?(t="datum",r=e.datum):S("Invalid field reference: "+U(e));return e.signal||(r=fe(r)?hl(r).map(U).join("]["):Kw(r)),t+"["+r+"]"}function iN(e,t){const n=Xw(e.scale);return e.range!=null?t=`lerp(_range(${n}), ${+e.range})`:(t!==void 0&&(t=`_scale(${n}, ${t})`),e.band&&(t=(t?t+"+":"")+`_bandwidth(${n})`+(+e.band==1?"":"*"+Ko(e.band)),e.extra&&(t=`(datum.extra ? _scale(${n}, datum.extra.value) : ${t})`)),t==null&&(t="0")),t}function aN(e){let t="";return e.forEach(n=>{const r=hf(n);t+=n.test?`(${n.test})?${r}:`:r}),re(t)===":"&&(t+="null"),t}function Jw(e,t,n,r,i,a){const u={};a=a||{},a.encoders={$encode:u},e=tN(e,t,n,r,i.config);for(const o in e)u[o]=uN(e[o],t,a,i);return a}function uN(e,t,n,r){const i={},a={};for(const u in e)e[u]!=null&&(i[u]=sN(oN(e[u]),r,n,a));return{$expr:{marktype:t,channels:i},$fields:Object.keys(a),$output:Object.keys(e)}}function oN(e){return z(e)?aN(e):hf(e)}function sN(e,t,n,r){const i=Cn(e,t);return i.$fields.forEach(a=>r[a]=1),Z(n,i.$params),i.$expr}const lN="outer",fN=["value","update","init","react","bind"];function Ky(e,t){S(e+' for "outer" push: '+U(t))}function Qw(e,t){const n=e.name;if(e.push===lN)t.signals[n]||Ky("No prior signal definition",n),fN.forEach(r=>{e[r]!==void 0&&Ky("Invalid property ",r)});else{const r=t.addSignal(n,e.value);e.react===!1&&(r.react=!1),e.bind&&t.addBinding(n,e.bind)}}function Gd(e,t,n,r){this.id=-1,this.type=e,this.value=t,this.params=n,r&&(this.parent=r)}function gf(e,t,n,r){return new Gd(e,t,n,r)}function rl(e,t){return gf("operator",e,t)}function W(e){const t={$ref:e.id};return e.id<0&&(e.refs=e.refs||[]).push(t),t}function bu(e,t){return t?{$field:e,$name:t}:{$field:e}}const Wd=bu("key");function Jy(e,t){return{$compare:e,$order:t}}function cN(e,t){const n={$key:e};return t&&(n.$flat=!0),n}const dN="ascending",hN="descending";function gN(e){return K(e)?(e.order===hN?"-":"+")+pf(e.op,e.field):""}function pf(e,t){return(e&&e.signal?"$"+e.signal:e||"")+(e&&t?"_":"")+(t&&t.signal?"$"+t.signal:t||"")}const qg="scope",Yd="view";function $e(e){return e&&e.signal}function pN(e){return e&&e.expr}function Jo(e){if($e(e))return!0;if(K(e)){for(const t in e)if(Jo(e[t]))return!0}return!1}function en(e,t){return e??t}function Yr(e){return e&&e.signal||e}const Qy="timer";function Au(e,t){return(e.merge?yN:e.stream?vN:e.type?xN:S("Invalid stream specification: "+U(e)))(e,t)}function mN(e){return e===qg?Yd:e||Yd}function yN(e,t){const n=e.merge.map(i=>Au(i,t)),r=jg({merge:n},e,t);return t.addStream(r).id}function vN(e,t){const n=Au(e.stream,t),r=jg({stream:n},e,t);return t.addStream(r).id}function xN(e,t){let n;e.type===Qy?(n=t.event(Qy,e.throttle),e={between:e.between,filter:e.filter}):n=t.event(mN(e.source),e.type);const r=jg({stream:n},e,t);return Object.keys(r).length===1?n:t.addStream(r).id}function jg(e,t,n){let r=t.between;return r&&(r.length!==2&&S('Stream "between" parameter must have 2 entries: '+U(t)),e.between=[Au(r[0],n),Au(r[1],n)]),r=t.filter?[].concat(t.filter):[],(t.marktype||t.markname||t.markrole)&&r.push(bN(t.marktype,t.markname,t.markrole)),t.source===qg&&r.push("inScope(event.item)"),r.length&&(e.filter=Cn("("+r.join(")&&(")+")",n).$expr),(r=t.throttle)!=null&&(e.throttle=+r),(r=t.debounce)!=null&&(e.debounce=+r),t.consume&&(e.consume=!0),e}function bN(e,t,n){const r="event.item";return r+(e&&e!=="*"?"&&"+r+".mark.marktype==='"+e+"'":"")+(n?"&&"+r+".mark.role==='"+n+"'":"")+(t?"&&"+r+".mark.name==='"+t+"'":"")}const AN={code:"_.$value",ast:{type:"Identifier",value:"value"}};function EN(e,t,n){const r=e.encode,i={target:n};let a=e.events,u=e.update,o=[];a||S("Signal update missing events specification."),fe(a)&&(a=Ww(a,t.isSubscope()?qg:Yd)),a=q(a).filter(s=>s.signal||s.scale?(o.push(s),0):1),o.length>1&&(o=[DN(o)]),a.length&&o.push(a.length>1?{merge:a}:a[0]),r!=null&&(u&&S("Signal encode and update are mutually exclusive."),u="encode(item(),"+U(r)+")"),i.update=fe(u)?Cn(u,t):u.expr!=null?Cn(u.expr,t):u.value!=null?u.value:u.signal!=null?{$expr:AN,$params:{$value:t.signalRef(u.signal)}}:S("Invalid signal update specification."),e.force&&(i.options={force:!0}),o.forEach(s=>t.addUpdate(Z(wN(s,t),i)))}function wN(e,t){return{source:e.signal?t.signalRef(e.signal):e.scale?t.scaleRef(e.scale):Au(e,t)}}function DN(e){return{signal:"["+e.map(t=>t.scale?'scale("'+t.scale+'")':t.signal)+"]"}}function CN(e,t){const n=t.getSignal(e.name);let r=e.update;e.init&&(r?S("Signals can not include both init and update expressions."):(r=e.init,n.initonly=!0)),r&&(r=Cn(r,t),n.update=r.$expr,n.params=r.$params),e.on&&e.on.forEach(i=>EN(i,t,n.id))}const he=e=>(t,n,r)=>gf(e,n,t||void 0,r),Zw=he("aggregate"),FN=he("axisticks"),e3=he("bound"),hn=he("collect"),Zy=he("compare"),kN=he("datajoin"),t3=he("encode"),MN=he("expression"),SN=he("facet"),$N=he("field"),BN=he("key"),_N=he("legendentries"),RN=he("load"),ON=he("mark"),TN=he("multiextent"),LN=he("multivalues"),NN=he("overlap"),zN=he("params"),n3=he("prefacet"),PN=he("projection"),IN=he("proxy"),UN=he("relay"),r3=he("render"),qN=he("scale"),li=he("sieve"),jN=he("sortitems"),i3=he("viewlayout"),GN=he("values");let WN=0;const a3={min:"min",max:"max",count:"sum"};function YN(e,t){const n=e.type||"linear";jb(n)||S("Unrecognized scale type: "+U(n)),t.addScale(e.name,{type:n,domain:void 0})}function HN(e,t){const n=t.getScale(e.name).params;let r;n.domain=u3(e.domain,e,t),e.range!=null&&(n.range=s3(e,t,n)),e.interpolate!=null&&rz(e.interpolate,n),e.nice!=null&&(n.nice=nz(e.nice)),e.bins!=null&&(n.bins=tz(e.bins,t));for(r in e)G(n,r)||r==="name"||(n[r]=Wt(e[r],t))}function Wt(e,t){return K(e)?e.signal?t.signalRef(e.signal):S("Unsupported object: "+U(e)):e}function Qo(e,t){return e.signal?t.signalRef(e.signal):e.map(n=>Wt(n,t))}function mf(e){S("Can not find data set: "+U(e))}function u3(e,t,n){if(!e){(t.domainMin!=null||t.domainMax!=null)&&S("No scale domain defined for domainMin/domainMax to override.");return}return e.signal?n.signalRef(e.signal):(z(e)?XN:e.fields?KN:VN)(e,t,n)}function XN(e,t,n){return e.map(r=>Wt(r,n))}function VN(e,t,n){const r=n.getData(e.data);return r||mf(e.data),Ii(t.type)?r.valuesRef(n,e.field,o3(e.sort,!1)):Yb(t.type)?r.domainRef(n,e.field):r.extentRef(n,e.field)}function KN(e,t,n){const r=e.data,i=e.fields.reduce((a,u)=>(u=fe(u)?{data:r,field:u}:z(u)||u.signal?JN(u,n):u,a.push(u),a),[]);return(Ii(t.type)?QN:Yb(t.type)?ZN:ez)(e,n,i)}function JN(e,t){const n="_:vega:_"+WN++,r=hn({});if(z(e))r.value={$ingest:e};else if(e.signal){const i="setdata("+U(n)+","+e.signal+")";r.params.input=t.signalRef(i)}return t.addDataPipeline(n,[r,li({})]),{data:n,field:"data"}}function QN(e,t,n){const r=o3(e.sort,!0);let i,a;const u=n.map(l=>{const f=t.getData(l.data);return f||mf(l.data),f.countsRef(t,l.field,r)}),o={groupby:Wd,pulse:u};r&&(i=r.op||"count",a=r.field?pf(i,r.field):"count",o.ops=[a3[i]],o.fields=[t.fieldRef(a)],o.as=[a]),i=t.add(Zw(o));const s=t.add(hn({pulse:W(i)}));return a=t.add(GN({field:Wd,sort:t.sortRef(r),pulse:W(s)})),W(a)}function o3(e,t){return e&&(!e.field&&!e.op?K(e)?e.field="key":e={field:"key"}:!e.field&&e.op!=="count"?S("No field provided for sort aggregate op: "+e.op):t&&e.field&&e.op&&!a3[e.op]&&S("Multiple domain scales can not be sorted using "+e.op)),e}function ZN(e,t,n){const r=n.map(i=>{const a=t.getData(i.data);return a||mf(i.data),a.domainRef(t,i.field)});return W(t.add(LN({values:r})))}function ez(e,t,n){const r=n.map(i=>{const a=t.getData(i.data);return a||mf(i.data),a.extentRef(t,i.field)});return W(t.add(TN({extents:r})))}function tz(e,t){return e.signal||z(e)?Qo(e,t):t.objectProperty(e)}function nz(e){return K(e)?{interval:Wt(e.interval),step:Wt(e.step)}:Wt(e)}function rz(e,t){t.interpolate=Wt(e.type||e),e.gamma!=null&&(t.interpolateGamma=Wt(e.gamma))}function s3(e,t,n){const r=t.config.range;let i=e.range;if(i.signal)return t.signalRef(i.signal);if(fe(i)){if(r&&G(r,i))return e=Z({},e,{range:r[i]}),s3(e,t,n);i==="width"?i=[0,{signal:"width"}]:i==="height"?i=Ii(e.type)?[0,{signal:"height"}]:[{signal:"height"},0]:S("Unrecognized scale range value: "+U(i))}else if(i.scheme){n.scheme=z(i.scheme)?Qo(i.scheme,t):Wt(i.scheme,t),i.extent&&(n.schemeExtent=Qo(i.extent,t)),i.count&&(n.schemeCount=Wt(i.count,t));return}else if(i.step){n.rangeStep=Wt(i.step,t);return}else{if(Ii(e.type)&&!z(i))return u3(i,e,t);z(i)||S("Unsupported range type: "+U(i))}return i.map(a=>(z(a)?Qo:Wt)(a,t))}function iz(e,t){const n=t.config.projection||{},r={};for(const i in e)i!=="name"&&(r[i]=Hd(e[i],i,t));for(const i in n)r[i]==null&&(r[i]=Hd(n[i],i,t));t.addProjection(e.name,r)}function Hd(e,t,n){return z(e)?e.map(r=>Hd(r,t,n)):K(e)?e.signal?n.signalRef(e.signal):t==="fit"?e:S("Unsupported parameter object: "+U(e)):e}const gn="top",ua="left",oa="right",wr="bottom",l3="center",az="vertical",uz="start",oz="middle",sz="end",Xd="index",Gg="label",lz="offset",Yi="perc",fz="perc2",Yt="value",eo="guide-label",Wg="guide-title",cz="group-title",dz="group-subtitle",e2="symbol",Zo="gradient",Vd="discrete",Kd="size",hz="shape",gz="fill",pz="stroke",mz="strokeWidth",yz="strokeDash",vz="opacity",Yg=[Kd,hz,gz,pz,mz,yz,vz],to={name:1,style:1,interactive:1},ae={value:0},Ht={value:1},yf="group",f3="rect",Hg="rule",xz="symbol",fi="text";function Eu(e){return e.type=yf,e.interactive=e.interactive||!1,e}function St(e,t){const n=(r,i)=>en(e[r],en(t[r],i));return n.isVertical=r=>az===en(e.direction,t.direction||(r?t.symbolDirection:t.gradientDirection)),n.gradientLength=()=>en(e.gradientLength,t.gradientLength||t.gradientWidth),n.gradientThickness=()=>en(e.gradientThickness,t.gradientThickness||t.gradientHeight),n.entryColumns=()=>en(e.columns,en(t.columns,+n.isVertical(!0))),n}function c3(e,t){const n=t&&(t.update&&t.update[e]||t.enter&&t.enter[e]);return n&&n.signal?n:n?n.value:null}function bz(e,t,n){const r=t.config.style[n];return r&&r[e]}function vf(e,t,n){return`item.anchor === '${uz}' ? ${e} : item.anchor === '${sz}' ? ${t} : ${n}`}const Xg=vf(U(ua),U(oa),U(l3));function Az(e){const t=e("tickBand");let n=e("tickOffset"),r,i;return t?t.signal?(r={signal:`(${t.signal}) === 'extent' ? 1 : 0.5`},i={signal:`(${t.signal}) === 'extent'`},K(n)||(n={signal:`(${t.signal}) === 'extent' ? 0 : ${n}`})):t==="extent"?(r=1,i=!0,n=0):(r=.5,i=!1):(r=e("bandPosition"),i=e("tickExtra")),{extra:i,band:r,offset:n}}function d3(e,t){return t?e?K(e)?Object.assign({},e,{offset:d3(e.offset,t)}):{value:e,offset:t}:t:e}function It(e,t){return t?(e.name=t.name,e.style=t.style||e.style,e.interactive=!!t.interactive,e.encode=aa(e.encode,t,to)):e.interactive=!1,e}function Ez(e,t,n,r){const i=St(e,n),a=i.isVertical(),u=i.gradientThickness(),o=i.gradientLength();let s,l,f,c,d;a?(l=[0,1],f=[0,0],c=u,d=o):(l=[0,0],f=[1,0],c=o,d=u);const h={enter:s={opacity:ae,x:ae,y:ae,width:Re(c),height:Re(d)},update:Z({},s,{opacity:Ht,fill:{gradient:t,start:l,stop:f}}),exit:{opacity:ae}};return Ue(h,{stroke:i("gradientStrokeColor"),strokeWidth:i("gradientStrokeWidth")},{opacity:i("gradientOpacity")}),It({type:f3,role:VL,encode:h},r)}function wz(e,t,n,r,i){const a=St(e,n),u=a.isVertical(),o=a.gradientThickness(),s=a.gradientLength();let l,f,c,d,h="";u?(l="y",c="y2",f="x",d="width",h="1-"):(l="x",c="x2",f="y",d="height");const g={opacity:ae,fill:{scale:t,field:Yt}};g[l]={signal:h+"datum."+Yi,mult:s},g[f]=ae,g[c]={signal:h+"datum."+fz,mult:s},g[d]=Re(o);const p={enter:g,update:Z({},g,{opacity:Ht}),exit:{opacity:ae}};return Ue(p,{stroke:a("gradientStrokeColor"),strokeWidth:a("gradientStrokeWidth")},{opacity:a("gradientOpacity")}),It({type:f3,role:HL,key:Yt,from:i,encode:p},r)}const Dz=`datum.${Yi}<=0?"${ua}":datum.${Yi}>=1?"${oa}":"${l3}"`,Cz=`datum.${Yi}<=0?"${wr}":datum.${Yi}>=1?"${gn}":"${oz}"`;function t2(e,t,n,r){const i=St(e,t),a=i.isVertical(),u=Re(i.gradientThickness()),o=i.gradientLength();let s=i("labelOverlap"),l,f,c,d,h="";const g={enter:l={opacity:ae},update:f={opacity:Ht,text:{field:Gg}},exit:{opacity:ae}};return Ue(g,{fill:i("labelColor"),fillOpacity:i("labelOpacity"),font:i("labelFont"),fontSize:i("labelFontSize"),fontStyle:i("labelFontStyle"),fontWeight:i("labelFontWeight"),limit:en(e.labelLimit,t.gradientLabelLimit)}),a?(l.align={value:"left"},l.baseline=f.baseline={signal:Cz},c="y",d="x",h="1-"):(l.align=f.align={signal:Dz},l.baseline={value:"top"},c="x",d="y"),l[c]=f[c]={signal:h+"datum."+Yi,mult:o},l[d]=f[d]=u,u.offset=en(e.labelOffset,t.gradientLabelOffset)||0,s=s?{separation:i("labelSeparation"),method:s,order:"datum."+Xd}:void 0,It({type:fi,role:Hw,style:eo,key:Yt,from:r,encode:g,overlap:s},n)}function Fz(e,t,n,r,i){const a=St(e,t),u=n.entries,o=!!(u&&u.interactive),s=u?u.name:void 0,l=a("clipHeight"),f=a("symbolOffset"),c={data:"value"},d=`(${i}) ? datum.${lz} : datum.${Kd}`,h=l?Re(l):{field:Kd},g=`datum.${Xd}`,p=`max(1, ${i})`;let m,y,v,x,b;h.mult=.5,m={enter:y={opacity:ae,x:{signal:d,mult:.5,offset:f},y:h},update:v={opacity:Ht,x:y.x,y:y.y},exit:{opacity:ae}};let E=null,w=null;e.fill||(E=t.symbolBaseFillColor,w=t.symbolBaseStrokeColor),Ue(m,{fill:a("symbolFillColor",E),shape:a("symbolType"),size:a("symbolSize"),stroke:a("symbolStrokeColor",w),strokeDash:a("symbolDash"),strokeDashOffset:a("symbolDashOffset"),strokeWidth:a("symbolStrokeWidth")},{opacity:a("symbolOpacity")}),Yg.forEach(F=>{e[F]&&(v[F]=y[F]={scale:e[F],field:Yt})});const A=It({type:xz,role:KL,key:Yt,from:c,clip:l?!0:void 0,encode:m},n.symbols),D=Re(f);D.offset=a("labelOffset"),m={enter:y={opacity:ae,x:{signal:d,offset:D},y:h},update:v={opacity:Ht,text:{field:Gg},x:y.x,y:y.y},exit:{opacity:ae}},Ue(m,{align:a("labelAlign"),baseline:a("labelBaseline"),fill:a("labelColor"),fillOpacity:a("labelOpacity"),font:a("labelFont"),fontSize:a("labelFontSize"),fontStyle:a("labelFontStyle"),fontWeight:a("labelFontWeight"),limit:a("labelLimit")});const C=It({type:fi,role:Hw,style:eo,key:Yt,from:c,encode:m},n.labels);return m={enter:{noBound:{value:!l},width:ae,height:l?Re(l):ae,opacity:ae},exit:{opacity:ae},update:v={opacity:Ht,row:{signal:null},column:{signal:null}}},a.isVertical(!0)?(x=`ceil(item.mark.items.length / ${p})`,v.row.signal=`${g}%${x}`,v.column.signal=`floor(${g} / ${x})`,b={field:["row",g]}):(v.row.signal=`floor(${g} / ${p})`,v.column.signal=`${g} % ${p}`,b={field:g}),v.column.signal=`(${i})?${v.column.signal}:${g}`,r={facet:{data:r,name:"value",groupby:Xd}},Eu({role:Ug,from:r,encode:aa(m,u,to),marks:[A,C],name:s,interactive:o,sort:b})}function kz(e,t){const n=St(e,t);return{align:n("gridAlign"),columns:n.entryColumns(),center:{row:!0,column:!1},padding:{row:n("rowPadding"),column:n("columnPadding")}}}const Vg='item.orient === "left"',Kg='item.orient === "right"',xf=`(${Vg} || ${Kg})`,Mz=`datum.vgrad && ${xf}`,Sz=vf('"top"','"bottom"','"middle"'),$z=vf('"right"','"left"','"center"'),Bz=`datum.vgrad && ${Kg} ? (${$z}) : (${xf} && !(datum.vgrad && ${Vg})) ? "left" : ${Xg}`,_z=`item._anchor || (${xf} ? "middle" : "start")`,Rz=`${Mz} ? (${Vg} ? -90 : 90) : 0`,Oz=`${xf} ? (datum.vgrad ? (${Kg} ? "bottom" : "top") : ${Sz}) : "top"`;function Tz(e,t,n,r){const i=St(e,t),a={enter:{opacity:ae},update:{opacity:Ht,x:{field:{group:"padding"}},y:{field:{group:"padding"}}},exit:{opacity:ae}};return Ue(a,{orient:i("titleOrient"),_anchor:i("titleAnchor"),anchor:{signal:_z},angle:{signal:Rz},align:{signal:Bz},baseline:{signal:Oz},text:e.title,fill:i("titleColor"),fillOpacity:i("titleOpacity"),font:i("titleFont"),fontSize:i("titleFontSize"),fontStyle:i("titleFontStyle"),fontWeight:i("titleFontWeight"),limit:i("titleLimit"),lineHeight:i("titleLineHeight")},{align:i("titleAlign"),baseline:i("titleBaseline")}),It({type:fi,role:JL,style:Wg,from:r,encode:a},n)}function Lz(e,t){let n;return K(e)&&(e.signal?n=e.signal:e.path?n="pathShape("+n2(e.path)+")":e.sphere&&(n="geoShape("+n2(e.sphere)+', {type: "Sphere"})')),n?t.signalRef(n):!!e}function n2(e){return K(e)&&e.signal?e.signal:U(e)}function h3(e){const t=e.role||"";return!t.indexOf("axis")||!t.indexOf("legend")||!t.indexOf("title")?t:e.type===yf?Ug:t||Pg}function Nz(e){return{marktype:e.type,name:e.name||void 0,role:e.role||h3(e),zindex:+e.zindex||void 0,aria:e.aria,description:e.description}}function zz(e,t){return e&&e.signal?t.signalRef(e.signal):e!==!1}function Jg(e,t){const n=Vx(e.type);n||S("Unrecognized transform type: "+U(e.type));const r=gf(n.type.toLowerCase(),null,g3(n,e,t));return e.signal&&t.addSignal(e.signal,t.proxy(r)),r.metadata=n.metadata||{},r}function g3(e,t,n){const r={},i=e.params.length;for(let a=0;a<i;++a){const u=e.params[a];r[u.name]=Pz(u,t,n)}return r}function Pz(e,t,n){const r=e.type,i=t[e.name];if(r==="index")return Iz(e,t,n);if(i===void 0){e.required&&S("Missing required "+U(t.type)+" parameter: "+U(e.name));return}else{if(r==="param")return Uz(e,t,n);if(r==="projection")return n.projectionRef(t[e.name])}return e.array&&!$e(i)?i.map(a=>r2(e,a,n)):r2(e,i,n)}function r2(e,t,n){const r=e.type;if($e(t))return a2(r)?S("Expression references can not be signals."):gc(r)?n.fieldRef(t):u2(r)?n.compareRef(t):n.signalRef(t.signal);{const i=e.expr||gc(r);return i&&qz(t)?n.exprRef(t.expr,t.as):i&&jz(t)?bu(t.field,t.as):a2(r)?Cn(t,n):Gz(r)?W(n.getData(t).values):gc(r)?bu(t):u2(r)?n.compareRef(t):t}}function Iz(e,t,n){return fe(t.from)||S('Lookup "from" parameter must be a string literal.'),n.getData(t.from).lookupRef(n,t.key)}function Uz(e,t,n){const r=t[e.name];return e.array?(z(r)||S("Expected an array of sub-parameters. Instead: "+U(r)),r.map(i=>i2(e,i,n))):i2(e,r,n)}function i2(e,t,n){const r=e.params.length;let i;for(let u=0;u<r;++u){i=e.params[u];for(const o in i.key)if(i.key[o]!==t[o]){i=null;break}if(i)break}i||S("Unsupported parameter: "+U(t));const a=Z(g3(i,t,n),i.key);return W(n.add(zN(a)))}const qz=e=>e&&e.expr,jz=e=>e&&e.field,Gz=e=>e==="data",a2=e=>e==="expr",gc=e=>e==="field",u2=e=>e==="compare";function Wz(e,t,n){let r,i,a,u,o;return e?(r=e.facet)&&(t||S("Only group marks can be faceted."),r.field!=null?u=o=es(r,n):(e.data?o=W(n.getData(e.data).aggregate):(a=Jg(Z({type:"aggregate",groupby:q(r.groupby)},r.aggregate),n),a.params.key=n.keyRef(r.groupby),a.params.pulse=es(r,n),u=o=W(n.add(a))),i=n.keyRef(r.groupby,!0))):u=W(n.add(hn(null,[{}]))),u||(u=es(e,n)),{key:i,pulse:u,parent:o}}function es(e,t){return e.$ref?e:e.data&&e.data.$ref?e.data:W(t.getData(e.data).output)}function ti(e,t,n,r,i){this.scope=e,this.input=t,this.output=n,this.values=r,this.aggregate=i,this.index={}}ti.fromEntries=function(e,t){const n=t.length,r=t[n-1],i=t[n-2];let a=t[0],u=null,o=1;for(a&&a.type==="load"&&(a=t[1]),e.add(t[0]);o<n;++o)t[o].params.pulse=W(t[o-1]),e.add(t[o]),t[o].type==="aggregate"&&(u=t[o]);return new ti(e,a,i,r,u)};function p3(e){return fe(e)?e:null}function o2(e,t,n){const r=pf(n.op,n.field);let i;if(t.ops){for(let a=0,u=t.as.length;a<u;++a)if(t.as[a]===r)return}else t.ops=["count"],t.fields=[null],t.as=["count"];n.op&&(t.ops.push((i=n.op.signal)?e.signalRef(i):n.op),t.fields.push(e.fieldRef(n.field)),t.as.push(r))}function ba(e,t,n,r,i,a,u){const o=t[n]||(t[n]={}),s=gN(a);let l=p3(i),f,c;if(l!=null&&(e=t.scope,l=l+(s?"|"+s:""),f=o[l]),!f){const d=a?{field:Wd,pulse:t.countsRef(e,i,a)}:{field:e.fieldRef(i),pulse:W(t.output)};s&&(d.sort=e.sortRef(a)),c=e.add(gf(r,void 0,d)),u&&(t.index[i]=c),f=W(c),l!=null&&(o[l]=f)}return f}ti.prototype={countsRef(e,t,n){const r=this,i=r.counts||(r.counts={}),a=p3(t);let u,o,s;return a!=null&&(e=r.scope,u=i[a]),u?n&&n.field&&o2(e,u.agg.params,n):(s={groupby:e.fieldRef(t,"key"),pulse:W(r.output)},n&&n.field&&o2(e,s,n),o=e.add(Zw(s)),u=e.add(hn({pulse:W(o)})),u={agg:o,ref:W(u)},a!=null&&(i[a]=u)),u.ref},tuplesRef(){return W(this.values)},extentRef(e,t){return ba(e,this,"extent","extent",t,!1)},domainRef(e,t){return ba(e,this,"domain","values",t,!1)},valuesRef(e,t,n){return ba(e,this,"vals","values",t,n||!0)},lookupRef(e,t){return ba(e,this,"lookup","tupleindex",t,!1)},indataRef(e,t){return ba(e,this,"indata","tupleindex",t,!0,!0)}};function Yz(e,t,n){const r=e.from.facet,i=r.name,a=es(r,t);let u;r.name||S("Facet must have a name: "+U(r)),r.data||S("Facet must reference a data set: "+U(r)),r.field?u=t.add(n3({field:t.fieldRef(r.field),pulse:a})):r.groupby?u=t.add(SN({key:t.keyRef(r.groupby),group:W(t.proxy(n.parent)),pulse:a})):S("Facet must specify groupby or field: "+U(r));const o=t.fork(),s=o.add(hn()),l=o.add(li({pulse:W(s)}));o.addData(i,new ti(o,s,s,l)),o.addSignal("parent",null),u.params.subflow={$subflow:o.parse(e).toRuntime()}}function Hz(e,t,n){const r=t.add(n3({pulse:n.pulse})),i=t.fork();i.add(li()),i.addSignal("parent",null),r.params.subflow={$subflow:i.parse(e).toRuntime()}}function m3(e,t,n){const r=e.remove,i=e.insert,a=e.toggle,u=e.modify,o=e.values,s=t.add(rl()),l="if("+e.trigger+',modify("'+n+'",'+[i,r,a,u,o].map(c=>c??"null").join(",")+"),0)",f=Cn(l,t);s.update=f.$expr,s.params=f.$params}function bf(e,t){const n=h3(e),r=e.type===yf,i=e.from&&e.from.facet,a=e.overlap;let u=e.layout||n===Ug||n===Ig,o,s,l,f,c,d,h;const g=n===Pg||u||i,p=Wz(e.from,r,t);s=t.add(kN({key:p.key||(e.key?bu(e.key):void 0),pulse:p.pulse,clean:!r}));const m=W(s);s=l=t.add(hn({pulse:m})),s=t.add(ON({markdef:Nz(e),interactive:zz(e.interactive,t),clip:Lz(e.clip,t),context:{$context:!0},groups:t.lookup(),parent:t.signals.parent?t.signalRef("parent"):null,index:t.markpath(),pulse:W(s)}));const y=W(s);s=f=t.add(t3(Jw(e.encode,e.type,n,e.style,t,{mod:!1,pulse:y}))),s.params.parent=t.encode(),e.transform&&e.transform.forEach(w=>{const A=Jg(w,t),D=A.metadata;(D.generates||D.changes)&&S("Mark transforms should not generate new data."),D.nomod||(f.params.mod=!0),A.params.pulse=W(s),t.add(s=A)}),e.sort&&(s=t.add(jN({sort:t.compareRef(e.sort),pulse:W(s)})));const v=W(s);(i||u)&&(u=t.add(i3({layout:t.objectProperty(e.layout),legends:t.legends,mark:y,pulse:v})),d=W(u));const x=t.add(e3({mark:y,pulse:d||v}));h=W(x),r&&(g&&(o=t.operators,o.pop(),u&&o.pop()),t.pushState(v,d||h,m),i?Yz(e,t,p):g?Hz(e,t,p):t.parse(e),t.popState(),g&&(u&&o.push(u),o.push(x))),a&&(h=Xz(a,h,t));const b=t.add(r3({pulse:h})),E=t.add(li({pulse:W(b)},void 0,t.parent()));e.name!=null&&(c=e.name,t.addData(c,new ti(t,l,b,E)),e.on&&e.on.forEach(w=>{(w.insert||w.remove||w.toggle)&&S("Marks only support modify triggers."),m3(w,t,c)}))}function Xz(e,t,n){const r=e.method,i=e.bound,a=e.separation,u={separation:$e(a)?n.signalRef(a.signal):a,method:$e(r)?n.signalRef(r.signal):r,pulse:t};if(e.order&&(u.sort=n.compareRef({field:e.order})),i){const o=i.tolerance;u.boundTolerance=$e(o)?n.signalRef(o.signal):+o,u.boundScale=n.scaleRef(i.scale),u.boundOrient=i.orient}return W(n.add(NN(u)))}function Vz(e,t){const n=t.config.legend,r=e.encode||{},i=St(e,n),a=r.legend||{},u=a.name||void 0,o=a.interactive,s=a.style,l={};let f=0,c,d,h;Yg.forEach(x=>e[x]?(l[x]=e[x],f=f||e[x]):0),f||S("Missing valid scale for legend.");const g=Kz(e,t.scaleType(f)),p={title:e.title!=null,scales:l,type:g,vgrad:g!=="symbol"&&i.isVertical()},m=W(t.add(hn(null,[p]))),y={enter:{x:{value:0},y:{value:0}}},v=W(t.add(_N(d={type:g,scale:t.scaleRef(f),count:t.objectProperty(i("tickCount")),limit:t.property(i("symbolLimit")),values:t.objectProperty(e.values),minstep:t.property(e.tickMinStep),formatType:t.property(e.formatType),formatSpecifier:t.property(e.format)})));return g===Zo?(h=[Ez(e,f,n,r.gradient),t2(e,n,r.labels,v)],d.count=d.count||t.signalRef(`max(2,2*floor((${Yr(i.gradientLength())})/100))`)):g===Vd?h=[wz(e,f,n,r.gradient,v),t2(e,n,r.labels,v)]:(c=kz(e,n),h=[Fz(e,n,r,v,Yr(c.columns))],d.size=Zz(e,t,h[0].marks)),h=[Eu({role:XL,from:m,encode:y,marks:h,layout:c,interactive:o})],p.title&&h.push(Tz(e,n,r.title,m)),bf(Eu({role:YL,from:m,encode:aa(Qz(i,e,n),a,to),marks:h,aria:i("aria"),description:i("description"),zindex:i("zindex"),name:u,interactive:o,style:s}),t)}function Kz(e,t){let n=e.type||e2;return!e.type&&Jz(e)===1&&(e.fill||e.stroke)&&(n=K0(t)?Zo:sd(t)?Vd:e2),n!==Zo?n:sd(t)?Vd:Zo}function Jz(e){return Yg.reduce((t,n)=>t+(e[n]?1:0),0)}function Qz(e,t,n){const r={enter:{},update:{}};return Ue(r,{orient:e("orient"),offset:e("offset"),padding:e("padding"),titlePadding:e("titlePadding"),cornerRadius:e("cornerRadius"),fill:e("fillColor"),stroke:e("strokeColor"),strokeWidth:n.strokeWidth,strokeDash:n.strokeDash,x:e("legendX"),y:e("legendY"),format:t.format,formatType:t.formatType}),r}function Zz(e,t,n){const r=Yr(s2("size",e,n)),i=Yr(s2("strokeWidth",e,n)),a=Yr(eP(n[1].encode,t,eo));return Cn(`max(ceil(sqrt(${r})+${i}),${a})`,t)}function s2(e,t,n){return t[e]?`scale("${t[e]}",datum)`:c3(e,n[0].encode)}function eP(e,t,n){return c3("fontSize",e)||bz("fontSize",t,n)}const tP=`item.orient==="${ua}"?-90:item.orient==="${oa}"?90:0`;function nP(e,t){e=fe(e)?{text:e}:e;const n=St(e,t.config.title),r=e.encode||{},i=r.group||{},a=i.name||void 0,u=i.interactive,o=i.style,s=[],l={},f=W(t.add(hn(null,[l])));return s.push(aP(e,n,rP(e),f)),e.subtitle&&s.push(uP(e,n,r.subtitle,f)),bf(Eu({role:QL,from:f,encode:iP(n,i),marks:s,aria:n("aria"),description:n("description"),zindex:n("zindex"),name:a,interactive:u,style:o}),t)}function rP(e){const t=e.encode;return t&&t.title||Z({name:e.name,interactive:e.interactive,style:e.style},t)}function iP(e,t){const n={enter:{},update:{}};return Ue(n,{orient:e("orient"),anchor:e("anchor"),align:{signal:Xg},angle:{signal:tP},limit:e("limit"),frame:e("frame"),offset:e("offset")||0,padding:e("subtitlePadding")}),aa(n,t,to)}function aP(e,t,n,r){const i={value:0},a=e.text,u={enter:{opacity:i},update:{opacity:{value:1}},exit:{opacity:i}};return Ue(u,{text:a,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:t("dx"),dy:t("dy"),fill:t("color"),font:t("font"),fontSize:t("fontSize"),fontStyle:t("fontStyle"),fontWeight:t("fontWeight"),lineHeight:t("lineHeight")},{align:t("align"),angle:t("angle"),baseline:t("baseline")}),It({type:fi,role:ZL,style:cz,from:r,encode:u},n)}function uP(e,t,n,r){const i={value:0},a=e.subtitle,u={enter:{opacity:i},update:{opacity:{value:1}},exit:{opacity:i}};return Ue(u,{text:a,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:t("dx"),dy:t("dy"),fill:t("subtitleColor"),font:t("subtitleFont"),fontSize:t("subtitleFontSize"),fontStyle:t("subtitleFontStyle"),fontWeight:t("subtitleFontWeight"),lineHeight:t("subtitleLineHeight")},{align:t("align"),angle:t("angle"),baseline:t("baseline")}),It({type:fi,role:eN,style:dz,from:r,encode:u},n)}function oP(e,t){const n=[];e.transform&&e.transform.forEach(r=>{n.push(Jg(r,t))}),e.on&&e.on.forEach(r=>{m3(r,t,e.name)}),t.addDataPipeline(e.name,sP(e,t,n))}function sP(e,t,n){const r=[];let i=null,a=!1,u=!1,o,s,l,f,c;for(e.values?$e(e.values)||Jo(e.format)?(r.push(l2(t,e)),r.push(i=$r())):r.push(i=$r({$ingest:e.values,$format:e.format})):e.url?Jo(e.url)||Jo(e.format)?(r.push(l2(t,e)),r.push(i=$r())):r.push(i=$r({$request:e.url,$format:e.format})):e.source&&(i=o=q(e.source).map(d=>W(t.getData(d).output)),r.push(null)),s=0,l=n.length;s<l;++s)f=n[s],c=f.metadata,!i&&!c.source&&r.push(i=$r()),r.push(f),c.generates&&(u=!0),c.modifies&&!u&&(a=!0),c.source?i=f:c.changes&&(i=null);return o&&(l=o.length-1,r[0]=UN({derive:a,pulse:l?o:o[0]}),(a||l)&&r.splice(1,0,$r())),i||r.push($r()),r.push(li({})),r}function $r(e){const t=hn({},e);return t.metadata={source:!0},t}function l2(e,t){return RN({url:t.url?e.property(t.url):void 0,async:t.async?e.property(t.async):void 0,values:t.values?e.property(t.values):void 0,format:e.objectProperty(t.format)})}const y3=e=>e===wr||e===gn,Af=(e,t,n)=>$e(e)?dP(e.signal,t,n):e===ua||e===gn?t:n,Oe=(e,t,n)=>$e(e)?fP(e.signal,t,n):y3(e)?t:n,sn=(e,t,n)=>$e(e)?cP(e.signal,t,n):y3(e)?n:t,v3=(e,t,n)=>$e(e)?hP(e.signal,t,n):e===gn?{value:t}:{value:n},lP=(e,t,n)=>$e(e)?gP(e.signal,t,n):e===oa?{value:t}:{value:n},fP=(e,t,n)=>x3(`${e} === '${gn}' || ${e} === '${wr}'`,t,n),cP=(e,t,n)=>x3(`${e} !== '${gn}' && ${e} !== '${wr}'`,t,n),dP=(e,t,n)=>Qg(`${e} === '${ua}' || ${e} === '${gn}'`,t,n),hP=(e,t,n)=>Qg(`${e} === '${gn}'`,t,n),gP=(e,t,n)=>Qg(`${e} === '${oa}'`,t,n),x3=(e,t,n)=>(t=t!=null?Re(t):t,n=n!=null?Re(n):n,f2(t)&&f2(n)?(t=t?t.signal||U(t.value):null,n=n?n.signal||U(n.value):null,{signal:`${e} ? (${t}) : (${n})`}):[Z({test:e},t)].concat(n||[])),f2=e=>e==null||Object.keys(e).length===1,Qg=(e,t,n)=>({signal:`${e} ? (${Mi(t)}) : (${Mi(n)})`}),pP=(e,t,n,r,i)=>({signal:(r!=null?`${e} === '${ua}' ? (${Mi(r)}) : `:"")+(n!=null?`${e} === '${wr}' ? (${Mi(n)}) : `:"")+(i!=null?`${e} === '${oa}' ? (${Mi(i)}) : `:"")+(t!=null?`${e} === '${gn}' ? (${Mi(t)}) : `:"")+"(null)"}),Mi=e=>$e(e)?e.signal:e==null?null:U(e),mP=(e,t)=>t===0?0:$e(e)?{signal:`(${e.signal}) * ${t}`}:{value:e*t},Bi=(e,t)=>{const n=e.signal;return n&&n.endsWith("(null)")?{signal:n.slice(0,-6)+t.signal}:e};function bi(e,t,n,r){let i;if(t&&G(t,e))return t[e];if(G(n,e))return n[e];if(e.startsWith("title")){switch(e){case"titleColor":i="fill";break;case"titleFont":case"titleFontSize":case"titleFontWeight":i=e[5].toLowerCase()+e.slice(6)}return r[Wg][i]}else if(e.startsWith("label")){switch(e){case"labelColor":i="fill";break;case"labelFont":case"labelFontSize":i=e[5].toLowerCase()+e.slice(6)}return r[eo][i]}return null}function c2(e){const t={};for(const n of e)if(n)for(const r in n)t[r]=1;return Object.keys(t)}function yP(e,t){var n=t.config,r=n.style,i=n.axis,a=t.scaleType(e.scale)==="band"&&n.axisBand,u=e.orient,o,s,l;if($e(u)){const c=c2([n.axisX,n.axisY]),d=c2([n.axisTop,n.axisBottom,n.axisLeft,n.axisRight]);o={};for(l of c)o[l]=Oe(u,bi(l,n.axisX,i,r),bi(l,n.axisY,i,r));s={};for(l of d)s[l]=pP(u.signal,bi(l,n.axisTop,i,r),bi(l,n.axisBottom,i,r),bi(l,n.axisLeft,i,r),bi(l,n.axisRight,i,r))}else o=u===gn||u===wr?n.axisX:n.axisY,s=n["axis"+u[0].toUpperCase()+u.slice(1)];return o||s||a?Z({},i,o,s,a):i}function vP(e,t,n,r){const i=St(e,t),a=e.orient;let u,o;const s={enter:u={opacity:ae},update:o={opacity:Ht},exit:{opacity:ae}};Ue(s,{stroke:i("domainColor"),strokeCap:i("domainCap"),strokeDash:i("domainDash"),strokeDashOffset:i("domainDashOffset"),strokeWidth:i("domainWidth"),strokeOpacity:i("domainOpacity")});const l=d2(e,0),f=d2(e,1);return u.x=o.x=Oe(a,l,ae),u.x2=o.x2=Oe(a,f),u.y=o.y=sn(a,l,ae),u.y2=o.y2=sn(a,f),It({type:Hg,role:UL,from:r,encode:s},n)}function d2(e,t){return{scale:e.scale,range:t}}function xP(e,t,n,r,i){const a=St(e,t),u=e.orient,o=e.gridScale,s=Af(u,1,-1),l=bP(e.offset,s);let f,c,d;const h={enter:f={opacity:ae},update:d={opacity:Ht},exit:c={opacity:ae}};Ue(h,{stroke:a("gridColor"),strokeCap:a("gridCap"),strokeDash:a("gridDash"),strokeDashOffset:a("gridDashOffset"),strokeOpacity:a("gridOpacity"),strokeWidth:a("gridWidth")});const g={scale:e.scale,field:Yt,band:i.band,extra:i.extra,offset:i.offset,round:a("tickRound")},p=Oe(u,{signal:"height"},{signal:"width"}),m=o?{scale:o,range:0,mult:s,offset:l}:{value:0,offset:l},y=o?{scale:o,range:1,mult:s,offset:l}:Z(p,{mult:s,offset:l});return f.x=d.x=Oe(u,g,m),f.y=d.y=sn(u,g,m),f.x2=d.x2=sn(u,y),f.y2=d.y2=Oe(u,y),c.x=Oe(u,g),c.y=sn(u,g),It({type:Hg,role:qL,key:Yt,from:r,encode:h},n)}function bP(e,t){if(t!==1)if(!K(e))e=$e(t)?{signal:`(${t.signal}) * (${e||0})`}:t*(e||0);else{let n=e=Z({},e);for(;n.mult!=null;)if(K(n.mult))n=n.mult=Z({},n.mult);else return n.mult=$e(t)?{signal:`(${n.mult}) * (${t.signal})`}:n.mult*t,e;n.mult=t}return e}function AP(e,t,n,r,i,a){const u=St(e,t),o=e.orient,s=Af(o,-1,1);let l,f,c;const d={enter:l={opacity:ae},update:c={opacity:Ht},exit:f={opacity:ae}};Ue(d,{stroke:u("tickColor"),strokeCap:u("tickCap"),strokeDash:u("tickDash"),strokeDashOffset:u("tickDashOffset"),strokeOpacity:u("tickOpacity"),strokeWidth:u("tickWidth")});const h=Re(i);h.mult=s;const g={scale:e.scale,field:Yt,band:a.band,extra:a.extra,offset:a.offset,round:u("tickRound")};return c.y=l.y=Oe(o,ae,g),c.y2=l.y2=Oe(o,h),f.x=Oe(o,g),c.x=l.x=sn(o,ae,g),c.x2=l.x2=sn(o,h),f.y=sn(o,g),It({type:Hg,role:GL,key:Yt,from:r,encode:d},n)}function pc(e,t,n,r,i){return{signal:'flush(range("'+e+'"), scale("'+e+'", datum.value), '+t+","+n+","+r+","+i+")"}}function EP(e,t,n,r,i,a){const u=St(e,t),o=e.orient,s=e.scale,l=Af(o,-1,1),f=Yr(u("labelFlush")),c=Yr(u("labelFlushOffset")),d=u("labelAlign"),h=u("labelBaseline");let g=f===0||!!f,p;const m=Re(i);m.mult=l,m.offset=Re(u("labelPadding")||0),m.offset.mult=l;const y={scale:s,field:Yt,band:.5,offset:d3(a.offset,u("labelOffset"))},v=Oe(o,g?pc(s,f,'"left"','"right"','"center"'):{value:"center"},lP(o,"left","right")),x=Oe(o,v3(o,"bottom","top"),g?pc(s,f,'"top"','"bottom"','"middle"'):{value:"middle"}),b=pc(s,f,`-(${c})`,c,0);g=g&&c;const E={opacity:ae,x:Oe(o,y,m),y:sn(o,y,m)},w={enter:E,update:p={opacity:Ht,text:{field:Gg},x:E.x,y:E.y,align:v,baseline:x},exit:{opacity:ae,x:E.x,y:E.y}};Ue(w,{dx:!d&&g?Oe(o,b):null,dy:!h&&g?sn(o,b):null}),Ue(w,{angle:u("labelAngle"),fill:u("labelColor"),fillOpacity:u("labelOpacity"),font:u("labelFont"),fontSize:u("labelFontSize"),fontWeight:u("labelFontWeight"),fontStyle:u("labelFontStyle"),limit:u("labelLimit"),lineHeight:u("labelLineHeight")},{align:d,baseline:h});const A=u("labelBound");let D=u("labelOverlap");return D=D||A?{separation:u("labelSeparation"),method:D,order:"datum.index",bound:A?{scale:s,orient:o,tolerance:A}:null}:void 0,p.align!==v&&(p.align=Bi(p.align,v)),p.baseline!==x&&(p.baseline=Bi(p.baseline,x)),It({type:fi,role:jL,style:eo,key:Yt,from:r,encode:w,overlap:D},n)}function wP(e,t,n,r){const i=St(e,t),a=e.orient,u=Af(a,-1,1);let o,s;const l={enter:o={opacity:ae,anchor:Re(i("titleAnchor",null)),align:{signal:Xg}},update:s=Z({},o,{opacity:Ht,text:Re(e.title)}),exit:{opacity:ae}},f={signal:`lerp(range("${e.scale}"), ${vf(0,1,.5)})`};return s.x=Oe(a,f),s.y=sn(a,f),o.angle=Oe(a,ae,mP(u,90)),o.baseline=Oe(a,v3(a,wr,gn),{value:wr}),s.angle=o.angle,s.baseline=o.baseline,Ue(l,{fill:i("titleColor"),fillOpacity:i("titleOpacity"),font:i("titleFont"),fontSize:i("titleFontSize"),fontStyle:i("titleFontStyle"),fontWeight:i("titleFontWeight"),limit:i("titleLimit"),lineHeight:i("titleLineHeight")},{align:i("titleAlign"),angle:i("titleAngle"),baseline:i("titleBaseline")}),DP(i,a,l,n),l.update.align=Bi(l.update.align,o.align),l.update.angle=Bi(l.update.angle,o.angle),l.update.baseline=Bi(l.update.baseline,o.baseline),It({type:fi,role:WL,style:Wg,from:r,encode:l},n)}function DP(e,t,n,r){const i=(o,s)=>o!=null?(n.update[s]=Bi(Re(o),n.update[s]),!1):!Ei(s,r),a=i(e("titleX"),"x"),u=i(e("titleY"),"y");n.enter.auto=u===a?Re(u):Oe(t,Re(u),Re(a))}function CP(e,t){const n=yP(e,t),r=e.encode||{},i=r.axis||{},a=i.name||void 0,u=i.interactive,o=i.style,s=St(e,n),l=Az(s),f={scale:e.scale,ticks:!!s("ticks"),labels:!!s("labels"),grid:!!s("grid"),domain:!!s("domain"),title:e.title!=null},c=W(t.add(hn({},[f]))),d=W(t.add(FN({scale:t.scaleRef(e.scale),extra:t.property(l.extra),count:t.objectProperty(e.tickCount),values:t.objectProperty(e.values),minstep:t.property(e.tickMinStep),formatType:t.property(e.formatType),formatSpecifier:t.property(e.format)}))),h=[];let g;return f.grid&&h.push(xP(e,n,r.grid,d,l)),f.ticks&&(g=s("tickSize"),h.push(AP(e,n,r.ticks,d,g,l))),f.labels&&(g=f.ticks?g:0,h.push(EP(e,n,r.labels,d,g,l))),f.domain&&h.push(vP(e,n,r.domain,c)),f.title&&h.push(wP(e,n,r.title,c)),bf(Eu({role:IL,from:c,encode:aa(FP(s,e),i,to),marks:h,aria:s("aria"),description:s("description"),zindex:s("zindex"),name:a,interactive:u,style:o}),t)}function FP(e,t){const n={enter:{},update:{}};return Ue(n,{orient:e("orient"),offset:e("offset")||0,position:en(t.position,0),titlePadding:e("titlePadding"),minExtent:e("minExtent"),maxExtent:e("maxExtent"),range:{signal:`abs(span(range("${t.scale}")))`},translate:e("translate"),format:t.format,formatType:t.formatType}),n}function b3(e,t,n){const r=q(e.signals),i=q(e.scales);return n||r.forEach(a=>Qw(a,t)),q(e.projections).forEach(a=>iz(a,t)),i.forEach(a=>YN(a,t)),q(e.data).forEach(a=>oP(a,t)),i.forEach(a=>HN(a,t)),(n||r).forEach(a=>CN(a,t)),q(e.axes).forEach(a=>CP(a,t)),q(e.marks).forEach(a=>bf(a,t)),q(e.legends).forEach(a=>Vz(a,t)),e.title&&nP(e.title,t),t.parseLambdas(),t}const kP=e=>aa({enter:{x:{value:0},y:{value:0}},update:{width:{signal:"width"},height:{signal:"height"}}},e);function MP(e,t){const n=t.config,r=W(t.root=t.add(rl())),i=SP(e,n);i.forEach(l=>Qw(l,t)),t.description=e.description||n.description,t.eventConfig=n.events,t.legends=t.objectProperty(n.legend&&n.legend.layout),t.locale=n.locale;const a=t.add(hn()),u=t.add(t3(Jw(kP(e.encode),yf,Ig,e.style,t,{pulse:W(a)}))),o=t.add(i3({layout:t.objectProperty(e.layout),legends:t.legends,autosize:t.signalRef("autosize"),mark:r,pulse:W(u)}));t.operators.pop(),t.pushState(W(u),W(o),null),b3(e,t,i),t.operators.push(o);let s=t.add(e3({mark:r,pulse:W(o)}));return s=t.add(r3({pulse:W(s)})),s=t.add(li({pulse:W(s)})),t.addData("root",new ti(t,a,a,s)),t}function Aa(e,t){return t&&t.signal?{name:e,update:t.signal}:{name:e,value:t}}function SP(e,t){const n=u=>en(e[u],t[u]),r=[Aa("background",n("background")),Aa("autosize",NL(n("autosize"))),Aa("padding",PL(n("padding"))),Aa("width",n("width")||0),Aa("height",n("height")||0)],i=r.reduce((u,o)=>(u[o.name]=o,u),{}),a={};return q(e.signals).forEach(u=>{G(i,u.name)?u=Z(i[u.name],u):r.push(u),a[u.name]=u}),q(t.signals).forEach(u=>{!G(a,u.name)&&!G(i,u.name)&&r.push(u)}),r}function A3(e,t){this.config=e||{},this.options=t||{},this.bindings=[],this.field={},this.signals={},this.lambdas={},this.scales={},this.events={},this.data={},this.streams=[],this.updates=[],this.operators=[],this.eventConfig=null,this.locale=null,this._id=0,this._subid=0,this._nextsub=[0],this._parent=[],this._encode=[],this._lookup=[],this._markpath=[]}function h2(e){this.config=e.config,this.options=e.options,this.legends=e.legends,this.field=Object.create(e.field),this.signals=Object.create(e.signals),this.lambdas=Object.create(e.lambdas),this.scales=Object.create(e.scales),this.events=Object.create(e.events),this.data=Object.create(e.data),this.streams=[],this.updates=[],this.operators=[],this._id=0,this._subid=++e._nextsub[0],this._nextsub=e._nextsub,this._parent=e._parent.slice(),this._encode=e._encode.slice(),this._lookup=e._lookup.slice(),this._markpath=e._markpath}A3.prototype=h2.prototype={parse(e){return b3(e,this)},fork(){return new h2(this)},isSubscope(){return this._subid>0},toRuntime(){return this.finish(),{description:this.description,operators:this.operators,streams:this.streams,updates:this.updates,bindings:this.bindings,eventConfig:this.eventConfig,locale:this.locale}},id(){return(this._subid?this._subid+":":0)+this._id++},add(e){return this.operators.push(e),e.id=this.id(),e.refs&&(e.refs.forEach(t=>{t.$ref=e.id}),e.refs=null),e},proxy(e){const t=e instanceof Gd?W(e):e;return this.add(IN({value:t}))},addStream(e){return this.streams.push(e),e.id=this.id(),e},addUpdate(e){return this.updates.push(e),e},finish(){let e,t;this.root&&(this.root.root=!0);for(e in this.signals)this.signals[e].signal=e;for(e in this.scales)this.scales[e].scale=e;function n(r,i,a){let u,o;r&&(u=r.data||(r.data={}),o=u[i]||(u[i]=[]),o.push(a))}for(e in this.data){t=this.data[e],n(t.input,e,"input"),n(t.output,e,"output"),n(t.values,e,"values");for(const r in t.index)n(t.index[r],e,"index:"+r)}return this},pushState(e,t,n){this._encode.push(W(this.add(li({pulse:e})))),this._parent.push(t),this._lookup.push(n?W(this.proxy(n)):null),this._markpath.push(-1)},popState(){this._encode.pop(),this._parent.pop(),this._lookup.pop(),this._markpath.pop()},parent(){return re(this._parent)},encode(){return re(this._encode)},lookup(){return re(this._lookup)},markpath(){const e=this._markpath;return++e[e.length-1]},fieldRef(e,t){if(fe(e))return bu(e,t);e.signal||S("Unsupported field reference: "+U(e));const n=e.signal;let r=this.field[n];if(!r){const i={name:this.signalRef(n)};t&&(i.as=t),this.field[n]=r=W(this.add($N(i)))}return r},compareRef(e){let t=!1;const n=a=>$e(a)?(t=!0,this.signalRef(a.signal)):pN(a)?(t=!0,this.exprRef(a.expr)):a,r=q(e.field).map(n),i=q(e.order).map(n);return t?W(this.add(Zy({fields:r,orders:i}))):Jy(r,i)},keyRef(e,t){let n=!1;const r=a=>$e(a)?(n=!0,W(i[a.signal])):a,i=this.signals;return e=q(e).map(r),n?W(this.add(BN({fields:e,flat:t}))):cN(e,t)},sortRef(e){if(!e)return e;const t=pf(e.op,e.field),n=e.order||dN;return n.signal?W(this.add(Zy({fields:t,orders:this.signalRef(n.signal)}))):Jy(t,n)},event(e,t){const n=e+":"+t;if(!this.events[n]){const r=this.id();this.streams.push({id:r,source:e,type:t}),this.events[n]=r}return this.events[n]},hasOwnSignal(e){return G(this.signals,e)},addSignal(e,t){this.hasOwnSignal(e)&&S("Duplicate signal name: "+U(e));const n=t instanceof Gd?t:this.add(rl(t));return this.signals[e]=n},getSignal(e){return this.signals[e]||S("Unrecognized signal name: "+U(e)),this.signals[e]},signalRef(e){return this.signals[e]?W(this.signals[e]):(G(this.lambdas,e)||(this.lambdas[e]=this.add(rl(null))),W(this.lambdas[e]))},parseLambdas(){const e=Object.keys(this.lambdas);for(let t=0,n=e.length;t<n;++t){const r=e[t],i=Cn(r,this),a=this.lambdas[r];a.params=i.$params,a.update=i.$expr}},property(e){return e&&e.signal?this.signalRef(e.signal):e},objectProperty(e){return!e||!K(e)?e:this.signalRef(e.signal||Zg(e))},exprRef(e,t){const n={expr:Cn(e,this)};return t&&(n.expr.$name=t),W(this.add(MN(n)))},addBinding(e,t){this.bindings||S("Nested signals do not support binding: "+U(e)),this.bindings.push(Z({signal:e},t))},addScaleProj(e,t){G(this.scales,e)&&S("Duplicate scale or projection name: "+U(e)),this.scales[e]=this.add(t)},addScale(e,t){this.addScaleProj(e,qN(t))},addProjection(e,t){this.addScaleProj(e,PN(t))},getScale(e){return this.scales[e]||S("Unrecognized scale name: "+U(e)),this.scales[e]},scaleRef(e){return W(this.getScale(e))},scaleType(e){return this.getScale(e).params.type},projectionRef(e){return this.scaleRef(e)},projectionType(e){return this.scaleType(e)},addData(e,t){return G(this.data,e)&&S("Duplicate data set name: "+U(e)),this.data[e]=t},getData(e){return this.data[e]||S("Undefined data set name: "+U(e)),this.data[e]},addDataPipeline(e,t){return G(this.data,e)&&S("Duplicate data set name: "+U(e)),this.addData(e,ti.fromEntries(this,t))}};function Zg(e){return(z(e)?$P:BP)(e)}function $P(e){const t=e.length;let n="[";for(let r=0;r<t;++r){const i=e[r];n+=(r>0?",":"")+(K(i)?i.signal||Zg(i):U(i))}return n+"]"}function BP(e){let t="{",n=0,r,i;for(r in e)i=e[r],t+=(++n>1?",":"")+U(r)+":"+(K(i)?i.signal||Zg(i):U(i));return t+"}"}function _P(){const e="sans-serif",r="#4c78a8",i="#000",a="#888",u="#ddd";return{description:"Vega visualization",padding:0,autosize:"pad",background:null,events:{defaults:{allow:["wheel"]}},group:null,mark:null,arc:{fill:r},area:{fill:r},image:null,line:{stroke:r,strokeWidth:2},path:{stroke:r},rect:{fill:r},rule:{stroke:i},shape:{stroke:r},symbol:{fill:r,size:64},text:{fill:i,font:e,fontSize:11},trail:{fill:r,size:2},style:{"guide-label":{fill:i,font:e,fontSize:10},"guide-title":{fill:i,font:e,fontSize:11,fontWeight:"bold"},"group-title":{fill:i,font:e,fontSize:13,fontWeight:"bold"},"group-subtitle":{fill:i,font:e,fontSize:12},point:{size:30,strokeWidth:2,shape:"circle"},circle:{size:30,strokeWidth:2},square:{size:30,strokeWidth:2,shape:"square"},cell:{fill:"transparent",stroke:u},view:{fill:"transparent"}},title:{orient:"top",anchor:"middle",offset:4,subtitlePadding:3},axis:{minExtent:0,maxExtent:200,bandPosition:.5,domain:!0,domainWidth:1,domainColor:a,grid:!1,gridWidth:1,gridColor:u,labels:!0,labelAngle:0,labelLimit:180,labelOffset:0,labelPadding:2,ticks:!0,tickColor:a,tickOffset:0,tickRound:!0,tickSize:5,tickWidth:1,titlePadding:4},axisBand:{tickOffset:-.5},projection:{type:"mercator"},legend:{orient:"right",padding:0,gridAlign:"each",columnPadding:10,rowPadding:2,symbolDirection:"vertical",gradientDirection:"vertical",gradientLength:200,gradientThickness:16,gradientStrokeColor:u,gradientStrokeWidth:0,gradientLabelOffset:2,labelAlign:"left",labelBaseline:"middle",labelLimit:160,labelOffset:4,labelOverlap:!0,symbolLimit:30,symbolType:"circle",symbolSize:100,symbolOffset:0,symbolStrokeWidth:1.5,symbolBaseFillColor:"transparent",symbolBaseStrokeColor:a,titleLimit:180,titleOrient:"top",titlePadding:5,layout:{offset:18,direction:"horizontal",left:{direction:"vertical"},right:{direction:"vertical"}}},range:{category:{scheme:"tableau10"},ordinal:{scheme:"blues"},heatmap:{scheme:"yellowgreenblue"},ramp:{scheme:"blues"},diverging:{scheme:"blueorange",extent:[1,0]},symbol:["circle","square","triangle-up","cross","diamond","triangle-right","triangle-down","triangle-left"]}}}function RP(e,t,n){return K(e)||S("Input Vega specification must be an object."),t=fx(_P(),t,e.config),MP(e,new A3(t,n)).toRuntime()}const OP="RawCode",TP="Literal",LP="Property",NP="Identifier",zP="ArrayExpression",PP="BinaryExpression",IP="CallExpression",UP="ConditionalExpression",qP="LogicalExpression",jP="MemberExpression",GP="ObjectExpression",WP="UnaryExpression";function pn(e){this.type=e}pn.prototype.visit=function(e){let t,n,r;if(e(this))return 1;for(t=YP(this),n=0,r=t.length;n<r;++n)if(t[n].visit(e))return 1};function YP(e){switch(e.type){case zP:return e.elements;case PP:case qP:return[e.left,e.right];case IP:return[e.callee].concat(e.arguments);case UP:return[e.test,e.consequent,e.alternate];case jP:return[e.object,e.property];case GP:return e.properties;case LP:return[e.key,e.value];case WP:return[e.argument];case NP:case TP:case OP:default:return[]}}var Bn,I,_,Qe,de,Ef=1,no=2,ni=3,kr=4,wf=5,ci=6,yt=7,ro=8,HP=9;Bn={};Bn[Ef]="Boolean";Bn[no]="<end>";Bn[ni]="Identifier";Bn[kr]="Keyword";Bn[wf]="Null";Bn[ci]="Numeric";Bn[yt]="Punctuator";Bn[ro]="String";Bn[HP]="RegularExpression";var XP="ArrayExpression",VP="BinaryExpression",KP="CallExpression",JP="ConditionalExpression",E3="Identifier",QP="Literal",ZP="LogicalExpression",eI="MemberExpression",tI="ObjectExpression",nI="Property",rI="UnaryExpression",Te="Unexpected token %0",iI="Unexpected number",aI="Unexpected string",uI="Unexpected identifier",oI="Unexpected reserved word",sI="Unexpected end of input",Jd="Invalid regular expression",mc="Invalid regular expression: missing /",w3="Octal literals are not allowed in strict mode.",lI="Duplicate data property in object literal not allowed in strict mode",Pe="ILLEGAL",wu="Disabled.",fI=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]"),cI=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B2\\u08E4-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58\\u0C59\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C81-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D57\\u0D60-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFC-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA69D\\uA69F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C4\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2D\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]");function Df(e,t){if(!e)throw new Error("ASSERT: "+t)}function Pn(e){return e>=48&&e<=57}function ep(e){return"0123456789abcdefABCDEF".indexOf(e)>=0}function eu(e){return"01234567".indexOf(e)>=0}function dI(e){return e===32||e===9||e===11||e===12||e===160||e>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0}function Du(e){return e===10||e===13||e===8232||e===8233}function io(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e===92||e>=128&&fI.test(String.fromCharCode(e))}function il(e){return e===36||e===95||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||e===92||e>=128&&cI.test(String.fromCharCode(e))}const hI={if:1,in:1,do:1,var:1,for:1,new:1,try:1,let:1,this:1,else:1,case:1,void:1,with:1,enum:1,while:1,break:1,catch:1,throw:1,const:1,yield:1,class:1,super:1,return:1,typeof:1,delete:1,switch:1,export:1,import:1,public:1,static:1,default:1,finally:1,extends:1,package:1,private:1,function:1,continue:1,debugger:1,interface:1,protected:1,instanceof:1,implements:1};function D3(){for(;_<Qe;){const e=I.charCodeAt(_);if(dI(e)||Du(e))++_;else break}}function Qd(e){var t,n,r,i=0;for(n=e==="u"?4:2,t=0;t<n;++t)_<Qe&&ep(I[_])?(r=I[_++],i=i*16+"0123456789abcdef".indexOf(r.toLowerCase())):se({},Te,Pe);return String.fromCharCode(i)}function gI(){var e,t,n,r;for(e=I[_],t=0,e==="}"&&se({},Te,Pe);_<Qe&&(e=I[_++],!!ep(e));)t=t*16+"0123456789abcdef".indexOf(e.toLowerCase());return(t>1114111||e!=="}")&&se({},Te,Pe),t<=65535?String.fromCharCode(t):(n=(t-65536>>10)+55296,r=(t-65536&1023)+56320,String.fromCharCode(n,r))}function C3(){var e,t;for(e=I.charCodeAt(_++),t=String.fromCharCode(e),e===92&&(I.charCodeAt(_)!==117&&se({},Te,Pe),++_,e=Qd("u"),(!e||e==="\\"||!io(e.charCodeAt(0)))&&se({},Te,Pe),t=e);_<Qe&&(e=I.charCodeAt(_),!!il(e));)++_,t+=String.fromCharCode(e),e===92&&(t=t.substr(0,t.length-1),I.charCodeAt(_)!==117&&se({},Te,Pe),++_,e=Qd("u"),(!e||e==="\\"||!il(e.charCodeAt(0)))&&se({},Te,Pe),t+=e);return t}function pI(){var e,t;for(e=_++;_<Qe;){if(t=I.charCodeAt(_),t===92)return _=e,C3();if(il(t))++_;else break}return I.slice(e,_)}function mI(){var e,t,n;return e=_,t=I.charCodeAt(_)===92?C3():pI(),t.length===1?n=ni:hI.hasOwnProperty(t)?n=kr:t==="null"?n=wf:t==="true"||t==="false"?n=Ef:n=ni,{type:n,value:t,start:e,end:_}}function yc(){var e=_,t=I.charCodeAt(_),n,r=I[_],i,a,u;switch(t){case 46:case 40:case 41:case 59:case 44:case 123:case 125:case 91:case 93:case 58:case 63:case 126:return++_,{type:yt,value:String.fromCharCode(t),start:e,end:_};default:if(n=I.charCodeAt(_+1),n===61)switch(t){case 43:case 45:case 47:case 60:case 62:case 94:case 124:case 37:case 38:case 42:return _+=2,{type:yt,value:String.fromCharCode(t)+String.fromCharCode(n),start:e,end:_};case 33:case 61:return _+=2,I.charCodeAt(_)===61&&++_,{type:yt,value:I.slice(e,_),start:e,end:_}}}if(u=I.substr(_,4),u===">>>=")return _+=4,{type:yt,value:u,start:e,end:_};if(a=u.substr(0,3),a===">>>"||a==="<<="||a===">>=")return _+=3,{type:yt,value:a,start:e,end:_};if(i=a.substr(0,2),r===i[1]&&"+-<>&|".indexOf(r)>=0||i==="=>")return _+=2,{type:yt,value:i,start:e,end:_};if(i==="//"&&se({},Te,Pe),"<>=!+-*%&|^/".indexOf(r)>=0)return++_,{type:yt,value:r,start:e,end:_};se({},Te,Pe)}function yI(e){let t="";for(;_<Qe&&ep(I[_]);)t+=I[_++];return t.length===0&&se({},Te,Pe),io(I.charCodeAt(_))&&se({},Te,Pe),{type:ci,value:parseInt("0x"+t,16),start:e,end:_}}function vI(e){let t="0"+I[_++];for(;_<Qe&&eu(I[_]);)t+=I[_++];return(io(I.charCodeAt(_))||Pn(I.charCodeAt(_)))&&se({},Te,Pe),{type:ci,value:parseInt(t,8),octal:!0,start:e,end:_}}function g2(){var e,t,n;if(n=I[_],Df(Pn(n.charCodeAt(0))||n===".","Numeric literal must start with a decimal digit or a decimal point"),t=_,e="",n!=="."){if(e=I[_++],n=I[_],e==="0"){if(n==="x"||n==="X")return++_,yI(t);if(eu(n))return vI(t);n&&Pn(n.charCodeAt(0))&&se({},Te,Pe)}for(;Pn(I.charCodeAt(_));)e+=I[_++];n=I[_]}if(n==="."){for(e+=I[_++];Pn(I.charCodeAt(_));)e+=I[_++];n=I[_]}if(n==="e"||n==="E")if(e+=I[_++],n=I[_],(n==="+"||n==="-")&&(e+=I[_++]),Pn(I.charCodeAt(_)))for(;Pn(I.charCodeAt(_));)e+=I[_++];else se({},Te,Pe);return io(I.charCodeAt(_))&&se({},Te,Pe),{type:ci,value:parseFloat(e),start:t,end:_}}function xI(){var e="",t,n,r,i,a=!1;for(t=I[_],Df(t==="'"||t==='"',"String literal must starts with a quote"),n=_,++_;_<Qe;)if(r=I[_++],r===t){t="";break}else if(r==="\\")if(r=I[_++],!r||!Du(r.charCodeAt(0)))switch(r){case"u":case"x":I[_]==="{"?(++_,e+=gI()):e+=Qd(r);break;case"n":e+=`
`;break;case"r":e+="\r";break;case"t":e+="	";break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:eu(r)?(i="01234567".indexOf(r),i!==0&&(a=!0),_<Qe&&eu(I[_])&&(a=!0,i=i*8+"01234567".indexOf(I[_++]),"0123".indexOf(r)>=0&&_<Qe&&eu(I[_])&&(i=i*8+"01234567".indexOf(I[_++]))),e+=String.fromCharCode(i)):e+=r;break}else r==="\r"&&I[_]===`
`&&++_;else{if(Du(r.charCodeAt(0)))break;e+=r}return t!==""&&se({},Te,Pe),{type:ro,value:e,octal:a,start:n,end:_}}function bI(e,t){let n=e;t.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}/g,(r,i)=>{if(parseInt(i,16)<=1114111)return"x";se({},Jd)}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x"));try{new RegExp(n)}catch{se({},Jd)}try{return new RegExp(e,t)}catch{return null}}function AI(){var e,t,n,r,i;for(e=I[_],Df(e==="/","Regular expression literal must start with a slash"),t=I[_++],n=!1,r=!1;_<Qe;)if(e=I[_++],t+=e,e==="\\")e=I[_++],Du(e.charCodeAt(0))&&se({},mc),t+=e;else if(Du(e.charCodeAt(0)))se({},mc);else if(n)e==="]"&&(n=!1);else if(e==="/"){r=!0;break}else e==="["&&(n=!0);return r||se({},mc),i=t.substr(1,t.length-2),{value:i,literal:t}}function EI(){var e,t,n;for(t="",n="";_<Qe&&(e=I[_],!!il(e.charCodeAt(0)));)++_,e==="\\"&&_<Qe?se({},Te,Pe):(n+=e,t+=e);return n.search(/[^gimuy]/g)>=0&&se({},Jd,n),{value:n,literal:t}}function wI(){var e,t,n,r;return de=null,D3(),e=_,t=AI(),n=EI(),r=bI(t.value,n.value),{literal:t.literal+n.literal,value:r,regex:{pattern:t.value,flags:n.value},start:e,end:_}}function DI(e){return e.type===ni||e.type===kr||e.type===Ef||e.type===wf}function F3(){if(D3(),_>=Qe)return{type:no,start:_,end:_};const e=I.charCodeAt(_);return io(e)?mI():e===40||e===41||e===59?yc():e===39||e===34?xI():e===46?Pn(I.charCodeAt(_+1))?g2():yc():Pn(e)?g2():yc()}function At(){const e=de;return _=e.end,de=F3(),_=e.end,e}function k3(){const e=_;de=F3(),_=e}function CI(e){const t=new pn(XP);return t.elements=e,t}function p2(e,t,n){const r=new pn(e==="||"||e==="&&"?ZP:VP);return r.operator=e,r.left=t,r.right=n,r}function FI(e,t){const n=new pn(KP);return n.callee=e,n.arguments=t,n}function kI(e,t,n){const r=new pn(JP);return r.test=e,r.consequent=t,r.alternate=n,r}function tp(e){const t=new pn(E3);return t.name=e,t}function Ua(e){const t=new pn(QP);return t.value=e.value,t.raw=I.slice(e.start,e.end),e.regex&&(t.raw==="//"&&(t.raw="/(?:)/"),t.regex=e.regex),t}function m2(e,t,n){const r=new pn(eI);return r.computed=e==="[",r.object=t,r.property=n,r.computed||(n.member=!0),r}function MI(e){const t=new pn(tI);return t.properties=e,t}function y2(e,t,n){const r=new pn(nI);return r.key=t,r.value=n,r.kind=e,r}function SI(e,t){const n=new pn(rI);return n.operator=e,n.argument=t,n.prefix=!0,n}function se(e,t){var n,r=Array.prototype.slice.call(arguments,2),i=t.replace(/%(\d)/g,(a,u)=>(Df(u<r.length,"Message reference must be in range"),r[u]));throw n=new Error(i),n.index=_,n.description=i,n}function Cf(e){e.type===no&&se(e,sI),e.type===ci&&se(e,iI),e.type===ro&&se(e,aI),e.type===ni&&se(e,uI),e.type===kr&&se(e,oI),se(e,Te,e.value)}function Ze(e){const t=At();(t.type!==yt||t.value!==e)&&Cf(t)}function me(e){return de.type===yt&&de.value===e}function vc(e){return de.type===kr&&de.value===e}function $I(){const e=[];for(_=de.start,Ze("[");!me("]");)me(",")?(At(),e.push(null)):(e.push(ri()),me("]")||Ze(","));return At(),CI(e)}function v2(){_=de.start;const e=At();return e.type===ro||e.type===ci?(e.octal&&se(e,w3),Ua(e)):tp(e.value)}function BI(){var e,t,n,r;if(_=de.start,e=de,e.type===ni)return n=v2(),Ze(":"),r=ri(),y2("init",n,r);if(e.type===no||e.type===yt)Cf(e);else return t=v2(),Ze(":"),r=ri(),y2("init",t,r)}function _I(){var e=[],t,n,r,i={},a=String;for(_=de.start,Ze("{");!me("}");)t=BI(),t.key.type===E3?n=t.key.name:n=a(t.key.value),r="$"+n,Object.prototype.hasOwnProperty.call(i,r)?se({},lI):i[r]=!0,e.push(t),me("}")||Ze(",");return Ze("}"),MI(e)}function RI(){Ze("(");const e=np();return Ze(")"),e}const OI={if:1};function TI(){var e,t,n;if(me("("))return RI();if(me("["))return $I();if(me("{"))return _I();if(e=de.type,_=de.start,e===ni||OI[de.value])n=tp(At().value);else if(e===ro||e===ci)de.octal&&se(de,w3),n=Ua(At());else{if(e===kr)throw new Error(wu);e===Ef?(t=At(),t.value=t.value==="true",n=Ua(t)):e===wf?(t=At(),t.value=null,n=Ua(t)):me("/")||me("/=")?(n=Ua(wI()),k3()):Cf(At())}return n}function LI(){const e=[];if(Ze("("),!me(")"))for(;_<Qe&&(e.push(ri()),!me(")"));)Ze(",");return Ze(")"),e}function NI(){_=de.start;const e=At();return DI(e)||Cf(e),tp(e.value)}function zI(){return Ze("."),NI()}function PI(){Ze("[");const e=np();return Ze("]"),e}function II(){var e,t,n;for(e=TI();;)if(me("."))n=zI(),e=m2(".",e,n);else if(me("("))t=LI(),e=FI(e,t);else if(me("["))n=PI(),e=m2("[",e,n);else break;return e}function x2(){const e=II();if(de.type===yt&&(me("++")||me("--")))throw new Error(wu);return e}function ts(){var e,t;if(de.type!==yt&&de.type!==kr)t=x2();else{if(me("++")||me("--"))throw new Error(wu);if(me("+")||me("-")||me("~")||me("!"))e=At(),t=ts(),t=SI(e.value,t);else{if(vc("delete")||vc("void")||vc("typeof"))throw new Error(wu);t=x2()}}return t}function b2(e){let t=0;if(e.type!==yt&&e.type!==kr)return 0;switch(e.value){case"||":t=1;break;case"&&":t=2;break;case"|":t=3;break;case"^":t=4;break;case"&":t=5;break;case"==":case"!=":case"===":case"!==":t=6;break;case"<":case">":case"<=":case">=":case"instanceof":case"in":t=7;break;case"<<":case">>":case">>>":t=8;break;case"+":case"-":t=9;break;case"*":case"/":case"%":t=11;break}return t}function UI(){var e,t,n,r,i,a,u,o,s,l;if(e=de,s=ts(),r=de,i=b2(r),i===0)return s;for(r.prec=i,At(),t=[e,de],u=ts(),a=[s,r,u];(i=b2(de))>0;){for(;a.length>2&&i<=a[a.length-2].prec;)u=a.pop(),o=a.pop().value,s=a.pop(),t.pop(),n=p2(o,s,u),a.push(n);r=At(),r.prec=i,a.push(r),t.push(de),n=ts(),a.push(n)}for(l=a.length-1,n=a[l],t.pop();l>1;)t.pop(),n=p2(a[l-1].value,a[l-2],n),l-=2;return n}function ri(){var e,t,n;return e=UI(),me("?")&&(At(),t=ri(),Ze(":"),n=ri(),e=kI(e,t,n)),e}function np(){const e=ri();if(me(","))throw new Error(wu);return e}function qI(e){I=e,_=0,Qe=I.length,de=null,k3();const t=np();if(de.type!==no)throw new Error("Unexpect token after expression.");return t}var jI={NaN:"NaN",E:"Math.E",LN2:"Math.LN2",LN10:"Math.LN10",LOG2E:"Math.LOG2E",LOG10E:"Math.LOG10E",PI:"Math.PI",SQRT1_2:"Math.SQRT1_2",SQRT2:"Math.SQRT2",MIN_VALUE:"Number.MIN_VALUE",MAX_VALUE:"Number.MAX_VALUE"};function GI(e){function t(u,o,s,l){let f=e(o[0]);return s&&(f=s+"("+f+")",s.lastIndexOf("new ",0)===0&&(f="("+f+")")),f+"."+u+(l<0?"":l===0?"()":"("+o.slice(1).map(e).join(",")+")")}function n(u,o,s){return l=>t(u,l,o,s)}const r="new Date",i="String",a="RegExp";return{isNaN:"Number.isNaN",isFinite:"Number.isFinite",abs:"Math.abs",acos:"Math.acos",asin:"Math.asin",atan:"Math.atan",atan2:"Math.atan2",ceil:"Math.ceil",cos:"Math.cos",exp:"Math.exp",floor:"Math.floor",log:"Math.log",max:"Math.max",min:"Math.min",pow:"Math.pow",random:"Math.random",round:"Math.round",sin:"Math.sin",sqrt:"Math.sqrt",tan:"Math.tan",clamp:function(u){u.length<3&&S("Missing arguments to clamp function."),u.length>3&&S("Too many arguments to clamp function.");const o=u.map(e);return"Math.max("+o[1]+", Math.min("+o[2]+","+o[0]+"))"},now:"Date.now",utc:"Date.UTC",datetime:r,date:n("getDate",r,0),day:n("getDay",r,0),year:n("getFullYear",r,0),month:n("getMonth",r,0),hours:n("getHours",r,0),minutes:n("getMinutes",r,0),seconds:n("getSeconds",r,0),milliseconds:n("getMilliseconds",r,0),time:n("getTime",r,0),timezoneoffset:n("getTimezoneOffset",r,0),utcdate:n("getUTCDate",r,0),utcday:n("getUTCDay",r,0),utcyear:n("getUTCFullYear",r,0),utcmonth:n("getUTCMonth",r,0),utchours:n("getUTCHours",r,0),utcminutes:n("getUTCMinutes",r,0),utcseconds:n("getUTCSeconds",r,0),utcmilliseconds:n("getUTCMilliseconds",r,0),length:n("length",null,-1),parseFloat:"parseFloat",parseInt:"parseInt",upper:n("toUpperCase",i,0),lower:n("toLowerCase",i,0),substring:n("substring",i),split:n("split",i),trim:n("trim",i,0),regexp:a,test:n("test",a),if:function(u){u.length<3&&S("Missing arguments to if function."),u.length>3&&S("Too many arguments to if function.");const o=u.map(e);return"("+o[0]+"?"+o[1]+":"+o[2]+")"}}}function WI(e){const t=e&&e.length-1;return t&&(e[0]==='"'&&e[t]==='"'||e[0]==="'"&&e[t]==="'")?e.slice(1,-1):e}function YI(e){e=e||{};const t=e.allowed?Tt(e.allowed):{},n=e.forbidden?Tt(e.forbidden):{},r=e.constants||jI,i=(e.functions||GI)(c),a=e.globalvar,u=e.fieldvar,o=J(a)?a:g=>`${a}["${g}"]`;let s={},l={},f=0;function c(g){if(fe(g))return g;const p=d[g.type];return p==null&&S("Unsupported type: "+g.type),p(g)}const d={Literal:g=>g.raw,Identifier:g=>{const p=g.name;return f>0?p:G(n,p)?S("Illegal identifier: "+p):G(r,p)?r[p]:G(t,p)?p:(s[p]=1,o(p))},MemberExpression:g=>{const p=!g.computed,m=c(g.object);p&&(f+=1);const y=c(g.property);return m===u&&(l[WI(y)]=1),p&&(f-=1),m+(p?"."+y:"["+y+"]")},CallExpression:g=>{g.callee.type!=="Identifier"&&S("Illegal callee type: "+g.callee.type);const p=g.callee.name,m=g.arguments,y=G(i,p)&&i[p];return y||S("Unrecognized function: "+p),J(y)?y(m):y+"("+m.map(c).join(",")+")"},ArrayExpression:g=>"["+g.elements.map(c).join(",")+"]",BinaryExpression:g=>"("+c(g.left)+" "+g.operator+" "+c(g.right)+")",UnaryExpression:g=>"("+g.operator+c(g.argument)+")",ConditionalExpression:g=>"("+c(g.test)+"?"+c(g.consequent)+":"+c(g.alternate)+")",LogicalExpression:g=>"("+c(g.left)+g.operator+c(g.right)+")",ObjectExpression:g=>"{"+g.properties.map(c).join(",")+"}",Property:g=>{f+=1;const p=c(g.key);return f-=1,p+":"+c(g.value)}};function h(g){const p={code:c(g),globals:Object.keys(s),fields:Object.keys(l)};return s={},l={},p}return h.functions=i,h.constants=r,h}var HI="5.23.0";Z(Ni,Rk,xS,JS,M$,T$,sB,U$,fB,gB,DB,BB);const EU=Object.freeze(Object.defineProperty({__proto__:null,Bounds:we,CanvasHandler:Xu,CanvasRenderer:Gs,DATE:kt,DAY:He,DAYOFYEAR:Dn,Dataflow:$i,Debug:sx,Error:Bh,EventStream:Ml,Gradient:r1,GroupItem:Xl,HOURS:Lt,Handler:Ar,Info:ox,Item:Hl,MILLISECONDS:fn,MINUTES:Nt,MONTH:Ve,Marks:Pt,MultiPulse:d0,None:ax,Operator:ge,Parameters:kl,Pulse:gr,QUARTER:Ft,RenderType:fr,Renderer:Mn,ResourceLoader:o1,SECONDS:Vt,SVGHandler:C1,SVGRenderer:B1,SVGStringRenderer:_1,Scenegraph:w1,TIME_UNITS:Vh,Transform:$,View:Sw,WEEK:Se,Warn:ux,YEAR:Ie,accessor:Mt,accessorFields:Xe,accessorName:Ee,array:q,ascending:xl,bandwidthNRD:m0,bin:Qx,bootstrapCI:Zx,boundClip:QA,boundContext:Gu,boundItem:pd,boundMark:DA,boundStroke:Xn,changeset:ui,clampRange:Ph,codegenExpression:YI,compare:Ih,constant:tt,cumulativeLogNormal:_l,cumulativeNormal:_u,cumulativeUniform:Ll,dayofyear:Zh,debounce:Uh,defaultLocale:o0,definition:Vx,densityLogNormal:Bl,densityNormal:Sl,densityUniform:Tl,domChild:xt,domClear:nn,domCreate:sr,domFind:D1,dotbin:eb,error:S,expressionFunction:ke,extend:Z,extent:ln,extentIndex:px,falsy:Qn,fastmap:Ki,field:Ot,flush:qh,font:Zl,fontFamily:Hu,fontSize:kn,format:Lo,formatLocale:Rs,formats:f0,hasOwnProperty:G,id:$u,identity:et,inferType:zx,inferTypes:Px,ingest:le,inherits:T,inrange:Ir,interpolate:Q0,interpolateColors:Wl,interpolateRange:Hb,intersect:R1,intersectBoxLine:Fi,intersectPath:s1,intersectPoint:l1,intersectRule:hA,isArray:z,isBoolean:jh,isDate:Gn,isFunction:J,isIterable:mx,isNumber:Yn,isObject:K,isRegExp:Gh,isString:fe,isTuple:ea,key:Wh,lerp:Yh,lineHeight:xr,loader:Dl,locale:Lx,logger:lx,lruCache:yx,markup:$1,merge:vx,mergeConfig:fx,multiLineOffset:b1,one:Vi,pad:Hh,panLinear:Rh,panLog:Oh,panPow:Th,panSymlog:Lh,parse:RP,parseExpression:qI,parseSelector:Ww,path:uh,pathCurves:i1,pathEqual:ZA,pathParse:Qr,pathRectangle:sA,pathRender:Ui,pathSymbols:oA,pathTrail:lA,peek:re,point:tf,projection:W1,quantileLogNormal:Rl,quantileNormal:Ru,quantileUniform:Nl,quantiles:g0,quantizeInterpolator:Xb,quarter:Nh,quartiles:p0,get random(){return zt},randomInteger:N9,randomKDE:v0,randomLCG:L9,randomLogNormal:nb,randomMixture:rb,randomNormal:y0,randomUniform:ib,read:qx,regressionExp:ub,regressionLinear:x0,regressionLoess:lb,regressionLog:ab,regressionPoly:sb,regressionPow:ob,regressionQuad:b0,renderModule:nf,repeat:$a,resetDefaultLocale:R8,resetSVGClipId:cA,resetSVGDefIds:N4,responseType:Ux,runtimeContext:mw,sampleCurve:Pl,sampleLogNormal:$l,sampleNormal:Bu,sampleUniform:Ol,scale:ue,sceneEqual:O1,sceneFromJSON:FA,scenePickVisit:zs,sceneToJSON:CA,sceneVisit:cn,sceneZOrder:f1,scheme:Z0,serializeXML:UA,setRandom:O9,span:Ji,splitAccessPath:hl,stringValue:U,textMetrics:on,timeBin:Sx,timeFloor:wx,timeFormatLocale:lu,timeInterval:Qi,timeOffset:r0,timeSequence:a0,timeUnitSpecifier:Jh,timeUnits:Kh,toBoolean:bl,toDate:Al,toNumber:Ne,toSet:Tt,toString:El,transform:Kx,transforms:Ni,truncate:Xh,truthy:Ct,tupleid:Y,typeParsers:ad,utcFloor:Dx,utcInterval:Zi,utcOffset:i0,utcSequence:u0,utcdayofyear:t0,utcquarter:zh,utcweek:n0,version:HI,visitArray:tr,week:e0,writeConfig:_h,zero:ir,zoomLinear:ml,zoomLog:yl,zoomPow:su,zoomSymlog:vl},Symbol.toStringTag,{value:"Module"}));function XI(e,t,n){let r;t.x2&&(t.x?(n&&e.x>e.x2&&(r=e.x,e.x=e.x2,e.x2=r),e.width=e.x2-e.x):e.x=e.x2-(e.width||0)),t.xc&&(e.x=e.xc-(e.width||0)/2),t.y2&&(t.y?(n&&e.y>e.y2&&(r=e.y,e.y=e.y2,e.y2=r),e.height=e.y2-e.y):e.y=e.y2-(e.height||0)),t.yc&&(e.y=e.yc-(e.height||0)/2)}var VI={NaN:NaN,E:Math.E,LN2:Math.LN2,LN10:Math.LN10,LOG2E:Math.LOG2E,LOG10E:Math.LOG10E,PI:Math.PI,SQRT1_2:Math.SQRT1_2,SQRT2:Math.SQRT2,MIN_VALUE:Number.MIN_VALUE,MAX_VALUE:Number.MAX_VALUE},KI={"*":(e,t)=>e*t,"+":(e,t)=>e+t,"-":(e,t)=>e-t,"/":(e,t)=>e/t,"%":(e,t)=>e%t,">":(e,t)=>e>t,"<":(e,t)=>e<t,"<=":(e,t)=>e<=t,">=":(e,t)=>e>=t,"==":(e,t)=>e==t,"!=":(e,t)=>e!=t,"===":(e,t)=>e===t,"!==":(e,t)=>e!==t,"&":(e,t)=>e&t,"|":(e,t)=>e|t,"^":(e,t)=>e^t,"<<":(e,t)=>e<<t,">>":(e,t)=>e>>t,">>>":(e,t)=>e>>>t},JI={"+":e=>+e,"-":e=>-e,"~":e=>~e,"!":e=>!e};const QI=Array.prototype.slice,Br=(e,t,n)=>{const r=n?n(t[0]):t[0];return r[e].apply(r,QI.call(t,1))},ZI=(e,t,n,r,i,a,u)=>new Date(e,t||0,n??1,r||0,i||0,a||0,u||0);var eU={isNaN:Number.isNaN,isFinite:Number.isFinite,abs:Math.abs,acos:Math.acos,asin:Math.asin,atan:Math.atan,atan2:Math.atan2,ceil:Math.ceil,cos:Math.cos,exp:Math.exp,floor:Math.floor,log:Math.log,max:Math.max,min:Math.min,pow:Math.pow,random:Math.random,round:Math.round,sin:Math.sin,sqrt:Math.sqrt,tan:Math.tan,clamp:(e,t,n)=>Math.max(t,Math.min(n,e)),now:Date.now,utc:Date.UTC,datetime:ZI,date:e=>new Date(e).getDate(),day:e=>new Date(e).getDay(),year:e=>new Date(e).getFullYear(),month:e=>new Date(e).getMonth(),hours:e=>new Date(e).getHours(),minutes:e=>new Date(e).getMinutes(),seconds:e=>new Date(e).getSeconds(),milliseconds:e=>new Date(e).getMilliseconds(),time:e=>new Date(e).getTime(),timezoneoffset:e=>new Date(e).getTimezoneOffset(),utcdate:e=>new Date(e).getUTCDate(),utcday:e=>new Date(e).getUTCDay(),utcyear:e=>new Date(e).getUTCFullYear(),utcmonth:e=>new Date(e).getUTCMonth(),utchours:e=>new Date(e).getUTCHours(),utcminutes:e=>new Date(e).getUTCMinutes(),utcseconds:e=>new Date(e).getUTCSeconds(),utcmilliseconds:e=>new Date(e).getUTCMilliseconds(),length:e=>e.length,join:function(){return Br("join",arguments)},indexof:function(){return Br("indexOf",arguments)},lastindexof:function(){return Br("lastIndexOf",arguments)},slice:function(){return Br("slice",arguments)},reverse:e=>e.slice().reverse(),parseFloat,parseInt,upper:e=>String(e).toUpperCase(),lower:e=>String(e).toLowerCase(),substring:function(){return Br("substring",arguments,String)},split:function(){return Br("split",arguments,String)},replace:function(){return Br("replace",arguments,String)},trim:e=>String(e).trim(),regexp:RegExp,test:(e,t)=>RegExp(e).test(t)};const tU=["view","item","group","xy","x","y"],Zd=new Set([Function,eval,setTimeout,setInterval]);typeof setImmediate=="function"&&Zd.add(setImmediate);const nU={Literal:(e,t)=>t.value,Identifier:(e,t)=>{const n=t.name;return e.memberDepth>0?n:n==="datum"?e.datum:n==="event"?e.event:n==="item"?e.item:VI[n]||e.params["$"+n]},MemberExpression:(e,t)=>{const n=!t.computed,r=e(t.object);n&&(e.memberDepth+=1);const i=e(t.property);if(n&&(e.memberDepth-=1),Zd.has(r[i])){console.error(`Prevented interpretation of member "${i}" which could lead to insecure code execution`);return}return r[i]},CallExpression:(e,t)=>{const n=t.arguments;let r=t.callee.name;return r.startsWith("_")&&(r=r.slice(1)),r==="if"?e(n[0])?e(n[1]):e(n[2]):(e.fn[r]||eU[r]).apply(e.fn,n.map(e))},ArrayExpression:(e,t)=>t.elements.map(e),BinaryExpression:(e,t)=>KI[t.operator](e(t.left),e(t.right)),UnaryExpression:(e,t)=>JI[t.operator](e(t.argument)),ConditionalExpression:(e,t)=>e(t.test)?e(t.consequent):e(t.alternate),LogicalExpression:(e,t)=>t.operator==="&&"?e(t.left)&&e(t.right):e(t.left)||e(t.right),ObjectExpression:(e,t)=>t.properties.reduce((n,r)=>{e.memberDepth+=1;const i=e(r.key);return e.memberDepth-=1,Zd.has(e(r.value))?console.error(`Prevented interpretation of property "${i}" which could lead to insecure code execution`):n[i]=e(r.value),n},{})};function Ea(e,t,n,r,i,a){const u=o=>nU[o.type](u,o);return u.memberDepth=0,u.fn=Object.create(t),u.params=n,u.datum=r,u.event=i,u.item=a,tU.forEach(o=>u.fn[o]=function(){return i.vega[o](...arguments)}),u(e)}var wU={operator(e,t){const n=t.ast,r=e.functions;return i=>Ea(n,r,i)},parameter(e,t){const n=t.ast,r=e.functions;return(i,a)=>Ea(n,r,a,i)},event(e,t){const n=t.ast,r=e.functions;return i=>Ea(n,r,void 0,void 0,i)},handler(e,t){const n=t.ast,r=e.functions;return(i,a)=>{const u=a.item&&a.item.datum;return Ea(n,r,i,u,a)}},encode(e,t){const{marktype:n,channels:r}=t,i=e.functions,a=n==="group"||n==="image"||n==="rect";return(u,o)=>{const s=u.datum;let l=0,f;for(const c in r)f=Ea(r[c].ast,i,o,s,void 0,u),u[c]!==f&&(u[c]=f,l=1);return n!=="rule"&&XI(u,r,a),l}}};function DU(e){const[t,n]=/schema\/([\w-]+)\/([\w\.\-]+)\.json$/g.exec(e).slice(1,3);return{library:t,version:n}}function rU(e,t,n,r){if(z(e))return`[${e.map(i=>t(fe(i)?i:A2(i,n))).join(", ")}]`;if(K(e)){let i="";const{title:a,image:u,...o}=e;a&&(i+=`<h2>${t(a)}</h2>`),u&&(i+=`<img src="${new URL(t(u),r||location.href).href}">`);const s=Object.keys(o);if(s.length>0){i+="<table>";for(const l of s){let f=o[l];f!==void 0&&(K(f)&&(f=A2(f,n)),i+=`<tr><td class="key">${t(l)}</td><td class="value">${t(f)}</td></tr>`)}i+="</table>"}return i||"{}"}return t(e)}function iU(e){const t=[];return function(n,r){if(typeof r!="object"||r===null)return r;const i=t.indexOf(this)+1;return t.length=i,t.length>e?"[Object]":t.indexOf(r)>=0?"[Circular]":(t.push(r),r)}}function A2(e,t){return JSON.stringify(e,iU(t))}var aU=`#vg-tooltip-element {
  visibility: hidden;
  padding: 8px;
  position: fixed;
  z-index: 1000;
  font-family: sans-serif;
  font-size: 11px;
  border-radius: 3px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  /* The default theme is the light theme. */
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #d9d9d9;
  color: black;
}
#vg-tooltip-element.visible {
  visibility: visible;
}
#vg-tooltip-element h2 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 13px;
}
#vg-tooltip-element table {
  border-spacing: 0;
}
#vg-tooltip-element table tr {
  border: none;
}
#vg-tooltip-element table tr td {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-top: 2px;
  padding-bottom: 2px;
}
#vg-tooltip-element table tr td.key {
  color: #808080;
  max-width: 150px;
  text-align: right;
  padding-right: 4px;
}
#vg-tooltip-element table tr td.value {
  display: block;
  max-width: 300px;
  max-height: 7em;
  text-align: left;
}
#vg-tooltip-element.dark-theme {
  background-color: rgba(32, 32, 32, 0.9);
  border: 1px solid #f5f5f5;
  color: white;
}
#vg-tooltip-element.dark-theme td.key {
  color: #bfbfbf;
}
`;const M3="vg-tooltip-element",uU={offsetX:10,offsetY:10,id:M3,styleId:"vega-tooltip-style",theme:"light",disableDefaultStyle:!1,sanitize:oU,maxDepth:2,formatTooltip:rU,baseURL:""};function oU(e){return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;")}function sU(e){if(!/^[A-Za-z]+[-:.\w]*$/.test(e))throw new Error("Invalid HTML ID");return aU.toString().replace(M3,e)}function lU(e,t,n,r){let i=e.clientX+n;i+t.width>window.innerWidth&&(i=+e.clientX-n-t.width);let a=e.clientY+r;return a+t.height>window.innerHeight&&(a=+e.clientY-r-t.height),{x:i,y:a}}class CU{constructor(t){this.options={...uU,...t};const n=this.options.id;if(this.el=null,this.call=this.tooltipHandler.bind(this),!this.options.disableDefaultStyle&&!document.getElementById(this.options.styleId)){const r=document.createElement("style");r.setAttribute("id",this.options.styleId),r.innerHTML=sU(n);const i=document.head;i.childNodes.length>0?i.insertBefore(r,i.childNodes[0]):i.appendChild(r)}}tooltipHandler(t,n,r,i){if(this.el=document.getElementById(this.options.id),this.el||(this.el=document.createElement("div"),this.el.setAttribute("id",this.options.id),this.el.classList.add("vg-tooltip"),(document.fullscreenElement??document.body).appendChild(this.el)),i==null||i===""){this.el.classList.remove("visible",`${this.options.theme}-theme`);return}this.el.innerHTML=this.options.formatTooltip(i,this.options.sanitize,this.options.maxDepth,this.options.baseURL),this.el.classList.add("visible",`${this.options.theme}-theme`);const{x:a,y:u}=lU(n,this.el.getBoundingClientRect(),this.options.offsetX,this.options.offsetY);this.el.style.top=`${u}px`,this.el.style.left=`${a}px`}}export{U as $,CU as H,ux as W,fe as a,K as b,jh as c,z as d,q as e,J as f,Ww as g,G as h,Yn as i,et as j,DU as k,lx as l,fx as m,Mh as n,wU as o,z_ as p,AU as q,hl as s,EU as v,_h as w};
//# sourceMappingURL=vega-tooltip.module-DosJ6Ifo.js.map
