#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建用户使用统计表的迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.main import app, db
from backend.models.user_usage import UserUsage

def create_user_usage_table():
    """创建用户使用统计表"""
    
    print("🔄 开始创建用户使用统计表...")
    
    try:
        with app.app_context():
            # 检查表是否已存在
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            if 'user_usage' in existing_tables:
                print("⚠️ 表 'user_usage' 已存在，跳过创建")
                return True
            
            # 创建表
            db.create_all()
            
            print("✅ 用户使用统计表创建成功！")
            
            # 验证表结构
            print("\n📋 表结构验证:")
            columns = inspector.get_columns('user_usage')
            for column in columns:
                print(f"   - {column['name']}: {column['type']}")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def test_user_usage_model():
    """测试用户使用统计模型"""
    
    print("\n🧪 测试用户使用统计模型...")
    
    try:
        with app.app_context():
            # 假设用户ID为1（需要确保用户存在）
            from backend.models.user import User
            
            # 检查是否有用户
            user = User.query.first()
            if not user:
                print("⚠️ 没有找到用户，跳过模型测试")
                return True
            
            print(f"📝 使用用户: {user.username} (ID: {user.id})")
            
            # 测试获取或创建今日使用记录
            usage = UserUsage.get_or_create_today_usage(user.id)
            print(f"✅ 获取今日使用记录: {usage}")
            
            # 测试权限检查
            can_download = usage.can_download(user)
            can_api_call = usage.can_api_call(user)
            
            print(f"📊 权限检查:")
            print(f"   - 可以下载: {can_download}")
            print(f"   - 可以API调用: {can_api_call}")
            
            # 测试剩余次数
            remaining_downloads = usage.get_remaining_downloads(user)
            remaining_api_calls = usage.get_remaining_api_calls_this_minute(user)
            
            print(f"📊 剩余次数:")
            print(f"   - 剩余下载: {remaining_downloads}")
            print(f"   - 剩余API调用: {remaining_api_calls}")
            
            # 测试用户权限
            permissions = user.get_permissions()
            print(f"📊 用户权限:")
            print(f"   - 下载限制: {permissions.get('download_limit', 'N/A')}")
            print(f"   - API限制: {permissions.get('api_rate_limit', 'N/A')}")
            
            return True
            
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_info():
    """显示使用信息"""
    
    print("\n📖 使用说明:")
    print("1. 这个脚本创建了 'user_usage' 表来记录用户使用统计")
    print("2. 表结构包含:")
    print("   - user_id: 用户ID")
    print("   - date: 日期")
    print("   - download_count: 当日下载次数")
    print("   - api_call_count: 当日API调用次数")
    print("   - api_calls_this_minute: 本分钟API调用次数")
    print("   - current_minute: 当前分钟开始时间")
    print("\n3. 限制配置:")
    print("   - 普通用户: 5次/天下载，3次/分钟API")
    print("   - Pro用户: 50次/天下载，10次/分钟API")
    print("   - 超级管理员: 无限制")
    print("\n4. 每日凌晨12点自动重置计数器")

if __name__ == "__main__":
    print("🚀 用户使用统计表创建脚本")
    print("=" * 50)
    
    # 创建表
    success = create_user_usage_table()
    
    if success:
        # 测试模型
        test_success = test_user_usage_model()
        
        if test_success:
            print("\n🎉 所有操作完成！")
            show_usage_info()
        else:
            print("\n⚠️ 表创建成功，但模型测试失败")
    else:
        print("\n❌ 表创建失败")
        sys.exit(1)
