import"./index-Ccc2t4AG.js";import{u as T}from"./utils-BsGrhMNe.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";import{B as W}from"./BlockLabel-3KxTaaiM.js";import{I as j}from"./IconButton-C_HS7fTi.js";import{E as G}from"./Empty-ZqppqzTN.js";import{S as J}from"./ShareButton-q6iG5u0X.js";import{D as K}from"./Download-DVtk-Jv3.js";import{M as E}from"./Music-CDm0RGMk.js";import{I as O}from"./IconButtonWrapper--EIOWuEM.js";import{A as Q}from"./AudioPlayer-UyFUW52_.js";import{D as R}from"./DownloadLink-QIttOhoR.js";import"./svelte/svelte.js";import"./prism-python-D8O99YiR.js";import"./Community-Dw1micSV.js";import"./Trim-JQYgj7Jd.js";import"./Play-B0Q0U1Qz.js";import"./Undo-DCjBnnSO.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./hls-CnVhpNcu.js";const{SvelteComponent:U,bubble:$,check_outros:I,create_component:b,destroy_component:w,detach:v,empty:N,flush:p,group_outros:B,init:V,insert:y,mount_component:h,safe_not_equal:X,space:D,transition_in:f,transition_out:m}=window.__gradio__svelte__internal,{createEventDispatcher:Y}=window.__gradio__svelte__internal;function Z(r){let e,l;return e=new G({props:{size:"small",$$slots:{default:[ee]},$$scope:{ctx:r}}}),{c(){b(e.$$.fragment)},m(t,n){h(e,t,n),l=!0},p(t,n){const o={};n&524288&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){l||(f(e.$$.fragment,t),l=!0)},o(t){m(e.$$.fragment,t),l=!1},d(t){w(e,t)}}}function x(r){let e,l,t,n;return e=new O({props:{display_top_corner:r[10],$$slots:{default:[oe]},$$scope:{ctx:r}}}),t=new Q({props:{value:r[0],label:r[1],i18n:r[5],waveform_settings:r[6],waveform_options:r[7],editable:r[8],loop:r[9]}}),t.$on("pause",r[14]),t.$on("play",r[15]),t.$on("stop",r[16]),t.$on("load",r[17]),{c(){b(e.$$.fragment),l=D(),b(t.$$.fragment)},m(o,i){h(e,o,i),y(o,l,i),h(t,o,i),n=!0},p(o,i){const _={};i&1024&&(_.display_top_corner=o[10]),i&524345&&(_.$$scope={dirty:i,ctx:o}),e.$set(_);const u={};i&1&&(u.value=o[0]),i&2&&(u.label=o[1]),i&32&&(u.i18n=o[5]),i&64&&(u.waveform_settings=o[6]),i&128&&(u.waveform_options=o[7]),i&256&&(u.editable=o[8]),i&512&&(u.loop=o[9]),t.$set(u)},i(o){n||(f(e.$$.fragment,o),f(t.$$.fragment,o),n=!0)},o(o){m(e.$$.fragment,o),m(t.$$.fragment,o),n=!1},d(o){o&&v(l),w(e,o),w(t,o)}}}function ee(r){let e,l;return e=new E({}),{c(){b(e.$$.fragment)},m(t,n){h(e,t,n),l=!0},i(t){l||(f(e.$$.fragment,t),l=!0)},o(t){m(e.$$.fragment,t),l=!1},d(t){w(e,t)}}}function S(r){let e,l;return e=new R({props:{href:r[0].is_stream?r[0].url?.replace("playlist.m3u8","playlist-file"):r[0].url,download:r[0].orig_name||r[0].path,$$slots:{default:[te]},$$scope:{ctx:r}}}),{c(){b(e.$$.fragment)},m(t,n){h(e,t,n),l=!0},p(t,n){const o={};n&1&&(o.href=t[0].is_stream?t[0].url?.replace("playlist.m3u8","playlist-file"):t[0].url),n&1&&(o.download=t[0].orig_name||t[0].path),n&524320&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){l||(f(e.$$.fragment,t),l=!0)},o(t){m(e.$$.fragment,t),l=!1},d(t){w(e,t)}}}function te(r){let e,l;return e=new j({props:{Icon:K,label:r[5]("common.download")}}),{c(){b(e.$$.fragment)},m(t,n){h(e,t,n),l=!0},p(t,n){const o={};n&32&&(o.label=t[5]("common.download")),e.$set(o)},i(t){l||(f(e.$$.fragment,t),l=!0)},o(t){m(e.$$.fragment,t),l=!1},d(t){w(e,t)}}}function A(r){let e,l;return e=new J({props:{i18n:r[5],formatter:r[11],value:r[0]}}),e.$on("error",r[12]),e.$on("share",r[13]),{c(){b(e.$$.fragment)},m(t,n){h(e,t,n),l=!0},p(t,n){const o={};n&32&&(o.i18n=t[5]),n&1&&(o.value=t[0]),e.$set(o)},i(t){l||(f(e.$$.fragment,t),l=!0)},o(t){m(e.$$.fragment,t),l=!1},d(t){w(e,t)}}}function oe(r){let e,l,t,n=r[3]&&S(r),o=r[4]&&A(r);return{c(){n&&n.c(),e=D(),o&&o.c(),l=N()},m(i,_){n&&n.m(i,_),y(i,e,_),o&&o.m(i,_),y(i,l,_),t=!0},p(i,_){i[3]?n?(n.p(i,_),_&8&&f(n,1)):(n=S(i),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(B(),m(n,1,1,()=>{n=null}),I()),i[4]?o?(o.p(i,_),_&16&&f(o,1)):(o=A(i),o.c(),f(o,1),o.m(l.parentNode,l)):o&&(B(),m(o,1,1,()=>{o=null}),I())},i(i){t||(f(n),f(o),t=!0)},o(i){m(n),m(o),t=!1},d(i){i&&(v(e),v(l)),n&&n.d(i),o&&o.d(i)}}}function ne(r){let e,l,t,n,o,i;e=new W({props:{show_label:r[2],Icon:E,float:!1,label:r[1]||r[5]("audio.audio")}});const _=[x,Z],u=[];function k(s,c){return s[0]!==null?0:1}return t=k(r),n=u[t]=_[t](r),{c(){b(e.$$.fragment),l=D(),n.c(),o=N()},m(s,c){h(e,s,c),y(s,l,c),u[t].m(s,c),y(s,o,c),i=!0},p(s,[c]){const d={};c&4&&(d.show_label=s[2]),c&34&&(d.label=s[1]||s[5]("audio.audio")),e.$set(d);let g=t;t=k(s),t===g?u[t].p(s,c):(B(),m(u[g],1,1,()=>{u[g]=null}),I(),n=u[t],n?n.p(s,c):(n=u[t]=_[t](s),n.c()),f(n,1),n.m(o.parentNode,o))},i(s){i||(f(e.$$.fragment,s),f(n),i=!0)},o(s){m(e.$$.fragment,s),m(n),i=!1},d(s){s&&(v(l),v(o)),w(e,s),u[t].d(s)}}}function re(r,e,l){let{value:t=null}=e,{label:n}=e,{show_label:o=!0}=e,{show_download_button:i=!0}=e,{show_share_button:_=!1}=e,{i18n:u}=e,{waveform_settings:k={}}=e,{waveform_options:s={show_recording_waveform:!0}}=e,{editable:c=!0}=e,{loop:d}=e,{display_icon_button_wrapper_top_corner:g=!1}=e;const L=Y(),M=async a=>a?`<audio controls src="${await T(a.url)}"></audio>`:"";function q(a){$.call(this,r,a)}function z(a){$.call(this,r,a)}function C(a){$.call(this,r,a)}function F(a){$.call(this,r,a)}function H(a){$.call(this,r,a)}function P(a){$.call(this,r,a)}return r.$$set=a=>{"value"in a&&l(0,t=a.value),"label"in a&&l(1,n=a.label),"show_label"in a&&l(2,o=a.show_label),"show_download_button"in a&&l(3,i=a.show_download_button),"show_share_button"in a&&l(4,_=a.show_share_button),"i18n"in a&&l(5,u=a.i18n),"waveform_settings"in a&&l(6,k=a.waveform_settings),"waveform_options"in a&&l(7,s=a.waveform_options),"editable"in a&&l(8,c=a.editable),"loop"in a&&l(9,d=a.loop),"display_icon_button_wrapper_top_corner"in a&&l(10,g=a.display_icon_button_wrapper_top_corner)},r.$$.update=()=>{r.$$.dirty&1&&t&&L("change",t)},[t,n,o,i,_,u,k,s,c,d,g,M,q,z,C,F,H,P]}class Ae extends U{constructor(e){super(),V(this,e,re,ne,X,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8,loop:9,display_icon_button_wrapper_top_corner:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),p()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),p()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),p()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),p()}get show_share_button(){return this.$$.ctx[4]}set show_share_button(e){this.$$set({show_share_button:e}),p()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),p()}get waveform_settings(){return this.$$.ctx[6]}set waveform_settings(e){this.$$set({waveform_settings:e}),p()}get waveform_options(){return this.$$.ctx[7]}set waveform_options(e){this.$$set({waveform_options:e}),p()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),p()}get loop(){return this.$$.ctx[9]}set loop(e){this.$$set({loop:e}),p()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),p()}}export{Ae as default};
//# sourceMappingURL=StaticAudio-C_goMxpb.js.map
