2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:50:45,859 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:45,860 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:46,353 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:47,063 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:47,069 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:47,103 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:47,116 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:47,121 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:47,124 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:47,127 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:47,132 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:47,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:47,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 20:50:47,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 20:50:47,233 - werkzeug - INFO -  * Restarting with stat
2025-05-27 20:50:48,289 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:48,291 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:48,801 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:49,431 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:49,436 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:49,462 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:49,474 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:49,489 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:49,500 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:49,504 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:49,510 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:49,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:49,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 20:50:49,657 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 20:51:42,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:42,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,647 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,675 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:42,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,713 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:42,714 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:45,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,262 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,274 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:45,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,394 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:45,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:45,571 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:46,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:56:46,479 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:56:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:01:46,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:01:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:02:08,756 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:09,363 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:10,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:10,400 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:10,935 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:11,615 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:11,619 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:11,640 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:11,649 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:11,654 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:11,669 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:11,672 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:11,679 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:11,685 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:11,729 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:11,788 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:11,823 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:25,063 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:25,243 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:26,004 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:26,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:26,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:26,413 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:26,861 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:26,865 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:26,881 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:26,934 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:26,943 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:26,957 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:26,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:26,965 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:26,969 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:26,995 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:27,016 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:27,029 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:50,371 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 21:02:50,545 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:51,261 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:51,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:51,669 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:52,131 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:52,135 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:52,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:52,192 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:52,206 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:52,211 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:52,215 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:52,218 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:52,223 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:52,246 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:52,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:52,286 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:06:22,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET / HTTP/1.1" 200 -
2025-05-27 21:06:22,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 21:06:22,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,747 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:22,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:23,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,266 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:06:24,207 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:24,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:29,712 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:06:30,362 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:06:34,567 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:06:34,569 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:34] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:06:43,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:43,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,419 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:08:04,841 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:05,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:06,115 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:06,116 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:06,117 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:06,573 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:07,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:07,155 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:07,219 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:07,228 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:07,244 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:07,255 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:07,259 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:07,263 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:07,268 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:07,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:07,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:07,357 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:08:18,583 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:18,946 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:19,684 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:19,686 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:20,063 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:20,461 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:20,464 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:20,475 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:20,482 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:20,487 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:20,499 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:20,503 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:20,505 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:20,507 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:20,527 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:20,546 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:20,558 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:11:24,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:11:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:16:24,265 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:16:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,008 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 21:19:55,778 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:05,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:05,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:11,609 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:11,654 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:21:24,255 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:21:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:26:24,309 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:26:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:31:24,244 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:31:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:04,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET / HTTP/1.1" 200 -
2025-05-27 21:32:04,680 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,710 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,774 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,788 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,808 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 21:32:04,827 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,973 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,001 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,497 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,509 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,610 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:32:06,547 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:06,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:10,762 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:32:11,323 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 21:32:15,800 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 21:32:15,802 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:15] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:32:19,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:19,942 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:28,851 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:32:33,053 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:32:33,054 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:33] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:32:36,042 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:36,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:36:10,855 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:36:11,646 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:16,204 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:16,207 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:16,790 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:17,577 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:17,584 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:17,622 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:17,633 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:17,638 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:17,640 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:17,644 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:17,648 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:17,675 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:17,721 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:40:17,722 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:40:17,724 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:18,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:18,435 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:18,436 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:18,950 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:19,450 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:19,453 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:19,467 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:19,472 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:19,477 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:19,479 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:19,482 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:19,485 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:19,505 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:19,524 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:40:19,546 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:41:05,247 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:05,249 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:05,249 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:05,628 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:06,039 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:06,043 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:06,062 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:06,067 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:06,073 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:06,074 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:06,079 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:06,081 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:06,101 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:06,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:41:06,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:41:06,137 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:41:06,825 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:06,827 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:06,827 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:07,233 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:07,664 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:07,667 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:07,680 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:07,686 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:07,691 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:07,693 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:07,696 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:07,698 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:07,714 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:07,735 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:41:07,748 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:06,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:42:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:42:13,566 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:13,842 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:14,537 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:14,540 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:14,540 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:14,894 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:15,327 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:15,331 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:15,347 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:15,353 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:15,361 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:15,367 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:15,371 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:15,374 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:15,394 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:15,419 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:15,478 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:34,744 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:34,899 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:35,749 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:35,751 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:36,113 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:36,515 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:36,518 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:36,531 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:36,536 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:36,539 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:36,546 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:36,549 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:36,551 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:36,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:36,585 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:36,598 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:50,814 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:50,977 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:51,820 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:51,822 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:51,822 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:52,199 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:52,681 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:52,685 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:52,699 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:52,706 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:52,710 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:52,715 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:52,718 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:52,722 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:52,737 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:52,761 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:52,781 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:06,987 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:43:07,183 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:43:07,960 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:07,962 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:08,379 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:08,850 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:08,854 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:08,870 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:08,880 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:08,887 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:08,897 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:08,909 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:08,949 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:09,027 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:09,131 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:43:09,144 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:58,520 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:58,522 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:58,522 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:58,928 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:59,387 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:59,392 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:59,407 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:59,412 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:59,416 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:59,421 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:59,424 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:59,428 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:59,450 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:59,502 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:43:59,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:43:59,505 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:00,152 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:00,155 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:00,532 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:00,954 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:00,957 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:00,972 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:00,977 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:00,980 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:00,983 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:00,987 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:00,990 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:01,008 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:01,029 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:01,045 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:11,951 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:11,953 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:12,371 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:12,790 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:12,794 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:12,806 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:12,813 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:12,816 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:12,820 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:12,822 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:12,824 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:12,842 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:12,886 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:44:12,887 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:44:12,889 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:13,549 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:13,550 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:13,908 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:14,301 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:14,305 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:14,317 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:14,324 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:14,330 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:14,332 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:14,335 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:14,337 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:14,351 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:14,374 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:14,389 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:32,666 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "GET / HTTP/1.1" 200 -
2025-05-27 21:44:32,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,931 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,945 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,958 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,963 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,986 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,992 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:33,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,282 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:44:35,724 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:35,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:41,186 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:44:41,574 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:44:45,354 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:44:45,357 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:45] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:44:49,005 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:49,031 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:45:19,105 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:19,106 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:19,591 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:20,133 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:20,137 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:20,151 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:20,155 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:20,159 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:20,162 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:20,165 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:20,168 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:20,184 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:20,243 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:45:20,244 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:45:20,248 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:20,870 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:20,872 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:21,219 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:21,618 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:21,621 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:21,638 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:21,644 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:21,650 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:21,652 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:21,655 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:21,657 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:21,681 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:21,705 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:21,722 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:45:46,086 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\run.py', reloading
2025-05-27 21:45:46,288 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:47,043 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:47,045 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:47,045 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:47,499 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:48,079 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:48,084 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:48,102 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:48,110 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:48,116 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:48,121 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:48,125 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:48,128 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:48,152 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:48,178 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:48,191 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:08,937 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:08,939 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:08,939 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:09,433 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:47:09,931 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:47:09,934 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:47:09,949 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:47:09,954 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:47:09,959 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:47:09,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:47:09,964 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:47:09,966 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:47:09,985 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:10,042 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:47:10,043 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:47:10,046 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:47:10,756 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:10,759 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:10,759 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:11,812 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:11,830 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:47:11,848 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:29,325 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "GET / HTTP/1.1" 200 -
2025-05-27 21:47:29,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,651 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,670 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,716 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,739 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,741 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,751 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,756 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:30,145 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,162 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,672 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:37,800 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:37,802 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:38,331 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:52:38,955 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:52:38,959 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:52:38,978 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:52:38,985 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:52:38,989 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:52:38,992 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:52:38,999 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:52:39,001 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:52:39,079 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:39,093 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:52:39,094 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:52:39,098 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:52:39,907 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:39,911 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:39,912 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:40,810 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:40,825 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:52:40,840 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:52:46,837 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "GET / HTTP/1.1" 200 -
2025-05-27 21:52:46,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,938 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,015 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,056 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,067 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,070 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,071 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:52:47,304 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,364 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,809 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:49,810 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:52:50,435 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:52:54,725 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:52:54,789 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,793 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:05,594 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:53:09,315 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:53:09,341 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:14,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,269 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,484 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:53:14,496 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:08,605 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:08,607 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:08,607 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:09,071 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:54:09,695 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:54:09,701 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:54:09,723 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:54:09,731 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:54:09,739 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:54:09,746 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:54:09,752 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:54:09,758 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:54:09,790 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:09,855 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:54:09,856 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:54:09,858 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:54:10,551 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:10,553 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:10,553 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:11,403 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:11,422 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:54:11,440 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:54:14,245 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 21:54:14,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,298 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,308 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,314 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,320 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,370 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,371 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,385 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,400 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,403 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,417 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,711 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:14,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:15,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,213 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,258 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,572 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:27,270 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-05-27 21:54:27,270 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-05-27 21:54:27,703 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-05-27 21:54:28,095 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,096 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-05-27 21:54:28,113 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-05-27 21:54:28,127 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,150 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,153 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:54:28,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:32,598 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,629 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:32,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:35,943 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:35] "DELETE /api/search/2 HTTP/1.1" 200 -
2025-05-27 21:54:36,264 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:36,296 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:18,718 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:18,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:18,751 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:18,754 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:18,755 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:19,888 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:58:21,135 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:58:21,140 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:58:21,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:58:21,188 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:58:21,194 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:58:21,198 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:58:21,203 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:58:21,208 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:58:21,233 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:21,293 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:58:21,294 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:58:21,297 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:58:23,012 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:23,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:24,418 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:24,441 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:58:24,511 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:58:30,505 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:32,178 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,179 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:32,235 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:34,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,000 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,064 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:35,103 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:44,324 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:44,680 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:44,704 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,706 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:46,910 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:47,023 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:47] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:50,586 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "DELETE /api/search/5 HTTP/1.1" 200 -
2025-05-27 21:58:50,905 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:50,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:59:15,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:59:16,547 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:59:16,896 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:59:16,918 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,919 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:01:25,789 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:01:26,190 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:26,994 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:26,996 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:28,182 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:28,206 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:28,221 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:01:48,540 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:01:48,902 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:49,829 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:49,830 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:50,709 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:50,731 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:50,751 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:04:15,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:04:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:09:15,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:09:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:14:15,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:14:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:19:15,248 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:19:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:24:15,267 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:24:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:30:08,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:30:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:35:08,250 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:35:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:40:08,235 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:40:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:45:08,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:45:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:09,267 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:09,269 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:09,270 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:09,826 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:10,880 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:10,884 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:10,912 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:10,919 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:10,922 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:10,925 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:10,930 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:10,933 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:10,958 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:10,999 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:10,999 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:11,002 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:16,330 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:16,332 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:16,332 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:16,692 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:17,275 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:17,280 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:17,299 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:17,309 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:17,314 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:17,317 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:17,320 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:17,325 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:17,348 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:17,398 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:17,399 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:17,401 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:18,035 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:18,036 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:18,037 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:18,913 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:18,929 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:48:18,949 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:48:28,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 22:48:28,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,345 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,461 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,482 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,527 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 22:48:28,579 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 22:48:28,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,832 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:48:28,848 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:48:29,324 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:29,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:29,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 22:48:32,322 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 22:48:32,820 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 22:48:36,687 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 22:48:36,696 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:36] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 22:48:37,457 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 22:48:37,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:37] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:48:44,678 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 22:48:46,134 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:48:46,135 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 22:48:46,528 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:48:46,543 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:48:46,738 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:48:46,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:48:46,790 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:48:46,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:49:49,526 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:49:49,555 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:49:49,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:49:49,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:53:30,381 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:53:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:54:19,570 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:54:20,290 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:54:21,440 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:54:21,442 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:54:21,443 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:54:21,450 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:54:21,450 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:54:22,924 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:54:22,954 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:54:22,976 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:55:44,504 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:55:44,958 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:55:46,197 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:55:46,271 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:55:46,277 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:55:46,282 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:55:46,282 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:55:47,714 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:55:47,758 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:55:47,887 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:56:29,626 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:56:30,024 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:56:31,387 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:56:31,388 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:56:31,388 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:56:31,389 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:56:31,389 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:56:32,619 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:56:32,651 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:56:32,668 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:56:43,882 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:56:44,154 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:56:45,168 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:56:45,170 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:56:46,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:56:46,204 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:56:46,220 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:13,716 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:14,105 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:15,258 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:15,264 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:15,267 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:15,268 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:15,269 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:16,596 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:16,612 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:16,625 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:34,028 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:34,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:35,231 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:35,233 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:36,409 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:36,441 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:36,466 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:49,784 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:50,061 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:51,017 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:51,019 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:52,035 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:52,053 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:52,066 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:01,229 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:58:01,470 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:58:02,158 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:58:02,160 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:58:03,064 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:58:03,080 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:58:03,093 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:16,330 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:58:16,567 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:58:17,187 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:58:17,190 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:58:17,973 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:58:17,988 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:58:18,000 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:30,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:58:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:32,678 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 22:59:32,766 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,771 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,785 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,786 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,817 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,831 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,851 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,852 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,855 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,864 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:59:33,014 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:59:33,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:33,506 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:33,542 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 22:59:34,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:34,368 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:34,402 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:34,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:41,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:41] "DELETE /api/search/8 HTTP/1.1" 200 -
2025-05-27 22:59:42,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:42] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:42,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:42] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:48,839 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 22:59:50,058 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:59:50,058 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:50] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 22:59:50,590 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:59:50,592 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:50] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:59:55,050 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:55,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:55,148 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:55,163 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:04,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:04] "DELETE /api/search/9 HTTP/1.1" 200 -
2025-05-27 23:00:05,166 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:05] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:05,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:05] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:45,515 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:00:45,517 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:00:45,902 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:00:46,441 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:00:46,446 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:00:46,470 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:00:46,479 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:00:46,488 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:00:46,492 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:00:46,499 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:00:46,503 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:00:46,529 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:00:46,576 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:00:46,576 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:00:46,578 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:00:47,214 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:00:47,216 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:00:48,050 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:00:48,067 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:00:48,085 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:00:49,677 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "GET / HTTP/1.1" 200 -
2025-05-27 23:00:49,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,896 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,914 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,930 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,943 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,951 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,971 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,974 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,987 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:50,321 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:00:50,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:00:50,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:00:50,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:00:50,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:00:52,321 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:52,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:52,406 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:52,441 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:59,735 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 23:01:00,233 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 23:01:03,889 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 23:01:03,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:03] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 23:01:04,488 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 23:01:04,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:04] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:01:09,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:09,070 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:09,134 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:09,159 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:13,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "DELETE /api/search/10 HTTP/1.1" 200 -
2025-05-27 23:01:13,462 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:13,527 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:24,806 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:01:24,808 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:01:25,770 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:01:26,362 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:01:26,366 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:01:26,390 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:01:26,396 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:01:26,403 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:01:26,407 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:01:26,411 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:01:26,413 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:01:26,440 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:01:26,494 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:01:26,495 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:01:26,497 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:01:27,413 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:01:27,420 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:01:28,558 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:01:28,578 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:01:28,629 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:04,312 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:04,875 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:05,085 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:06,709 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:07,358 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:07,360 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:08,157 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:08,159 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:09,105 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:09,135 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:09,169 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:09,441 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:09,466 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:09,480 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:21,703 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:21,971 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:22,402 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:23,036 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:23,386 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:23,389 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:23,390 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:23,391 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:23,391 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:24,011 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:24,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:24,570 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:24,602 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:24,620 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:25,152 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:25,170 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:25,186 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:30,311 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:30,657 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:30,744 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:31,042 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:31,728 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:31,731 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:31,986 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:31,988 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:31,988 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:31,989 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:31,989 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:33,085 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:33,106 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:33,123 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:33,313 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:33,330 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:33,343 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:46,355 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:46,543 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:46,635 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:46,812 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:47,365 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:47,367 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:47,367 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:47,368 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:47,368 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:47,606 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:47,607 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:47,607 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:47,608 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:47,608 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:48,298 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:48,321 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:48,336 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:48,572 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:48,595 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:48,607 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:58,815 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:59,096 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:59,545 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:59,811 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:59,943 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:59,945 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:59,945 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:59,946 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:59,946 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:03:00,842 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:03:00,843 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:03:00,843 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:03:00,844 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:03:00,844 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:03:01,249 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:03:01,280 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:03:01,302 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:03:01,976 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:03:01,995 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:03:02,010 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:05:51,423 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:05:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:10:51,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:10:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:11:50,035 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:11:50,036 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:11:50,516 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:11:51,283 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:11:51,288 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:11:51,316 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:11:51,329 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:11:51,335 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:11:51,344 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:11:51,351 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:11:51,365 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:11:51,438 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:11:51,662 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:11:51,662 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:11:51,665 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:11:52,584 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:11:52,586 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:11:54,129 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:11:54,147 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:11:54,165 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:12:01,648 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "GET / HTTP/1.1" 200 -
2025-05-27 23:12:01,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,761 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,779 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,782 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,783 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,817 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,916 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:02,058 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:12:02,081 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:12:02,544 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:12:02,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:12:02,612 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:12:06,075 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:07,580 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:07,580 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:07] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:08,128 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:08] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:35,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:35,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:36,024 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:36,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:41,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:41,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:41,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:41,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:44,639 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:44] "DELETE /api/search/4 HTTP/1.1" 200 -
2025-05-27 23:12:44,985 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:44] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:45,051 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:45] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:46,533 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:46,880 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:46,913 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:46] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:47,516 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:47,522 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:47] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:51,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:51,949 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:51,953 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:51,971 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:54,497 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:54,948 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:54,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:54] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:55,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:55] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:59,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:59,764 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:59,781 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:59,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:22,660 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:13:23,087 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:13:23,088 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:23] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:13:23,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:23] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:13:25,137 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET / HTTP/1.1" 200 -
2025-05-27 23:13:25,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,240 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,408 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,412 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,450 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:13:25,526 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:13:25,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:25,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:26,023 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:26] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:13:29,572 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 23:13:30,073 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 23:13:33,339 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 23:13:33,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:33] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 23:13:33,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:33] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:13:38,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:13:38,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:13:38,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:38,331 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:46,562 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:46] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 23:13:47,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:47,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:14:12,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:14:12] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 23:18:26,246 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:18:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:23:26,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:23:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:28:26,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:28:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:33:26,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:33:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:38:26,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:38:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:43:26,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:43:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:32:45,318 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:32:45,320 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:32:45,798 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:32:46,547 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:32:46,552 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:32:46,574 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:32:46,581 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:32:46,585 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:32:46,588 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:32:46,591 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:32:46,595 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:32:46,618 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:32:46,682 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:32:46,683 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:32:46,685 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:32:47,372 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:32:47,374 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:32:47,375 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:32:47,376 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:32:47,376 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:32:48,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:32:48,317 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:32:48,331 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:33:06,693 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "GET / HTTP/1.1" 200 -
2025-05-28 15:33:06,915 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:06,934 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:06,999 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,014 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,016 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,021 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,039 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,065 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,078 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 15:33:07,090 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,118 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,125 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,379 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:07,401 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:07,873 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:07,883 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:07,954 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:33:14,817 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:14] "GET / HTTP/1.1" 200 -
2025-05-28 15:33:15,050 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,057 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,069 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,090 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,125 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,183 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,234 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,258 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,372 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,473 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,474 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,693 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:15,707 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:16,183 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:16,191 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:16,222 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:33:35,129 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:33:35,148 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,148 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,162 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,163 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,187 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,229 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,242 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,264 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,299 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,342 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,397 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,417 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,460 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:35,473 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:35,936 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:35,947 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:35,979 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:35:24,005 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:35:24,225 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:35:25,169 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:35:25,172 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:35:26,456 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:35:26,484 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:35:26,495 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:35:52,865 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:35:53,206 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:35:54,003 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:35:54,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:35:54,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:35:54,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:35:54,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:35:55,110 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:35:55,144 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:35:55,176 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:36:48,017 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:36:48,262 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:36:49,082 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:36:49,084 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:36:50,215 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:36:50,233 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:36:50,244 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:37:11,595 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:37:11,953 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:37:12,991 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:37:12,996 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:37:12,996 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:37:12,997 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:37:12,997 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:37:14,301 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:37:14,330 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:37:14,344 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:38:25,653 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\mailer.py', reloading
2025-05-28 15:38:25,930 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:38:26,979 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:38:26,981 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:38:28,315 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:38:28,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:38:28,347 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:38:36,032 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:38:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:02,013 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:39:02,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:39:02,397 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:39:02,788 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:39:02,792 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:39:02,977 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:39:02,981 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:39:02,985 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:39:02,989 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:39:02,993 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:39:02,996 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:39:03,015 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:39:03,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:39:03,078 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:39:03,080 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:39:03,747 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:39:03,748 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:39:03,748 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:39:03,749 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:39:03,749 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:39:04,873 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:39:04,901 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:39:04,933 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:39:08,732 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:08] "GET / HTTP/1.1" 200 -
2025-05-28 15:39:09,143 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,173 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,200 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,209 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,260 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,286 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,339 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,345 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,397 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,421 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,832 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:09,849 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:10,180 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:10,197 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:10,276 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:39:19,428 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:39:19,454 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,464 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,468 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,486 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,502 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,527 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,542 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,548 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,561 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,577 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,594 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,657 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,759 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:19,777 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:20,169 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:20,175 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:20,201 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:45:08,276 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:45:08,278 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:45:08,279 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:45:08,279 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:45:08,279 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:45:08,700 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:45:09,294 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:45:09,299 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:45:09,504 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:45:09,510 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:45:09,514 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:45:09,516 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:45:09,519 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:45:09,523 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:45:09,549 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:45:09,609 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:45:09,610 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:45:09,613 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:45:10,263 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:45:10,264 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:45:11,453 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:45:11,473 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:45:11,485 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:45:16,616 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-28 15:45:17,158 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-28 15:45:23,637 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-28 15:45:23,640 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:23] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-28 15:45:24,287 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:24] "POST /api/search/record HTTP/1.1" 200 -
2025-05-28 15:45:31,369 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:45:31,403 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,439 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,440 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,445 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,453 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,462 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,469 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,474 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,488 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 15:45:31,493 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,497 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,505 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,541 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:45:31,556 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:45:32,036 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:45:32,043 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:45:32,078 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:46:57,596 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-05-28 15:46:57,598 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-05-28 15:46:57,600 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:46:57] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-05-28 15:47:01,964 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:01] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:47:01,991 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,009 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,028 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,038 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,061 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,073 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,075 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,137 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,160 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,177 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,182 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,214 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:47:02,234 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:47:02,700 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:47:02,709 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:47:02,731 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:47:07,174 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-05-28 15:47:07,175 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-05-28 15:47:07,176 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:07] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-05-28 15:48:18,290 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 15:48:18,547 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:48:19,471 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:19,472 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:19,472 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:19,473 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:19,473 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:20,722 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:20,752 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:48:20,772 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:48:51,906 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:51,907 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:51,907 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:51,908 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:51,908 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:52,329 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:48:52,769 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:48:52,772 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:48:52,959 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:48:52,965 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:48:52,968 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:48:52,971 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:48:52,973 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:48:52,975 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:48:52,999 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:53,035 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:48:53,036 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:48:53,038 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:48:53,788 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:53,790 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:54,896 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:54,940 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:48:54,972 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:48:57,503 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:48:57,520 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,534 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,538 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,547 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,585 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,643 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,653 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,676 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,691 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,691 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,709 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,737 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,783 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:48:57,815 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:48:58,269 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:48:58,285 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:48:58,385 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:49:02,884 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:49:02,885 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:02,885 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:03,429 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 15:49:03,430 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:03,430 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:49:03,432 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:49:03] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:49:03,950 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:49:03] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:49:25,403 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:49:25,404 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:35,085 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:49:35,272 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:49:35,273 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:49:35,273 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 305128 字节)
2025-05-28 15:49:35,285 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:49:35,313 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:49:35,548 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:49:35,844 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:49:36,844 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:49:36,845 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:49:36,846 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:52:02,033 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:52:02,035 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,037 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,698 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 15:52:02,698 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,699 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:52:02,702 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:52:02] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:52:03,214 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:52:03] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:52:15,692 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:52:15,692 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:23,525 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:52:23,666 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:52:23,666 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:52:23,667 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 309689 字节)
2025-05-28 15:52:23,676 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:52:23,699 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:52:23,879 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:52:24,099 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:52:25,079 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:52:25,080 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:52:25,080 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:53:08,460 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:53:08,461 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:08,461 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:09,717 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos设置用户名密码
2025-05-28 15:53:09,717 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:09,717 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:53:09,721 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:09] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:53:10,228 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:10] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:53:25,640 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:53:25,640 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:34,272 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:34,432 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:34,433 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:53:34,433 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 70081 字节)
2025-05-28 15:53:34,438 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:53:34,461 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:53:34,692 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:53:34,918 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:53:35,734 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:53:35,735 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:53:35,736 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:59,035 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:55:10,742 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:55:11,272 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:55:12,145 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:55:12,146 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:55:12,146 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:55:12,147 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:55:12,147 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:55:13,423 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:55:13,446 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:55:13,462 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:05,193 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:05,487 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:06,124 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:06,124 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:07,204 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:07,223 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:07,235 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:27,599 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:28,081 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:28,747 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:28,749 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:29,818 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:29,852 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:29,868 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:48,155 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:48,431 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:49,149 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:49,150 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:49,151 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:49,151 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:49,151 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:50,204 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:50,222 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:50,237 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 16:04:31,022 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 16:04:31,024 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 16:04:31,456 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 16:04:31,888 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 16:04:31,891 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 16:04:32,060 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 16:04:32,065 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 16:04:32,068 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 16:04:32,071 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 16:04:32,074 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 16:04:32,076 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 16:04:32,095 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 16:04:32,138 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 16:04:32,139 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 16:04:32,141 - werkzeug - INFO -  * Restarting with stat
2025-05-28 16:04:32,950 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 16:04:32,954 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 16:04:32,954 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 16:04:32,955 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 16:04:32,955 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 16:04:35,248 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 16:04:35,288 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 16:04:35,312 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 16:04:35,553 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 16:04:35,716 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,718 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,725 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,786 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,800 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,820 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,859 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,863 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,906 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,921 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,931 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,940 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,963 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 16:04:35,978 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 16:04:36,454 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:04:36,461 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:04:36,520 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 16:04:42,252 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 16:04:42,253 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:42,254 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:43,141 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 16:04:43,141 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:43,141 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 16:04:43,147 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:43] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 16:04:43,671 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:43] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 16:04:57,671 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 16:04:57,672 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:05:01,389 - backend.utils.screenshot - INFO - 找到CSDN文章内容区域
2025-05-28 16:05:08,506 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:05:08,774 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:05:08,774 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 16:05:08,775 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 301689 字节)
2025-05-28 16:05:08,784 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 16:05:08,819 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 16:05:09,033 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 16:05:09,275 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 16:05:10,495 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 16:05:10,497 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 16:05:10,498 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:09:37,028 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:09:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:14:37,023 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:14:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:19:37,029 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:19:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:24:37,019 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:24:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:29:37,035 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:29:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:34:37,017 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:34:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:40:08,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:40:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:45:08,049 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:45:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:50:08,022 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:50:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:55:08,018 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:55:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:00:08,017 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:00:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:08:30,837 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:08:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:09:37,022 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:09:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:23:55,615 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:23:55,617 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:23:56,080 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 18:23:56,815 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 18:23:56,821 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 18:23:57,074 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 18:23:57,080 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 18:23:57,088 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 18:23:57,091 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 18:23:57,094 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 18:23:57,097 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 18:23:57,129 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:23:57,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 18:23:57,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 18:23:57,228 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:23:58,176 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:23:58,177 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:23:58,178 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:23:58,178 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:23:58,178 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:23:59,640 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:23:59,677 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:23:59,712 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:24:02,704 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "GET / HTTP/1.1" 200 -
2025-05-28 18:24:02,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-28 18:24:02,992 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,005 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,025 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,033 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,057 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,076 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 18:24:03,080 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,092 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,102 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,111 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,471 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:24:03,491 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:24:03,955 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:24:03,973 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:24:04,024 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:04] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 18:24:09,838 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:24:09,838 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:09,838 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,476 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 18:24:10,478 - backend.api.csdn_api - INFO - 开始后台处理HTML生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,478 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 18:24:10,481 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:10] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 18:24:10,482 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:24:10,484 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,984 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 18:24:10,986 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_182410.html
2025-05-28 18:24:10,986 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 18:24:10,987 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_完整版.html (大小: 5117 字节)
2025-05-28 18:24:10,987 - backend.utils.mailer - INFO - 附件 CSDN文章_完整版.html 添加成功
2025-05-28 18:24:11,014 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 18:24:11,018 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:11] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 18:24:11,213 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 18:24:11,493 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 18:24:12,063 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 18:24:12,064 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 18:24:12,065 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_182410.html
2025-05-28 18:29:31,170 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:29:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:32:54,368 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:32:55,796 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:32:58,909 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:32:58,911 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:32:58,912 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:32:58,912 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:32:58,913 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:01,388 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:01,416 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:01,451 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:15,887 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:16,307 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:20,216 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:20,224 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:20,224 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:20,228 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:20,233 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:22,635 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:22,663 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:22,693 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:45,058 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:45,367 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:46,450 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:46,452 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:47,961 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:47,986 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:48,003 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:56,194 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:56,540 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:57,326 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:57,327 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:58,649 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:58,681 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:58,710 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:14,123 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:34:14,498 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:34:15,714 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:34:15,715 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:34:18,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:34:18,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:34:18,644 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:41,052 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:34:41,244 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:34:42,019 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:34:42,021 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:34:43,208 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:34:43,226 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:34:43,247 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:59,527 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:34:59,819 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:35:00,748 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:35:00,750 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:35:02,089 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:35:02,110 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:35:02,129 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:19,575 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:19,995 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:21,260 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:21,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:23,144 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:23,171 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:23,202 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:32,372 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:32,683 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:33,546 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:33,547 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:33,547 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:33,548 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:33,548 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:34,632 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:34,650 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:34,663 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:44,844 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:45,157 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:45,944 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:45,945 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:45,946 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:45,946 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:45,946 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:47,173 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:47,196 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:47,215 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:37:10,691 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:37:10,925 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:37:11,983 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:37:11,984 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:37:13,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:37:13,587 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:37:13,609 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:41:14,609 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:41:14,610 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:41:15,012 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 18:41:15,599 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 18:41:15,605 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 18:41:15,893 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 18:41:15,897 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 18:41:15,900 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 18:41:15,902 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 18:41:15,906 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 18:41:15,908 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 18:41:15,923 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:41:15,979 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 18:41:15,980 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 18:41:15,982 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:41:16,680 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:41:16,680 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:41:17,668 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:41:17,681 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:41:17,696 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:41:20,791 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:20] "GET / HTTP/1.1" 200 -
2025-05-28 18:41:21,054 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,058 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,096 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,100 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,107 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,124 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,131 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,136 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,154 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 18:41:21,167 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,180 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,181 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,514 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:41:21,528 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:41:22,005 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:41:22,013 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:41:22,068 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 18:41:31,306 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:41:31,307 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:31,307 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:32,521 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:41:35,633 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:41:39,916 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:41:39,916 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:41:39,917 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:39,917 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 18:41:39,917 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:41:39,920 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:39,921 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:39] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 18:41:40,444 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:40] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 18:41:43,812 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:41:49,068 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:41:54,139 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:41:54,139 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:41:54,151 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_184154.html
2025-05-28 18:41:54,151 - backend.api.csdn_api - ERROR - 后台处理失败: name '_send_file_email' is not defined
2025-05-28 18:41:54,153 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 379, in _process_screenshot_and_email
    _send_file_email(email, full_article_data, output_path, format_type)
    ^^^^^^^^^^^^^^^^
NameError: name '_send_file_email' is not defined

2025-05-28 18:43:57,595 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:43:58,218 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:43:59,797 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:43:59,807 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:43:59,808 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:43:59,815 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:43:59,815 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:44:02,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:44:02,260 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:44:02,295 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:44:20,671 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:44:20,949 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:44:21,967 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:44:21,968 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:44:23,531 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:44:23,559 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:44:23,577 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:45:03,834 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:45:07,342 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:45:09,900 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:45:09,905 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:45:09,905 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:45:09,905 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:45:09,906 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:45:11,863 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:45:11,898 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:45:11,923 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:46:23,592 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:46:24,910 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:24] "GET / HTTP/1.1" 200 -
2025-05-28 18:46:25,057 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,059 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,073 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,089 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,093 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,122 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,150 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,160 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 18:46:25,166 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,209 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,212 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,222 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 18:46:25,422 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:46:25,451 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:46:25,888 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:46:25,922 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:46:25,989 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:25] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 18:46:35,398 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:46:35,412 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:46:35,417 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:46:35,847 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:46:40,713 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:46:45,174 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:46:45,178 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:46:45,179 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:46:45,180 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 18:46:45,180 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:46:45,182 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:45] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 18:46:45,183 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:46:45,669 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:46:45,707 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:46:45] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 18:46:49,098 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:46:51,781 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:46:51,782 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:46:51,801 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_184651.html
2025-05-28 18:46:51,812 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 18:46:51,829 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章.html (大小: 5148 字节)
2025-05-28 18:46:51,833 - backend.utils.mailer - INFO - 附件 CSDN文章.html 添加成功
2025-05-28 18:46:52,029 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 18:46:52,260 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 18:46:52,495 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 18:46:53,030 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 18:46:53,031 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 18:46:53,031 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_184651.html
2025-05-28 18:51:25,992 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:51:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:56:25,982 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:56:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:01:25,983 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:01:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:06:25,986 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:06:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:11:25,986 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:11:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:16:25,991 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:16:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:22:07,986 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:22:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:27:08,004 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:27:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:32:07,988 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:32:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:37:07,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:37:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:42:08,001 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:42:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 19:47:07,968 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 19:47:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:05:36,709 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:05:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:06:26,211 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:06:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:20:50,147 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:20:50] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:21:25,987 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:21:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:23:59,679 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:24:02,551 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:24:05,127 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:24:05,128 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:24:05,129 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:24:05,129 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:24:05,129 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:24:07,592 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:24:07,636 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:24:07,678 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:24:22,970 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:24:23,696 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:24:24,813 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:24:24,814 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:24:24,814 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:24:24,815 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:24:24,815 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:24:26,280 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:24:26,310 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:24:26,327 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:24:40,607 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:24:40,959 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:24:41,909 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:24:41,909 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:24:41,909 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:24:41,910 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:24:41,910 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:24:44,383 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:24:44,468 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:24:44,506 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:25:08,017 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:25:08,897 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:25:10,524 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:25:10,525 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:25:10,525 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:25:10,525 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:25:10,525 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:25:12,995 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:25:13,027 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:25:13,050 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:25:28,288 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:25:28,747 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:25:29,852 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:25:29,854 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:25:29,854 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:25:29,854 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:25:29,855 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:25:31,131 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:25:31,150 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:25:31,167 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:25:53,215 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "GET / HTTP/1.1" 200 -
2025-05-28 20:25:53,340 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,341 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,369 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,373 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,375 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,409 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,414 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,424 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,452 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,472 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,473 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,475 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:25:53,608 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:25:53,625 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:25:54,071 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:25:54,082 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:25:54,147 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:25:54] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 20:26:00,743 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 20:26:00,748 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:26:00,748 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:26:01,764 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 20:26:07,104 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 20:26:11,319 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 20:26:11,320 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 20:26:11,321 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:26:11,321 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 20:26:11,322 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 20:26:11,324 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:26:11,326 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:26:11] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 20:26:11,672 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 20:26:11,840 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:26:11] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 20:26:14,683 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 20:26:17,014 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 20:26:17,014 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 20:26:17,021 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_202617.html
2025-05-28 20:26:17,022 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 20:26:17,027 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章.html (大小: 5148 字节)
2025-05-28 20:26:17,028 - backend.utils.mailer - INFO - 附件 CSDN文章.html 添加成功
2025-05-28 20:26:17,069 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 20:26:17,282 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 20:26:17,832 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 20:26:19,024 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 20:26:19,025 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 20:26:19,025 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_202617.html
2025-05-28 20:28:42,099 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:28:43,027 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:28:47,148 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:28:47,161 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:28:47,162 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:28:47,170 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:28:47,172 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:28:49,833 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:28:49,878 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:28:49,931 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:29:04,198 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:29:04,805 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:29:08,238 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:29:08,239 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:29:08,239 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:29:08,239 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:29:08,239 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:29:10,011 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:29:10,040 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:29:10,075 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:29:24,393 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:29:24,948 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:29:26,515 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:29:26,516 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:29:26,516 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:29:26,516 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:29:26,516 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:29:27,968 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:29:28,036 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:29:28,149 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:29:50,893 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:50] "GET / HTTP/1.1" 200 -
2025-05-28 20:29:50,983 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 20:29:50,986 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:50] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 20:29:50,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:50] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,002 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,004 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,033 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,034 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,061 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,062 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,066 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,073 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,075 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:29:51,206 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:29:51,219 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:29:51,701 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:29:51,713 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:29:51,754 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:29:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 20:30:00,815 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 20:30:00,818 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:30:00,819 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:30:01,264 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 20:30:01,264 - backend.spiders.csdn_spider - INFO - 等待 7.2 秒后重试...
2025-05-28 20:30:06,109 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:30:06,911 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:30:11,124 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:30:11,169 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:30:11,173 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:30:11,178 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:30:11,182 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:30:13,420 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:30:13,463 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:30:13,494 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:30:31,876 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:30:32,888 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:30:35,524 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:30:35,527 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:30:35,527 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:30:35,527 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:30:35,533 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:30:37,602 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:30:37,655 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:30:37,677 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:30:49,640 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 20:30:50,776 - werkzeug - INFO -  * Restarting with stat
2025-05-28 20:30:52,825 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 20:30:52,840 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 20:30:52,840 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 20:30:52,843 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 20:30:52,844 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 20:30:55,155 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 20:30:55,191 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 20:30:55,209 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 20:31:54,528 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "GET / HTTP/1.1" 200 -
2025-05-28 20:31:54,654 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,656 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,672 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,693 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,699 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,773 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,797 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,810 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,823 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,825 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,838 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,858 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 20:31:54,939 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:31:54,972 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:54] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 20:31:55,418 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:31:55,432 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:31:55,502 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:31:55] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 20:32:01,055 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 20:32:01,065 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:32:01,071 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:32:01,560 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 20:32:01,560 - backend.spiders.csdn_spider - INFO - 等待 15.7 秒后重试...
2025-05-28 20:32:17,574 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 20:32:17,575 - backend.spiders.csdn_spider - INFO - 等待 23.0 秒后重试...
2025-05-28 20:32:40,893 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 20:32:40,895 - backend.spiders.csdn_spider - INFO - 等待 19.9 秒后重试...
2025-05-28 20:33:01,290 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第4次)
2025-05-28 20:33:01,291 - backend.spiders.csdn_spider - INFO - 等待 16.5 秒后重试...
2025-05-28 20:33:18,085 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-05-28 20:33:18,090 - backend.spiders.csdn_spider - WARNING - 标准请求方法失败: 达到最大重试次数
2025-05-28 20:33:18,091 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-05-28 20:33:18,580 - backend.spiders.csdn_spider - INFO - 页面中找到的所有h1标签: 1
2025-05-28 20:33:18,581 - backend.spiders.csdn_spider - INFO - H1[0]: class=['title-article'], id=articleContentId, text=nacos 2.x开启登录的用户名和密码
2025-05-28 20:33:18,585 - backend.spiders.csdn_spider - INFO - 页面中找到包含'阅读'的文本: 3
2025-05-28 20:33:18,585 - backend.spiders.csdn_spider - INFO - Read[0]: tag=script, class=None, text=var isCorporate = false;
        var username =  "fengbin2005";
        var skinImg = "white";

        var blog_address = "https://blog.csdn.net/fengbin2005";
        var currentUserName = "";
        var isOwner = false;
        var loginUrl = "http://passport.csdn.net/account/login?from=https://blog.csdn.net/fengbin2005/article/details/*********";
        var blogUrl = "https://blog.csdn.net/";
        var avatar = "https://profile-avatar.csdnimg.cn/61d3f09ac121444ab2afc891637cdbad_fengbin2005.jpg!1";
        var articleTitle = "nacos 2.x开启登录的用户名和密码";
        var articleDesc = "文章浏览阅读1.5k次，点赞13次，收藏17次。## 配置自定义身份识别的key（不可为空）和value（不可为空） nacos.core.auth.server.identity.key=配置完成后，重启服务端，访问控制台需要登录，默认的用户名/密码为。nacos 默认是集群模式,本地用改为单机模式.B.配置一个自定义密钥，用于生成JWT令牌。修改nacos的配置文件。_nacos开启密码";
        var articleTitles = "nacos 2.x开启登录的用户名和密码_nacos开启密码-CSDN博客";
        var nickName = "拿破轮";
        var articleDetailUrl = "https://blog.csdn.net/fengbin2005/article/details/*********";
        var vipUrlV = "https://mall.csdn.net/vip?vipSource=learningVip";
        if(window.location.host.split('.').length == 3) {
            blog_address = blogUrl + username;
        }
        var skinStatus = "White";
        var blogStaticHost = "https://csdnimg.cn/release/blogv2/"
          var payColumn = false
2025-05-28 20:33:18,588 - backend.spiders.csdn_spider - INFO - Read[1]: tag=script, class=None, text=var articleId = *********;
        var privateEduData = [];
        var privateData = ["ai","base64","express","framework","mapper"];//高亮数组
      var crytojs = "https://csdnimg.cn/release/blogv2/dist/components/js/crytojs-ca5b8bf6ae.min.js";
      var commentscount = 0;
      var commentAuth = 2;
      var curentUrl = "https://blog.csdn.net/fengbin2005/article/details/*********";
      var myUrl = "https://my.csdn.net/";
      var isGitCodeBlog = false;
      var isOpenSourceBlog = false;
      var isVipArticle = true;
        var highlight = ["nacos","用户名","密码","配置","开启","登录","2","和","."];//高亮数组
        var isRecommendModule = true;
          var isBaiduPre = true;
          var baiduCount = 2;
          var setBaiduJsCount = 10;
        var viewCountFormat = 1589;
      var share_card_url = "https://app-blog.csdn.net/share?article_id=*********&username=fengbin2005"
      var mallVipUrl = "https://mall.csdn.net/vip?vipSource=article"
      var vipArticleAbStyle = "t_1"
      var vipArticleCpStyle = "t_1"
      var codeAbStyle = "exp1"
      var detailheaderAbStyle = "control"
      var codeAiAbStyle = "control"
      var articleType = 1;
      var baiduKey = "nacos开启密码";
      var copyPopSwitch = true;
      var needInsertBaidu = true;
      var recommendRegularDomainArr = ["blog.csdn.net/.+/article/details/","download.csdn.net/download/","edu.csdn.net/course/detail/","ask.csdn.net/questions/","bbs.csdn.net/topics/","www.csdn.net/gather_.+/"]
      var codeStyle = "";
      var baiduSearchType = "baidulandingword";
      var sharData = "{\"hot\":[{\"id\":1,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a5f4260710904e538002a6ab337939b3.png\"},{\"id\":2,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/188b37199a2c4b74b1d9ffc39e0d52de.png\"},{\"id\":3,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/14ded358b631444581edd98a256bc5af.png\"},{\"id\":4,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1470f23a770444d986ad551b9c33c5be.png\"},{\"id\":5,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c329f5181dc74f6c9bd28c982bb9f91d.png\"},{\"id\":6,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ccd8a3305e81460f9c505c95b432a65f.png\"},{\"id\":7,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bc89d8283389440d97fc4d30e30f45e1.png\"},{\"id\":8,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/452d485b4a654f5592390550d2445edf.png\"},{\"id\":9,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f8b9939db2ed474a8f43a643015fc8b7.png\"},{\"id\":10,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6de8864187ab4ed3b1db0856369c36ff.png\"},{\"id\":11,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/673cc3470ff74072acba958dc0c46e2d.png\"},{\"id\":12,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/930c119760ac4491804db80f9c6d4e3f.png\"},{\"id\":13,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/15e6befb05a24233bc2b65e96aa8d972.png\"},{\"id\":14,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2075fd6822184b95a41e214de4daec13.png\"},{\"id\":15,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/859b1552db244eb6891a809263a5c657.png\"},{\"id\":16,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/0be2f920f1f74290a98921974a9613fd.png\"},{\"id\":17,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2e97e00b43f14afab494ea55ef3f4a6e.png\"},{\"id\":18,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ff4ab252f46e444686f5135d6ebbfec0.png\"},{\"id\":19,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ae029bbe99564e79911657912d36524f.png\"},{\"id\":20,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b3ece39963de440388728e9e7b9bf427.png\"},{\"id\":21,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6f14651a99ba486e926d63b6fa692997.png\"},{\"id\":22,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/83ceddf050084875a341e32dcceca721.png\"},{\"id\":23,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b90368b8fd5d4c6c8c79a707d877cf7c.png\"},{\"id\":24,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/aeffae14ecf14e079b2616528c9a393b.png\"},{\"id\":25,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c5a06b5a13d44d16bed868fc3384897a.png\"},{\"id\":26,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/08b697658b844b318cea3b119e9541ef.png\"},{\"id\":27,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/68ccb0b8d09346ac961d2b5c1a8c77bf.png\"},{\"id\":28,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a2227a247e37418cbe0ea972ba6a859b.png\"},{\"id\":29,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/3a42825fede748f9993e5bb844ad350d.png\"},{\"id\":30,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/8882abc1dd484224b636966ea38555c3.png\"},{\"id\":31,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/4f6a5f636a3e444d83cf8cc06d87a159.png\"},{\"id\":32,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1953ef79c56b4407b78d7181bdff11c3.png\"},{\"id\":33,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c04a2a4f772948ed85b5b0380ed36287.png\"},{\"id\":34,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5b4fecd05091405ea04d8c0f53e9f2c7.png\"},{\"id\":35,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b89f576d700344e280d6ceb2a66c2420.png\"},{\"id\":36,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1c65780e11804bbd9971ebadb3d78bcf.png\"},{\"id\":37,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d590db2055f345db9706eb68a7ec151a.png\"},{\"id\":38,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fe602f80700b4f6fb3c4a9e4c135510e.png\"},{\"id\":39,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/39ff2fcd31e04feba301a071976a0ba7.png\"},{\"id\":40,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f9b61b3d113f436b828631837f89fb39.png\"},{\"id\":41,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/df1aca5f610c4ad48cd16da88c9c8499.png\"},{\"id\":42,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d7acf73a1e6b41399a77a85040e10961.png\"},{\"id\":43,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b7f1b63542524b97962ff649ab4e7e23.png\"}],\"vip\":[{\"id\":1,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101150.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101154.png\"},{\"id\":2,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101204.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101208.png\"},{\"id\":3,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101211.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101215.png\"},{\"id\":4,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101218.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101220.png\"},{\"id\":5,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101223.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101226.png\"},{\"id\":6,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100635.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100639.png\"},{\"id\":7,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100642.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100644.png\"},{\"id\":8,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100647.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100649.png\"},{\"id\":9,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100652.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100655.png\"},{\"id\":10,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/55de67481fde4b04b97ad78f11fe369a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bb2418fb537e4d78b10d8765ccd810c5.png\"},{\"id\":11,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/579c713394584d128104ef1044023954.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f420d9fbcf5548079d31b5e809b6d6cd.png\"},{\"id\":12,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/75b7f3155ba642f5a4cc16b7baf44122.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a9030f5877be401f8b340b80b0d91e64.png\"},{\"id\":13,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0903d33cafa54934be3780aa54ae958d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2cd8c8929f5a42fca5da2a0aeb456203.png\"},{\"id\":14,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/949fd7c22884439fbfc3c0e9c3b8dee7.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/dafbea9bd9eb4f3b962b48dc41657f89.png\"},{\"id\":15,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4119cfddd71d4e6a8a27a18dbb74d90e.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c56310c8b6384d9e85388e4e342ce508.png\"},{\"id\":16,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/121575274da142bcbbbbc2e8243dd411.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5013993de06542f881018bb9abe2edf7.png\"},{\"id\":17,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4d97aa6dd4fe4f09a6bef5bdf8a6abcd.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/76f23877b6ad4066ad45ce8e31b4b977.png\"},{\"id\":18,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdb619daf21b4c829de63b9ebc78859d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a1abe5d27a5441f599adfe662f510243.png\"},{\"id\":19,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/676b7707bb11410f8f56bc0ed2b2345c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/7ac5b467fbf24e1d8c2de3f3332c4f54.png\"},{\"id\":20,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0becb8cc227e4723b765bdd69a20fd4a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdec85b26091486b9a89d0b8d45c3749.png\"},{\"id\":21,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/1a6c06235ad44941b38c54cbc25a370c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/410a06cda2d44b0c84578f88275caf70.png\"}],\"map\":{\"hot\":\"热门\",\"vip\":\"VIP\"}}";
      
      var canRead = false;
      var blogMoveHomeArticle = false;
      var showSearchText = "";
      var sideToolbarResult = "exp";
      var articleSource = 1;
      var articleReport = '{"pid": "blog", "spm":"1001.2101"}';
        var baiduSearchChannel = 'pc_relevant'
        var baiduSearchIdentification = '.235^v43^pc_blog_bottom_relevance_base2'
        var distRequestId = '1748435598258_12979'
        var initRewardObject = {
          giver: currentUserName,
          anchor: username,
          articleId: articleId,
          sign: ''
        }
        var isLikeStatus = false;
        var isUnLikeStatus = false;
        var studyLearnWord = "";
        var unUseCount = 0;
        var codeMaxSize = 0;
        var overCost = true;
        var isCurrentUserVip = false;
        var contentViewsHeight = 0;
        var contentViewsCount = 0;
        var contentViewsCountLimit = 5;
        var isShowConcision = false;
        var lastTime = "2025-01-17 19:10:23"
        var postTime = "2025-01-17 19:09:48"
      var isCookieConcision = false
      var isHasDirectoryModel = false
      var isShowSideModel = false
      var isShowDirectoryModel = true
      function getCookieConcision(sName){
        var allCookie = document.cookie.split("; ");
        for (var i=0; i < allCookie.length; i++){
          var aCrumb = allCookie[i].split("=");
          if (sName == aCrumb[0])
            return aCrumb[1];
        }
        return null;
      }
      if (getCookieConcision('blog_details_concision') && getCookieConcision('blog_details_concision') == 0){
        isCookieConcision = true
        isShowSideModel = true
        isShowDirectoryModel = false
      }
2025-05-28 20:33:18,596 - backend.spiders.csdn_spider - INFO - Read[2]: tag=span, class=['read-count'], text=阅读量1.5k
2025-05-28 20:33:18,597 - backend.spiders.csdn_spider - INFO - 找到标题: nacos 2.x开启登录的用户名和密码
2025-05-28 20:33:18,623 - backend.spiders.csdn_spider - INFO - 找到作者: 
2025-05-28 20:33:18,627 - backend.spiders.csdn_spider - INFO - 找到发布时间: 已于 2025-01-17 19:10:23 修改
2025-05-28 20:33:18,628 - backend.spiders.csdn_spider - INFO - 找到阅读数文本: 阅读量1.5k
2025-05-28 20:33:18,629 - backend.spiders.csdn_spider - INFO - 找到阅读数: 1500
2025-05-28 20:33:18,630 - backend.spiders.csdn_spider - INFO - 找到内容元素: {'id': 'content_views'}
2025-05-28 20:33:18,638 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 20:33:18,639 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:33:18,639 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 20:33:18,640 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 20:33:18,646 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 20:33:18,650 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:33:18] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 20:33:18,985 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 20:33:18,985 - backend.spiders.csdn_spider - INFO - 等待 11.0 秒后重试...
2025-05-28 20:33:19,964 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:33:19] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 20:33:30,262 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 20:33:30,263 - backend.spiders.csdn_spider - INFO - 等待 11.8 秒后重试...
2025-05-28 20:33:42,368 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 20:33:42,369 - backend.spiders.csdn_spider - INFO - 等待 16.4 秒后重试...
2025-05-28 20:33:59,151 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第4次)
2025-05-28 20:33:59,152 - backend.spiders.csdn_spider - INFO - 等待 22.5 秒后重试...
2025-05-28 20:34:21,989 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-05-28 20:34:21,989 - backend.spiders.csdn_spider - WARNING - 标准请求方法失败: 达到最大重试次数
2025-05-28 20:34:21,990 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-05-28 20:34:22,355 - backend.spiders.csdn_spider - INFO - 页面中找到的所有h1标签: 1
2025-05-28 20:34:22,356 - backend.spiders.csdn_spider - INFO - H1[0]: class=['title-article'], id=articleContentId, text=nacos 2.x开启登录的用户名和密码
2025-05-28 20:34:22,360 - backend.spiders.csdn_spider - INFO - 页面中找到包含'阅读'的文本: 3
2025-05-28 20:34:22,361 - backend.spiders.csdn_spider - INFO - Read[0]: tag=script, class=None, text=var isCorporate = false;
        var username =  "fengbin2005";
        var skinImg = "white";

        var blog_address = "https://blog.csdn.net/fengbin2005";
        var currentUserName = "";
        var isOwner = false;
        var loginUrl = "http://passport.csdn.net/account/login?from=https://blog.csdn.net/fengbin2005/article/details/*********";
        var blogUrl = "https://blog.csdn.net/";
        var avatar = "https://profile-avatar.csdnimg.cn/61d3f09ac121444ab2afc891637cdbad_fengbin2005.jpg!1";
        var articleTitle = "nacos 2.x开启登录的用户名和密码";
        var articleDesc = "文章浏览阅读1.5k次，点赞13次，收藏17次。## 配置自定义身份识别的key（不可为空）和value（不可为空） nacos.core.auth.server.identity.key=配置完成后，重启服务端，访问控制台需要登录，默认的用户名/密码为。nacos 默认是集群模式,本地用改为单机模式.B.配置一个自定义密钥，用于生成JWT令牌。修改nacos的配置文件。_nacos开启密码";
        var articleTitles = "nacos 2.x开启登录的用户名和密码_nacos开启密码-CSDN博客";
        var nickName = "拿破轮";
        var articleDetailUrl = "https://blog.csdn.net/fengbin2005/article/details/*********";
        var vipUrlV = "https://mall.csdn.net/vip?vipSource=learningVip";
        if(window.location.host.split('.').length == 3) {
            blog_address = blogUrl + username;
        }
        var skinStatus = "White";
        var blogStaticHost = "https://csdnimg.cn/release/blogv2/"
          var payColumn = false
2025-05-28 20:34:22,363 - backend.spiders.csdn_spider - INFO - Read[1]: tag=script, class=None, text=var articleId = *********;
        var privateEduData = [];
        var privateData = ["ai","base64","express","framework","mapper"];//高亮数组
      var crytojs = "https://csdnimg.cn/release/blogv2/dist/components/js/crytojs-ca5b8bf6ae.min.js";
      var commentscount = 0;
      var commentAuth = 2;
      var curentUrl = "https://blog.csdn.net/fengbin2005/article/details/*********";
      var myUrl = "https://my.csdn.net/";
      var isGitCodeBlog = false;
      var isOpenSourceBlog = false;
      var isVipArticle = true;
        var highlight = ["nacos","用户名","密码","配置","开启","登录","2","和","."];//高亮数组
        var isRecommendModule = true;
          var isBaiduPre = true;
          var baiduCount = 2;
          var setBaiduJsCount = 10;
        var viewCountFormat = 1590;
      var share_card_url = "https://app-blog.csdn.net/share?article_id=*********&username=fengbin2005"
      var mallVipUrl = "https://mall.csdn.net/vip?vipSource=article"
      var vipArticleAbStyle = "t_1"
      var vipArticleCpStyle = "t_1"
      var codeAbStyle = "control"
      var detailheaderAbStyle = "control"
      var codeAiAbStyle = "control"
      var articleType = 1;
      var baiduKey = "nacos开启密码";
      var copyPopSwitch = true;
      var needInsertBaidu = true;
      var recommendRegularDomainArr = ["blog.csdn.net/.+/article/details/","download.csdn.net/download/","edu.csdn.net/course/detail/","ask.csdn.net/questions/","bbs.csdn.net/topics/","www.csdn.net/gather_.+/"]
      var codeStyle = "";
      var baiduSearchType = "baidulandingword";
      var sharData = "{\"hot\":[{\"id\":1,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a5f4260710904e538002a6ab337939b3.png\"},{\"id\":2,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/188b37199a2c4b74b1d9ffc39e0d52de.png\"},{\"id\":3,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/14ded358b631444581edd98a256bc5af.png\"},{\"id\":4,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1470f23a770444d986ad551b9c33c5be.png\"},{\"id\":5,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c329f5181dc74f6c9bd28c982bb9f91d.png\"},{\"id\":6,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ccd8a3305e81460f9c505c95b432a65f.png\"},{\"id\":7,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bc89d8283389440d97fc4d30e30f45e1.png\"},{\"id\":8,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/452d485b4a654f5592390550d2445edf.png\"},{\"id\":9,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f8b9939db2ed474a8f43a643015fc8b7.png\"},{\"id\":10,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6de8864187ab4ed3b1db0856369c36ff.png\"},{\"id\":11,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/673cc3470ff74072acba958dc0c46e2d.png\"},{\"id\":12,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/930c119760ac4491804db80f9c6d4e3f.png\"},{\"id\":13,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/15e6befb05a24233bc2b65e96aa8d972.png\"},{\"id\":14,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2075fd6822184b95a41e214de4daec13.png\"},{\"id\":15,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/859b1552db244eb6891a809263a5c657.png\"},{\"id\":16,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/0be2f920f1f74290a98921974a9613fd.png\"},{\"id\":17,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2e97e00b43f14afab494ea55ef3f4a6e.png\"},{\"id\":18,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ff4ab252f46e444686f5135d6ebbfec0.png\"},{\"id\":19,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ae029bbe99564e79911657912d36524f.png\"},{\"id\":20,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b3ece39963de440388728e9e7b9bf427.png\"},{\"id\":21,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6f14651a99ba486e926d63b6fa692997.png\"},{\"id\":22,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/83ceddf050084875a341e32dcceca721.png\"},{\"id\":23,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b90368b8fd5d4c6c8c79a707d877cf7c.png\"},{\"id\":24,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/aeffae14ecf14e079b2616528c9a393b.png\"},{\"id\":25,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c5a06b5a13d44d16bed868fc3384897a.png\"},{\"id\":26,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/08b697658b844b318cea3b119e9541ef.png\"},{\"id\":27,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/68ccb0b8d09346ac961d2b5c1a8c77bf.png\"},{\"id\":28,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a2227a247e37418cbe0ea972ba6a859b.png\"},{\"id\":29,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/3a42825fede748f9993e5bb844ad350d.png\"},{\"id\":30,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/8882abc1dd484224b636966ea38555c3.png\"},{\"id\":31,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/4f6a5f636a3e444d83cf8cc06d87a159.png\"},{\"id\":32,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1953ef79c56b4407b78d7181bdff11c3.png\"},{\"id\":33,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c04a2a4f772948ed85b5b0380ed36287.png\"},{\"id\":34,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5b4fecd05091405ea04d8c0f53e9f2c7.png\"},{\"id\":35,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b89f576d700344e280d6ceb2a66c2420.png\"},{\"id\":36,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1c65780e11804bbd9971ebadb3d78bcf.png\"},{\"id\":37,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d590db2055f345db9706eb68a7ec151a.png\"},{\"id\":38,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fe602f80700b4f6fb3c4a9e4c135510e.png\"},{\"id\":39,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/39ff2fcd31e04feba301a071976a0ba7.png\"},{\"id\":40,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f9b61b3d113f436b828631837f89fb39.png\"},{\"id\":41,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/df1aca5f610c4ad48cd16da88c9c8499.png\"},{\"id\":42,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d7acf73a1e6b41399a77a85040e10961.png\"},{\"id\":43,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b7f1b63542524b97962ff649ab4e7e23.png\"}],\"vip\":[{\"id\":1,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101150.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101154.png\"},{\"id\":2,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101204.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101208.png\"},{\"id\":3,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101211.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101215.png\"},{\"id\":4,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101218.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101220.png\"},{\"id\":5,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101223.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101226.png\"},{\"id\":6,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100635.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100639.png\"},{\"id\":7,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100642.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100644.png\"},{\"id\":8,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100647.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100649.png\"},{\"id\":9,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100652.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100655.png\"},{\"id\":10,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/55de67481fde4b04b97ad78f11fe369a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bb2418fb537e4d78b10d8765ccd810c5.png\"},{\"id\":11,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/579c713394584d128104ef1044023954.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f420d9fbcf5548079d31b5e809b6d6cd.png\"},{\"id\":12,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/75b7f3155ba642f5a4cc16b7baf44122.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a9030f5877be401f8b340b80b0d91e64.png\"},{\"id\":13,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0903d33cafa54934be3780aa54ae958d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2cd8c8929f5a42fca5da2a0aeb456203.png\"},{\"id\":14,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/949fd7c22884439fbfc3c0e9c3b8dee7.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/dafbea9bd9eb4f3b962b48dc41657f89.png\"},{\"id\":15,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4119cfddd71d4e6a8a27a18dbb74d90e.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c56310c8b6384d9e85388e4e342ce508.png\"},{\"id\":16,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/121575274da142bcbbbbc2e8243dd411.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5013993de06542f881018bb9abe2edf7.png\"},{\"id\":17,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4d97aa6dd4fe4f09a6bef5bdf8a6abcd.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/76f23877b6ad4066ad45ce8e31b4b977.png\"},{\"id\":18,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdb619daf21b4c829de63b9ebc78859d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a1abe5d27a5441f599adfe662f510243.png\"},{\"id\":19,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/676b7707bb11410f8f56bc0ed2b2345c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/7ac5b467fbf24e1d8c2de3f3332c4f54.png\"},{\"id\":20,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0becb8cc227e4723b765bdd69a20fd4a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdec85b26091486b9a89d0b8d45c3749.png\"},{\"id\":21,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/1a6c06235ad44941b38c54cbc25a370c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/410a06cda2d44b0c84578f88275caf70.png\"}],\"map\":{\"hot\":\"热门\",\"vip\":\"VIP\"}}";
      
      var canRead = false;
      var blogMoveHomeArticle = false;
      var showSearchText = "";
      var sideToolbarResult = "exp";
      var articleSource = 1;
      var articleReport = '{"pid": "blog", "spm":"1001.2101"}';
        var baiduSearchChannel = 'pc_relevant'
        var baiduSearchIdentification = '.235^v43^pc_blog_bottom_relevance_base6'
        var distRequestId = '1748435662120_08848'
        var initRewardObject = {
          giver: currentUserName,
          anchor: username,
          articleId: articleId,
          sign: ''
        }
        var isLikeStatus = false;
        var isUnLikeStatus = false;
        var studyLearnWord = "";
        var unUseCount = 0;
        var codeMaxSize = 0;
        var overCost = true;
        var isCurrentUserVip = false;
        var contentViewsHeight = 0;
        var contentViewsCount = 0;
        var contentViewsCountLimit = 5;
        var isShowConcision = false;
        var lastTime = "2025-01-17 19:10:23"
        var postTime = "2025-01-17 19:09:48"
      var isCookieConcision = false
      var isHasDirectoryModel = false
      var isShowSideModel = false
      var isShowDirectoryModel = true
      function getCookieConcision(sName){
        var allCookie = document.cookie.split("; ");
        for (var i=0; i < allCookie.length; i++){
          var aCrumb = allCookie[i].split("=");
          if (sName == aCrumb[0])
            return aCrumb[1];
        }
        return null;
      }
      if (getCookieConcision('blog_details_concision') && getCookieConcision('blog_details_concision') == 0){
        isCookieConcision = true
        isShowSideModel = true
        isShowDirectoryModel = false
      }
2025-05-28 20:34:22,370 - backend.spiders.csdn_spider - INFO - Read[2]: tag=span, class=['read-count'], text=阅读量1.5k
2025-05-28 20:34:22,372 - backend.spiders.csdn_spider - INFO - 找到标题: nacos 2.x开启登录的用户名和密码
2025-05-28 20:34:22,400 - backend.spiders.csdn_spider - INFO - 找到作者: 
2025-05-28 20:34:22,405 - backend.spiders.csdn_spider - INFO - 找到发布时间: 已于 2025-01-17 19:10:23 修改
2025-05-28 20:34:22,406 - backend.spiders.csdn_spider - INFO - 找到阅读数文本: 阅读量1.5k
2025-05-28 20:34:22,406 - backend.spiders.csdn_spider - INFO - 找到阅读数: 1500
2025-05-28 20:34:22,406 - backend.spiders.csdn_spider - INFO - 找到内容元素: {'id': 'content_views'}
2025-05-28 20:34:22,411 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 20:34:22,424 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_203422.html
2025-05-28 20:34:22,426 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 20:34:22,434 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章.html (大小: 5148 字节)
2025-05-28 20:34:22,436 - backend.utils.mailer - INFO - 附件 CSDN文章.html 添加成功
2025-05-28 20:34:22,496 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 20:34:22,773 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 20:34:23,037 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 20:34:23,687 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 20:34:23,687 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 20:34:23,688 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_203422.html
2025-05-28 20:36:55,997 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:36:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:41:55,964 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:41:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:46:55,974 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:46:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:51:55,975 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:51:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 20:56:55,962 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 20:56:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:01:55,957 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:01:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:07:07,962 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:07:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:12:07,976 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:12:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:17:07,966 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:17:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:22:07,959 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:22:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:27:07,954 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:27:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:32:08,119 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:32:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:48:15,818 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:48:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:48:15,832 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:48:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:48:15,923 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:48:15] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 21:53:17,466 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:53:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 21:58:16,185 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 21:58:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:03:15,973 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:03:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:08:15,957 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:08:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:13:15,950 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:13:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:18:16,355 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:18:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:33:29,760 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:33:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:33:29,786 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:33:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:33:29,860 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:33:29] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 22:38:29,963 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:38:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:43:29,949 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:43:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:48:29,955 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:53:29,942 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:53:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 22:58:29,932 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 22:58:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:03:29,929 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:03:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:09:07,926 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:14:07,936 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:19:07,935 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:24:07,927 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:29:07,935 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:34:07,950 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:39:07,926 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:44:07,927 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:49:07,922 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:54:08,219 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:54:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 23:59:07,957 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 23:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:04:07,930 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:09:07,923 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:14:07,922 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:19:07,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:24:07,959 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:29:08,046 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:29:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:34:07,991 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:39:07,924 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:41:18,701 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 00:41:19,003 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 00:44:07,910 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:49:07,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:54:07,906 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 00:59:07,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 00:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:04:07,900 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:09:07,909 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:14:07,898 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:19:07,916 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:24:07,905 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:29:07,942 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:34:07,898 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:39:07,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:44:07,901 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:49:07,924 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:54:07,902 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 01:59:07,890 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 01:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:04:07,889 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:09:07,895 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:14:07,888 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:19:07,887 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:24:07,882 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:29:07,882 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:30:55,317 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 02:30:55,351 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 02:34:07,878 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:39:07,961 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:44:07,880 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:49:07,887 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:54:07,872 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 02:59:07,877 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 02:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:04:07,868 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:09:07,867 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:14:07,866 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:19:07,880 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:24:07,871 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:29:07,876 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:34:07,867 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:39:07,863 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:44:07,864 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:49:07,861 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:54:07,862 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 03:59:07,862 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 03:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:04:07,863 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:09:07,853 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:14:07,854 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:19:07,865 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:24:07,854 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:29:07,864 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:34:07,863 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:39:07,846 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:44:07,887 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:49:07,852 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:54:07,849 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 04:59:07,847 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 04:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:04:07,838 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:09:07,837 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:14:07,852 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:19:07,850 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:24:07,839 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:29:07,850 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:34:07,846 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:39:07,838 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:44:07,840 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:49:07,828 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:54:07,842 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 05:59:07,848 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 05:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:04:07,830 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:09:07,831 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:14:07,841 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:19:07,850 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:24:07,836 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:29:07,842 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:34:07,845 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:39:07,837 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:41:19,150 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 06:41:19,351 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 06:44:07,834 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:49:07,877 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:54:07,820 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 06:59:07,823 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 06:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:04:07,816 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:09:07,824 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:14:07,820 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:19:07,822 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:24:07,811 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:29:07,823 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:34:07,822 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:39:07,815 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:44:07,813 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:49:07,826 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:54:07,814 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 07:59:07,816 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 07:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:04:07,811 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:09:07,800 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:14:07,816 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:19:07,811 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:24:07,804 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:29:07,809 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:30:55,494 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 08:30:55,510 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 08:34:07,807 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:39:07,808 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:44:07,802 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:49:07,815 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:54:07,840 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 08:59:07,825 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 08:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:04:07,799 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:09:07,820 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:14:07,791 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:19:07,806 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:24:07,787 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:29:07,796 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:34:07,787 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:39:07,797 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:44:07,794 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:49:07,790 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:54:07,822 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 09:59:07,805 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 09:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:04:07,797 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:09:07,791 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:14:07,787 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:19:07,793 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:24:07,792 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:29:07,778 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:34:07,785 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:39:07,788 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:44:07,784 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:49:07,781 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:54:07,776 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 10:59:07,874 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:04:07,768 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:09:07,780 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:14:07,774 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:19:07,776 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:24:07,758 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:29:07,751 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:34:07,760 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:39:07,752 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:44:07,756 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:49:07,735 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:54:07,738 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 11:59:07,735 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:04:07,735 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:09:07,733 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:14:07,727 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:19:07,740 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:24:07,723 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:29:07,726 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:34:07,718 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:39:07,716 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:41:19,474 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 12:41:19,607 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 12:44:07,721 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:49:07,715 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:54:07,721 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 12:59:07,723 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 12:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:04:07,743 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:09:07,716 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:09:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:14:07,717 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:14:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:19:07,706 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:19:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:24:07,701 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:24:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:29:07,715 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:29:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:34:07,705 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:34:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:39:07,725 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:39:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:44:07,713 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:44:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:49:07,707 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:49:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:54:07,754 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:54:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 13:59:07,708 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 13:59:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 14:04:07,716 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 14:04:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-29 16:34:40,247 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-29 16:34:42,685 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-29 16:34:43,666 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 16:34:43] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:05:18,928 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:05:18,931 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:05:18,931 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:05:18,931 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:05:18,931 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:05:19,538 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-30 01:05:21,847 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-30 01:05:21,862 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-30 01:05:22,172 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-30 01:05:22,180 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-30 01:05:22,186 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-30 01:05:22,189 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-30 01:05:22,194 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-30 01:05:22,197 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-30 01:05:22,249 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:05:22,288 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-05-30 01:05:22,289 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-30 01:05:22,292 - werkzeug - INFO -  * Restarting with stat
2025-05-30 01:05:23,155 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:05:23,156 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:05:23,157 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:05:23,157 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:05:23,158 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:05:24,484 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:05:24,511 - werkzeug - WARNING -  * Debugger is active!
2025-05-30 01:05:24,531 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-30 01:05:30,315 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "GET / HTTP/1.1" 200 -
2025-05-30 01:05:30,369 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,393 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,420 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,438 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,456 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,460 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,471 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,486 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,490 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,513 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,523 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,532 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:30,949 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-30 01:05:30,952 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:30] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-30 01:05:31,001 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:31] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-30 01:05:31,464 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:31] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-30 01:05:31,474 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:31] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-30 01:05:31,483 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:31] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-30 01:05:31,494 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:31] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-30 01:05:52,662 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-30 01:05:58,401 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "GET / HTTP/1.1" 200 -
2025-05-30 01:05:58,417 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,420 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,429 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,436 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,448 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,452 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,458 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,465 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,471 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,479 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,481 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,485 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:05:58,515 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:05:58,532 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:05:59,013 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:05:59,019 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:05:59,056 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:05:59] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-30 01:06:01,327 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:06:01] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-30 01:06:02,163 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:06:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:06:02,196 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:06:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-30 01:06:11,056 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:06:11] "GET /api/activation/list?per_page=50 HTTP/1.1" 200 -
2025-05-30 01:06:34,638 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:06:34] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-30 01:07:12,680 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:07:12] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-30 01:07:21,728 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:07:21] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-30 01:07:29,374 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-30 01:07:31,453 - backend.spiders.kuaishou_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:07:31,465 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:07:31,469 - backend.spiders.kuaishou_spider - ERROR - 快手爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:07:31,473 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB590C790>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 221, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 186, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:07:31,486 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:07:31] "[35m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 500 -
2025-05-30 01:07:53,892 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:07:55,905 - backend.spiders.douyin_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /Br5NxU49rb4/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:07:55,908 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /Br5NxU49rb4/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /Br5NxU49rb4/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:07:55,918 - backend.spiders.douyin_spider - ERROR - 抖音爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:07:55,922 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /Br5NxU49rb4/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /Br5NxU49rb4/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59F4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 227, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 192, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:07:55,931 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:07:55] "[35m[1mPOST /api/douyin/parse HTTP/1.1[0m" 500 -
2025-05-30 01:08:11,518 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-30 01:08:13,533 - backend.spiders.kuaishou_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:08:13,543 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:08:13,561 - backend.spiders.kuaishou_spider - ERROR - 快手爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:08:13,565 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EDDD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 221, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 186, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:08:13,573 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:08:13] "[35m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 500 -
2025-05-30 01:08:41,766 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-30 01:08:43,784 - backend.spiders.kuaishou_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:08:43,788 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:08:43,797 - backend.spiders.kuaishou_spider - ERROR - 快手爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:08:43,802 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59301D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 221, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 186, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:08:43,809 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:08:43] "[35m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 500 -
2025-05-30 01:08:44,892 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-30 01:08:46,926 - backend.spiders.kuaishou_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:08:46,935 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:08:46,954 - backend.spiders.kuaishou_spider - ERROR - 快手爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:08:46,959 - backend.spiders.kuaishou_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 161, in extract_video_info
    result_list = parseKsVideo(url)
                  ^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 50, in parseKsVideo
    final_url = get_final_url(video_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 27, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.kuaishou.com', port=443): Max retries exceeded with url: /2zmLflo (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB592E390>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 221, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\kuaishou_spider.py", line 186, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:08:46,969 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:08:46] "[35m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 500 -
2025-05-30 01:08:52,044 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-05-30 01:08:52,044 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-05-30 01:08:54,093 - backend.spiders.bilibili_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='www.bilibili.com', port=443): Max retries exceeded with url: /video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:08:54,101 - backend.spiders.bilibili_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.bilibili.com', port=443): Max retries exceeded with url: /video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 136, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 36, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='www.bilibili.com', port=443): Max retries exceeded with url: /video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:08:54,116 - backend.spiders.bilibili_spider - ERROR - 哔哩哔哩爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:08:54,120 - backend.spiders.bilibili_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.bilibili.com', port=443): Max retries exceeded with url: /video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 136, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 36, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='www.bilibili.com', port=443): Max retries exceeded with url: /video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB59EE1D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 327, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\bilibili_spider.py", line 208, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:08:54,126 - backend.api.bilibili_api - ERROR - 解析视频失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:08:54,128 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:08:54] "[35m[1mPOST /api/bilibili/parse HTTP/1.1[0m" 500 -
2025-05-30 01:09:29,604 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "GET / HTTP/1.1" 200 -
2025-05-30 01:09:29,625 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,628 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,643 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,644 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,646 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,649 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,658 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,664 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,669 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,682 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,684 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,692 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:29,706 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:09:29,716 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:29] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:09:30,210 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:09:30,216 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:09:30,238 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:30] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-30 01:09:32,650 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:09:34,682 - backend.spiders.douyin_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:09:34,685 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:09:34,690 - backend.spiders.douyin_spider - ERROR - 抖音爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:09:34,693 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A87210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 227, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 192, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:09:34,698 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:34] "[35m[1mPOST /api/douyin/parse HTTP/1.1[0m" 500 -
2025-05-30 01:09:40,820 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:09:42,846 - backend.spiders.douyin_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:09:42,851 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:09:42,857 - backend.spiders.douyin_spider - ERROR - 抖音爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:09:42,860 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A86B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 227, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 192, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:09:42,869 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:42] "[35m[1mPOST /api/douyin/parse HTTP/1.1[0m" 500 -
2025-05-30 01:09:44,504 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "GET / HTTP/1.1" 200 -
2025-05-30 01:09:44,522 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,526 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,528 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,532 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,548 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,551 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,563 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,573 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,573 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,582 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,592 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,599 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:09:44,619 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:09:44,628 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:09:45,118 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:09:45,125 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:09:45,150 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:45] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-30 01:09:47,170 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:09:49,188 - backend.spiders.douyin_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:09:49,196 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:09:49,210 - backend.spiders.douyin_spider - ERROR - 抖音爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:09:49,216 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022EB5A84350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 227, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 192, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:09:49,223 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:09:49] "[35m[1mPOST /api/douyin/parse HTTP/1.1[0m" 500 -
2025-05-30 01:10:14,057 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:10:14,087 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:10:14,087 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:10:14,095 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:10:14,095 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:10:14,691 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-30 01:10:15,221 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-30 01:10:15,225 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-30 01:10:15,472 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-30 01:10:15,477 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-30 01:10:15,483 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-30 01:10:15,486 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-30 01:10:15,490 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-30 01:10:15,493 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-30 01:10:15,515 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:10:15,566 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-30 01:10:15,568 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-30 01:10:15,570 - werkzeug - INFO -  * Restarting with stat
2025-05-30 01:10:16,170 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:10:16,171 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:10:16,172 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:10:16,172 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:10:16,172 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:10:17,037 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:10:17,052 - werkzeug - WARNING -  * Debugger is active!
2025-05-30 01:10:17,061 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-30 01:10:24,884 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "GET / HTTP/1.1" 200 -
2025-05-30 01:10:24,912 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,915 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,920 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,923 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,934 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,979 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,990 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:24,991 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:24] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:25,004 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:25,008 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:25,016 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:25,018 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-30 01:10:27,090 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:10:27,101 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-30 01:10:27,598 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:10:27,602 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-30 01:10:27,630 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:27] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-30 01:10:30,400 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:10:32,441 - backend.spiders.douyin_spider - ERROR - 提取视频信息失败: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-05-30 01:10:32,447 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

2025-05-30 01:10:32,450 - backend.spiders.douyin_spider - ERROR - 抖音爬虫执行失败: 网络连接失败，请检查网络连接或稍后重试
2025-05-30 01:10:32,452 - backend.spiders.douyin_spider - ERROR - Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 131, in extract_video_info
    final_url = get_final_url(url)
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 34, in get_final_url
    response = requests.get(
               ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='v.douyin.com', port=443): Max retries exceeded with url: /LAu5x4yaRq8/ (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001D0F6DEDD50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 227, in execute
    video_info = self.extract_video_info(video_url)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\douyin_spider.py", line 192, in extract_video_info
    raise Exception(user_error)
Exception: 网络连接失败，请检查网络连接或稍后重试

2025-05-30 01:10:32,460 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:10:32] "[35m[1mPOST /api/douyin/parse HTTP/1.1[0m" 500 -
2025-05-30 01:19:26,610 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:19:26,611 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:19:26,611 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:19:26,612 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:19:26,612 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:19:27,590 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-30 01:19:30,270 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-30 01:19:30,282 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-30 01:19:30,573 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-30 01:19:30,578 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-30 01:19:30,583 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-30 01:19:30,585 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-30 01:19:30,589 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-30 01:19:30,592 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-30 01:19:30,637 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:19:30,669 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-30 01:19:30,670 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-30 01:19:30,672 - werkzeug - INFO -  * Restarting with stat
2025-05-30 01:19:31,326 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-30 01:19:31,327 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-30 01:19:31,327 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-30 01:19:31,327 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-30 01:19:31,327 - superspider - INFO - 定时任务调度器初始化成功
2025-05-30 01:19:32,217 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-30 01:19:32,232 - werkzeug - WARNING -  * Debugger is active!
2025-05-30 01:19:32,242 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-30 01:19:44,025 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET / HTTP/1.1" 200 -
2025-05-30 01:19:44,069 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-30 01:19:44,069 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-30 01:19:44,083 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-30 01:19:44,103 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-30 01:19:44,128 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-30 01:19:44,138 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-30 01:19:44,165 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-30 01:19:44,165 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-30 01:19:44,168 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-30 01:19:44,172 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-30 01:19:44,184 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-30 01:19:44,184 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-30 01:19:44,482 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-30 01:19:44,489 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-30 01:19:44,848 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-30 01:19:45,002 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:45] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-30 01:19:45,007 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:45] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-30 01:19:45,009 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:45] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-30 01:19:45,013 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:19:45] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-30 01:20:03,993 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:03] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-30 01:20:11,587 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-30 01:20:12,329 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-30 01:20:18,134 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-30 01:20:18,136 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:18] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-30 01:20:18,661 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:18] "POST /api/search/record HTTP/1.1" 200 -
2025-05-30 01:20:24,867 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:24] "POST /api/search/record HTTP/1.1" 200 -
2025-05-30 01:20:34,942 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-30 01:20:34,954 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-30 01:20:35,006 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-30 01:20:35,023 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 01:20:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-05 20:44:35,519 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-05 20:44:35,520 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-05 20:44:35,520 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-05 20:44:35,520 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-05 20:44:35,520 - superspider - INFO - 定时任务调度器初始化成功
2025-06-05 20:44:35,918 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-05 20:44:38,943 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-05 20:44:38,951 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-05 20:44:39,262 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-05 20:44:39,266 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-05 20:44:39,270 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-05 20:44:39,274 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-05 20:44:39,279 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-05 20:44:39,283 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-05 20:44:39,294 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-05 20:44:39,342 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-05 20:44:39,390 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-05 20:44:39,391 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-05 20:44:39,393 - werkzeug - INFO -  * Restarting with stat
2025-06-05 20:44:40,103 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-05 20:44:40,104 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-05 20:44:40,104 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-05 20:44:40,104 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-05 20:44:40,104 - superspider - INFO - 定时任务调度器初始化成功
2025-06-05 20:44:41,313 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-05 20:44:41,345 - werkzeug - WARNING -  * Debugger is active!
2025-06-05 20:44:41,364 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-05 20:44:45,565 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "GET / HTTP/1.1" 200 -
2025-06-05 20:44:45,709 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,711 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,746 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,785 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,794 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,808 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,832 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,843 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,849 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-05 20:44:45,859 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,889 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:45,894 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:44:46,260 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 20:44:46,267 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 20:44:46,298 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 20:44:46,717 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 20:44:46,725 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 20:44:46,730 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 20:44:46,739 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:46] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 20:44:58,108 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:44:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-05 20:45:02,506 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "GET / HTTP/1.1" 200 -
2025-06-05 20:45:02,525 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,525 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,526 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,538 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,540 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,553 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,553 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,564 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,568 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,575 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,575 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,582 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:02,600 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-05 20:45:02,610 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-05 20:45:03,103 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:03,108 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:03,146 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:03] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-05 20:45:04,282 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:04] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-05 20:45:05,302 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:05,320 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-05 20:45:44,158 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET / HTTP/1.1" 200 -
2025-06-05 20:45:44,176 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,189 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,191 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,193 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,196 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,217 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,225 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,252 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,272 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,287 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,295 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,297 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:45:44,334 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-05 20:45:44,347 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-05 20:45:44,836 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:44,844 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:44,863 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:44] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-05 20:45:47,113 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:47] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-05 20:45:48,325 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:48] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-05 20:45:48,340 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:45:48] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-05 20:46:59,499 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:46:59] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-05 20:49:08,499 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "GET / HTTP/1.1" 200 -
2025-06-05 20:49:08,518 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,521 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,523 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,521 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,539 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,542 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,547 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,552 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,557 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-05 20:49:08,565 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,580 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,581 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 20:49:08,601 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 20:49:08,609 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:08] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 20:49:09,103 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 20:49:09,110 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 20:49:09,112 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 20:49:09,116 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:49:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 20:54:09,643 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:54:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 20:54:09,647 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 20:54:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:04:59,449 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:04:59] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:04:59,461 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:04:59] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:09:09,633 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:09:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:09:09,637 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:09:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:14:35,625 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:14:35] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:14:35,638 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:14:35] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:19:09,628 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:19:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:19:09,634 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:19:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:24:09,635 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:24:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:24:09,640 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:24:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:29:29,632 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:29:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:29:29,635 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:29:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:34:29,645 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:34:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:34:29,650 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:34:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:39:29,658 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:39:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:39:29,662 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:39:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:44:29,621 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:44:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:44:29,625 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:44:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:49:29,657 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:49:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:49:29,660 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:49:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:54:29,651 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:54:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:54:29,655 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:54:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 21:59:29,651 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:59:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 21:59:29,660 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 21:59:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 22:04:29,613 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:04:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 22:04:29,617 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:04:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 22:09:29,612 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:09:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 22:09:29,615 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:09:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 22:14:29,629 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:14:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 22:14:29,633 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 22:14:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:23:59,777 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:23:59] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:23:59,795 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:23:59] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:28:40,851 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-05 23:28:40,852 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-05 23:28:40,852 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-05 23:28:40,853 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-05 23:28:40,853 - superspider - INFO - 定时任务调度器初始化成功
2025-06-05 23:28:41,254 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-05 23:28:41,870 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-05 23:28:41,877 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-05 23:28:42,128 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-05 23:28:42,133 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-05 23:28:42,136 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-05 23:28:42,139 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-05 23:28:42,145 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-05 23:28:42,148 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-05 23:28:42,152 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-05 23:28:42,179 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-05 23:28:42,223 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-05 23:28:42,223 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-05 23:28:42,227 - werkzeug - INFO -  * Restarting with stat
2025-06-05 23:28:42,914 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-05 23:28:42,916 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-05 23:28:42,916 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-05 23:28:42,916 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-05 23:28:42,917 - superspider - INFO - 定时任务调度器初始化成功
2025-06-05 23:28:44,013 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-05 23:28:44,031 - werkzeug - WARNING -  * Debugger is active!
2025-06-05 23:28:44,047 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-05 23:28:47,460 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "GET / HTTP/1.1" 200 -
2025-06-05 23:28:47,603 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,653 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,668 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,693 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,696 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,703 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,718 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,732 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-05 23:28:47,737 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,759 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,852 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:47,853 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:48,069 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 23:28:48,079 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 23:28:48,568 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:28:48,576 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:28:48,578 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:28:48,584 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:48] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:28:51,012 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "GET / HTTP/1.1" 200 -
2025-06-05 23:28:51,028 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,029 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,031 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,043 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,062 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,062 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,079 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,090 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,092 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,100 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,102 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,104 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-05 23:28:51,129 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 23:28:51,135 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-05 23:28:51,627 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:28:51,632 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:28:51,636 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:28:51,644 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:28:51] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:29:09,609 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:29:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:29:09,615 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:29:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:30:19,145 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-05 23:30:19,857 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-06-05 23:30:30,891 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-05 23:30:30,892 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:30:30] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-05 23:34:09,620 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:34:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:34:09,625 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:34:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:39:29,611 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:39:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:39:29,614 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:39:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:44:29,609 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:44:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:44:29,613 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:44:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:49:29,604 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:49:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:49:29,610 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:49:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:54:29,609 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:54:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:54:29,616 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:54:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-05 23:59:29,600 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:59:29] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-05 23:59:29,603 - werkzeug - INFO - 127.0.0.1 - - [05/Jun/2025 23:59:29] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:02:16,306 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 00:02:16,308 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 00:02:16,308 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 00:02:16,308 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 00:02:16,308 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 00:02:16,735 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 00:02:17,285 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 00:02:17,288 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 00:02:17,524 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 00:02:17,531 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 00:02:17,535 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 00:02:17,538 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 00:02:17,541 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 00:02:17,546 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 00:02:17,552 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 00:02:17,575 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 00:02:17,626 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 00:02:17,626 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 00:02:17,635 - werkzeug - INFO -  * Restarting with stat
2025-06-06 00:02:18,382 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 00:02:18,384 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 00:02:18,384 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 00:02:18,384 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 00:02:18,385 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 00:02:19,473 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 00:02:19,509 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 00:02:19,523 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 00:02:23,129 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "GET / HTTP/1.1" 200 -
2025-06-06 00:02:23,239 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,246 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,254 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,318 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,320 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,320 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,354 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-06 00:02:23,355 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,356 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,389 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,405 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:23,405 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-06 00:02:23,852 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:02:23,876 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:23] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:02:24,199 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:24] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:02:24,206 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:24] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:02:24,216 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:24] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:02:24,223 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:24] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:02:26,920 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:26] "GET / HTTP/1.1" 200 -
2025-06-06 00:02:26,948 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:26,968 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:26] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:26,987 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:26] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:26,994 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:26] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,000 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,030 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,049 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,058 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,068 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,077 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,086 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,097 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:27,118 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:02:27,137 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:02:27,612 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:02:27,616 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:02:27,624 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:02:27,632 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:27] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:02:41,523 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:41] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-06 00:02:46,081 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET / HTTP/1.1" 200 -
2025-06-06 00:02:46,123 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,125 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,129 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,138 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,141 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,149 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,161 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,170 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,173 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,195 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,210 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,216 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:02:46,241 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 00:02:46,253 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 00:02:46,736 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:02:46,742 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:02:46,785 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 00:02:53,856 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:53] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-06 00:02:53,870 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:53] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-06 00:02:53,922 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:53] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-06 00:02:53,969 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:53] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-06 00:02:59,381 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:02:59] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-06 00:03:04,353 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "GET / HTTP/1.1" 200 -
2025-06-06 00:03:04,376 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,378 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,398 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,403 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,411 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,414 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,417 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,439 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,467 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,477 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,479 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,483 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:03:04,497 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:03:04,508 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 00:03:04,991 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:03:04,995 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 00:03:04,997 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:04] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:03:05,001 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:05] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 00:03:23,035 - superspider.auth - INFO - 验证码发送到 13157568559: 539539
2025-06-06 00:03:23,037 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:23] "POST /api/auth/send-sms HTTP/1.1" 200 -
2025-06-06 00:03:42,092 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:03:42] "POST /api/auth/sms-login HTTP/1.1" 200 -
2025-06-06 00:04:40,386 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "GET / HTTP/1.1" 200 -
2025-06-06 00:04:40,403 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,404 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,413 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,421 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,435 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,438 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,448 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,455 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,473 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,504 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,506 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,520 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 00:04:40,548 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 00:04:40,563 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 00:04:41,047 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:04:41,054 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:04:41,103 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:04:41] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 00:09:41,606 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:09:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:14:41,596 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:14:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:19:41,594 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:19:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:24:41,597 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:24:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:29:41,604 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:29:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:34:41,592 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:34:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:40:29,591 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:40:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:45:29,639 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:45:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:50:29,607 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:50:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 00:55:29,612 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 00:55:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 01:00:29,591 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 01:00:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 01:05:29,598 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 01:05:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 01:10:29,634 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 01:10:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 06:02:17,744 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 06:02:17,914 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 06:02:19,634 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 06:02:19,813 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 11:42:01,900 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:42:01,914 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:42:01,937 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:42:01,946 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:42:02,009 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 11:42:02,027 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:42:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 11:47:01,938 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:47:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:52:02,417 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:52:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 11:57:02,413 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 11:57:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:02:02,438 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:02:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:02:18,070 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 12:02:18,153 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 12:02:19,971 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 12:02:19,989 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 12:07:02,422 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:07:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:12:02,443 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:12:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:17:29,424 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:17:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:22:29,426 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:22:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:27:29,418 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:27:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:32:29,422 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:32:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:37:29,412 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:37:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:42:29,411 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:42:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:47:29,424 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:47:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:52:29,399 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:52:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 12:57:29,418 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 12:57:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 13:02:29,411 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 13:02:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 13:07:02,410 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 13:07:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 13:12:29,399 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 13:12:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 13:17:02,411 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 13:17:02] "GET /api/permission/check HTTP/1.1" 200 -
