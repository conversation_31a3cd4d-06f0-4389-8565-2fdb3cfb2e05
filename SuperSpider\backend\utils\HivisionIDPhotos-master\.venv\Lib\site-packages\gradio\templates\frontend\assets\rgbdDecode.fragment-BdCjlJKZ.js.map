{"version": 3, "file": "rgbdDecode.fragment-BdCjlJKZ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/rgbdDecode.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"rgbdDecodePixelShader\";\nconst shader = `varying vUV: vec2f;var textureSamplerSampler: sampler;var textureSampler: texture_2d<f32>;\n#include<helperFunctions>\n#define CUSTOM_FRAGMENT_DEFINITIONS\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {fragmentOutputs.color=vec4f(fromRGBD(textureSample(textureSampler,textureSamplerSampler,input.vUV)),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const rgbdDecodePixelShaderWGSL = { name, shader };\n//# sourceMappingURL=rgbdDecode.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "rgbdDecodePixelShaderWGSL"], "mappings": "qIAGA,MAAMA,EAAO,wBACPC,EAAS;AAAA;AAAA;AAAA;AAAA,6JAMVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAA4B,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}