import{ar as a,an as m,ao as d}from"./index-Cb4A4-Xi.js";import{GLTFLoader as h}from"./glTFLoader-D-NF4fEj.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./bone-CgNwSK5F.js";import"./rawTexture-PjZ4PTsN.js";import"./assetContainer-CIn78ZXO.js";import"./objectModelMapping-D3Nr8hfO.js";const t="KHR_materials_emissive_strength";class p{constructor(e){this.name=t,this.order=170,this._loader=e,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,i){return h.LoadExtensionAsync(e,s,this.name,(o,n)=>this._loader.loadMaterialPropertiesAsync(e,s,i).then(()=>{this._loadEmissiveProperties(o,n,i)}))}_loadEmissiveProperties(e,s,i){if(!(i instanceof a))throw new Error(`${e}: Material type not supported`);s.emissiveStrength!==void 0&&(i.emissiveIntensity=s.emissiveStrength)}}m(t);d(t,!0,r=>new p(r));export{p as KHR_materials_emissive_strength};
//# sourceMappingURL=KHR_materials_emissive_strength-D_ItCyTt.js.map
