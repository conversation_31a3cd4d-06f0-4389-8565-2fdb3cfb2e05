#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的限制系统测试
创建一个测试用户并测试限制功能
"""

import sys
import os
import requests
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.main import app, db
from backend.models.user import User
from backend.models.user_usage import UserUsage

def create_test_user():
    """创建测试用户"""
    
    print("👤 创建测试用户...")
    
    try:
        with app.app_context():
            # 检查是否已存在测试用户
            test_user = User.query.filter_by(username='testlimit').first()
            
            if test_user:
                print(f"✅ 测试用户已存在: {test_user.username}")
                return test_user.username
            
            # 创建新的测试用户（普通用户）
            test_user = User(
                username='testlimit',
                password='test123456',  # 构造函数需要password参数
                phone='13900139000',
                role='normal_user'  # 普通用户，限制为5次/天，3次/分钟
            )
            
            db.session.add(test_user)
            db.session.commit()
            
            print(f"✅ 创建测试用户成功: {test_user.username}")
            print(f"   📊 角色: {test_user.role}")
            
            # 显示权限
            permissions = test_user.get_permissions()
            print(f"   📥 下载限制: {permissions.get('download_limit', 'N/A')}/天")
            print(f"   🔄 API限制: {permissions.get('api_rate_limit', 'N/A')}/分钟")
            
            return test_user.username
            
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_limit_system():
    """测试限制系统"""
    
    print("\n🔍 测试限制系统...")
    
    base_url = "http://127.0.0.1:5000/api"
    session = requests.Session()
    
    # 1. 登录
    print("1️⃣ 登录测试用户...")
    login_data = {
        "account": "testlimit",
        "password": "test123456"
    }
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data)
        print(f"登录响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功!")
                user_info = result.get('data', {})
                print(f"👤 用户: {user_info.get('username', 'N/A')}")
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            try:
                result = response.json()
                print(f"错误信息: {result.get('message', response.text[:100])}")
            except:
                print(f"错误信息: {response.text[:100]}")
            return False
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 2. 检查初始使用情况
    print("\n2️⃣ 检查初始使用情况...")
    try:
        response = session.get(f"{base_url}/usage/today")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                usage_data = data.get('data', {})
                print(f"✅ 初始使用情况:")
                print(f"   📥 下载次数: {usage_data.get('download_count', 0)}")
                print(f"   🔄 API调用: {usage_data.get('api_call_count', 0)}")
                
                limits = usage_data.get('limits', {})
                print(f"   📊 限制: 下载{limits.get('download_limit', 'N/A')}/天, API{limits.get('api_rate_limit', 'N/A')}/分钟")
            else:
                print(f"❌ 获取使用情况失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ 使用统计API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 检查使用情况失败: {e}")
    
    # 3. 测试CSDN搜索限制
    print("\n3️⃣ 测试CSDN搜索限制...")
    test_data = {
        "article_url": "https://blog.csdn.net/weixin_44799217/article/details/126896103",
        "email": "<EMAIL>",
        "format": "html"
    }
    
    # 连续发送请求测试限制
    max_requests = 8  # 测试8次，普通用户限制是5次/天，3次/分钟
    
    for i in range(max_requests):
        print(f"\n   第{i+1}次搜索请求:")
        
        try:
            response = session.post(f"{base_url}/csdn/parse", json=test_data, timeout=10)
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 搜索成功")
                    data = result.get('data', {})
                    title = data.get('title', 'N/A')
                    print(f"   📄 文章标题: {title[:30]}...")
                else:
                    print(f"   ⚠️ 搜索失败: {result.get('message', '未知错误')}")
            elif response.status_code == 429:
                result = response.json()
                print(f"   🚫 达到限制: {result.get('message', '限制超出')}")
                limit_data = result.get('data', {})
                print(f"   📊 剩余下载: {limit_data.get('remaining_downloads', 'N/A')}")
                print(f"   📊 剩余API调用: {limit_data.get('remaining_api_calls', 'N/A')}")
                print(f"   📊 限制类型: {limit_data.get('limit_type', 'N/A')}")
                
                # 如果达到限制，停止测试
                if i >= 4:  # 第5次开始应该被限制
                    print("   ✅ 限制系统工作正常!")
                    break
            elif response.status_code == 403:
                result = response.json()
                print(f"   🔒 权限不足: {result.get('message', '权限被拒绝')}")
            else:
                print(f"   ❌ 其他错误: {response.status_code}")
                try:
                    result = response.json()
                    print(f"   错误信息: {result.get('message', response.text[:50])}")
                except:
                    print(f"   错误信息: {response.text[:50]}")
                    
        except requests.exceptions.Timeout:
            print("   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 检查当前使用情况
        try:
            response = session.get(f"{base_url}/usage/today")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    usage_data = data.get('data', {})
                    print(f"   📊 当前使用: 下载{usage_data.get('download_count', 0)}, API{usage_data.get('api_call_count', 0)}")
        except:
            pass
        
        # 如果不是最后一次，等待一下
        if i < max_requests - 1:
            print("   ⏳ 等待3秒...")
            time.sleep(3)
    
    # 4. 最终统计
    print("\n4️⃣ 最终使用统计...")
    try:
        response = session.get(f"{base_url}/usage/today")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                usage_data = data.get('data', {})
                print(f"✅ 最终使用情况:")
                print(f"   📥 下载次数: {usage_data.get('download_count', 0)}")
                print(f"   🔄 API调用: {usage_data.get('api_call_count', 0)}")
                print(f"   ⏱️ 本分钟API: {usage_data.get('api_calls_this_minute', 0)}")
                
                limits = usage_data.get('limits', {})
                remaining = usage_data.get('remaining', {})
                print(f"   📊 限制: 下载{limits.get('download_limit', 'N/A')}/天, API{limits.get('api_rate_limit', 'N/A')}/分钟")
                print(f"   📊 剩余: 下载{remaining.get('downloads', 'N/A')}, API{remaining.get('api_calls', 'N/A')}")
    except Exception as e:
        print(f"❌ 最终统计失败: {e}")
    
    return True

def main():
    """主函数"""
    
    print("🚀 简单限制系统测试")
    print("=" * 50)
    
    # 创建测试用户
    username = create_test_user()
    
    if not username:
        print("❌ 无法创建测试用户，测试终止")
        return
    
    # 等待一下确保服务器准备好
    print("\n⏳ 等待服务器准备...")
    time.sleep(2)
    
    # 测试限制系统
    success = test_limit_system()
    
    if success:
        print("\n🎉 测试完成!")
        print("\n📝 总结:")
        print("1. ✅ 测试用户创建成功")
        print("2. ✅ 登录系统正常")
        print("3. ✅ 使用统计API正常")
        print("4. ✅ 限制系统按预期工作")
        print("5. ✅ 普通用户限制: 5次/天搜索，3次/分钟API")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
