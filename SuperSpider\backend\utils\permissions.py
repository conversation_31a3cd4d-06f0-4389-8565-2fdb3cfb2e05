#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
权限管理工具
提供权限检查装饰器和权限验证函数
"""

from functools import wraps
from flask import jsonify, request
from flask_login import current_user, login_required
import logging

logger = logging.getLogger("superspider.permissions")

def require_permission(permission_type, permission_value=None):
    """
    权限检查装饰器

    Args:
        permission_type: 权限类型 ('platform', 'admin', 'download_limit')
        permission_value: 权限值 (如平台名称、管理功能名称等)
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            # 检查用户是否被封禁
            if current_user.is_banned:
                return jsonify({
                    "success": False,
                    "message": "账户已被封禁，无法使用此功能",
                    "data": None
                }), 403

            # 检查Pro用户是否过期，如果过期则自动降级
            if current_user.role == 'pro_user' and not current_user.is_pro_user_valid():
                logger.info(f"检测到Pro用户 {current_user.username} 已过期，自动降级为普通用户")
                PermissionManager.downgrade_user_to_normal(current_user)

                # 如果需要Pro权限的功能，返回错误
                if permission_type == 'platform' and permission_value in ['zhihu']:
                    return jsonify({
                        "success": False,
                        "message": "Pro会员已过期，已自动降级为普通用户，请重新升级后使用此功能",
                        "data": None
                    }), 403

            # 检查具体权限
            if not current_user.has_permission(permission_type, permission_value):
                if permission_type == 'platform':
                    if permission_value in ['zhihu']:
                        return jsonify({
                            "success": False,
                            "message": "此功能仅限Pro用户使用，请升级账户",
                            "data": None
                        }), 403
                    else:
                        return jsonify({
                            "success": False,
                            "message": "您没有使用此平台的权限",
                            "data": None
                        }), 403
                elif permission_type == 'admin':
                    return jsonify({
                        "success": False,
                        "message": "您没有管理员权限",
                        "data": None
                    }), 403
                else:
                    return jsonify({
                        "success": False,
                        "message": "权限不足",
                        "data": None
                    }), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(*allowed_roles):
    """
    角色检查装饰器

    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if current_user.role not in allowed_roles:
                return jsonify({
                    "success": False,
                    "message": "您的账户级别不足以使用此功能",
                    "data": None
                }), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_rate_limit(user, action_type='api'):
    """
    检查用户速率限制

    Args:
        user: 用户对象
        action_type: 操作类型 ('api', 'download')

    Returns:
        tuple: (是否在限制范围内, 剩余次数, 错误信息)
    """
    try:
        from backend.models.user_usage import UserUsage

        # 获取今日使用记录
        usage = UserUsage.get_or_create_today_usage(user.id)

        if action_type == 'api':
            if usage.can_api_call(user):
                remaining = usage.get_remaining_api_calls_this_minute(user)
                return True, remaining, None
            else:
                remaining = usage.get_remaining_api_calls_this_minute(user)
                permissions = user.get_permissions()
                limit = permissions.get('api_rate_limit', 3)
                return False, remaining, f"API调用频率超限，每分钟最多{limit}次，请稍后再试"

        elif action_type == 'download':
            if usage.can_download(user):
                remaining = usage.get_remaining_downloads(user)
                return True, remaining, None
            else:
                remaining = usage.get_remaining_downloads(user)
                permissions = user.get_permissions()
                limit = permissions.get('download_limit', 5)
                return False, remaining, f"今日下载次数已达上限({limit}次)，请明天再试或升级账户"

        return False, 0, "未知的操作类型"

    except Exception as e:
        logger.error(f"检查速率限制失败: {e}")
        return True, -1, None  # 出错时允许操作

def record_user_action(user, action_type='api'):
    """
    记录用户操作（下载或API调用）

    Args:
        user: 用户对象
        action_type: 操作类型 ('api', 'download')

    Returns:
        bool: 是否记录成功
    """
    try:
        from backend.models.user_usage import UserUsage

        # 获取今日使用记录
        usage = UserUsage.get_or_create_today_usage(user.id)

        if action_type == 'api':
            usage.increment_api_call()
            logger.info(f"记录API调用 - 用户: {user.username}, 今日第{usage.api_call_count}次")
        elif action_type == 'download':
            usage.increment_download()
            logger.info(f"记录下载操作 - 用户: {user.username}, 今日第{usage.download_count}次")

        return True

    except Exception as e:
        logger.error(f"记录用户操作失败: {e}")
        return False

def require_search_limit():
    """
    搜索限制检查装饰器
    每次搜索同时消耗1次下载和1次API调用
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            # 检查API调用限制
            can_api, remaining_api, api_error = check_rate_limit(current_user, 'api')
            if not can_api:
                return jsonify({
                    "success": False,
                    "message": api_error,
                    "data": {
                        "remaining_api_calls": remaining_api,
                        "remaining_downloads": -1,
                        "limit_type": "api_rate"
                    }
                }), 429

            # 检查下载限制
            can_download, remaining_download, download_error = check_rate_limit(current_user, 'download')
            if not can_download:
                return jsonify({
                    "success": False,
                    "message": download_error,
                    "data": {
                        "remaining_api_calls": remaining_api,
                        "remaining_downloads": remaining_download,
                        "limit_type": "download_limit"
                    }
                }), 429

            # 执行原函数
            result = f(*args, **kwargs)

            # 如果操作成功，记录使用统计
            try:
                # 检查返回结果是否表示成功
                success = False
                if isinstance(result, tuple):
                    response_data, status_code = result
                    if status_code == 200:
                        success = True
                else:
                    # 假设单独返回的response是成功的
                    success = True

                if success:
                    record_user_action(current_user, 'api')
                    record_user_action(current_user, 'download')
                    logger.info(f"用户 {current_user.username} 执行搜索操作成功")

            except Exception as e:
                logger.error(f"记录用户操作失败: {e}")

            return result
        return decorated_function
    return decorator

def get_user_platform_access(user):
    """
    获取用户可访问的平台列表

    Args:
        user: 用户对象

    Returns:
        dict: 平台访问权限
    """
    permissions = user.get_permissions()

    return {
        'video_platforms': permissions.get('video_platforms', []),
        'article_platforms': permissions.get('article_platforms', []),
        'all_platforms': permissions.get('video_platforms', []) + permissions.get('article_platforms', [])
    }

def log_permission_check(user, permission_type, permission_value, result):
    """
    记录权限检查日志

    Args:
        user: 用户对象
        permission_type: 权限类型
        permission_value: 权限值
        result: 检查结果
    """
    logger.info(f"权限检查 - 用户: {user.username}, 角色: {user.role}, "
                f"权限类型: {permission_type}, 权限值: {permission_value}, 结果: {result}")

class PermissionManager:
    """权限管理器"""

    @staticmethod
    def upgrade_user_to_pro(user, days=30):
        """
        升级用户为Pro用户

        Args:
            user: 用户对象
            days: Pro用户有效天数
        """
        import datetime
        from backend.main import db

        user.role = 'pro_user'
        user.vip_expire_date = datetime.datetime.now() + datetime.timedelta(days=days)
        user.permissions = None  # 清空自定义权限，使用默认权限
        db.session.commit()

        logger.info(f"用户 {user.username} 已升级为Pro用户，有效期至 {user.vip_expire_date}")

    @staticmethod
    def downgrade_user_to_normal(user):
        """
        降级用户为普通用户

        Args:
            user: 用户对象
        """
        from backend.main import db

        user.role = 'normal_user'
        user.vip_expire_date = None
        user.permissions = None  # 清空自定义权限，使用默认权限
        db.session.commit()

        logger.info(f"用户 {user.username} 已降级为普通用户")

    @staticmethod
    def ban_user(user, reason="违规操作"):
        """
        封禁用户

        Args:
            user: 用户对象
            reason: 封禁原因
        """
        from backend.main import db

        user.is_banned = True
        db.session.commit()

        logger.warning(f"用户 {user.username} 已被封禁，原因: {reason}")

    @staticmethod
    def unban_user(user):
        """
        解封用户

        Args:
            user: 用户对象
        """
        from backend.main import db

        user.is_banned = False
        db.session.commit()

        logger.info(f"用户 {user.username} 已被解封")

    @staticmethod
    def check_and_downgrade_expired_users():
        """
        检查并降级所有过期的Pro用户

        Returns:
            int: 降级的用户数量
        """
        from backend.main import db
        from backend.models.user import User
        import datetime

        # 查找所有过期的Pro用户
        expired_users = User.query.filter(
            User.role == 'pro_user',
            User.vip_expire_date < datetime.datetime.now()
        ).all()

        downgraded_count = 0
        for user in expired_users:
            try:
                PermissionManager.downgrade_user_to_normal(user)
                downgraded_count += 1
                logger.info(f"自动降级过期Pro用户: {user.username}")
            except Exception as e:
                logger.error(f"降级用户 {user.username} 失败: {e}")

        if downgraded_count > 0:
            logger.info(f"批量降级完成，共降级 {downgraded_count} 个过期Pro用户")

        return downgraded_count
