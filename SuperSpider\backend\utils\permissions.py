#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
权限管理工具
提供权限检查装饰器和权限验证函数
"""

from functools import wraps
from flask import jsonify, request
from flask_login import current_user, login_required
import logging

logger = logging.getLogger("superspider.permissions")

def require_permission(permission_type, permission_value=None):
    """
    权限检查装饰器

    Args:
        permission_type: 权限类型 ('platform', 'admin', 'download_limit')
        permission_value: 权限值 (如平台名称、管理功能名称等)
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            # 检查用户是否被封禁
            if current_user.is_banned:
                return jsonify({
                    "success": False,
                    "message": "账户已被封禁，无法使用此功能",
                    "data": None
                }), 403

            # 检查Pro用户是否过期，如果过期则自动降级
            if current_user.role == 'pro_user' and not current_user.is_pro_user_valid():
                logger.info(f"检测到Pro用户 {current_user.username} 已过期，自动降级为普通用户")
                PermissionManager.downgrade_user_to_normal(current_user)

                # 如果需要Pro权限的功能，返回错误
                if permission_type == 'platform' and permission_value in ['csdn', 'zhihu']:
                    return jsonify({
                        "success": False,
                        "message": "Pro会员已过期，已自动降级为普通用户，请重新升级后使用此功能",
                        "data": None
                    }), 403

            # 检查具体权限
            if not current_user.has_permission(permission_type, permission_value):
                if permission_type == 'platform':
                    if permission_value in ['csdn', 'zhihu']:
                        return jsonify({
                            "success": False,
                            "message": "此功能仅限Pro用户使用，请升级账户",
                            "data": None
                        }), 403
                    else:
                        return jsonify({
                            "success": False,
                            "message": "您没有使用此平台的权限",
                            "data": None
                        }), 403
                elif permission_type == 'admin':
                    return jsonify({
                        "success": False,
                        "message": "您没有管理员权限",
                        "data": None
                    }), 403
                else:
                    return jsonify({
                        "success": False,
                        "message": "权限不足",
                        "data": None
                    }), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(*allowed_roles):
    """
    角色检查装饰器

    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if current_user.role not in allowed_roles:
                return jsonify({
                    "success": False,
                    "message": "您的账户级别不足以使用此功能",
                    "data": None
                }), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_rate_limit(user, action_type='api'):
    """
    检查用户速率限制

    Args:
        user: 用户对象
        action_type: 操作类型 ('api', 'download')

    Returns:
        bool: 是否在限制范围内
    """
    permissions = user.get_permissions()

    if action_type == 'api':
        limit = permissions.get('api_rate_limit', 20)
        if limit == -1:  # 无限制
            return True
        # TODO: 实现具体的速率限制逻辑（可以使用Redis）
        return True

    elif action_type == 'download':
        limit = permissions.get('download_limit', 50)
        if limit == -1:  # 无限制
            return True
        # TODO: 实现具体的下载限制逻辑
        return True

    return True

def get_user_platform_access(user):
    """
    获取用户可访问的平台列表

    Args:
        user: 用户对象

    Returns:
        dict: 平台访问权限
    """
    permissions = user.get_permissions()

    return {
        'video_platforms': permissions.get('video_platforms', []),
        'article_platforms': permissions.get('article_platforms', []),
        'all_platforms': permissions.get('video_platforms', []) + permissions.get('article_platforms', [])
    }

def log_permission_check(user, permission_type, permission_value, result):
    """
    记录权限检查日志

    Args:
        user: 用户对象
        permission_type: 权限类型
        permission_value: 权限值
        result: 检查结果
    """
    logger.info(f"权限检查 - 用户: {user.username}, 角色: {user.role}, "
                f"权限类型: {permission_type}, 权限值: {permission_value}, 结果: {result}")

class PermissionManager:
    """权限管理器"""

    @staticmethod
    def upgrade_user_to_pro(user, days=30):
        """
        升级用户为Pro用户

        Args:
            user: 用户对象
            days: Pro用户有效天数
        """
        import datetime
        from backend.main import db

        user.role = 'pro_user'
        user.vip_expire_date = datetime.datetime.now() + datetime.timedelta(days=days)
        user.permissions = None  # 清空自定义权限，使用默认权限
        db.session.commit()

        logger.info(f"用户 {user.username} 已升级为Pro用户，有效期至 {user.vip_expire_date}")

    @staticmethod
    def downgrade_user_to_normal(user):
        """
        降级用户为普通用户

        Args:
            user: 用户对象
        """
        from backend.main import db

        user.role = 'normal_user'
        user.vip_expire_date = None
        user.permissions = None  # 清空自定义权限，使用默认权限
        db.session.commit()

        logger.info(f"用户 {user.username} 已降级为普通用户")

    @staticmethod
    def ban_user(user, reason="违规操作"):
        """
        封禁用户

        Args:
            user: 用户对象
            reason: 封禁原因
        """
        from backend.main import db

        user.is_banned = True
        db.session.commit()

        logger.warning(f"用户 {user.username} 已被封禁，原因: {reason}")

    @staticmethod
    def unban_user(user):
        """
        解封用户

        Args:
            user: 用户对象
        """
        from backend.main import db

        user.is_banned = False
        db.session.commit()

        logger.info(f"用户 {user.username} 已被解封")

    @staticmethod
    def check_and_downgrade_expired_users():
        """
        检查并降级所有过期的Pro用户

        Returns:
            int: 降级的用户数量
        """
        from backend.main import db
        from backend.models.user import User
        import datetime

        # 查找所有过期的Pro用户
        expired_users = User.query.filter(
            User.role == 'pro_user',
            User.vip_expire_date < datetime.datetime.now()
        ).all()

        downgraded_count = 0
        for user in expired_users:
            try:
                PermissionManager.downgrade_user_to_normal(user)
                downgraded_count += 1
                logger.info(f"自动降级过期Pro用户: {user.username}")
            except Exception as e:
                logger.error(f"降级用户 {user.username} 失败: {e}")

        if downgraded_count > 0:
            logger.info(f"批量降级完成，共降级 {downgraded_count} 个过期Pro用户")

        return downgraded_count
