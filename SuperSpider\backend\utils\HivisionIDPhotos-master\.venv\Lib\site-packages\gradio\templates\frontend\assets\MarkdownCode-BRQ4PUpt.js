const __vite__fileDeps=["./mermaid.core-DGK6UhOk.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./dispatch-kxCwF96_.js","./step-Ce-xBr2D.js","./select-BigU4G0v.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as I}from"./index-Ccc2t4AG.js";import{k as x,A as q,c as B}from"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";var H=function(e,t,r){for(var a=r,l=0,s=e.length;a<t.length;){var o=t[a];if(l<=0&&t.slice(a,a+s)===e)return a;o==="\\"?a++:o==="{"?l++:o==="}"&&l--,a++}return-1},N=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},G=/^\\begin{/,X=function(e,t){for(var r,a=[],l=new RegExp("("+t.map(h=>N(h.left)).join("|")+")");r=e.search(l),r!==-1;){r>0&&(a.push({type:"text",data:e.slice(0,r)}),e=e.slice(r));var s=t.findIndex(h=>e.startsWith(h.left));if(r=H(t[s].right,e,t[s].left.length),r===-1)break;var o=e.slice(0,r+t[s].right.length),f=G.test(o)?o:e.slice(t[s].left.length,r);a.push({type:"math",data:f,rawData:o,display:t[s].display}),e=e.slice(r+t[s].right.length)}return e!==""&&a.push({type:"text",data:e}),a},K=function(e,t){var r=X(e,t.delimiters);if(r.length===1&&r[0].type==="text")return null;for(var a=document.createDocumentFragment(),l=0;l<r.length;l++)if(r[l].type==="text")a.appendChild(document.createTextNode(r[l].data));else{var s=document.createElement("span"),o=r[l].data;t.displayMode=r[l].display;try{t.preProcess&&(o=t.preProcess(o)),x.render(o,s,t)}catch(f){if(!(f instanceof x.ParseError))throw f;t.errorCallback("KaTeX auto-render: Failed to parse `"+r[l].data+"` with ",f),a.appendChild(document.createTextNode(r[l].rawData));continue}a.appendChild(s)}return a},U=function i(e,t){for(var r=0;r<e.childNodes.length;r++){var a=e.childNodes[r];if(a.nodeType===3){for(var l=a.textContent,s=a.nextSibling,o=0;s&&s.nodeType===Node.TEXT_NODE;)l+=s.textContent,s=s.nextSibling,o++;var f=K(l,t);if(f){for(var h=0;h<o;h++)a.nextSibling.remove();r+=f.childNodes.length-1,e.replaceChild(f,a)}else r+=o}else a.nodeType===1&&function(){var w=" "+a.className+" ",p=t.ignoredTags.indexOf(a.nodeName.toLowerCase())===-1&&t.ignoredClasses.every(v=>w.indexOf(" "+v+" ")===-1);p&&i(a,t)}()}},j=function(e,t){if(!e)throw new Error("No element provided to render");var r={};for(var a in t)t.hasOwnProperty(a)&&(r[a]=t[a]);r.delimiters=r.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],r.ignoredTags=r.ignoredTags||["script","noscript","style","textarea","pre","code","option"],r.ignoredClasses=r.ignoredClasses||[],r.errorCallback=r.errorCallback||console.error,r.macros=r.macros||{},U(e,r)};const V=(i,e)=>{try{return!!i&&new URL(i).origin!==new URL(e).origin}catch{return!1}};function T(i,e){const t=new q,r=new DOMParser().parseFromString(i,"text/html");return A(r.body,"A",a=>{a instanceof HTMLElement&&"target"in a&&V(a.getAttribute("href"),e)&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"))}),t.sanitize(r).body.innerHTML}function A(i,e,t){i&&(i.nodeName===e||typeof e=="function")&&t(i);const r=i?.childNodes||[];for(let a=0;a<r.length;a++)A(r[a],e,t)}const E=["!--","!doctype","a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","menu","meta","meter","nav","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","search","section","select","small","source","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr"],Z=["g","defs","use","symbol","rect","circle","ellipse","line","polyline","polygon","path","image","text","tspan","textPath","linearGradient","radialGradient","stop","pattern","clipPath","mask","filter","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feGaussianBlur","feMerge","feMorphology","feOffset","feSpecularLighting","feTurbulence","feMergeNode","feFuncR","feFuncG","feFuncB","feFuncA","feDistantLight","fePointLight","feSpotLight","feFlood","feTile","animate","animateTransform","animateMotion","mpath","set","view","cursor","foreignObject","desc","title","metadata","switch"],W=[...E,...Z.filter(i=>!E.includes(i))],{SvelteComponent:J,attr:Q,binding_callbacks:Y,detach:$,element:ee,flush:m,init:te,insert:re,noop:L,safe_not_equal:ae,toggle_class:k}=window.__gradio__svelte__internal,{afterUpdate:ne,tick:ie,onMount:fe}=window.__gradio__svelte__internal;function se(i){let e;return{c(){e=ee("span"),Q(e,"class","md svelte-7ddecg"),k(e,"chatbot",i[0]),k(e,"prose",i[1])},m(t,r){re(t,e,r),e.innerHTML=i[3],i[12](e)},p(t,[r]){r&8&&(e.innerHTML=t[3]),r&1&&k(e,"chatbot",t[0]),r&2&&k(e,"prose",t[1])},i:L,o:L,d(t){t&&$(e),i[12](null)}}}function M(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function le(i,e,t){let{chatbot:r=!0}=e,{message:a}=e,{sanitize_html:l=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:o=!0}=e,{line_breaks:f=!0}=e,{header_links:h=!1}=e,{root:w}=e,{allow_tags:p=!1}=e,{theme_mode:v="system"}=e,_,y;const C=B({header_links:h,line_breaks:f,latex_delimiters:s||[]});function D(n,d){if(d===!0){const g=/<\/?([a-zA-Z][a-zA-Z0-9-]*)([\s>])/g;return n.replace(g,(c,u,b)=>W.includes(u.toLowerCase())?c:c.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}if(Array.isArray(d)){const g=d.map(u=>({open:new RegExp(`<(${u})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${u})>`,"gi")}));let c=n;return g.forEach(u=>{c=c.replace(u.open,b=>b.replace(/</g,"&lt;").replace(/>/g,"&gt;")),c=c.replace(u.close,b=>b.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}),c}return n}function O(n){let d=n;if(o){const g=[];s.forEach((c,u)=>{const b=M(c.left),P=M(c.right),F=new RegExp(`${b}([\\s\\S]+?)${P}`,"g");d=d.replace(F,(S,oe)=>(g.push(S),`%%%LATEX_BLOCK_${g.length-1}%%%`))}),d=C.parse(d),d=d.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(c,u)=>g[parseInt(u,10)])}return p&&(d=D(d,p)),l&&T&&(d=T(d,w)),d}async function R(n){if(s.length>0&&n&&s.some(g=>n.includes(g.left)&&n.includes(g.right))&&j(_,{delimiters:s,throwOnError:!1}),_){const d=_.querySelectorAll(".mermaid");if(d.length>0){await ie();const{default:g}=await I(()=>import("./mermaid.core-DGK6UhOk.js").then(c=>c.b3),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url);g.initialize({startOnLoad:!1,theme:v==="dark"?"dark":"default",securityLevel:"antiscript"}),await g.run({nodes:Array.from(d).map(c=>c)})}}}ne(async()=>{_&&document.body.contains(_)?await R(a):console.error("Element is not in the DOM")});function z(n){Y[n?"unshift":"push"](()=>{_=n,t(2,_)})}return i.$$set=n=>{"chatbot"in n&&t(0,r=n.chatbot),"message"in n&&t(4,a=n.message),"sanitize_html"in n&&t(5,l=n.sanitize_html),"latex_delimiters"in n&&t(6,s=n.latex_delimiters),"render_markdown"in n&&t(1,o=n.render_markdown),"line_breaks"in n&&t(7,f=n.line_breaks),"header_links"in n&&t(8,h=n.header_links),"root"in n&&t(9,w=n.root),"allow_tags"in n&&t(10,p=n.allow_tags),"theme_mode"in n&&t(11,v=n.theme_mode)},i.$$.update=()=>{i.$$.dirty&16&&(a&&a.trim()?t(3,y=O(a)):t(3,y=""))},[r,o,_,y,a,l,s,f,h,w,p,v,z]}class ge extends J{constructor(e){super(),te(this,e,le,se,ae,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,root:9,allow_tags:10,theme_mode:11})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),m()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),m()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),m()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),m()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),m()}get allow_tags(){return this.$$.ctx[10]}set allow_tags(e){this.$$set({allow_tags:e}),m()}get theme_mode(){return this.$$.ctx[11]}set theme_mode(e){this.$$set({theme_mode:e}),m()}}export{ge as M};
//# sourceMappingURL=MarkdownCode-BRQ4PUpt.js.map
