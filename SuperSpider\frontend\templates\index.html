<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperSpider - 内容聚合平台</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/downloads.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #8e44ad;
            --primary-hover: #7d3c98;
            --background: #ffffff;
            --foreground: #09090b;
            --muted: #f4f4f5;
            --muted-foreground: #71717a;
            --border: #e4e4e7;
            --radius: 0.5rem;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* 平台卡片 Shadcn风格 */
        .platform-card {
            background-color: var(--background);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            padding: 1.5rem;
            transition: all 0.2s ease;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .platform-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        /* 按钮 Shadcn风格 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
            transition: background-color 0.2s ease;
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        .btn:hover {
            background-color: var(--primary-hover);
        }

        /* CSDN图标 Shadcn风格 */
        .platform-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            background-color: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .platform-icon i {
            font-size: 1.5rem;
        }

        .platform-icon-small {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 0.375rem;
            background-color: var(--primary);
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            vertical-align: middle;
        }

        .platform-icon-small i {
            font-size: 0.75rem;
        }

        /* 表单元素 Shadcn风格 */
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            border: 1px solid var(--border);
            background-color: var(--background);
            color: var(--foreground);
            font-size: 0.875rem;
            line-height: 1.25rem;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus, .form-group select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 1px var(--primary);
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--foreground);
        }

        .submit-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background-color: var(--primary);
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        .submit-btn:hover {
            background-color: var(--primary-hover);
        }

        /* 权限相关样式 */
        .user-role-info {
            padding: 0.5rem 0;
            text-align: center;
        }

        .user-role {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .role-normal {
            background-color: #e5e7eb;
            color: #374151;
        }

        .role-pro {
            background-color: #fbbf24;
            color: #92400e;
        }

        .role-admin {
            background-color: #ef4444;
            color: #ffffff;
        }



        .vip-expire {
            display: block;
            font-size: 0.75rem;
            color: var(--muted-foreground);
            margin-top: 0.25rem;
        }

        .platform-card.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .platform-card.disabled .btn {
            background-color: #9ca3af;
            cursor: not-allowed;
        }

        .platform-card.disabled .btn:hover {
            background-color: #9ca3af;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: var(--radius);
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .message-error {
            background-color: #ef4444;
        }

        .message-success {
            background-color: #10b981;
        }

        .message-info {
            background-color: #3b82f6;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container header-content">
            <div class="logo">
                <i class="fas fa-spider"></i>
                <span>SuperSpider</span>
            </div>
            <p class="subtitle">一站式内容聚合与下载平台</p>
            <nav class="main-nav">
                <ul>
                    <li><a href="#" class="active">首页</a></li>
                    <li><a href="#platforms">支持平台</a></li>
                    <li><a href="#tools">工具</a></li>
                    <li><a href="#about">关于</a></li>
                </ul>
            </nav>
            <!-- 用户认证区域 -->
            <div class="auth-controls">
                <div class="auth-buttons" id="auth-buttons">
                    <button id="login-btn" class="btn btn-secondary" onclick="showLoginModal()">登录</button>
                    <button id="register-btn" class="btn" onclick="showRegisterModal()">注册</button>
                </div>
                <div class="user-menu" id="user-menu" style="display: none;">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="dropdown">
                        <div class="dropdown-toggle" id="user-dropdown-toggle">
                            <span id="username-display">用户名</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="dropdown-menu">
                            <div class="user-role-info">
                                <span id="user-role" class="user-role">普通用户</span>
                                <span id="vip-expire" class="vip-expire" style="display: none;"></span>
                            </div>
                            <div class="divider"></div>
                            <a href="#" id="profile-link">我的资料</a>
                            <a href="#" id="downloads-link">搜索记录</a>
                            <a href="#" id="settings-link">设置</a>
                            <div class="divider"></div>
                            <a href="#" id="logout-link">退出登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 英雄区 -->
    <section class="hero">
        <div class="container">
            <h1>智能聚合，一键获取</h1>
            <p>SuperSpider 是一款多平台内容聚合与下载工具，支持多种流行平台内容的获取、格式转换与分享。无需注册，快速便捷。</p>
            <form id="hero-search-form" class="search-box">
                <input type="text" id="hero-search-input" placeholder="输入内容链接...">
                <button type="submit" id="hero-search-button"><i class="fas fa-search"></i> 获取</button>
            </form>
            <div id="hero-search-result" class="search-result" style="display: none;">
                <div class="result-card">
                    <div class="result-header">
                        <h3>提取结果</h3>
                        <button id="hero-result-close" class="result-close"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="result-content">
                        <div class="result-item">
                            <div class="result-label">原始链接:</div>
                            <div class="result-value" id="original-link"></div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">提取链接:</div>
                            <div class="result-value" id="extracted-link"></div>
                        </div>
                    </div>
                    <div class="result-actions">
                        <button id="copy-link-btn" class="btn btn-primary"><i class="fas fa-copy"></i> 复制链接</button>
                        <button id="open-link-btn" class="btn btn-secondary"><i class="fas fa-external-link-alt"></i> 打开链接</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 平台部分 -->
    <section id="platforms" class="platforms">
        <div class="container">
            <h2 class="section-title">支持平台</h2>

            <!-- 平台分类标签页 -->
            <div class="platform-tabs">
                <button class="platform-tab-btn active" data-platform-category="video" onclick="switchPlatformTab('video', this)">视频平台</button>
                <button class="platform-tab-btn" data-platform-category="article" onclick="switchPlatformTab('article', this)">文章平台</button>
                <button class="platform-tab-btn" data-platform-category="tools" onclick="switchPlatformTab('tools', this)">工具平台</button>
                <button class="platform-tab-btn" data-platform-category="all" onclick="switchPlatformTab('all', this)">全部平台</button>
                <div class="platform-search-container">
                    <input type="text" id="platform-search" placeholder="搜索平台...">
                    <i class="fas fa-search"></i>
                </div>
            </div>



            <!-- 视频平台 (默认显示) -->
            <div class="platform-category-content active" id="video-platforms">
                <div class="platform-grid">
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <h3>抖音</h3>
                        <p>一键获取抖音视频，无水印下载高清视频和音频。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="douyin-tool-content" data-platform="douyin">立即使用</button>
                    </div>
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>快手</h3>
                        <p>解析快手视频，获取无水印视频链接与作者信息。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="kuaishou-tool-content" data-platform="kuaishou">立即使用</button>
                    </div>
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fab fa-bilibili"></i>
                        </div>
                        <h3>哔哩哔哩</h3>
                        <p>解析B站视频，下载无水印高清视频。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="bilibili-tool-content" data-platform="bilibili">立即使用</button>
                    </div>
                </div>


            </div>

            <!-- 文章平台 -->
            <div class="platform-category-content" id="article-platforms">
                <div class="platform-grid">
                    <div class="platform-card" data-platform-type="article">
                        <div class="platform-icon">
                            <i class="fab fa-cuttlefish"></i>
                        </div>
                        <h3>CSDN</h3>
                        <p>智能解析CSDN技术文章，自动处理VIP内容，支持多种格式导出。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="csdn-tool-content" data-platform="csdn">立即使用</button>
                    </div>
                    <div class="platform-card coming-soon" data-platform-type="article">
                        <div class="platform-icon">
                            <i class="fab fa-zhihu"></i>
                        </div>
                        <h3>知乎</h3>
                        <p>获取知乎文章和视频内容，支持批量下载。</p>
                        <div class="coming-soon-badge">开发中</div>
                    </div>

                </div>
            </div>

            <!-- 工具平台 -->
            <div class="platform-category-content" id="tools-platforms">
                <div class="platform-grid">
                    <div class="platform-card" data-platform-type="tool">
                        <div class="platform-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <h3>证件照制作</h3>
                        <p>智能证件照制作工具，支持一寸、二寸等多种规格，自动抠图换背景。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="idphoto-tool-content" data-platform="idphoto">立即使用</button>
                    </div>
                </div>
            </div>

            <!-- 全部平台 -->
            <div class="platform-category-content" id="all-platforms">
                <div class="platform-grid">
                    <!-- 视频平台 -->
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <h3>抖音</h3>
                        <p>一键获取抖音视频，无水印下载高清视频和音频。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="douyin-tool-content">立即使用</button>
                    </div>
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>快手</h3>
                        <p>解析快手视频，获取无水印视频链接与作者信息。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="kuaishou-tool-content">立即使用</button>
                    </div>
                    <div class="platform-card" data-platform-type="video">
                        <div class="platform-icon">
                            <i class="fab fa-bilibili"></i>
                        </div>
                        <h3>哔哩哔哩</h3>
                        <p>解析B站视频，下载无水印高清视频。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="bilibili-tool-content">立即使用</button>
                    </div>

                    <!-- 文章平台 -->
                    <div class="platform-card" data-platform-type="article">
                        <div class="platform-icon">
                            <i class="fab fa-cuttlefish"></i>
                        </div>
                        <h3>CSDN</h3>
                        <p>智能解析CSDN技术文章，自动处理VIP内容，支持多种格式导出。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="csdn-tool-content" data-platform="csdn">立即使用</button>
                    </div>
                    <div class="platform-card coming-soon" data-platform-type="article">
                        <div class="platform-icon">
                            <i class="fab fa-zhihu"></i>
                        </div>
                        <h3>知乎</h3>
                        <p>获取知乎文章和视频内容，支持批量下载。</p>
                        <div class="coming-soon-badge">开发中</div>
                    </div>

                    <!-- 工具平台 -->
                    <div class="platform-card" data-platform-type="tool">
                        <div class="platform-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <h3>证件照制作</h3>
                        <p>智能证件照制作工具，支持一寸、二寸等多种规格，自动抠图换背景。</p>
                        <button class="btn btn-primary platform-select-btn" data-target-tool="idphoto-tool-content">立即使用</button>
                    </div>

                    <!-- 更多平台 -->
                    <div class="platform-card">
                        <div class="platform-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h3>更多平台</h3>
                        <p>更多平台支持正在开发中，敬请期待...</p>
                        <button id="more-platforms-btn" class="btn btn-secondary">了解更多</button>
                    </div>
                </div>


            </div>
        </div>
    </section>

    <!-- 工具部分 -->
    <section id="tools" class="tools">
        <div class="container" id="tools-container">
            <h2 class="section-title">内容获取工具</h2>

            <!-- 未登录提示 -->
            <div id="tools-login-required" style="display: none;" class="login-required-message">
                <div class="message-card">
                    <i class="fas fa-lock"></i>
                    <h3>需要登录</h3>
                    <p>内容获取工具仅对登录用户开放，请先登录或注册账号。</p>
                    <div class="message-actions">
                        <button class="btn btn-primary" onclick="showLoginModal()">登录</button>
                        <button class="btn btn-secondary" onclick="showRegisterModal()">注册</button>
                    </div>
                </div>
            </div>

            <!-- 工具内容区域 -->
            <div id="tool-contents" style="display: none;">
                <!-- 抖音工具 - 默认隐藏 -->
                <div id="douyin-tool-content" class="tool-content-wrapper">
                    <div class="tool-card">
                        <h3 class="tool-title">
                            <div class="tool-icon">
                                <i class="fab fa-tiktok"></i>
                            </div>
                            抖音视频解析
                        </h3>
                        <p class="tool-desc">解析抖音分享链接，获取无水印视频下载地址、视频标题和作者信息。</p>

                        <form class="form-card" id="douyin-form">
                            <div class="form-group">
                                <label class="form-label" for="douyin-url">视频链接</label>
                                <input class="form-input" type="url" id="douyin-url" name="video_url" placeholder="https://v.douyin.com/..." required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 解析视频
                            </button>
                        </form>

                        <div class="status-container" id="douyin-status">
                            <div class="status-message" id="douyin-message"></div>
                            <div class="progress-container">
                                <div class="progress-bar" id="douyin-progress"></div>
                            </div>
                        </div>

                        <div id="douyin-result" class="result-container" style="display: none;">
                            <!-- 视频预览区域 -->
                            <div class="video-preview-container">
                                <video id="douyin-video-preview" controls autoplay muted class="video-player" data-platform="douyin">
                                    您的浏览器不支持HTML5视频播放
                                </video>
                            </div>

                            <div class="result-item">
                                <div class="result-label">标题</div>
                                <div class="result-value" id="douyin-video-title"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">作者</div>
                                <div class="result-value" id="douyin-video-author"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">视频下载</div>
                                <div class="result-value">
                                    <span id="douyin-video-url">
                                        <i class="fas fa-info-circle"></i> 点击视频右下角三个点进行下载
                                    </span>
                                    <button id="douyin-open-video" class="btn btn-secondary" style="margin-left: 10px; display: none;">
                                        <i class="fas fa-external-link-alt"></i> 在新窗口打开
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快手视频工具 - 默认隐藏 -->
                <div id="kuaishou-tool-content" class="tool-content-wrapper">
                    <div class="tool-card">
                        <h3 class="tool-title">
                            <div class="tool-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            快手视频解析
                        </h3>
                        <p class="tool-desc">解析快手分享链接，获取无水印视频下载地址、视频标题和作者信息。</p>

                        <form class="form-card" id="kuaishou-form">
                            <div class="form-group">
                                <label class="form-label" for="kuaishou-url">视频链接</label>
                                <input class="form-input" type="url" id="kuaishou-url" name="video_url" placeholder="https://v.kuaishou.com/..." required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 解析视频
                            </button>
                        </form>

                        <div class="status-container" id="kuaishou-status">
                            <div class="status-message" id="kuaishou-message"></div>
                            <div class="progress-container">
                                <div class="progress-bar" id="kuaishou-progress"></div>
                            </div>
                        </div>

                        <div id="kuaishou-result" class="result-container">
                            <!-- 视频预览区域 -->
                            <div class="video-preview-container">
                                <video id="video-preview" controls muted class="video-player" data-platform="kuaishou">
                                    您的浏览器不支持HTML5视频播放
                                </video>
                            </div>

                            <div class="result-item">
                                <div class="result-label">标题</div>
                                <div class="result-value" id="video-title"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">作者</div>
                                <div class="result-value" id="video-author"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">视频下载</div>
                                <div class="result-value">
                                    <span id="video-url">
                                        <i class="fas fa-info-circle"></i> 点击视频右下角三个点进行下载
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 哔哩哔哩工具 - 默认隐藏 -->
                <div id="bilibili-tool-content" class="tool-content-wrapper">
                    <div class="tool-card">
                        <h3 class="tool-title">
                            <div class="tool-icon">
                                <i class="fab fa-bilibili"></i>
                            </div>
                            哔哩哔哩视频解析
                        </h3>
                        <p class="tool-desc">解析B站视频链接，获取无水印高清视频下载地址。</p>

                        <form class="form-card" id="bilibili-form">
                            <div class="form-group">
                                <label class="form-label" for="bilibili-url">视频链接</label>
                                <input class="form-input" type="url" id="bilibili-url" name="video_url" placeholder="https://www.bilibili.com/video/..." required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 解析视频
                            </button>
                        </form>

                        <div class="status-container" id="bilibili-status">
                            <div class="status-message" id="bilibili-message"></div>
                            <div class="progress-container">
                                <div class="progress-bar" id="bilibili-progress"></div>
                            </div>
                        </div>

                        <div id="bilibili-result" class="result-container" style="display: none;">
                            <!-- 视频预览区域 -->
                            <div class="video-preview-container">
                                <video id="bilibili-video-preview" controls muted class="video-player" data-platform="bilibili">
                                    您的浏览器不支持HTML5视频播放
                                </video>
                            </div>

                            <div class="result-item">
                                <div class="result-label">标题</div>
                                <div class="result-value" id="bilibili-video-title"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">UP主</div>
                                <div class="result-value" id="bilibili-video-author"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">视频下载</div>
                                <div class="result-value">
                                    <span id="bilibili-video-url">
                                        <i class="fas fa-info-circle"></i> 点击视频右下角三个点进行下载
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <!-- CSDN工具 - 默认隐藏 -->
                <div id="csdn-tool-content" class="tool-content-wrapper">
                    <div class="tool-card">
                        <h3 class="tool-title">
                            <div class="tool-icon">
                                <i class="fab fa-cuttlefish"></i>
                            </div>
                            CSDN文章解析
                        </h3>
                        <p class="tool-desc">解析CSDN文章链接，生成完整HTML文件并通过邮件发送给您。</p>

                        <form class="form-card" id="csdn-form">
                            <div class="form-group">
                                <label class="form-label" for="csdn-url">文章链接</label>
                                <input class="form-input" type="url" id="csdn-url" name="article_url" placeholder="https://blog.csdn.net/..." required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="csdn-email">接收邮箱</label>
                                <input class="form-input" type="email" id="csdn-email" name="email" placeholder="<EMAIL>" required>
                                <div class="form-hint">
                                    <i class="fas fa-info-circle"></i>
                                    完整HTML文件将发送到此邮箱
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="csdn-format">输出格式</label>
                                <select class="form-input" id="csdn-format" name="format">
                                    <option value="html">HTML文件 (推荐)</option>
                                    <option value="pdf">PDF文件</option>
                                    <option value="markdown">Markdown文件</option>
                                </select>
                                <div class="form-hint">
                                    <i class="fas fa-info-circle"></i>
                                    选择文章的输出格式
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-hint">
                                    <i class="fas fa-magic"></i>
                                    系统将自动检测并使用最佳方式获取文章内容，包括VIP专属文章
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary" id="csdn-submit-btn">
                                <i class="fas fa-file-code"></i>生成HTML
                            </button>
                        </form>

                        <div class="status-container" id="csdn-status" style="display: none;">
                            <div class="status-message"></div>
                            <div class="progress-container">
                                <div class="progress-bar" id="csdn-progress"></div>
                            </div>
                        </div>

                        <div id="csdn-result" class="result-container" style="display: none;">
                            <div class="result-item">
                                <div class="result-label">文章标题</div>
                                <div class="result-value" id="csdn-article-title"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">作者</div>
                                <div class="result-value" id="csdn-article-author"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">发布时间</div>
                                <div class="result-value" id="csdn-article-time"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">阅读量</div>
                                <div class="result-value" id="csdn-article-reads"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">文章摘要</div>
                                <div class="result-value" id="csdn-article-summary"></div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">处理状态</div>
                                <div class="result-value">
                                    <span class="status-badge success">
                                        <i class="fas fa-check-circle"></i>
                                        截图已生成并发送到您的邮箱
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 证件照制作工具 -->
                <div id="idphoto-tool-content" class="tool-content-wrapper">
                    <div class="tool-card">
                        <h3 class="tool-title">
                            <div class="tool-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            证件照制作
                        </h3>
                        <p class="tool-desc">智能证件照制作工具，支持多种规格，自动抠图换背景，一键生成标准证件照。</p>

                        <form class="form-card" id="idphoto-form">
                            <div class="form-group">
                                <label class="form-label" for="idphoto-upload">上传照片</label>
                                <div class="upload-area" id="idphoto-upload-area">
                                    <input type="file" id="idphoto-upload" name="photo" accept="image/*" required style="display: none;">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>点击或拖拽上传照片</p>
                                        <span class="upload-hint">支持 JPG、PNG 格式，建议分辨率不低于 300x400</span>
                                    </div>
                                </div>
                                <div class="uploaded-preview" id="uploaded-preview" style="display: none;">
                                    <img id="preview-image" src="" alt="预览图">
                                    <button type="button" class="btn btn-secondary btn-sm" id="change-photo">更换照片</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="idphoto-size">证件照规格</label>
                                <select class="form-input" id="idphoto-size" name="size" required>
                                    <option value="">请选择规格</option>
                                    <optgroup label="常用规格">
                                        <option value="1inch">一寸 (413, 295)</option>
                                        <option value="2inch">二寸 (626, 413)</option>
                                        <option value="small1inch">小一寸 (378, 260)</option>
                                        <option value="small2inch">小二寸 (531, 413)</option>
                                        <option value="big1inch">大一寸 (567, 390)</option>
                                        <option value="big2inch">大二寸 (626, 413)</option>
                                        <option value="5inch">五寸 (1499, 1050)</option>
                                    </optgroup>
                                    <optgroup label="考试证件">
                                        <option value="teacher_cert">教师资格证 (413, 295)</option>
                                        <option value="civil_servant">国家公务员考试 (413, 295)</option>
                                        <option value="cpa_exam">初级会计考试 (413, 295)</option>
                                        <option value="cet4_exam">英语四六级考试 (192, 144)</option>
                                        <option value="computer_exam">计算机等级考试 (567, 390)</option>
                                        <option value="graduate_exam">研究生考试 (709, 531)</option>
                                    </optgroup>
                                    <optgroup label="证件卡片">
                                        <option value="social_card">社保卡 (441, 358)</option>
                                        <option value="driver_license">电子驾驶证 (378, 260)</option>
                                    </optgroup>
                                    <optgroup label="签证护照">
                                        <option value="us_visa">美国签证 (600, 600)</option>
                                        <option value="japan_visa">日本签证 (413, 295)</option>
                                        <option value="korea_visa">韩国签证 (531, 413)</option>
                                    </optgroup>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="idphoto-background">背景颜色</label>
                                <div class="background-options">
                                    <label class="background-option">
                                        <input type="radio" name="background" value="white" checked>
                                        <div class="background-color" style="background-color: white; border: 1px solid #ddd;"></div>
                                        <span>白色</span>
                                    </label>
                                    <label class="background-option">
                                        <input type="radio" name="background" value="blue">
                                        <div class="background-color" style="background-color: #4285f4;"></div>
                                        <span>蓝色</span>
                                    </label>
                                    <label class="background-option">
                                        <input type="radio" name="background" value="red">
                                        <div class="background-color" style="background-color: #ea4335;"></div>
                                        <span>红色</span>
                                    </label>
                                    <label class="background-option">
                                        <input type="radio" name="background" value="custom">
                                        <div class="background-color" id="custom-color-display" style="background: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 8px 8px; background-position: 0 0, 0 4px, 4px -4px, -4px 0px;"></div>
                                        <span>自定义</span>
                                    </label>
                                </div>
                                <div class="custom-color-picker" id="custom-color-picker" style="display: none;">
                                    <input type="color" id="background-color-picker" value="#ffffff">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary" id="idphoto-submit">
                                <i class="fas fa-magic"></i> 制作证件照
                            </button>
                        </form>

                        <div class="status-container" id="idphoto-status" style="display: none;">
                            <div class="status-message"></div>
                            <div class="progress-container">
                                <div class="progress-bar" id="idphoto-progress"></div>
                            </div>
                        </div>

                        <div id="idphoto-result" class="result-container" style="display: none;">
                            <div class="result-item">
                                <div class="result-label">证件照预览</div>
                                <div class="result-value">
                                    <div class="idphoto-preview">
                                        <img id="idphoto-result-image" src="" alt="证件照">
                                        <div class="photo-info">
                                            <span id="photo-size-info"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">下载选项</div>
                                <div class="result-value">
                                    <div class="download-options">
                                        <button class="btn btn-primary" id="download-single">
                                            <i class="fas fa-download"></i> 下载单张
                                        </button>
                                        <button class="btn btn-secondary" id="download-layout">
                                            <i class="fas fa-th"></i> 下载排版 (6张)
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于部分 -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">关于 SuperSpider</h2>
            <div class="about-content">
                <p>SuperSpider 是一个内容聚合与下载平台，旨在简化用户从各大平台获取内容的流程。我们严格遵守相关法律法规，不鼓励任何侵权行为，仅用于个人学习研究。</p>

                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3>高效快速</h3>
                        <p>采用异步处理技术，确保内容获取速度快且稳定</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h3>安全可靠</h3>
                        <p>不存储用户敏感信息，所有处理均在服务器端完成</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code-branch"></i>
                        </div>
                        <h3>功能免费</h3>
                        <p>部分功能免费使用，欢迎反馈问题</p>
                    </div>
                </div>

                <div class="feedback-link">
                    <button id="feedback-btn" class="btn btn-primary">
                        <i class="fab fa-weixin"></i>提交反馈
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">SuperSpider</div>
                <div class="footer-links">
                    <a href="#">首页</a>
                    <a href="#platforms">支持平台</a>
                    <a href="#tools">工具</a>
                    <a href="#about">关于我们</a>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 YUMUKeJi 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- 立即隐藏工具内容的脚本 -->
    <script>
        // 立即隐藏工具内容，防止未登录用户看到
        (function() {
            const toolContents = document.getElementById('tool-contents');
            const toolsLoginRequired = document.getElementById('tools-login-required');

            if (toolContents) {
                toolContents.style.display = 'none';
            }
            if (toolsLoginRequired) {
                toolsLoginRequired.style.display = 'block';
            }

            // 移除所有工具的active类
            const toolWrappers = document.querySelectorAll('.tool-content-wrapper');
            toolWrappers.forEach(wrapper => {
                wrapper.classList.remove('active');
                wrapper.style.display = 'none';
            });

            console.log('立即隐藏工具内容完成');
        })();
    </script>

    <!-- 导航栏修复脚本 -->
    <script>
        // 立即执行的导航栏修复脚本
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                console.log('执行导航栏修复脚本...');

                const navLinks = document.querySelectorAll('.main-nav a');
                console.log('找到导航链接:', navLinks.length);

                if (navLinks.length > 0) {
                    // 移除所有active类
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                    });

                    // 设置首页为活动状态
                    navLinks[0].classList.add('active');
                    console.log('设置首页为活动状态');

                    // 为每个导航链接添加点击事件
                    navLinks.forEach((link, index) => {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();

                            console.log('点击导航链接:', this.textContent);

                            // 移除所有active类
                            navLinks.forEach(navLink => {
                                navLink.classList.remove('active');
                            });

                            // 添加active类到当前链接
                            this.classList.add('active');
                            console.log('设置active类到:', this.textContent);

                            // 处理滚动
                            const href = this.getAttribute('href');
                            if (href === '#' || index === 0) {
                                // 首页
                                window.scrollTo({
                                    top: 0,
                                    behavior: 'smooth'
                                });
                            } else if (href && href.startsWith('#')) {
                                // 其他锚点
                                const targetId = href.substring(1);
                                const targetElement = document.getElementById(targetId);

                                if (targetElement) {
                                    const headerHeight = 80;
                                    const targetTop = targetElement.offsetTop - headerHeight - 20;

                                    window.scrollTo({
                                        top: Math.max(0, targetTop),
                                        behavior: 'smooth'
                                    });
                                }
                            }
                        });
                    });

                    console.log('导航栏修复完成');
                }
            }, 1000); // 延迟1秒执行，确保所有其他脚本都已加载
        });

        // 强制确保导航栏样式正确应用
        setInterval(() => {
            const navLinks = document.querySelectorAll('.main-nav a');
            const activeLink = document.querySelector('.main-nav a.active');

            if (activeLink) {
                // 强制重新应用样式
                activeLink.style.color = '#7c3aed';
                activeLink.style.position = 'relative';
            }
        }, 1000);
    </script>

    <!-- 在页面底部添加模态框 -->
    <!-- 登录模态框 -->
    <div class="modal" id="login-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>账户登录</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <!-- 登录方式切换 -->
                <div class="login-tabs">
                    <button type="button" class="tab-btn active" data-tab="password-login">密码登录</button>
                    <button type="button" class="tab-btn" data-tab="sms-login">验证码登录</button>
                </div>

                <!-- 密码登录表单 -->
                <form id="password-login-form" class="login-form active">
                    <div class="form-group">
                        <label for="login-account">用户名/手机号</label>
                        <input type="text" id="login-account" name="account" placeholder="输入用户名或手机号" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">密码</label>
                        <input type="password" id="login-password" name="password" placeholder="输入密码" required>
                    </div>
                    <div class="form-checkbox">
                        <input type="checkbox" id="login-remember" name="remember">
                        <label for="login-remember">记住我</label>
                    </div>
                    <div class="status-container" id="password-login-status" style="display: none;">
                        <p class="status-message"></p>
                    </div>
                    <button type="submit" class="submit-btn">登录</button>
                </form>

                <!-- 验证码登录表单 -->
                <form id="sms-login-form" class="login-form">
                    <div class="form-group">
                        <label for="login-phone">手机号</label>
                        <input type="tel" id="login-phone" name="phone" placeholder="输入手机号" required pattern="^1[3-9]\d{9}$" maxlength="11">
                    </div>
                    <div class="form-group">
                        <label for="login-sms-code">验证码</label>
                        <div class="sms-input-group">
                            <input type="text" id="login-sms-code" name="sms_code" placeholder="输入验证码" required maxlength="6">
                            <button type="button" id="send-sms-btn" class="sms-btn">发送验证码</button>
                        </div>
                    </div>
                    <div class="status-container" id="sms-login-status" style="display: none;">
                        <p class="status-message"></p>
                    </div>
                    <button type="submit" class="submit-btn">登录</button>
                </form>
                <div class="modal-footer">
                    <p>没有账号？ <a href="#" id="switch-to-register">立即注册</a></p>
                    <p class="forgot-password-link">
                        <a href="#" id="forgot-password-link">
                            <i class="fas fa-question-circle"></i> 忘记密码
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal" id="register-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>账户注册</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="register-form">
                    <div class="form-group">
                        <label for="register-username">用户名</label>
                        <input type="text" id="register-username" name="username" placeholder="输入用户名 (4-20个字符)" required minlength="4" maxlength="20">
                        <div class="validation-message" id="username-validation" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-phone">手机号</label>
                        <input type="tel" id="register-phone" name="phone" placeholder="输入手机号" required pattern="^1[3-9]\d{9}$" maxlength="11">
                        <div class="validation-message" id="phone-validation" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-sms-code">手机验证码</label>
                        <div class="sms-input-group">
                            <input type="text" id="register-sms-code" name="sms_code" placeholder="输入验证码" required maxlength="6">
                            <button type="button" id="register-send-sms-btn" class="sms-btn" disabled>发送验证码</button>
                        </div>
                        <div class="validation-message" id="register-sms-validation" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-password">密码</label>
                        <input type="password" id="register-password" name="password" placeholder="输入密码 (至少6个字符)" required minlength="6">
                        <div class="password-hint">密码长度至少6个字符</div>
                    </div>
                    <div class="form-group">
                        <label for="register-confirm-password">确认密码</label>
                        <input type="password" id="register-confirm-password" name="confirm_password" placeholder="再次输入密码" required>
                    </div>
                    <div class="status-container" id="register-status" style="display: none;">
                        <p class="status-message"></p>
                    </div>
                    <button type="submit" class="submit-btn">注册</button>
                </form>
                <div class="modal-footer">
                    <p>已有账号？ <a href="#" id="switch-to-login">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户资料模态框 -->
    <div class="modal" id="profile-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>用户资料</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="profile-details">
                        <div class="profile-row">
                            <span class="profile-label">用户名</span>
                            <span class="profile-value" id="profile-username">-</span>
                        </div>
                        <div class="profile-row">
                            <span class="profile-label">手机号</span>
                            <span class="profile-value" id="profile-phone">-</span>
                        </div>
                        <div class="profile-row">
                            <span class="profile-label">注册时间</span>
                            <span class="profile-value" id="profile-created-at">-</span>
                        </div>
                        <div class="profile-row">
                            <span class="profile-label">上次登录</span>
                            <span class="profile-value" id="profile-last-login">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索历史记录模态框 -->
    <div class="modal" id="downloads-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container modal-large">
            <div class="modal-header">
                <h2>搜索历史记录</h2>
                <div class="modal-header-actions">
                    <button id="export-downloads-btn" class="btn btn-secondary" title="导出搜索记录">
                        <i class="fas fa-file-export"></i> 导出
                    </button>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="downloads-container">
                    <!-- 搜索统计 -->
                    <div class="download-stats" id="download-stats">
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">总搜索数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">最近7天</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">收藏</div>
                        </div>
                    </div>

                    <!-- 工具栏 -->
                    <div class="downloads-toolbar">
                        <!-- 批量操作 -->
                        <div class="batch-actions">
                            <span class="selected-count-label">已选择 <span id="selected-count">0</span> 项</span>
                            <button class="batch-action-btn disabled" data-action="favorite" title="批量收藏">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="batch-action-btn disabled" data-action="unfavorite" title="批量取消收藏">
                                <i class="fas fa-star-half-alt"></i>
                            </button>
                            <button class="batch-action-btn disabled" data-action="delete" title="批量删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>

                        <!-- 视图切换 -->
                        <div class="view-mode-switcher">
                            <button class="view-mode-btn active" data-mode="list" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="view-mode-btn" data-mode="grid" title="网格视图">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>

                        <!-- 排序 -->
                        <div class="sort-container">
                            <label for="sort-select">排序:</label>
                            <select id="sort-select">
                                <option value="created_at-desc">最新添加</option>
                                <option value="created_at-asc">最早添加</option>
                                <option value="title-asc">标题 A-Z</option>
                                <option value="title-desc">标题 Z-A</option>
                                <option value="search_count-desc">搜索次数</option>
                                <option value="last_searched_at-desc">最近搜索</option>
                            </select>
                        </div>
                    </div>

                    <!-- 筛选表单 -->
                    <div class="filter-container">
                        <form id="download-filter-form" class="filter-form">
                            <div class="filter-group">
                                <label for="filter-platform">平台</label>
                                <select id="filter-platform" name="platform">
                                    <option value="">全部</option>
                                    <option value="douyin">抖音</option>
                                    <option value="kuaishou">快手</option>
                                    <option value="bilibili">哔哩哔哩</option>
                                    <option value="csdn">CSDN</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter-content-type">类型</label>
                                <select id="filter-content-type" name="content_type">
                                    <option value="">全部</option>
                                    <option value="video">视频</option>
                                    <option value="article">文章</option>
                                    <option value="resource">资源</option>
                                    <option value="image">图片</option>
                                    <option value="audio">音频</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter-status">状态</label>
                                <select id="filter-status" name="status">
                                    <option value="">全部</option>
                                    <option value="success">成功</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div class="filter-group filter-checkbox">
                                <input type="checkbox" id="filter-favorite" name="favorite">
                                <label for="filter-favorite">只显示收藏</label>
                            </div>
                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary">筛选</button>
                                <button type="button" id="reset-filter-btn" class="btn btn-secondary">重置</button>
                            </div>
                        </form>
                    </div>

                    <!-- 搜索列表 -->
                    <div class="downloads-list-container">
                        <!-- 加载中 -->
                        <div id="downloads-loading" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>加载中...</p>
                        </div>

                        <!-- 空状态 -->
                        <div id="downloads-empty" class="empty-container" style="display: none;">
                            <i class="fas fa-inbox"></i>
                            <p>暂无搜索记录</p>
                        </div>

                        <!-- 搜索列表 -->
                        <div id="downloads-list"></div>

                        <!-- 分页 -->
                        <div id="downloads-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 平台规划模态框 -->
    <div class="modal" id="platforms-roadmap-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container modal-large">
            <div class="modal-header">
                <h2>平台支持规划</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="roadmap-container">
                    <div class="roadmap-intro">
                        <p>我们正在不断扩展支持的平台，以下是当前的开发规划。您可以为希望优先支持的平台投票，帮助我们确定开发优先级。</p>
                    </div>

                    <div class="platform-status-section">
                        <h3>当前支持的平台</h3>
                        <div class="platform-status-grid">
                            <div class="platform-status-card active">
                                <div class="platform-status-icon">
                                    <i class="fab fa-tiktok"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>抖音</h4>
                                    <span class="status-badge live">已上线</span>
                                    <p>支持无水印视频和音频下载</p>
                                </div>
                            </div>
                            <div class="platform-status-card active">
                                <div class="platform-status-icon">
                                    <i class="fas fa-video"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>快手</h4>
                                    <span class="status-badge live">已上线</span>
                                    <p>支持无水印视频下载</p>
                                </div>
                            </div>
                            <div class="platform-status-card active">
                                <div class="platform-status-icon">
                                    <i class="fab fa-bilibili"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>哔哩哔哩</h4>
                                    <span class="status-badge live">已上线</span>
                                    <p>支持无水印视频下载</p>
                                </div>
                            </div>
                            <div class="platform-status-card active">
                                <div class="platform-status-icon">
                                    <i class="fab fa-cuttlefish"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>CSDN</h4>
                                    <span class="status-badge live">已上线</span>
                                    <p>支持文章截图和邮件发送</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="platform-status-section">
                        <h3>即将支持的平台</h3>
                        <div class="platform-status-grid">
                            <div class="platform-status-card">
                                <div class="platform-status-icon">
                                    <i class="fab fa-zhihu"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>知乎</h4>
                                    <span class="status-badge in-development">开发中</span>
                                    <p>支持文章和视频下载，预计上线时间：2025年7月</p>
                                    <div class="vote-container">
                                        <button class="vote-btn" data-platform="zhihu">
                                            <i class="fas fa-thumbs-up"></i>
                                            <span class="vote-count">18</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="platform-status-card">
                                <div class="platform-status-icon">
                                    <i class="fab fa-weibo"></i>
                                </div>
                                <div class="platform-status-info">
                                    <h4>微博</h4>
                                    <span class="status-badge planned">计划中</span>
                                    <p>支持视频和图片下载，预计上线时间：2025年8月</p>
                                    <div class="vote-container">
                                        <button class="vote-btn" data-platform="weibo">
                                            <i class="fas fa-thumbs-up"></i>
                                            <span class="vote-count">12</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="platform-suggestion">
                        <h3>平台建议</h3>
                        <p>您希望我们支持其他平台吗？请关注我们的公众号提交您的建议！</p>

                        <div class="wechat-suggestion-container">
                            <div class="qrcode-container">
                                <img src="/static/images/wechat-qrcode.jpg" alt="微信公众号二维码" class="wechat-qrcode">
                                <p class="qrcode-tip">扫码关注公众号</p>
                            </div>
                            <div class="suggestion-guide">
                                <h4>如何提交建议：</h4>
                                <ol>
                                    <li>扫描左侧二维码关注公众号</li>
                                    <li>在公众号中发送消息 <strong>"平台建议"</strong></li>
                                    <li>按照提示填写您希望支持的平台和功能</li>
                                </ol>
                                <p class="suggestion-benefits">通过公众号提交建议，您将第一时间收到平台更新通知，并有机会参与内测！</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户设置模态框 -->
    <div class="modal" id="settings-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>账户设置</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="tabs">
                    <button class="tab-btn active" data-tab="profile-settings">个人资料</button>
                    <button class="tab-btn" data-tab="password-settings">修改密码</button>
                    <button class="tab-btn" data-tab="permission-settings">权限管理</button>
                </div>

                <!-- 个人资料设置 -->
                <div id="profile-settings-tab" class="tab-content active">
                    <form id="profile-settings-form">
                        <div class="form-group">
                            <label for="settings-username">用户名</label>
                            <input type="text" id="settings-username" name="username" placeholder="输入用户名 (4-20个字符)" required minlength="4" maxlength="20">
                            <div class="validation-message" id="username-settings-validation" style="display: none;"></div>
                        </div>
                        <div class="form-group">
                            <label for="settings-phone">手机号码</label>
                            <input type="tel" id="settings-phone" name="phone" placeholder="手机号码" readonly>
                            <div class="form-hint">手机号码一旦注册不可更改</div>
                        </div>
                        <div class="status-container" id="profile-settings-status" style="display: none;">
                            <p class="status-message"></p>
                        </div>
                        <button type="submit" class="submit-btn">保存设置</button>
                    </form>
                </div>

                <!-- 修改密码 -->
                <div id="password-settings-tab" class="tab-content">
                    <form id="password-settings-form">
                        <div class="form-group">
                            <label for="current-password">当前密码</label>
                            <input type="password" id="current-password" name="current_password" placeholder="输入当前密码" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">新密码</label>
                            <input type="password" id="new-password" name="new_password" placeholder="输入新密码 (至少6个字符)" required minlength="6">
                            <div class="password-hint">密码长度至少6个字符</div>
                        </div>
                        <div class="form-group">
                            <label for="confirm-new-password">确认新密码</label>
                            <input type="password" id="confirm-new-password" name="confirm_new_password" placeholder="再次输入新密码" required>
                        </div>
                        <div class="status-container" id="password-settings-status" style="display: none;">
                            <p class="status-message"></p>
                        </div>
                        <button type="submit" class="submit-btn">修改密码</button>
                    </form>
                </div>

                <!-- 权限管理设置 -->
                <div id="permission-settings-tab" class="tab-content">
                    <div class="permission-info">
                        <div class="current-role-card">
                            <h3>当前账户类型</h3>
                            <div class="role-display">
                                <span class="role-badge" id="current-role-badge">普通用户</span>
                                <span class="role-description" id="current-role-description">可使用视频平台功能</span>
                            </div>
                            <div class="role-expire" id="role-expire-info" style="display: none;">
                                <span class="expire-label">到期时间：</span>
                                <span class="expire-date" id="expire-date"></span>
                            </div>
                        </div>

                        <div class="upgrade-options">
                            <h3>升级选项</h3>

                            <!-- Pro用户升级卡片 -->
                            <div class="upgrade-card pro-upgrade" id="pro-upgrade-card">
                                <div class="upgrade-header">
                                    <h4>升级到 Pro 用户</h4>
                                </div>
                                <div class="upgrade-features">
                                    <ul>
                                        <li>✅ 所有视频平台功能</li>
                                        <li>✅ 文章平台功能 (知乎)</li>
                                        <li>✅ 每日1000次下载限制</li>
                                        <li>✅ 优先技术支持</li>
                                        <li>✅ 无广告体验</li>
                                    </ul>
                                </div>
                                <div class="activation-input">
                                    <div class="form-group">
                                        <label for="activation-code">激活码</label>
                                        <input type="text" id="activation-code" placeholder="请输入激活码 (格式: XXXX-XXXX-XXXX-XXXX)" maxlength="19">
                                        <div class="input-hint">请输入有效的激活码来升级您的账户</div>
                                    </div>
                                    <div class="upgrade-actions">
                                        <button class="btn btn-primary" id="activate-code-btn">激活升级</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 降级选项 -->
                            <div class="downgrade-card" id="downgrade-card" style="display: none;">
                                <div class="downgrade-header">
                                    <h4>降级到普通用户</h4>
                                    <span class="downgrade-warning">⚠️ 将失去Pro功能</span>
                                </div>
                                <div class="downgrade-features">
                                    <ul>
                                        <li>❌ 文章平台功能将被禁用</li>
                                        <li>❌ 下载限制降至每日50次</li>
                                        <li>❌ 失去优先技术支持</li>
                                    </ul>
                                </div>
                                <div class="downgrade-actions">
                                    <button class="btn btn-danger downgrade-btn" data-target="normal_user">确认降级</button>
                                </div>
                            </div>
                        </div>

                        <!-- 管理员卡密生成区域 -->
                        <div class="admin-section" id="admin-section" style="display: none;">
                            <h3>卡密管理 (管理员)</h3>

                            <div class="admin-card">
                                <div class="admin-header">
                                    <h4>生成激活码</h4>
                                </div>
                                <div class="admin-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="code-days">激活天数</label>
                                            <select id="code-days">
                                                <option value="1">1天</option>
                                                <option value="7">7天</option>
                                                <option value="30" selected>30天</option>
                                                <option value="90">90天</option>
                                                <option value="365">365天</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="code-count">生成数量</label>
                                            <input type="number" id="code-count" min="1" max="100" value="1">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="code-description">描述信息</label>
                                        <input type="text" id="code-description" placeholder="可选的描述信息">
                                    </div>
                                    <div class="admin-actions">
                                        <button class="btn btn-primary" id="generate-codes-btn">生成激活码</button>
                                        <button class="btn btn-secondary" id="view-codes-btn">查看激活码</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 生成的激活码显示区域 -->
                            <div class="generated-codes" id="generated-codes" style="display: none;">
                                <h4>新生成的激活码</h4>
                                <div class="codes-list" id="codes-list">
                                    <!-- 激活码列表将通过JavaScript动态加载 -->
                                </div>
                            </div>

                            <!-- 激活码统计 -->
                            <div class="activation-stats" id="activation-stats">
                                <h4>激活码统计</h4>
                                <div class="stats-grid" id="stats-grid">
                                    <!-- 统计信息将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>

                        <div class="permission-details">
                            <h3>当前权限详情</h3>
                            <div class="permission-list" id="permission-list">
                                <!-- 权限列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="status-container" id="permission-settings-status" style="display: none;">
                        <p class="status-message"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 忘记密码模态框 -->
    <div class="modal" id="forgot-password-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>忘记密码</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="forgot-password-info">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3>密码找回说明</h3>
                    <p>由于系统安全考虑，我们暂时无法提供在线密码重置功能。</p>
                    <p>如果您忘记了密码，请通过以下方式联系我们：</p>
                </div>

                <div class="contact-methods">
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div class="method-info">
                            <h4>微信公众号</h4>
                            <p>关注我们的公众号，发送"忘记密码"获取帮助</p>
                            <button class="btn btn-primary" id="show-wechat-qr">
                                <i class="fas fa-qrcode"></i> 查看二维码
                            </button>
                        </div>
                    </div>

                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="method-info">
                            <h4>邮件联系</h4>
                            <p>发送邮件至：<EMAIL></p>
                            <p class="note">请在邮件中提供您的用户名和注册手机号</p>
                        </div>
                    </div>
                </div>

                <div class="security-tips">
                    <h4><i class="fas fa-shield-alt"></i> 安全提示</h4>
                    <ul>
                        <li>为了保护您的账户安全，我们需要验证您的身份</li>
                        <li>请提供准确的用户名和注册时使用的手机号</li>
                        <li>我们会在1-2个工作日内回复您的请求</li>
                        <li>建议您设置一个容易记住但足够安全的密码</li>
                    </ul>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-secondary" id="back-to-login">返回登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 公众号二维码模态框 -->
    <div class="modal" id="wechat-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container wechat-modal-container">
            <div class="modal-header wechat-modal-header">
                <h2>关注我们的公众号</h2>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body text-center">
                <p class="mb-3">扫描下方二维码，关注我们的公众号获取更多资讯</p>
                <div class="qrcode-container">
                    <img src="{{ url_for('static', filename='images/wechat-qrcode.jpg') }}" alt="宇慕科技公众号" class="qrcode-image">
                </div>
                <p class="mt-3 wechat-account-name">宇慕科技 - 您的技术伙伴</p>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件引用 -->
    <script src="{{ url_for('static', filename='js/permissions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/validation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/downloads.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/video-controls.js') }}"></script>
    <script src="{{ url_for('static', filename='js/permission-management.js') }}"></script>
    <!-- 直接的认证事件处理 -->
    <script src="{{ url_for('static', filename='js/direct-auth.js') }}"></script>

    <!-- 平台标签页切换功能 -->
    <script>
        // 标签页切换函数
        function switchPlatformTab(category, clickedButton) {
            // 获取所有标签按钮和内容区域
            const allTabButtons = document.querySelectorAll('.platform-tab-btn');
            const allTabContents = document.querySelectorAll('.platform-category-content');

            // 移除所有按钮的活动状态
            allTabButtons.forEach(btn => btn.classList.remove('active'));

            // 添加当前按钮的活动状态
            clickedButton.classList.add('active');

            // 隐藏所有内容区域
            allTabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // 显示目标内容区域
            const targetContent = document.getElementById(category + '-platforms');
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }
        }

        // 下载Base64图片的辅助函数
        function downloadBase64Image(base64Data, filename) {
            try {
                const link = document.createElement('a');
                link.href = 'data:image/jpeg;base64,' + base64Data;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showMessage('下载成功', 'success');
            } catch (error) {
                console.error('下载失败:', error);
                showMessage('下载失败', 'error');
            }
        }

        // 工具平台功能
        function initToolsPlatform() {
            // 证件照制作工具
            const idphotoForm = document.getElementById('idphoto-form');
            if (idphotoForm) {
                const uploadArea = document.getElementById('idphoto-upload-area');
                const fileInput = document.getElementById('idphoto-upload');
                const uploadedPreview = document.getElementById('uploaded-preview');
                const previewImage = document.getElementById('preview-image');
                const changePhotoBtn = document.getElementById('change-photo');

                // 上传区域点击事件
                uploadArea.addEventListener('click', () => {
                    fileInput.click();
                });

                // 拖拽上传
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0]);
                    }
                });

                // 文件选择事件
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleFileUpload(e.target.files[0]);
                    }
                });

                // 更换照片按钮
                changePhotoBtn.addEventListener('click', () => {
                    fileInput.click();
                });

                // 处理文件上传
                function handleFileUpload(file) {
                    if (!file.type.startsWith('image/')) {
                        alert('请选择图片文件');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        previewImage.src = e.target.result;
                        uploadArea.style.display = 'none';
                        uploadedPreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }

                // 自定义背景颜色
                const customRadio = document.querySelector('input[name="background"][value="custom"]');
                const customColorPicker = document.getElementById('custom-color-picker');
                const backgroundColorPicker = document.getElementById('background-color-picker');
                const customColorDisplay = document.getElementById('custom-color-display');

                document.querySelectorAll('input[name="background"]').forEach(radio => {
                    radio.addEventListener('change', () => {
                        if (radio.value === 'custom') {
                            customColorPicker.style.display = 'block';
                        } else {
                            customColorPicker.style.display = 'none';
                        }
                    });
                });

                backgroundColorPicker.addEventListener('input', (e) => {
                    customColorDisplay.style.backgroundColor = e.target.value;
                });

                // 表单提交
                idphotoForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData();
                    const file = fileInput.files[0];
                    const size = document.getElementById('idphoto-size').value;
                    const background = document.querySelector('input[name="background"]:checked').value;

                    if (!file) {
                        showMessage('请先上传照片', 'error');
                        return;
                    }

                    if (!size) {
                        showMessage('请选择证件照规格', 'error');
                        return;
                    }

                    // 准备表单数据
                    formData.append('photo', file);
                    formData.append('size', size);
                    formData.append('background', background);

                    // 如果是自定义颜色，添加颜色值
                    if (background === 'custom') {
                        const customColor = document.getElementById('background-color-picker').value;
                        formData.append('custom_color', customColor);
                    }

                    // 显示处理状态
                    const statusContainer = document.getElementById('idphoto-status');
                    const statusMessage = statusContainer.querySelector('.status-message');
                    const progressBar = document.getElementById('idphoto-progress');

                    statusContainer.style.display = 'block';
                    statusMessage.textContent = '正在上传照片...';
                    setProgress('idphoto-progress', 10);

                    // 调用API
                    fetch('/api/idphoto/create', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        setProgress('idphoto-progress', 50);
                        statusMessage.textContent = '正在处理照片...';
                        return response.json();
                    })
                    .then(data => {
                        setProgress('idphoto-progress', 90);
                        statusMessage.textContent = '正在生成证件照...';

                        if (data.success) {
                            setProgress('idphoto-progress', 100);
                            statusMessage.textContent = '处理完成！';

                            // 显示结果
                            const resultImage = document.getElementById('idphoto-result-image');
                            resultImage.src = 'data:image/jpeg;base64,' + data.data.single_photo;

                            const sizeInfo = document.getElementById('photo-size-info');
                            sizeInfo.textContent = data.data.size_info;

                            // 设置下载链接
                            window.idphotoResult = {
                                single: data.data.single_photo,
                                layout: data.data.layout_photo
                            };

                            document.getElementById('idphoto-result').style.display = 'block';

                            setTimeout(() => {
                                statusContainer.style.display = 'none';
                            }, 1000);
                        } else {
                            throw new Error(data.message || '证件照制作失败');
                        }
                    })
                    .catch(error => {
                        console.error('证件照制作失败:', error);
                        statusContainer.style.display = 'none';
                        showMessage(error.message || '证件照制作失败，请稍后重试', 'error');
                    });
                });

                // 下载功能
                document.getElementById('download-single').addEventListener('click', () => {
                    if (window.idphotoResult && window.idphotoResult.single) {
                        downloadBase64Image(window.idphotoResult.single, 'idphoto_single.jpg');
                    } else {
                        showMessage('没有可下载的证件照', 'error');
                    }
                });

                document.getElementById('download-layout').addEventListener('click', () => {
                    if (window.idphotoResult && window.idphotoResult.layout) {
                        downloadBase64Image(window.idphotoResult.layout, 'idphoto_layout.jpg');
                    } else {
                        showMessage('排版照生成失败，只能下载单张', 'warning');
                    }
                });
            }
        }



        // 初始化按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工具平台功能
            initToolsPlatform();

            // 延迟确保所有脚本都加载完成
            setTimeout(function() {
                // 初始化"了解更多"按钮
                const morePlatformsBtn = document.getElementById('more-platforms-btn');
                if (morePlatformsBtn) {
                    morePlatformsBtn.addEventListener('click', function() {
                        const modal = document.getElementById('platforms-roadmap-modal');
                        if (modal) {
                            modal.classList.add('show');
                            document.body.classList.add('modal-open');
                        }
                    });
                }

                // 初始化"提交反馈"按钮
                const feedbackBtn = document.getElementById('feedback-btn');
                if (feedbackBtn) {
                    feedbackBtn.addEventListener('click', function() {
                        const modal = document.getElementById('wechat-modal');
                        if (modal) {
                            modal.classList.add('show');
                            document.body.classList.add('modal-open');
                        }
                    });
                }

                // 初始化模态框关闭功能
                document.querySelectorAll('.modal').forEach(modal => {
                    const closeBtn = modal.querySelector('.close-modal');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', function() {
                            modal.classList.remove('show');
                            document.body.classList.remove('modal-open');
                        });
                    }

                    const overlay = modal.querySelector('.modal-overlay');
                    if (overlay) {
                        overlay.addEventListener('click', function() {
                            modal.classList.remove('show');
                            document.body.classList.remove('modal-open');
                        });
                    }
                });
            }, 1000);
        });
    </script>

    <!-- 引入JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/limit-handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/downloads.js') }}"></script>
    <script src="{{ url_for('static', filename='js/permissions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/video-controls.js') }}"></script>
</body>

</html>