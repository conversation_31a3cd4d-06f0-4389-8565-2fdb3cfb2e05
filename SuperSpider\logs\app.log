2025-06-06 15:27:38,232 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 15:27:38,233 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 15:27:38,234 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 15:27:38,234 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 15:27:38,234 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 15:27:38,683 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 15:27:39,324 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 15:27:39,330 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 15:27:39,688 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 15:27:39,697 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 15:27:39,700 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 15:27:39,703 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 15:27:39,706 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 15:27:39,708 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 15:27:39,716 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 15:27:39,749 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 15:27:39,823 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 15:27:39,824 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 15:27:39,830 - werkzeug - INFO -  * Restarting with stat
2025-06-06 15:27:40,616 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 15:27:40,618 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 15:27:41,657 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 15:27:41,661 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 15:27:41,674 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 15:27:43,473 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET / HTTP/1.1" 200 -
2025-06-06 15:27:43,585 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,586 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,597 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,630 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,649 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,669 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,670 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,686 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,698 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,698 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-06 15:27:43,704 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,729 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,940 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 15:27:43,956 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 15:27:44,444 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:44,449 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:44,487 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 15:27:55,961 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:55] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-06 15:27:56,926 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:56,945 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:56] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 15:28:40,914 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 15:28:40,920 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:28:40,924 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:28:40,926 - backend.spiders.csdn_spider - INFO - 尝试标准请求...
2025-06-06 15:28:41,529 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-06-06 15:28:41,533 - backend.spiders.csdn_spider - INFO - 等待 15.3 秒后重试...
2025-06-06 15:28:57,368 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-06-06 15:28:57,369 - backend.spiders.csdn_spider - INFO - 等待 22.0 秒后重试...
2025-06-06 15:29:19,846 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-06-06 15:29:19,847 - backend.spiders.csdn_spider - INFO - 等待 27.3 秒后重试...
2025-06-06 15:29:52,139 - backend.spiders.csdn_spider - WARNING - 请求失败 (第4次): HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/********* (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-06-06 15:29:52,140 - backend.spiders.csdn_spider - INFO - 等待 19.9 秒后重试...
2025-06-06 15:30:12,441 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-06-06 15:30:12,442 - backend.spiders.csdn_spider - WARNING - 标准请求失败: 达到最大重试次数
2025-06-06 15:30:12,442 - backend.spiders.csdn_spider - INFO - 标准请求失败，尝试VIP账号...
2025-06-06 15:30:12,464 - backend.utils.vip_account_pool - INFO - 刷新csdn账号池完成，共0个账号
2025-06-06 15:30:12,464 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 15:30:12,464 - backend.spiders.csdn_spider - WARNING - VIP账号请求也失败: 没有可用的CSDN VIP账号
2025-06-06 15:30:12,465 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-06-06 15:30:13,004 - backend.spiders.csdn_spider - ERROR - 解析文章失败: 'CSDNSpider' object has no attribute '_handle_vip_content_by_type'
2025-06-06 15:30:13,005 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:30:13,005 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 15:30:13,006 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 15:30:13,007 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:30:13,007 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:30:13] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 15:30:13,007 - backend.spiders.csdn_spider - INFO - 尝试标准请求...
2025-06-06 15:30:13,357 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-06-06 15:30:13,358 - backend.spiders.csdn_spider - INFO - 等待 12.5 秒后重试...
2025-06-06 15:30:14,375 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:30:14] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-06-06 15:30:26,275 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-06-06 15:30:26,276 - backend.spiders.csdn_spider - INFO - 等待 21.3 秒后重试...
2025-06-06 15:30:47,839 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-06-06 15:30:47,848 - backend.spiders.csdn_spider - INFO - 等待 21.5 秒后重试...
2025-06-06 15:31:09,827 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第4次)
2025-06-06 15:31:09,828 - backend.spiders.csdn_spider - INFO - 等待 18.1 秒后重试...
2025-06-06 15:31:28,426 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-06-06 15:31:28,426 - backend.spiders.csdn_spider - WARNING - 标准请求失败: 达到最大重试次数
2025-06-06 15:31:28,427 - backend.spiders.csdn_spider - INFO - 标准请求失败，尝试VIP账号...
2025-06-06 15:31:28,427 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 15:31:28,427 - backend.spiders.csdn_spider - WARNING - VIP账号请求也失败: 没有可用的CSDN VIP账号
2025-06-06 15:31:28,428 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-06-06 15:31:28,894 - backend.spiders.csdn_spider - ERROR - 解析文章失败: 'CSDNSpider' object has no attribute '_handle_vip_content_by_type'
2025-06-06 15:31:28,899 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250606_153128.html
2025-06-06 15:31:28,900 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 15:31:28,900 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章.html (大小: 5148 字节)
2025-06-06 15:31:28,901 - backend.utils.mailer - INFO - 附件 CSDN文章.html 添加成功
2025-06-06 15:31:28,928 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 15:31:29,126 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 15:31:29,483 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 15:31:30,053 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-06-06 15:31:30,053 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-06-06 15:31:30,054 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250606_153128.html
2025-06-06 15:32:45,385 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:32:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:37:45,372 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:37:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:42:45,374 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:42:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:43:22,757 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\__init__.py', reloading
2025-06-06 15:43:23,153 - werkzeug - INFO -  * Restarting with stat
2025-06-06 20:59:11,348 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 20:59:11,352 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 20:59:11,352 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 20:59:11,353 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 20:59:11,353 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 20:59:11,870 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 20:59:12,633 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 20:59:12,637 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 20:59:12,872 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 20:59:12,879 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 20:59:12,884 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 20:59:12,887 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 20:59:12,891 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 20:59:12,895 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 20:59:12,902 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 20:59:12,940 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 20:59:13,010 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 20:59:13,011 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 20:59:13,020 - werkzeug - INFO -  * Restarting with stat
2025-06-06 20:59:13,849 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 20:59:13,851 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 20:59:13,852 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 20:59:13,852 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 20:59:13,853 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 20:59:15,500 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 20:59:15,543 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 20:59:15,562 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 20:59:15,584 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 20:59:17,808 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:17] "GET / HTTP/1.1" 200 -
2025-06-06 20:59:18,141 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,141 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,187 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,188 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,204 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,249 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,266 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,269 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-06 20:59:18,271 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,291 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,293 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,325 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,413 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 20:59:18,424 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 20:59:18,914 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 20:59:18,935 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 20:59:18,979 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 20:59:29,882 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:29] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-06-06 21:01:34,231 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:01:34,592 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:01:35,749 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:01:35,751 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:01:37,252 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:01:37,330 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:01:37,364 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:01:37,381 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:01:50,632 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:01:50,850 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:01:51,876 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:01:51,877 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:01:51,878 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:01:51,878 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:01:51,878 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:01:53,157 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:01:53,221 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:01:53,236 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:01:53,246 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:08,621 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:09,064 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:09,984 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:09,985 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:09,986 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:09,986 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:09,986 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:11,342 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:02:11,405 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:02:11,429 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:02:11,447 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:47,113 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:47,410 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:48,250 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:48,251 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:49,444 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:02:49,506 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:02:49,531 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:02:49,547 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:57,742 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:57,971 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:58,791 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:58,792 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:58,792 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:58,793 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:58,793 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:59,995 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:00,077 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:00,097 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:03:00,121 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:03:11,340 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:03:11,664 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:03:12,515 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:12,516 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:12,516 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:12,517 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:12,517 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:03:13,680 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:13,734 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:13,750 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:03:13,763 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:03:47,918 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:47,920 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:03:48,461 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 21:03:48,931 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 21:03:48,935 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 21:03:49,151 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:49,160 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 21:03:49,164 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 21:03:49,167 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 21:03:49,171 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 21:03:49,176 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 21:03:49,182 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 21:03:49,221 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:49,265 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 21:03:49,265 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 21:03:49,267 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:03:50,111 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:50,113 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:50,113 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:50,114 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:50,114 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:03:51,444 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:51,495 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:51,519 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:03:51,533 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:04:19,299 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:04:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:04:52,917 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:04:53,507 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:04:55,170 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:04:55,171 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:04:55,171 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:04:55,172 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:04:55,172 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:04:56,399 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:04:56,469 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:04:56,529 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:04:56,572 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:05:06,763 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-06-06 21:05:07,105 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:05:08,064 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:05:08,065 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:05:08,065 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:05:08,066 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:05:08,066 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:05:09,348 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:05:09,378 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:05:09,399 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:05:18,625 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-06-06 21:05:18,969 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:05:19,883 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:05:19,884 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:05:19,884 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:05:19,884 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:05:19,885 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:05:21,224 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:05:21,247 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:05:21,260 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:05:52,208 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:05:52,209 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:05:52] "GET /api/csdn/status HTTP/1.1" 200 -
2025-06-06 21:05:52,220 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:05:52] "[32mPOST /api/csdn/parse HTTP/1.1[0m" 302 -
2025-06-06 21:05:52,227 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:05:52] "[31m[1mGET /api/auth/login?next=/api/csdn/parse HTTP/1.1[0m" 405 -
2025-06-06 21:06:06,112 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET / HTTP/1.1" 200 -
2025-06-06 21:06:06,131 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,142 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,171 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,180 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,182 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,194 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,200 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,211 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,214 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,268 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,269 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,269 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 21:06:06,327 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:06:06,342 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:06:06,815 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:06:06,826 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:06:06,882 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:06] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 21:06:12,373 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-06-06 21:06:12,374 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-06-06 21:06:12,376 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:06:12] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-06-06 21:07:28,869 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:07:29,071 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:07:29,771 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:07:29,772 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:07:29,772 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:07:29,773 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:07:29,773 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:07:31,034 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:07:31,044 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:07:31,059 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:10:48,910 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:10:48,911 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:10:48] "GET /api/csdn/status HTTP/1.1" 200 -
2025-06-06 21:10:49,012 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-06-06 21:10:49,018 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-06-06 21:10:49,022 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:10:49] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-06-06 21:11:07,290 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:11:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:11:51,258 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-06-06 21:11:51,638 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:11:52,797 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:11:52,798 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:11:52,801 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:11:52,802 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:11:52,803 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:11:53,992 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:11:54,017 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:11:54,031 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:12:10,679 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:12:10,680 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:12:10] "GET /api/csdn/status HTTP/1.1" 200 -
2025-06-06 21:12:10,685 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:12:10,686 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_44799217/article/details/*********
2025-06-06 21:12:10,687 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_44799217/article/details/*********
2025-06-06 21:12:10,688 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:12:17,950 - backend.spiders.csdn_spider - ERROR - 请求异常: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /weixin_44799217/article/details/********* (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-06-06 21:12:17,950 - backend.spiders.csdn_spider - WARNING - 标准请求失败，尝试VIP账号...
2025-06-06 21:12:17,964 - backend.utils.vip_account_pool - INFO - 刷新csdn账号池完成，共0个账号
2025-06-06 21:12:17,964 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 21:12:17,964 - backend.spiders.csdn_spider - WARNING - 没有可用的CSDN VIP账号
2025-06-06 21:12:17,966 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:12:17] "[31m[1mPOST /api/csdn/parse HTTP/1.1[0m" 400 -
2025-06-06 21:14:55,780 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:14:55,781 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:14:55,781 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:14:55,781 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:14:55,781 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:14:56,247 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 21:14:56,788 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 21:14:56,794 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 21:14:56,963 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 21:14:56,967 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 21:14:56,972 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 21:14:56,980 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 21:14:56,984 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 21:14:56,988 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 21:14:57,016 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 21:14:57,049 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:14:57,088 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 21:14:57,088 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 21:14:57,090 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:14:57,793 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:14:57,795 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:14:57,795 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:14:57,795 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:14:57,795 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:14:58,846 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:14:58,865 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:14:58,878 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:15:01,744 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:01] "GET / HTTP/1.1" 200 -
2025-06-06 21:15:01,968 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:01] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,019 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,062 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,075 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,083 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,094 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,117 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,117 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,127 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,129 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,152 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,153 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:15:02,380 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:15:02,395 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:15:02,880 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:15:02,886 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:15:02,935 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 21:15:36,652 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:15:36,652 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:15:36,653 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:15:36,657 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:15:40,316 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:15:40,530 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:15:40,531 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:15:40,531 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 21:15:40,531 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:15:40,533 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:40] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 21:15:40,533 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:15:40,533 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:15:41,091 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车
2025-06-06 21:15:41,092 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:15:41] "POST /api/search/record HTTP/1.1" 200 -
2025-06-06 21:15:43,869 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:15:44,074 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:15:44,076 - backend.spiders.csdn_spider - INFO - 文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215744.html
2025-06-06 21:15:44,076 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215744.html
2025-06-06 21:15:44,077 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:15:44,077 - backend.utils.mailer - INFO - 正在添加附件: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.html (大小: 16867 字节)
2025-06-06 21:15:44,079 - backend.utils.mailer - INFO - 附件 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.html 添加成功
2025-06-06 21:15:44,111 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 21:15:44,312 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 21:15:44,645 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 21:15:45,700 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-06-06 21:15:45,705 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-06-06 21:15:45,706 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215744.html
2025-06-06 21:17:25,321 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:17:25,321 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:17:25,322 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:17:25,322 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:17:27,831 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:17:28,041 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:17:28,043 - backend.api.csdn_api - INFO - 开始后台处理PDF文件生成和邮件发送: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:17:28,043 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 21:17:28,043 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:17:28,044 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:17:28] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 21:17:28,044 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:17:28,044 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:17:28,572 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:17:28] "POST /api/search/record HTTP/1.1" 200 -
2025-06-06 21:17:31,067 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:17:31,264 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:17:31,267 - backend.spiders.csdn_spider - ERROR - PDF生成需要安装weasyprint库
2025-06-06 21:17:31,267 - backend.spiders.csdn_spider - ERROR - 生成文件失败: PDF生成功能不可用，请安装weasyprint库
2025-06-06 21:17:31,267 - backend.api.csdn_api - ERROR - PDF生成失败: PDF生成功能不可用，请安装weasyprint库
2025-06-06 21:17:31,269 - backend.spiders.csdn_spider - INFO - 文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215851.html
2025-06-06 21:17:31,269 - backend.api.csdn_api - INFO - PDF生成失败，已生成HTML文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215851.html
2025-06-06 21:17:31,269 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:17:31,270 - backend.utils.mailer - INFO - 正在添加附件: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.html (大小: 16867 字节)
2025-06-06 21:17:31,271 - backend.utils.mailer - INFO - 附件 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.html 添加成功
2025-06-06 21:17:31,301 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 21:17:31,808 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 21:17:32,050 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 21:17:33,149 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-06-06 21:17:33,150 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-06-06 21:17:33,150 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749215851.html
2025-06-06 21:18:57,625 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\mailer.py', reloading
2025-06-06 21:18:58,084 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:18:59,387 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:18:59,390 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:18:59,390 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:18:59,391 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:18:59,391 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:19:00,765 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:19:00,806 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:19:00,829 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:19:18,181 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\mailer.py', reloading
2025-06-06 21:19:18,471 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:19:19,268 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:19:19,268 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:19:19,269 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:19:19,269 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:19:19,269 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:19:20,465 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:19:20,483 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:19:20,498 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:20:03,283 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:20:10,489 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:20:10,777 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:20:11,689 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:20:11,690 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:20:11,690 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:20:11,690 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:20:11,690 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:20:13,078 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:20:13,107 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:20:13,128 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:20:48,122 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:20:48,123 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:20:48,123 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:20:48,123 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:20:48,123 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:20:48,558 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 21:20:49,038 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 21:20:49,042 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 21:20:49,223 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 21:20:49,228 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 21:20:49,235 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 21:20:49,238 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 21:20:49,242 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 21:20:49,244 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 21:20:49,250 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 21:20:49,282 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:20:49,321 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 21:20:49,322 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 21:20:49,323 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:20:50,059 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:20:50,060 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:20:50,060 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:20:50,061 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:20:50,061 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:20:52,105 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:20:52,138 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:20:52,158 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:20:52,245 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "GET / HTTP/1.1" 200 -
2025-06-06 21:20:52,277 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,286 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,293 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,317 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,319 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,325 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,328 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,342 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,352 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,352 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,373 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,377 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:20:52,585 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:20:52,592 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:52] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:20:53,086 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:20:53,092 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:20:53,158 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:20:53] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 21:21:00,291 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:21:00,294 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:21:00,296 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:21:00,296 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:21:04,033 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:21:04,281 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:21:04,282 - backend.api.csdn_api - INFO - 开始后台处理MARKDOWN文件生成和邮件发送: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:21:04,282 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 21:21:04,282 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:21:04,283 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:21:04] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 21:21:04,283 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:21:04,283 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:21:04,820 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:21:04] "POST /api/search/record HTTP/1.1" 200 -
2025-06-06 21:21:08,075 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:21:08,312 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:21:08,317 - backend.spiders.csdn_spider - INFO - 文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216068.md
2025-06-06 21:21:08,317 - backend.api.csdn_api - INFO - Markdown文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216068.md
2025-06-06 21:21:08,318 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:21:08,319 - backend.utils.mailer - INFO - 正在添加附件: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.md (大小: 9808 字节)
2025-06-06 21:21:08,320 - backend.utils.mailer - INFO - 附件 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.md 添加成功
2025-06-06 21:21:08,351 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 21:21:09,866 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 21:21:10,150 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 21:21:11,041 - backend.utils.mailer - WARNING - SMTP连接关闭时出现错误，但邮件可能已发送成功: (-1, b'\x00\x00\x00')
2025-06-06 21:21:11,042 - backend.api.csdn_api - INFO - 邮件发送成功: <EMAIL>
2025-06-06 21:21:11,043 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216068.md
2025-06-06 21:21:11,043 - backend.api.csdn_api - INFO - CSDN文章处理完成，邮件已发送到: <EMAIL>
2025-06-06 21:22:52,823 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:22:52,824 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:22:52,824 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:22:52,824 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:22:59,604 - backend.spiders.csdn_spider - ERROR - 请求异常: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /csdnnews/article/details/********* (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-06-06 21:22:59,605 - backend.spiders.csdn_spider - WARNING - 标准请求失败，尝试VIP账号...
2025-06-06 21:22:59,613 - backend.utils.vip_account_pool - INFO - 刷新csdn账号池完成，共0个账号
2025-06-06 21:22:59,614 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 21:22:59,614 - backend.spiders.csdn_spider - WARNING - 没有可用的CSDN VIP账号
2025-06-06 21:22:59,615 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:22:59] "[31m[1mPOST /api/csdn/parse HTTP/1.1[0m" 400 -
2025-06-06 21:23:03,618 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:23:03,619 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:23:03,619 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:23:03,619 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:23:11,468 - backend.spiders.csdn_spider - ERROR - 请求异常: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /csdnnews/article/details/********* (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-06-06 21:23:11,469 - backend.spiders.csdn_spider - WARNING - 标准请求失败，尝试VIP账号...
2025-06-06 21:23:11,469 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 21:23:11,469 - backend.spiders.csdn_spider - WARNING - 没有可用的CSDN VIP账号
2025-06-06 21:23:11,470 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:23:11] "[31m[1mPOST /api/csdn/parse HTTP/1.1[0m" 400 -
2025-06-06 21:25:17,110 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-06-06 21:25:17,941 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:25:19,400 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:25:19,401 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:25:19,402 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:25:19,402 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:25:19,402 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:25:21,187 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:25:21,217 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:25:21,240 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:25:46,690 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:25:46,970 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:25:48,076 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:25:48,077 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:25:48,077 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:25:48,077 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:25:48,078 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:25:49,441 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:25:49,467 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:25:49,489 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:25:53,278 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:25:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:26:19,555 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:26:19,556 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:26:19,556 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:26:19,556 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:26:19,557 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:26:20,008 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 21:26:20,474 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 21:26:20,479 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 21:26:20,693 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 21:26:20,703 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 21:26:20,706 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 21:26:20,710 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 21:26:20,713 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 21:26:20,716 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 21:26:20,721 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 21:26:20,742 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:26:20,786 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 21:26:20,787 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 21:26:20,789 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:26:21,787 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:26:21,788 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:26:21,788 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:26:21,789 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:26:21,789 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:26:22,964 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:26:22,983 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:26:22,996 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:26:24,158 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "GET / HTTP/1.1" 200 -
2025-06-06 21:26:24,313 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,321 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,342 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,343 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,345 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,355 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,374 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,374 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,389 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,393 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,418 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,421 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:26:24,653 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:26:24,658 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:24] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:26:25,194 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:26:25,252 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:26:25,307 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:25] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 21:26:32,722 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:26:32,722 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:26:32,723 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:26:32,724 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:26:35,546 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:26:35,851 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:26:35,851 - backend.api.csdn_api - INFO - 开始后台处理PDF文件生成和邮件发送: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:26:35,851 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 21:26:35,852 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 21:26:35,853 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:35] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 21:26:35,853 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 21:26:35,854 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 21:26:36,380 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:26:36] "POST /api/search/record HTTP/1.1" 200 -
2025-06-06 21:26:39,435 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 21:26:39,816 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 21:26:42,046 - backend.spiders.csdn_spider - INFO - PDF生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216399.pdf
2025-06-06 21:26:42,046 - backend.api.csdn_api - INFO - PDF文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216399.pdf
2025-06-06 21:26:42,046 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 21:26:42,048 - backend.utils.mailer - INFO - 正在添加附件: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.pdf (大小: 545198 字节)
2025-06-06 21:26:42,065 - backend.utils.mailer - INFO - 附件 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.pdf 添加成功
2025-06-06 21:26:42,111 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 21:26:42,369 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 21:26:42,639 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 21:26:44,970 - backend.utils.mailer - WARNING - SMTP连接关闭时出现错误，但邮件可能已发送成功: (-1, b'\x00\x00\x00')
2025-06-06 21:26:44,985 - backend.api.csdn_api - INFO - 邮件发送成功: <EMAIL>
2025-06-06 21:26:44,990 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749216399.pdf
2025-06-06 21:26:44,995 - backend.api.csdn_api - INFO - CSDN文章处理完成，邮件已发送到: <EMAIL>
2025-06-06 21:31:25,270 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:31:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:45:13,558 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:13] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:45:15,728 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "GET / HTTP/1.1" 200 -
2025-06-06 21:45:15,782 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,787 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-06 21:45:15,845 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,854 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,856 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,859 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,865 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,897 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,914 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,930 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,955 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:15,972 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:15] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 21:45:16,014 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:16] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:45:16,047 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:16] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 21:45:16,508 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:45:16,516 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:45:16,598 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:45:16] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 21:50:16,519 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:50:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 21:55:16,529 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 21:55:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:00:16,514 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:00:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:05:16,519 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:05:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:10:16,529 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:10:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:15:16,530 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:15:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:20:16,522 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:20:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:25:16,524 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:25:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:30:16,515 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:30:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:35:16,532 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:35:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:40:14,822 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-06 22:40:16,323 - werkzeug - INFO -  * Restarting with stat
2025-06-06 22:40:18,384 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 22:40:18,404 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 22:40:18,404 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 22:40:18,410 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 22:40:18,412 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 22:40:21,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 22:40:21,224 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 22:40:21,252 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 22:40:21,308 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:40:21] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:40:34,643 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-06 22:40:35,147 - werkzeug - INFO -  * Restarting with stat
2025-06-06 22:40:36,195 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 22:40:36,196 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 22:40:36,196 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 22:40:36,196 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 22:40:36,196 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 22:40:37,584 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 22:40:37,604 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 22:40:37,623 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 22:41:35,704 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\user.py', reloading
2025-06-06 22:41:35,936 - werkzeug - INFO -  * Restarting with stat
2025-06-06 22:41:36,714 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 22:41:36,715 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 22:41:36,715 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 22:41:36,716 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 22:41:36,716 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 22:41:37,853 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 22:41:37,876 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 22:41:37,886 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 22:42:17,563 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 22:42:18,029 - werkzeug - INFO -  * Restarting with stat
2025-06-06 22:42:19,046 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 22:42:19,047 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 22:42:19,047 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 22:42:19,047 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 22:42:19,047 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 22:42:20,298 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 22:42:20,325 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 22:42:20,345 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 22:45:29,331 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:45:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:50:29,248 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:50:43,216 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 22:50:43,226 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 22:50:43,234 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[32mGET /api/permission/platforms HTTP/1.1[0m" 302 -
2025-06-06 22:50:43,243 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[31m[1mGET /api/auth/login?next=/api/permission/platforms HTTP/1.1[0m" 405 -
2025-06-06 22:50:43,251 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[32mPOST /api/csdn/parse HTTP/1.1[0m" 302 -
2025-06-06 22:50:43,259 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "[31m[1mGET /api/auth/login?next=/api/csdn/parse HTTP/1.1[0m" 405 -
2025-06-06 22:50:43,313 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:43] "GET / HTTP/1.1" 200 -
2025-06-06 22:50:56,059 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET / HTTP/1.1" 200 -
2025-06-06 22:50:56,108 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,121 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,122 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,123 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,125 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,157 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,158 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,169 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,166 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,171 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,193 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,207 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:50:56,303 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 22:50:56,321 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 22:50:56,792 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:50:56,807 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:50:56,923 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:50:56] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 22:51:02,454 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:02] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-06 22:51:04,816 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:04] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:51:04,853 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:04] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 22:51:07,050 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:07] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-06 22:51:13,726 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "GET / HTTP/1.1" 200 -
2025-06-06 22:51:13,749 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,754 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,778 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,784 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,800 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,841 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,850 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,867 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,883 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,894 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,909 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,913 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:13,934 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 22:51:13,956 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:13] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-06 22:51:14,429 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:14] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 22:51:14,433 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:14] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-06-06 22:51:14,436 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:14] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 22:51:14,451 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:14] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-06-06 22:51:29,868 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-06 22:51:33,488 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "GET / HTTP/1.1" 200 -
2025-06-06 22:51:33,510 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,526 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,537 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,537 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,541 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,554 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,567 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,585 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,588 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,624 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,675 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,726 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 22:51:33,849 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 22:51:33,891 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 22:51:34,354 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:51:34,367 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 22:51:40,553 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 22:51:40,554 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 22:51:40,555 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 22:51:40,555 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 22:51:43,641 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 22:51:43,871 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 22:51:43,875 - backend.api.csdn_api - INFO - 开始后台处理PDF文件生成和邮件发送: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 22:51:43,875 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 22:51:43,875 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 22:51:43,880 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdnnews/article/details/*********
2025-06-06 22:51:43,881 - backend.spiders.csdn_spider - INFO - 🔍 正在分析文章链接...
2025-06-06 22:51:43,881 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:43] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 22:51:45,260 - backend.api.search_api - INFO - 用户 A888 创建搜索记录: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车
2025-06-06 22:51:45,262 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:51:45] "POST /api/search/record HTTP/1.1" 200 -
2025-06-06 22:51:46,782 - backend.spiders.csdn_spider - INFO - 📖 正在解析文章内容...
2025-06-06 22:51:47,011 - backend.spiders.csdn_spider - INFO - ✅ 成功解析CSDN文章: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车 (VIP类型: paid, 使用VIP: False, 内容长度: 3294字符)
2025-06-06 22:51:49,074 - backend.spiders.csdn_spider - INFO - PDF生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749221507.pdf
2025-06-06 22:51:49,074 - backend.api.csdn_api - INFO - PDF文件生成成功: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749221507.pdf
2025-06-06 22:51:49,075 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 22:51:49,076 - backend.utils.mailer - INFO - 正在添加附件: 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.pdf (大小: 545197 字节)
2025-06-06 22:51:49,095 - backend.utils.mailer - INFO - 附件 他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车.pdf 添加成功
2025-06-06 22:51:49,257 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 22:51:49,516 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 22:51:49,773 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 22:51:52,256 - backend.utils.mailer - WARNING - SMTP连接关闭时出现错误，但邮件可能已发送成功: (-1, b'\x00\x00\x00')
2025-06-06 22:51:52,257 - backend.api.csdn_api - INFO - 邮件发送成功: <EMAIL>
2025-06-06 22:51:52,258 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\他用AI三天做了个网站，结果被黑了两次！氛围编码大翻车_1749221507.pdf
2025-06-06 22:51:52,258 - backend.api.csdn_api - INFO - CSDN文章处理完成，邮件已发送到: <EMAIL>
2025-06-06 22:56:35,253 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 22:56:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:01:35,236 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:01:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:06:35,234 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:06:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:11:35,246 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:11:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:16:35,251 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:16:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:21:35,239 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:21:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:27:29,245 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:27:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:31:34,366 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:31:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:36:34,368 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:36:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:41:34,390 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:41:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:46:34,363 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:46:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:51:34,355 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:51:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 23:56:34,369 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 23:56:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:01:34,367 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:01:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:06:34,355 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:06:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:11:34,360 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:11:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:16:34,363 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:16:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:21:34,356 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:21:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:26:34,364 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:26:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:31:34,350 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:31:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:36:34,350 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:36:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:41:34,348 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:41:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:47:29,268 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:47:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:52:29,223 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:52:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 00:57:29,211 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 00:57:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:02:29,213 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:02:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:07:29,225 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:07:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:12:29,242 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:12:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:17:29,216 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:17:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:22:29,213 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:22:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:27:29,212 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:27:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:32:29,225 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:32:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 01:37:29,210 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 01:37:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:31:43,193 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:31:43] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:37:49,920 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:37:49] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:42:29,195 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:42:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:47:29,201 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:47:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:52:29,246 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:52:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 02:57:29,196 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 02:57:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:02:29,196 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:02:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:07:29,224 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:07:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:12:29,199 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:12:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:17:29,196 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:17:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:22:29,181 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:22:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:26:20,972 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 03:26:21,151 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 03:27:29,203 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:27:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:32:29,186 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:32:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 03:37:29,178 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 03:37:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 04:42:47,604 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 04:42:47,711 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 09:26:48,873 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 09:26:48,973 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 10:42:48,358 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 10:42:48,402 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 15:26:49,183 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 15:26:49,316 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 15:53:30,307 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 15:53:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 15:57:45,061 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 15:57:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:02:29,527 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 16:02:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:07:29,532 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 16:07:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:12:29,527 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 16:12:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:17:29,543 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 16:17:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:21:35,544 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 16:21:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 16:42:48,567 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 16:42:48,707 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 21:26:49,572 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 21:26:49,734 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 22:42:49,063 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-07 22:42:49,189 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-07 23:38:04,217 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:38:04] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-07 23:38:04,599 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:38:04] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 23:38:04,610 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:38:04] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 23:43:05,480 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:43:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 23:48:05,454 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:48:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 23:53:05,464 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:53:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-07 23:58:05,467 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 23:58:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:03:05,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:03:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:06:48,174 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:06:48] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 00:06:49,217 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:06:49] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:07:15,620 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:07:15] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 00:08:05,453 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:08:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:09:26,513 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 00:09:27,873 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:09:29,304 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:09:29,306 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:09:29,306 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:09:29,306 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:09:29,306 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:09:31,264 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:09:31,305 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:09:31,329 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:09:43,767 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 00:09:44,062 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:09:44,999 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:09:45,000 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:09:45,000 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:09:45,000 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:09:45,000 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:09:46,756 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:09:46,779 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:09:46,795 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:09:50,177 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 00:09:50,782 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:09:51,881 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:09:51,885 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:09:51,885 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:09:51,888 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:09:51,888 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:09:53,420 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:09:53,443 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:09:53,464 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:10:04,716 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 00:10:05,026 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:10:06,119 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:10:06,120 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:10:06,120 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:10:06,121 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:10:06,121 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:10:07,279 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:10:07,300 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:10:07,317 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:10:56,239 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-08 00:10:56,536 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:10:57,352 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:10:57,354 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:10:57,355 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:10:57,355 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:10:57,355 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:10:58,946 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:10:58,964 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:10:58,979 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:11:09,190 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-08 00:11:09,430 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:11:10,287 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:11:10,288 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:11:10,288 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:11:10,288 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:11:10,288 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:11:11,452 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:11:11,475 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:11:11,498 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:11:50,324 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-08 00:11:50,743 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:11:52,116 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:11:52,145 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:11:52,145 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:11:52,147 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:11:52,147 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:11:53,810 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:11:53,847 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:11:53,866 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:13:29,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:13:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:16:05,991 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:05] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 00:16:10,869 - superspider.permissions - INFO - 用户 A888 已升级为Pro用户，有效期至 2025-07-08 00:16:11
2025-06-08 00:16:10,871 - backend.api.activation_api - INFO - 用户 A888 使用激活码 BKCV-9AXV-MR2T-FWB7 升级为Pro用户 (30天)
2025-06-08 00:16:10,872 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:10] "POST /api/activation/use HTTP/1.1" 200 -
2025-06-08 00:16:11,886 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:11] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:16:14,632 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "GET / HTTP/1.1" 200 -
2025-06-08 00:16:14,668 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,675 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,684 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,687 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,708 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,722 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,740 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,765 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,805 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,815 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,818 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,834 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 00:16:14,861 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 00:16:14,883 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 00:16:15,347 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:16:15,355 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:16:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 00:17:25,966 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\user.py', reloading
2025-06-08 00:17:26,537 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:17:27,385 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:17:27,386 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:17:27,386 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:17:27,386 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:17:27,386 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:17:28,844 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:17:28,866 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:17:28,882 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:18:31,046 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\scheduler.py', reloading
2025-06-08 00:18:31,342 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:18:32,900 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:18:32,951 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:18:32,952 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:18:32,968 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:18:32,968 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:18:34,469 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:18:34,593 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:18:34,675 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:18:43,902 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\scheduler.py', reloading
2025-06-08 00:18:44,215 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:18:45,038 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:18:45,039 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 00:18:45,040 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:18:45,040 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:18:45,040 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:18:45,040 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:18:46,318 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:18:46,320 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 00:18:46,321 - backend.utils.scheduler - ERROR - 每日重置任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:18:46,322 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:18:46,340 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:18:46,354 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:19:46,335 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 00:19:46,591 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:19:47,699 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:19:47,700 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 00:19:47,704 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:19:47,704 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:19:47,704 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:19:47,707 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:19:48,882 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:19:48,885 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 00:19:48,888 - backend.utils.scheduler - ERROR - 每日重置任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:19:48,888 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:19:48,909 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:19:48,924 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:20:03,240 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-08 00:20:03,471 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:20:04,463 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:20:04,463 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 00:20:04,464 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:20:04,464 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:20:04,464 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:20:04,465 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:20:05,687 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:20:05,687 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 00:20:05,687 - backend.utils.scheduler - ERROR - 每日重置任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:20:05,688 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:20:05,702 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:20:05,719 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:20:12,901 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-08 00:20:13,208 - werkzeug - INFO -  * Restarting with stat
2025-06-08 00:20:14,150 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 00:20:14,150 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 00:20:14,151 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 00:20:14,151 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 00:20:14,151 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 00:20:14,151 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 00:20:15,316 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 00:20:15,317 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 00:20:15,318 - backend.utils.scheduler - ERROR - 每日重置任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:20:15,318 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 任务失败: No module named 'backend.models.user_usage'
2025-06-08 00:20:15,336 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 00:20:15,353 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 00:21:15,450 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 00:21:15] "GET /api/permission/check HTTP/1.1" 200 -
