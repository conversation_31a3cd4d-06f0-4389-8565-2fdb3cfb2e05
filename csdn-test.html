<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>csdn文章浏览/资源下载</title>
  <link href="css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
  <link rel="stylesheet" href="css/font_3112205_f25la713bjb.css"/>
  <link href="css/custom-theme.css" rel="stylesheet" type="text/css"/>
  <link href="js/layui/css/layui.css" rel="stylesheet" type="text/css"/>
  <!--    <link href="js/layui/css/layui.css"></link>-->
  <script src="js/pace.min.js" defer></script>

  <style>
    .reward-container {
      display: flex;
      justify-content: center;  /* 水平居中 */
      margin-top: 10px;         /* 与上方内容的距离 */
    }

    .reward-container img {
      display: block;           /* 使图片不在行内显示 */
      margin: auto;             /* 图片居中 */
      width: 150px;             /* 图片宽度 */
      height: 150px;            /* 图片高度 */
    }

    .reward-text {
      text-align: center;       /* 文字居中 */
      color: #0e1055;           /* 文字颜色 */
      font-size: 0.7rem;        /* 文字大小 */
      margin-bottom: 10px;      /* 文字与图片的间距 */
    }

    /* 浮动广告容器 */
    .floating-ad {
      position: fixed;
      top: 50%;
      right: 0;
      transform: translateY(-50%); /* 使广告居中显示 */
      width: 250px;
      height: auto;
      background-color: #fff;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      padding: 10px;
      z-index: 9999; /* 确保广告浮动在所有元素之上 */
    }

    .floating-ad img {
      width: 100%;
      border-radius: 8px;
    }

    .close-ad-btn {
      position: absolute;
      top: 5px;
      right: 5px;
      background: none;
      border: none;
      font-size: 20px;
      color: #888;
      cursor: pointer;
      font-weight: bold;
    }

    .close-ad-btn:hover {
      color: #ff0000; /* 鼠标悬停时改变颜色 */
    }

  </style>
</head>
<body>
<div class="container">
  <form class="form-horizontal mt-4">
    <h3 class="mb-3 text-center">csdn文章浏览/资源下载</h3>
    <div class="form-group">
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="inputType" id="linkOption" value="link" checked>
        <label class="form-check-label" for="linkOption">文章浏览</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="inputType" id="fileOption" value="file">
        <label class="form-check-label" for="fileOption">文件代下载</label>
      </div>
    </div>
    <!-- 添加一个新的文本行 -->
    <div class="form-group" id="undefinedTextGroup" style="display: none;">
      <p style="color: #f30923;font-size: 1rem;" >【文件代下载】功能收费，杜绝二次倒卖</p>
      <p style="color: #f30923;font-size: 1rem;" >推荐只使用VIP文章浏览功能即可！</p>
      <p style="color: #f30923;font-size: 1rem;" >如果付费后下载有问题可以随时加我微信：kaka99800 解决</p>
    </div>

    <div class="form-group">
      <input type="text" class="form-control" id="link" placeholder="请输入文章链接" required>
      <input type="text" class="form-control d-none" id="fileLink" placeholder="输入Csdn文件地址">
    </div>

    <div class="form-group">
      <input type="email" class="form-control" id="email" placeholder="输入收件邮箱（如收不到邮件,可能在垃圾箱中）" required>
    </div>
    <div class="form-group" id="formatSelectGroup">
      <label for="formatSelect">选择文章发送格式:</label>
      <select class="form-control" id="formatSelect">
        <option value="html" selected>HTML</option>
<!--        <option value="screenshot">长截图</option>-->
      </select>
    </div>
    <div class="form-group">
      <button type="button" class="btn btn-primary btn-block" id="download">下载</button>
    </div>
  </form>

  <div class="layui-form-item" id="table-container">
    <div class="layui-table-box">
      <table id="commit-aiartcle-list" class="layui-table" lay-filter="commit-aiartcle-list"></table>
    </div>
  </div>

  <div class="form-group reward-container">
    <div>
      <div class="reward-text" id = 'reward-text-id'  style="color: #f30923;font-size: 1.0rem;" >当前人数过多，使用过程中请耐心等待，如有问题可添加微信kaka99800咨询帮助</div>
    </div>
  </div>

</div>

<!-- 浮动广告容器 -->
<!--<div id="floating-ad" class="floating-ad">-->
<!--  <img src="images/img_2.png" style="width: 100px;height: 100px" alt="广告" />-->
<!--  <p>接计算机专业毕设</p>-->
<!--  <p>软件，硬件，论文（包查重）</p>-->
<!--  <p>提供各种参考模版资料</p>-->
<!--  <button id="close-ad" class="close-ad-btn">X</button>-->
<!--</div>-->

<div id="loginLayer" style="display: none;overflow-x: hidden;">
  <form class="form-horizontal">
    <div class="form-group">
      <div class="form-group">
        <div class="col-sm-12 control-label" style="left: 20px">
          <img src="images/WechatIMG5074.jpg"  style="width: 250px;height: 250px" class="d-block carousel-img">
        </div>
      </div>
      <div class="form-group">
        <div class="col-sm-12 control-label">扫一扫二维码，关注微信公众号<span  style="font-weight: bold"></span></div>
        <div class="col-sm-12 control-label">发送验证码 <span id="row_carousel_container" style="font-weight: bold"></span> 即可登录！</div>
      </div>
    </div>
  </form>
</div>


<script src="js/jquery.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="plugins/layer/layer.js"></script>
<script src="js/layui/layui.js"></script>
<script src="js/login.js"></script>
<script>

  $(document).ready(function() {
    // 点击关闭按钮，隐藏广告
    $("#close-ad").click(function() {
      $("#floating-ad").hide();  // 隐藏广告
      // localStorage.setItem("adClosed", "true");  // 存储广告关闭状态
    });

    // // 检查是否已经关闭过广告，如果关闭过则不再显示
    // if (localStorage.getItem("adClosed") === "true") {
    //   $("#floating-ad").hide();
    // }

    // 实时监听 fileLink 的内容是否合法
    // $("#fileLink").on("input", function () {
    //   const val = $(this).val();
    //   if ((val.indexOf("download.csdn.net") ===-1) && (val.indexOf("wenku.csdn.net") ===-1)) {
    //     layer.msg("请填写正确的CSDN资源下载链接。");
    //     $(this).val("");  // 清空输入框
    //   }
    // });

  });

  $(document).ready(function() {
    // 切换输入框显示
    $('input[name="inputType"]').change(function() {
      if ($('#linkOption').is(':checked')) {
        $('#link').removeClass('d-none').attr('required', true);
        $('#formatSelectGroup').removeClass('d-none');
        $('#fileLink').addClass('d-none').attr('required', false);
        $('#undefinedTextGroup').hide();  // 隐藏"未定义"文字
      } else {
        $('#link').addClass('d-none').attr('required', false);
        $('#formatSelectGroup').addClass('d-none');
        $('#fileLink').removeClass('d-none').attr('required', true);

      }
    });


    $("#download").click(function () {
      var link = $("#link").val().trim();
      var fileLink = $("#fileLink").val().trim();
      var format = $("#formatSelect").val();
      var email = $("#email").val();
      var queryParams = new URLSearchParams(window.location.search);
      var cardKey = queryParams.get('cardKey') || ''; // 如果 URL 中没有 cardKey，则使用默认值

      if ($('#linkOption').is(':checked') && !link) {
        layer.msg("请输入文章链接");
        return;
      }

      if ($('#linkOption').is(':checked') && link.indexOf("download.csdn.net")>-1) {
        layer.msg("该链接非文章类型! 请点击上方文件代下载选项");
        return;
      }

      if ($('#linkOption').is(':checked')&& !link.startsWith('http')) {
        layer.msg("文章链接应该是http开头的哦~");
        return;
      }

      if ($('#fileOption').is(':checked') && !fileLink) {
        layer.msg("请输入Csdn文件地址");
        return;
      }

      if ($('#fileOption').is(':checked') && !fileLink.startsWith('http')) {
        layer.msg("下载链接应该是http开头的哦~");
        return;
      }

      if ($('#fileOption').is(':checked') && fileLink.includes("blog.csdn.net")) {
        layer.msg("该链接为文章类型，请在上方选择文章浏览!");
        return;
      }

      if ($('#fileOption').is(':checked') && (fileLink.indexOf("download.csdn.net") ===-1) && (fileLink.indexOf("wenku.csdn.net") ===-1)) {
        layer.msg("请填写正确的CSDN资源下载链接。");
        return;
      }

      if (!email) {
        layer.msg("请输入收件邮箱");
        return;
      }



      if (!cardKey) {
        var token = localStorage.getItem("token");

        if (!token) {
          getLoginType();
          return;
        }
      }

      var dataToSend = { "email": email, "cardKey": cardKey };
      if ($('#linkOption').is(':checked')) {
        //分割
        splits = link.split("#:~:text=")
        if (splits.length > 1) {
          link = splits[0]
        }
        dataToSend.link = link;
        dataToSend.format = format;
      } else {
        dataToSend.fileLink = fileLink;
      }
      if ( dataToSend["cardKey"] === "" || dataToSend["cardKey"] == null){
        dataToSend.token  = localStorage.getItem("token")
      }
      var index = layer.load(1);

      $.ajax({
        url: "/article/downloadnew",
        type: "post",
        contentType: "application/json;charset=UTF-8",
        timeout: 90000,
        data: JSON.stringify(dataToSend),
        success: function (data) {
          layer.close(index);
          if (data.code == 0){
            if($('#linkOption').is(':checked')){
              layer.msg("任务已提交，请1-3分钟后检查您的邮箱");
            }else {
              layer.msg("任务已提交，付费后自动开始下载");
              $('#undefinedTextGroup').show();  // 显示"未定义"文字
            }
            $('#table-container').show();
            // 动态更新 token 并重新加载表格
            var updatedToken = localStorage.getItem("token"); // 确保获取最新的 token
            layui.table.reload('commit-aiartcle-list', {
              where: { token: updatedToken } // 动态传递最新的 token
            });
          }else if(data.code == 401) {
            layer.msg(data.msg);
            getLoginType();
          }else{
            layer.msg(data.msg);
          }
        },
        error: function (xhr, textStatus) {
          console.log(textStatus);
          layer.close(index);
          layer.msg("请求失败，请稍后重试");
        }
      });

    });


    layui.use(['table'], function() {
      var table = layui.table;
      let autoRefreshInterval; // 存储定时器 ID

      // 表格渲染函数
      function renderTable(searchTitle = '') {
        table.render({
          elem: '#commit-aiartcle-list',
          url: '/csdn/article/csdnTaskDownloadHistorylist',
          method: 'POST',
          contentType: 'application/json',
          where: {
            token: localStorage.getItem("token"),
            title: searchTitle  // 加入标题过滤条件
          },
          page: true,
          limits: [5, 10, 20],
          limit: 10,
          request: {
            pageName: 'page',
            limitName: 'pageSize'
          },
          parseData: function(res) {
            if (res.code === 401) {
              $('#table-container').hide();  // 隐藏表格
            } else {
              manageAutoRefresh(res.data.records);
              return {
                code: res.code,
                msg: res.msg || '',
                count: res.data.total,
                data: res.data.records
              };
            }
          },
          cols: [[
            { field: 'id', title: '序号', width: '9%', align: 'center' },
            { field: 'taskName', title: '标题', width: '37%' },
            { field: 'status', title: '状态', width: '8%', templet: function(d) {
                switch (d.status) {
                  case -1: return '待付费';
                  case 0: return '准备开始';
                  case 1: return '正在发送';
                  case 2: return '出错';
                  case 3: return '发送完毕';
                  case 4: return '中止';
                  default: return '未知状态';
                }
              }
            },
            {
              field: 'taskType',title: '执行类型',width: '8%', templet: function(d) {
                switch (d.taskType) {
                  case 0: return '文章浏览';
                  case 1: return '文件下载';
                  default: return '文章浏览';
                }
              }
            },
            {
              field: 'email',title: '收件箱地址',width: '15%'
            },
            { field: 'createTime', title: '创建时间', width: '12%',
              templet: function(d) {
                if (d.createTime) {
                  return d.createTime.substring(5, 16); // 格式化时间
                }
                return '';
              }
            },
            // 这里是操作列，显示"付款"按钮
            {
              fixed:'right',
              title: '操作',
              width: '11%',
              // align: 'center',
              templet: function(d) {
                // 只有状态为-1的情况下才显示付款按钮
                if (d.status === -1) {
                  return '<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="pay">支付</button>';
                }

                if (d.status === 2) {
                  return '<button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="retry">重新发送</button>';
                }
                if (d.status === 3&& d.taskType === 0) {
                  return '<button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="retry">重新发送</button>';
                }

                if (d.status === 3&& d.taskType === 1) {
                  return '<button class="layui-btn layui-btn-green layui-btn-sm" lay-event="onlineGet">提取</button>';
                }
                return '';
              }
            }
          ]]
        });
      }
      // 初次渲染表格
      renderTable();

      // 工具条事件监听
      table.on('tool(commit-aiartcle-list)', function(obj) {
        var data = obj.data;

        if (obj.event === 'retry') {
          tips_str = ''
          // 点击"重发"按钮时
          if (data.status === 2) {
            tips_str = '确定重新发送任务《' + data.taskName + '》吗？'
          }
          if (data.status === 3&& data.taskType === 0) {
            tips_str = '确定重新发送任务吗？' +
                    '\n(重新发送前请检查邮箱是否正确输入，并查找垃圾箱中是否含有误判邮件)'
          }
          layer.confirm(tips_str, function(index) {
            layer.close(index);

            var token = localStorage.getItem("token");
            var requestData = { token: token, id: data.id };

            $.ajax({
              url: "/csdn/article/retryTask",  // 假设的重发请求 URL
              type: "POST",
              headers: {
                'Access-Control-Allow-Origin': '*',
                'token': localStorage.getItem('token') || '', // 从localStorage中获取token
              },
              contentType: "application/json",
              data: JSON.stringify(requestData),
              success: function(response) {
                if (response.code === 0) {
                  layer.msg("任务已重新发送，请稍后查看");
                  table.reload('commit-aiartcle-list', {
                    where: { token: localStorage.getItem("token") }
                  });
                } else {
                  layer.msg(response.msg);
                }
              },
              error: function() {
                layer.msg("重发失败，请稍后重试");
              }
            });
          });
        } else if (obj.event === 'view') {
          //打开新页面
          // window.open("/md.html?id=" + data.id);
        }else if (obj.event === 'delete') {
          layer.confirm('确定删除任务《' + data.taskName + '》？', function(index) {
            layer.close(index);

            var token = localStorage.getItem("token");
            var requestData = { token: token, id: data.id, status: 4 }; // 状态设为 4 (中止)

            $.ajax({
              url: "/gpt/auto/article/delete/byId",
              type: "POST",
              contentType: "application/json",
              data: JSON.stringify(requestData),
              success: function(response) {
                if (response.code === 0) {
                  layer.msg("稿件已删除");
                  table.reload('commit-aiartcle-list', {
                    where: { token: localStorage.getItem("token") }
                  });
                } else {
                  layer.msg(response.msg);
                }
              },
              error: function() {
                layer.msg("删除任务失败，请稍后重试");
              }
            });
          });
        }else if (obj.event === 'pay') {
          // 发起请求，获取付款二维码
          $.ajax({
            url: '/payment/getQRCode',  // 假设你的接口是这个
            type: 'POST',
            data: JSON.stringify({ id: data.id ,token: localStorage.getItem("token")}),  // 传递任务 ID
            contentType: 'application/json',
            success: function(response) {
              if (response.code === 0) {
                var qrCodeBase64 = response.data;  // 后端返回的Base64编码

                // 在弹窗中显示二维码
                var content = '<div style="text-align: center;">' +
                        '<img src="data:image/png;base64,' + qrCodeBase64 + '" style="width: 200px; height: 200px;" alt="二维码"/>' +
                        '<p>请扫描微信二维码付款2元</p>' +
                        '<p>付款完毕后，刷新页面即可</p>'
                '</div>';

                // 弹窗显示二维码
                layer.open({
                  type: 1,
                  title: '付款二维码',
                  content: content,
                  area: ['260px', '380px'],  // 弹窗大小
                  btn: ['关闭'],
                  yes: function(index, layero) {
                    layer.close(index); // 关闭弹窗
                  }
                });
              } else {
                layer.msg('获取二维码失败: ' + response.msg);
              }
            },
            error: function(xhr, status, error) {
              layer.msg('请求失败，请稍后重试');
            }
          });
        }else if (obj.event === 'onlineGet') {

          layer.prompt({
            formType: 2,  // 0是默认值，1是不明文显示值，2是大输入框
            value: data.shareLink +"\n操作提示：在线提取为网盘链接，建议到邮箱中直接获取源文件更好！"+ "\n如邮箱中未收到邮件可以在垃圾箱中找找",
            title: '网盘下载链接'
          }, function(value, index, elem){
            layer.close(index);
          });
        }
      });


      // 搜索按钮点击事件
      $('#searchButton').click(function() {
        var searchTitle = $('#searchTitle').val().trim();
        renderTable(searchTitle);
      });


      // 管理自动刷新逻辑
      function manageAutoRefresh(records) {
        const hasPendingTasks = records.some(record => record.status === 0 || record.status === 1);

        if (hasPendingTasks) {
          if (!autoRefreshInterval) { // 如果没有定时器，则创建一个
            autoRefreshInterval = setInterval(function() {
              table.reload('commit-aiartcle-list', {
                where: { token: localStorage.getItem("token") }
              });
            }, 60000); // 每1分钟刷新一次
          }
        } else {
          if (autoRefreshInterval) { // 如果定时器存在，清除它
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
          }
        }
      }

    });
  });

</script>
</body>
</html>
