{"version": 3, "file": "module-C-VadMaF.js", "sources": ["../../../../node_modules/.pnpm/fast-unique-numbers@8.0.10/node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js", "../../../../node_modules/.pnpm/fast-unique-numbers@8.0.10/node_modules/fast-unique-numbers/build/es2019/factories/cache.js", "../../../../node_modules/.pnpm/fast-unique-numbers@8.0.10/node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js", "../../../../node_modules/.pnpm/fast-unique-numbers@8.0.10/node_modules/fast-unique-numbers/build/es2019/module.js", "../../../../node_modules/.pnpm/broker-factory@3.0.87/node_modules/broker-factory/build/es2019/guards/message-port.js", "../../../../node_modules/.pnpm/broker-factory@3.0.87/node_modules/broker-factory/build/es2019/helpers/port-map.js", "../../../../node_modules/.pnpm/broker-factory@3.0.87/node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js", "../../../../node_modules/.pnpm/broker-factory@3.0.87/node_modules/broker-factory/build/es2019/module.js"], "sourcesContent": ["export const createAddUniqueNumber = (generateUniqueNumber) => {\n    return (set) => {\n        const number = generateUniqueNumber(set);\n        set.add(number);\n        return number;\n    };\n};\n//# sourceMappingURL=add-unique-number.js.map", "export const createCache = (lastNumberWeakMap) => {\n    return (collection, nextNumber) => {\n        lastNumberWeakMap.set(collection, nextNumber);\n        return nextNumber;\n    };\n};\n//# sourceMappingURL=cache.js.map", "/*\n * The value of the constant Number.MAX_SAFE_INTEGER equals (2 ** 53 - 1) but it\n * is fairly new.\n */\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER === undefined ? 9007199254740991 : Number.MAX_SAFE_INTEGER;\nconst TWO_TO_THE_POWER_OF_TWENTY_NINE = 536870912;\nconst TWO_TO_THE_POWER_OF_THIRTY = TWO_TO_THE_POWER_OF_TWENTY_NINE * 2;\nexport const createGenerateUniqueNumber = (cache, lastNumberWeakMap) => {\n    return (collection) => {\n        const lastNumber = lastNumberWeakMap.get(collection);\n        /*\n         * Let's try the cheapest algorithm first. It might fail to produce a new\n         * number, but it is so cheap that it is okay to take the risk. Just\n         * increase the last number by one or reset it to 0 if we reached the upper\n         * bound of SMIs (which stands for small integers). When the last number is\n         * unknown it is assumed that the collection contains zero based consecutive\n         * numbers.\n         */\n        let nextNumber = lastNumber === undefined ? collection.size : lastNumber < TWO_TO_THE_POWER_OF_THIRTY ? lastNumber + 1 : 0;\n        if (!collection.has(nextNumber)) {\n            return cache(collection, nextNumber);\n        }\n        /*\n         * If there are less than half of 2 ** 30 numbers stored in the collection,\n         * the chance to generate a new random number in the range from 0 to 2 ** 30\n         * is at least 50%. It's benifitial to use only SMIs because they perform\n         * much better in any environment based on V8.\n         */\n        if (collection.size < TWO_TO_THE_POWER_OF_TWENTY_NINE) {\n            while (collection.has(nextNumber)) {\n                nextNumber = Math.floor(Math.random() * TWO_TO_THE_POWER_OF_THIRTY);\n            }\n            return cache(collection, nextNumber);\n        }\n        // Quickly check if there is a theoretical chance to generate a new number.\n        if (collection.size > MAX_SAFE_INTEGER) {\n            throw new Error('Congratulations, you created a collection of unique numbers which uses all available integers!');\n        }\n        // Otherwise use the full scale of safely usable integers.\n        while (collection.has(nextNumber)) {\n            nextNumber = Math.floor(Math.random() * MAX_SAFE_INTEGER);\n        }\n        return cache(collection, nextNumber);\n    };\n};\n//# sourceMappingURL=generate-unique-number.js.map", "import { createAddUniqueNumber } from './factories/add-unique-number';\nimport { createCache } from './factories/cache';\nimport { createGenerateUniqueNumber } from './factories/generate-unique-number';\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\nexport * from './types/index';\nconst LAST_NUMBER_WEAK_MAP = new WeakMap();\nconst cache = createCache(LAST_NUMBER_WEAK_MAP);\nconst generateUniqueNumber = createGenerateUniqueNumber(cache, LAST_NUMBER_WEAK_MAP);\nconst addUniqueNumber = createAddUniqueNumber(generateUniqueNumber);\nexport { addUniqueNumber, generateUniqueNumber };\n//# sourceMappingURL=module.js.map", "export const isMessagePort = (sender) => {\n    return typeof sender.start === 'function';\n};\n//# sourceMappingURL=message-port.js.map", "export const PORT_MAP = new WeakMap();\n//# sourceMappingURL=port-map.js.map", "import { PORT_MAP } from './port-map';\nexport const extendBrokerImplementation = (partialBrokerImplementation) => ({\n    ...partialBrokerImplementation,\n    connect: ({ call }) => {\n        return async () => {\n            const { port1, port2 } = new MessageChannel();\n            const portId = await call('connect', { port: port1 }, [port1]);\n            PORT_MAP.set(port2, portId);\n            return port2;\n        };\n    },\n    disconnect: ({ call }) => {\n        return async (port) => {\n            const portId = PORT_MAP.get(port);\n            if (portId === undefined) {\n                throw new Error('The given port is not connected.');\n            }\n            await call('disconnect', { portId });\n        };\n    },\n    isSupported: ({ call }) => {\n        return () => call('isSupported');\n    }\n});\n//# sourceMappingURL=extend-broker-implementation.js.map", "import { generateUniqueNumber } from 'fast-unique-numbers';\nimport { isMessagePort } from './guards/message-port';\nimport { extendBrokerImplementation } from './helpers/extend-broker-implementation';\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\nexport * from './interfaces/index';\nexport * from './types/index';\nconst ONGOING_REQUESTS = new WeakMap();\nconst createOrGetOngoingRequests = (sender) => {\n    if (ONGOING_REQUESTS.has(sender)) {\n        // @todo TypeScript needs to be convinced that has() works as expected.\n        return ONGOING_REQUESTS.get(sender);\n    }\n    const ongoingRequests = new Map();\n    ONGOING_REQUESTS.set(sender, ongoingRequests);\n    return ongoingRequests;\n};\nexport const createBroker = (brokerImplementation) => {\n    const fullBrokerImplementation = extendBrokerImplementation(brokerImplementation);\n    return (sender) => {\n        const ongoingRequests = createOrGetOngoingRequests(sender);\n        sender.addEventListener('message', (({ data: message }) => {\n            const { id } = message;\n            if (id !== null && ongoingRequests.has(id)) {\n                const { reject, resolve } = ongoingRequests.get(id);\n                ongoingRequests.delete(id);\n                if (message.error === undefined) {\n                    resolve(message.result);\n                }\n                else {\n                    reject(new Error(message.error.message));\n                }\n            }\n        }));\n        if (isMessagePort(sender)) {\n            sender.start();\n        }\n        const call = (method, params = null, transferables = []) => {\n            return new Promise((resolve, reject) => {\n                const id = generateUniqueNumber(ongoingRequests);\n                ongoingRequests.set(id, { reject, resolve });\n                if (params === null) {\n                    sender.postMessage({ id, method }, transferables);\n                }\n                else {\n                    sender.postMessage({ id, method, params }, transferables);\n                }\n            });\n        };\n        const notify = (method, params, transferables = []) => {\n            sender.postMessage({ id: null, method, params }, transferables);\n        };\n        let functions = {};\n        for (const [key, handler] of Object.entries(fullBrokerImplementation)) {\n            functions = { ...functions, [key]: handler({ call, notify }) };\n        }\n        return { ...functions };\n    };\n};\n//# sourceMappingURL=module.js.map"], "names": ["createAddUniqueNumber", "generateUniqueNumber", "set", "number", "createCache", "lastNumberWeakMap", "collection", "nextNumber", "MAX_SAFE_INTEGER", "TWO_TO_THE_POWER_OF_TWENTY_NINE", "TWO_TO_THE_POWER_OF_THIRTY", "createGenerateUniqueNumber", "cache", "lastNumber", "LAST_NUMBER_WEAK_MAP", "addUniqueNumber", "isMessagePort", "sender", "PORT_MAP", "extendBrokerImplementation", "partialBrokerImplementation", "call", "port1", "port2", "portId", "port", "ONGOING_REQUESTS", "createOrGetOngoingRequests", "ongoingRequests", "createBroker", "brokerImplementation", "fullBrokerImplementation", "message", "id", "reject", "resolve", "method", "params", "transferables", "notify", "functions", "key", "handler"], "mappings": "AAAO,MAAMA,EAAyBC,GAC1BC,GAAQ,CACZ,MAAMC,EAASF,EAAqBC,CAAG,EACvC,OAAAA,EAAI,IAAIC,CAAM,EACPA,CACf,ECLaC,EAAeC,GACjB,CAACC,EAAYC,KAChBF,EAAkB,IAAIC,EAAYC,CAAU,EACrCA,GCCTC,EAAmB,OAAO,mBAAqB,OAAY,iBAAmB,OAAO,iBACrFC,EAAkC,UAClCC,EAA6BD,EAAkC,EACxDE,EAA6B,CAACC,EAAOP,IACtCC,GAAe,CACnB,MAAMO,EAAaR,EAAkB,IAAIC,CAAU,EASnD,IAAIC,EAAaM,IAAe,OAAYP,EAAW,KAAOO,EAAaH,EAA6BG,EAAa,EAAI,EACzH,GAAI,CAACP,EAAW,IAAIC,CAAU,EAC1B,OAAOK,EAAMN,EAAYC,CAAU,EAQvC,GAAID,EAAW,KAAOG,EAAiC,CACnD,KAAOH,EAAW,IAAIC,CAAU,GAC5BA,EAAa,KAAK,MAAM,KAAK,OAAM,EAAKG,CAA0B,EAEtE,OAAOE,EAAMN,EAAYC,CAAU,CACtC,CAED,GAAID,EAAW,KAAOE,EAClB,MAAM,IAAI,MAAM,gGAAgG,EAGpH,KAAOF,EAAW,IAAIC,CAAU,GAC5BA,EAAa,KAAK,MAAM,KAAK,OAAM,EAAKC,CAAgB,EAE5D,OAAOI,EAAMN,EAAYC,CAAU,CAC3C,ECnCMO,EAAuB,IAAI,QAC3BF,EAAQR,EAAYU,CAAoB,EACxCb,EAAuBU,EAA2BC,EAAOE,CAAoB,EAC7EC,EAAkBf,EAAsBC,CAAoB,ECXrDe,EAAiBC,GACnB,OAAOA,EAAO,OAAU,WCDtBC,EAAW,IAAI,QCCfC,EAA8BC,IAAiC,CACxE,GAAGA,EACH,QAAS,CAAC,CAAE,KAAAC,KACD,SAAY,CACf,KAAM,CAAE,MAAAC,EAAO,MAAAC,GAAU,IAAI,eACvBC,EAAS,MAAMH,EAAK,UAAW,CAAE,KAAMC,CAAO,EAAE,CAACA,CAAK,CAAC,EAC7D,OAAAJ,EAAS,IAAIK,EAAOC,CAAM,EACnBD,CACnB,EAEI,WAAY,CAAC,CAAE,KAAAF,KACJ,MAAOI,GAAS,CACnB,MAAMD,EAASN,EAAS,IAAIO,CAAI,EAChC,GAAID,IAAW,OACX,MAAM,IAAI,MAAM,kCAAkC,EAEtD,MAAMH,EAAK,aAAc,CAAE,OAAAG,CAAQ,CAAA,CAC/C,EAEI,YAAa,CAAC,CAAE,KAAAH,KACL,IAAMA,EAAK,aAAa,CAEvC,GCdMK,EAAmB,IAAI,QACvBC,EAA8BV,GAAW,CAC3C,GAAIS,EAAiB,IAAIT,CAAM,EAE3B,OAAOS,EAAiB,IAAIT,CAAM,EAEtC,MAAMW,EAAkB,IAAI,IAC5B,OAAAF,EAAiB,IAAIT,EAAQW,CAAe,EACrCA,CACX,EACaC,EAAgBC,GAAyB,CAClD,MAAMC,EAA2BZ,EAA2BW,CAAoB,EAChF,OAAQb,GAAW,CACf,MAAMW,EAAkBD,EAA2BV,CAAM,EACzDA,EAAO,iBAAiB,UAAY,CAAC,CAAE,KAAMe,CAAO,IAAO,CACvD,KAAM,CAAE,GAAAC,CAAI,EAAGD,EACf,GAAIC,IAAO,MAAQL,EAAgB,IAAIK,CAAE,EAAG,CACxC,KAAM,CAAE,OAAAC,EAAQ,QAAAC,CAAO,EAAKP,EAAgB,IAAIK,CAAE,EAClDL,EAAgB,OAAOK,CAAE,EACrBD,EAAQ,QAAU,OAClBG,EAAQH,EAAQ,MAAM,EAGtBE,EAAO,IAAI,MAAMF,EAAQ,MAAM,OAAO,CAAC,CAE9C,CACb,GACYhB,EAAcC,CAAM,GACpBA,EAAO,MAAK,EAEhB,MAAMI,EAAO,CAACe,EAAQC,EAAS,KAAMC,EAAgB,KAC1C,IAAI,QAAQ,CAACH,EAASD,IAAW,CACpC,MAAMD,EAAKhC,EAAqB2B,CAAe,EAC/CA,EAAgB,IAAIK,EAAI,CAAE,OAAAC,EAAQ,QAAAC,CAAS,CAAA,EACvCE,IAAW,KACXpB,EAAO,YAAY,CAAE,GAAAgB,EAAI,OAAAG,CAAQ,EAAEE,CAAa,EAGhDrB,EAAO,YAAY,CAAE,GAAAgB,EAAI,OAAAG,EAAQ,OAAAC,CAAM,EAAIC,CAAa,CAE5E,CAAa,EAECC,EAAS,CAACH,EAAQC,EAAQC,EAAgB,CAAA,IAAO,CACnDrB,EAAO,YAAY,CAAE,GAAI,KAAM,OAAAmB,EAAQ,OAAAC,CAAM,EAAIC,CAAa,CAC1E,EACQ,IAAIE,EAAY,CAAA,EAChB,SAAW,CAACC,EAAKC,CAAO,IAAK,OAAO,QAAQX,CAAwB,EAChES,EAAY,CAAE,GAAGA,EAAW,CAACC,CAAG,EAAGC,EAAQ,CAAE,KAAArB,EAAM,OAAAkB,CAAQ,CAAA,GAE/D,MAAO,CAAE,GAAGC,EACpB,CACA", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}