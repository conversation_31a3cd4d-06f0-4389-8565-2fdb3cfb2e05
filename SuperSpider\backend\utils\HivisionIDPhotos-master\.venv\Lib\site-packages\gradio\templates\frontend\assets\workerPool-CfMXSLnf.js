class h{constructor(e){this._pendingActions=new Array,this._workerInfos=e.map(s=>({workerPromise:Promise.resolve(s),idle:!0}))}dispose(){for(const e of this._workerInfos)e.workerPromise.then(s=>{s.terminate()});this._workerInfos.length=0,this._pendingActions.length=0}push(e){this._executeOnIdleWorker(e)||this._pendingActions.push(e)}_executeOnIdleWorker(e){for(const s of this._workerInfos)if(s.idle)return this._execute(s,e),!0;return!1}_execute(e,s){e.idle=!1,e.workerPromise.then(t=>{s(t,()=>{const i=this._pendingActions.shift();i?this._execute(e,i):e.idle=!0})})}}class r extends h{constructor(e,s,t=r.DefaultOptions){super([]),this._maxWorkers=e,this._createWorkerAsync=s,this._options=t}push(e){if(!this._executeOnIdleWorker(e))if(this._workerInfos.length<this._maxWorkers){const s={workerPromise:this._createWorkerAsync(),idle:!1};this._workerInfos.push(s),this._execute(s,e)}else this._pendingActions.push(e)}_execute(e,s){e.timeoutId&&(clearTimeout(e.timeoutId),delete e.timeoutId),super._execute(e,(t,i)=>{s(t,()=>{i(),e.idle&&(e.timeoutId=setTimeout(()=>{e.workerPromise.then(n=>{n.terminate()});const o=this._workerInfos.indexOf(e);o!==-1&&this._workerInfos.splice(o,1)},this._options.idleTimeElapsedBeforeRelease))})})}}r.DefaultOptions={idleTimeElapsedBeforeRelease:1e3};export{r as A};
//# sourceMappingURL=workerPool-CfMXSLnf.js.map
