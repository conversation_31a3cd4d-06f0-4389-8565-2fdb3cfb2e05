#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN爬虫模块
用于解析CSDN文章内容
"""

import re
import time
import random
import requests
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup

from .base_spider import BaseSpider
from ..utils.vip_account_pool import vip_account_pool

# 创建日志记录器
logger = logging.getLogger(__name__)

class CSDNSpider(BaseSpider):
    """CSDN文章爬虫类"""

    def __init__(self):
        """初始化CSDN爬虫"""
        super().__init__("CSDN爬虫")

        # 设置请求头，模拟真实浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-GPC': '1',
        }

        # 设置会话
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 添加反反爬虫措施
        self._setup_anti_detection()

    def _setup_anti_detection(self):
        """设置反反爬虫措施"""
        # 添加随机cookies模拟真实用户
        timestamp = str(int(time.time() * 1000))
        random_id = str(random.randint(100000000, 999999999))

        self.session.cookies.update({
            'uuid_tt_dd': f'10_{timestamp}_{random.randint(10000, 99999)}',
            'dc_session_id': random_id,
            'csrfToken': self._generate_csrf_token(),
            'Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
            'Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
        })

        # 设置请求间隔，避免请求过于频繁
        self.request_delay = random.uniform(1, 3)

    def _generate_csrf_token(self) -> str:
        """生成CSRF token"""
        chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        return ''.join(random.choice(chars) for _ in range(32))

    def _make_request_with_retry(self, url: str, max_retries: int = 5) -> requests.Response:
        """带重试机制的请求方法"""
        for attempt in range(max_retries):
            try:
                # 添加随机延迟，重试时延迟更长
                if attempt > 0:
                    delay = random.uniform(3, 8) * (attempt + 1)  # 指数退避
                    logger.info(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)

                # 更新User-Agent（随机化）
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                ]
                self.session.headers['User-Agent'] = random.choice(user_agents)

                # 添加更多随机化的请求头
                self.session.headers.update({
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': random.choice([
                        'zh-CN,zh;q=0.9,en;q=0.8',
                        'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                        'zh-CN,zh;q=0.9'
                    ]),
                    'Referer': 'https://www.google.com/' if attempt == 0 else 'https://blog.csdn.net/',
                    'Cache-Control': random.choice(['no-cache', 'max-age=0']),
                })

                # 更新cookies
                if attempt > 0:
                    self._update_cookies()

                # 发送请求
                response = self.session.get(url, timeout=30, allow_redirects=True)

                # 检查是否被反爬虫拦截
                if self._is_blocked_response(response):
                    logger.warning(f"请求被拦截，尝试重试 (第{attempt + 1}次)")
                    # 清除session，重新初始化
                    if attempt > 1:
                        self._reset_session()
                    continue

                response.raise_for_status()
                logger.info(f"请求成功，状态码: {response.status_code}")
                return response

            except requests.RequestException as e:
                logger.warning(f"请求失败 (第{attempt + 1}次): {e}")
                if attempt == max_retries - 1:
                    raise

        raise requests.RequestException("达到最大重试次数")

    def _is_blocked_response(self, response: requests.Response) -> bool:
        """检查响应是否被反爬虫拦截"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True

        # 检查响应内容中的关键词
        content = response.text.lower()
        blocked_keywords = [
            '访问过于频繁',
            '请稍后再试',
            '验证码',
            'captcha',
            '安全验证',
            'security check',
            '访问被拒绝',
            'access denied'
        ]

        return any(keyword in content for keyword in blocked_keywords)

    def _update_cookies(self):
        """更新cookies"""
        timestamp = str(int(time.time() * 1000))
        random_id = str(random.randint(100000000, 999999999))

        self.session.cookies.update({
            'uuid_tt_dd': f'10_{timestamp}_{random.randint(10000, 99999)}',
            'dc_session_id': random_id,
            'csrfToken': self._generate_csrf_token(),
            'Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
            'Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
        })

    def _reset_session(self):
        """重置session"""
        self.session.close()
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self._setup_anti_detection()

    def _fallback_request(self, url: str) -> requests.Response:
        """备用请求方法"""
        # 尝试使用最基本的请求
        import urllib.request

        try:
            # 构造最简单的请求
            req = urllib.request.Request(
                url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0)'
                }
            )

            with urllib.request.urlopen(req, timeout=10) as response:
                content = response.read().decode('utf-8')

                # 创建一个模拟的requests.Response对象
                class MockResponse:
                    def __init__(self, text, status_code=200):
                        self.text = text
                        self.status_code = status_code

                    def raise_for_status(self):
                        if self.status_code >= 400:
                            raise requests.HTTPError(f"HTTP {self.status_code}")

                return MockResponse(content)

        except Exception as e:
            logger.error(f"备用请求方法失败: {e}")
            raise

    def _request_with_vip_account(self, url: str) -> requests.Response:
        """使用VIP账号请求内容"""
        with vip_account_pool.use_account('csdn') as account:
            if not account:
                raise Exception("没有可用的CSDN VIP账号")

            try:
                # 创建新的session
                session = requests.Session()

                # 设置VIP账号的请求头
                headers = account.get_headers()
                if headers:
                    session.headers.update(headers)
                else:
                    # 使用默认请求头
                    session.headers.update(self.headers)

                # 设置VIP账号的cookies
                cookies = account.get_cookies()
                if cookies:
                    session.cookies.update(cookies)

                # 设置代理（如果有）
                proxy_config = account.get_proxy_config()
                proxies = None
                if proxy_config:
                    proxies = proxy_config

                # 发送请求
                response = session.get(url, timeout=30, proxies=proxies)
                response.raise_for_status()

                # 标记使用了VIP账号
                response._used_vip = True

                logger.info(f"使用VIP账号 {account.username} 成功获取内容")
                return response

            except Exception as e:
                # 标记账号错误
                account.mark_error(str(e))
                raise Exception(f"VIP账号请求失败: {e}")

    def _check_if_vip_content(self, soup: BeautifulSoup) -> bool:
        """检查是否为VIP内容"""
        # 检查常见的VIP内容标识
        vip_indicators = [
            '.vip-article',
            '.member-article',
            '.pay-article',
            '[class*="vip"]',
            '[class*="member"]',
            '[class*="pay"]'
        ]

        for indicator in vip_indicators:
            if soup.select(indicator):
                return True

        # 检查文本内容中的VIP标识
        text_content = soup.get_text().lower()
        vip_keywords = ['vip', '会员', '付费', '专栏', '订阅']

        for keyword in vip_keywords:
            if keyword in text_content and ('需要' in text_content or '开通' in text_content):
                return True

        return False

    def is_valid_url(self, url: str) -> bool:
        """
        验证是否为有效的CSDN文章URL

        Args:
            url: 待验证的URL

        Returns:
            bool: 是否为有效的CSDN URL
        """
        try:
            parsed = urlparse(url)

            # 检查域名
            valid_domains = ['blog.csdn.net', 'www.csdn.net', 'csdn.net']
            if not any(domain in parsed.netloc for domain in valid_domains):
                return False

            # 检查路径格式
            # CSDN文章URL通常格式：https://blog.csdn.net/username/article/details/123456
            if '/article/details/' in parsed.path:
                return True

            return False

        except Exception as e:
            logger.error(f"URL验证失败: {e}")
            return False

    def extract_article_id(self, url: str) -> Optional[str]:
        """
        从URL中提取文章ID

        Args:
            url: CSDN文章URL

        Returns:
            str: 文章ID，如果提取失败返回None
        """
        try:
            # 使用正则表达式提取文章ID
            pattern = r'/article/details/(\d+)'
            match = re.search(pattern, url)

            if match:
                return match.group(1)

            return None

        except Exception as e:
            logger.error(f"提取文章ID失败: {e}")
            return None

    def parse_article(self, url: str, use_vip: bool = None) -> Dict[str, Any]:
        """
        解析CSDN文章

        Args:
            url: CSDN文章URL
            use_vip: 是否使用VIP账号 (None表示自动判断)

        Returns:
            Dict: 包含文章信息的字典
        """
        try:
            # 验证URL
            if not self.is_valid_url(url):
                raise ValueError("无效的CSDN文章URL")

            logger.info(f"开始解析CSDN文章: {url}")

            # 智能请求策略：先尝试普通请求，如果检测到VIP内容再使用VIP账号
            response = None
            used_vip_account = False

            # 方法1: 先使用标准请求快速检测内容类型
            try:
                logger.info("尝试标准请求...")
                response = self._make_request_with_retry(url)

                # 快速检测是否为VIP内容
                if response and response.text:
                    soup_preview = BeautifulSoup(response.text, 'html.parser')
                    vip_info_preview = self._check_if_vip_content(soup_preview)

                    # 如果检测到VIP内容且内容很短，尝试使用VIP账号
                    content_preview = soup_preview.get_text()
                    if (vip_info_preview['is_vip_content'] and
                        (len(content_preview) < 1000 or
                         any(keyword in content_preview for keyword in ['需要开通', '立即订阅', '超级会员']))):

                        logger.info(f"检测到VIP内容 ({vip_info_preview['vip_type']})，尝试使用VIP账号...")
                        try:
                            vip_response = self._request_with_vip_account(url)
                            if vip_response and len(vip_response.text) > len(response.text):
                                logger.info("VIP账号获取到更多内容，使用VIP响应")
                                response = vip_response
                                used_vip_account = True
                            else:
                                logger.info("VIP账号未获取到更多内容，使用标准响应")
                        except Exception as e:
                            logger.warning(f"VIP账号请求失败，使用标准响应: {e}")
                    else:
                        logger.info("检测到免费内容或内容完整，使用标准响应")

            except requests.RequestException as e:
                logger.warning(f"标准请求失败: {e}")

                # 方法2: 如果标准请求失败，直接尝试VIP账号
                try:
                    logger.info("标准请求失败，尝试VIP账号...")
                    response = self._request_with_vip_account(url)
                    used_vip_account = True
                    logger.info("VIP账号请求成功")
                except Exception as e2:
                    logger.warning(f"VIP账号请求也失败: {e2}")

            # 方法3: 如果都失败，尝试简单请求
            if not response:
                try:
                    logger.info("尝试简单请求方法...")
                    simple_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }
                    response = requests.get(url, headers=simple_headers, timeout=15)
                    response.raise_for_status()
                except requests.RequestException as e3:
                    logger.error(f"简单请求也失败: {e3}")

                    # 方法4: 最后尝试备用方法
                    try:
                        logger.info("尝试使用备用解析方法...")
                        response = self._fallback_request(url)
                    except Exception as e4:
                        logger.error(f"备用方法也失败: {e4}")
                        raise requests.RequestException(
                            "所有请求方法都失败，请稍后重试或检查网络连接"
                        )

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 检查是否为VIP内容
            vip_info = self._check_if_vip_content(soup)

            # 根据VIP内容类型处理
            vip_handling = self._handle_vip_content_by_type(vip_info, url, used_vip_account)

            # 提取文章信息
            article_info = self._extract_article_info(soup, url)

            # 添加VIP相关信息
            article_info['is_vip_content'] = vip_info['is_vip_content']
            article_info['vip_type'] = vip_info['vip_type']
            article_info['unlock_methods'] = vip_info['unlock_methods']
            article_info['price_info'] = vip_info['price_info']
            article_info['can_unlock'] = vip_info['can_unlock']
            article_info['used_vip_account'] = used_vip_account
            article_info['vip_handling'] = vip_handling

            # 检查内容是否完整
            content_text_length = len(article_info.get('content_text', ''))

            if vip_info['is_vip_content']:
                if content_text_length < 500:
                    if used_vip_account:
                        logger.warning(f"使用VIP账号但内容仍然较短 ({content_text_length}字符)，可能解锁失败")
                        article_info['content_warning'] = 'VIP解锁可能失败，内容不完整'
                    else:
                        logger.warning(f"VIP内容未使用VIP账号，内容可能不完整 ({content_text_length}字符)")
                        article_info['content_warning'] = '检测到VIP内容，但未使用VIP账号解锁'
                else:
                    if used_vip_account:
                        logger.info(f"VIP账号成功解锁内容 ({content_text_length}字符)")
                        article_info['content_status'] = 'VIP内容已成功解锁'
                    else:
                        logger.info(f"免费获取VIP内容 ({content_text_length}字符)")
                        article_info['content_status'] = '成功获取内容'

            logger.info(f"成功解析CSDN文章: {article_info.get('title', '未知标题')} "
                       f"(VIP类型: {vip_info['vip_type']}, 使用VIP: {used_vip_account}, "
                       f"内容长度: {content_text_length}字符)")

            return {
                'success': True,
                'data': article_info
            }

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"解析文章失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def _extract_article_info(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        从BeautifulSoup对象中提取文章信息

        Args:
            soup: BeautifulSoup解析对象
            url: 原始URL

        Returns:
            Dict: 文章信息字典
        """
        article_info = {
            'url': url,
            'title': '',
            'author': '',
            'content': '',
            'publish_time': '',
            'read_count': 0,
            'tags': []
        }

        try:
            # 调试：打印页面中的所有h1标签
            h1_tags = soup.find_all('h1')
            logger.info(f"页面中找到的所有h1标签: {len(h1_tags)}")
            for i, h1 in enumerate(h1_tags):
                logger.info(f"H1[{i}]: class={h1.get('class')}, id={h1.get('id')}, text={h1.get_text().strip()[:50]}")

            # 调试：打印页面中的所有包含read的元素
            read_elements = soup.find_all(text=re.compile(r'阅读|read', re.I))
            logger.info(f"页面中找到包含'阅读'的文本: {len(read_elements)}")
            for i, elem in enumerate(read_elements[:5]):  # 只显示前5个
                parent = elem.parent if elem.parent else None
                if parent:
                    logger.info(f"Read[{i}]: tag={parent.name}, class={parent.get('class')}, text={str(elem).strip()}")

            # 提取标题 - 支持多种选择器
            title_selectors = [
                ('h1', {'class_': 'title-article', 'id': 'articleContentId'}),  # 最新CSDN结构
                ('h1', {'class_': 'title-article'}),
                ('h1', {'id': 'articleTitle'}),
                ('h1', {'id': 'articleContentId'}),  # 新版CSDN
                ('h1', {'class_': 'article-title'}),
                ('.article-header h1', None),
                ('h1', None)  # 通用h1标签
            ]

            for selector, attrs in title_selectors:
                title_elem = None
                if attrs:
                    # 构建查找参数
                    find_attrs = {}
                    if 'class_' in attrs:
                        find_attrs['class_'] = attrs['class_']
                    if 'id' in attrs:
                        find_attrs['id'] = attrs['id']

                    title_elem = soup.find(selector, **find_attrs)
                else:
                    # CSS选择器
                    if selector.startswith('.'):
                        title_elem = soup.select_one(selector)
                    else:
                        title_elem = soup.find(selector)

                if title_elem:
                    article_info['title'] = title_elem.get_text().strip()
                    logger.info(f"找到标题: {article_info['title']}")
                    break

            # 提取作者 - 支持多种选择器
            author_selectors = [
                ('a', {'class_': 'follow-nickName'}),
                ('a', {'class_': 'follow-nickName'}),
                ('.article-info-box .user-info a', None),
                ('.article-bar-top a', None),
                ('.user-profile-head .username', None),
                ('a[href*="/fengbin2005"]', None)  # 根据URL模式匹配
            ]

            for selector, attrs in author_selectors:
                if attrs:
                    author_elem = soup.find(selector, attrs)
                else:
                    if selector.startswith('.') or '[' in selector:
                        author_elem = soup.select_one(selector)
                    else:
                        author_elem = soup.find(selector)

                if author_elem:
                    article_info['author'] = author_elem.get_text().strip()
                    logger.info(f"找到作者: {article_info['author']}")
                    break

            # 提取发布时间
            time_selectors = [
                ('span', {'class_': 'time'}),
                ('.article-info-box .time', None),
                ('.article-bar-top .time', None),
                ('.publish-time', None)
            ]

            for selector, attrs in time_selectors:
                if attrs:
                    time_elem = soup.find(selector, attrs)
                else:
                    time_elem = soup.select_one(selector)

                if time_elem:
                    article_info['publish_time'] = time_elem.get_text().strip()
                    logger.info(f"找到发布时间: {article_info['publish_time']}")
                    break

            # 提取阅读数 - 支持多种选择器
            read_selectors = [
                ('span', {'class_': 'read-count'}),  # 主要选择器
                ('.read-count', None),
                ('.article-info-box .read-count', None),
                ('.article-bar-top .read-count', None),
                ('span[class*="read"]', None),
                ('.read-count-box span', None)
            ]

            for selector, attrs in read_selectors:
                read_elem = None
                if attrs:
                    # 构建查找参数
                    find_attrs = {}
                    if 'class_' in attrs:
                        find_attrs['class_'] = attrs['class_']
                    if 'id' in attrs:
                        find_attrs['id'] = attrs['id']

                    read_elem = soup.find(selector, **find_attrs)
                else:
                    if selector.startswith('.') or '[' in selector:
                        read_elem = soup.select_one(selector)
                    else:
                        read_elem = soup.find(selector)

                if read_elem:
                    read_text = read_elem.get_text().strip()
                    logger.info(f"找到阅读数文本: {read_text}")

                    # 提取数字，支持K、万等单位
                    if '万' in read_text:
                        # 处理万单位
                        read_match = re.search(r'(\d+(?:\.\d+)?)万', read_text)
                        if read_match:
                            article_info['read_count'] = int(float(read_match.group(1)) * 10000)
                    elif 'K' in read_text or 'k' in read_text:
                        # 处理K单位
                        read_match = re.search(r'(\d+(?:\.\d+)?)[Kk]', read_text)
                        if read_match:
                            article_info['read_count'] = int(float(read_match.group(1)) * 1000)
                    else:
                        # 提取纯数字
                        read_match = re.search(r'(\d+)', read_text)
                        if read_match:
                            article_info['read_count'] = int(read_match.group(1))

                    if article_info['read_count'] > 0:
                        logger.info(f"找到阅读数: {article_info['read_count']}")
                        break

            # 提取文章内容 - 支持多种页面结构
            content_elem = self._find_content_element(soup)
            if content_elem:
                # 清理内容：移除脚本、样式、广告等
                self._clean_content_element(content_elem)

                # 获取HTML内容（保留格式）
                article_info['content'] = str(content_elem)

                # 同时保存纯文本内容
                article_info['content_text'] = content_elem.get_text().strip()

            # 提取标签
            tag_elems = soup.find_all('a', class_='tag-link')
            if tag_elems:
                article_info['tags'] = [tag.get_text().strip() for tag in tag_elems]

            # 提取文章ID
            article_id = self.extract_article_id(url)
            if article_id:
                article_info['article_id'] = article_id

        except Exception as e:
            logger.error(f"提取文章信息时出错: {e}")

        return article_info

    def _find_content_element(self, soup: BeautifulSoup):
        """查找文章内容元素，支持多种页面结构"""
        # 按优先级尝试不同的选择器
        selectors = [
            {'id': 'content_views'},  # 新版CSDN
            {'class_': 'article-content'},  # 备用选择器
            {'class_': 'blog-content-box'},  # 另一种结构
            {'class_': 'baidu_pl'},  # 旧版CSDN
            {'tag': 'article'},  # 通用文章标签
            {'class_': 'markdown_views'},  # Markdown格式
        ]

        for selector in selectors:
            if 'id' in selector:
                elem = soup.find('div', id=selector['id'])
            elif 'class_' in selector:
                elem = soup.find('div', class_=selector['class_'])
            elif 'tag' in selector:
                elem = soup.find(selector['tag'])
            else:
                continue

            if elem:
                logger.info(f"找到内容元素: {selector}")
                return elem

        logger.warning("未找到文章内容元素")
        return None

    def _clean_content_element(self, content_elem):
        """清理内容元素，移除不需要的标签和内容"""
        # 移除的标签类型
        remove_tags = ['script', 'style', 'noscript', 'iframe']

        # 移除的CSS类名（广告、推荐等）
        remove_classes = [
            'ad', 'advertisement', 'recommend', 'related',
            'comment', 'share', 'toolbar', 'sidebar',
            'footer', 'header', 'nav', 'menu'
        ]

        # 移除标签
        for tag in remove_tags:
            for elem in content_elem.find_all(tag):
                elem.decompose()

        # 移除包含特定类名的元素
        for class_name in remove_classes:
            for elem in content_elem.find_all(class_=lambda x: x and class_name in ' '.join(x).lower()):
                elem.decompose()

        # 移除空的段落和div
        for elem in content_elem.find_all(['p', 'div']):
            if not elem.get_text().strip():
                elem.decompose()

        # 清理属性，只保留必要的
        for elem in content_elem.find_all():
            # 保留的属性
            keep_attrs = ['href', 'src', 'alt', 'title']
            attrs_to_remove = []

            for attr in elem.attrs:
                if attr not in keep_attrs:
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                del elem.attrs[attr]

    def get_article_summary(self, article_info: Dict[str, Any], max_length: int = 200) -> str:
        """
        获取文章摘要

        Args:
            article_info: 文章信息字典
            max_length: 摘要最大长度

        Returns:
            str: 文章摘要
        """
        content = article_info.get('content', '')
        if len(content) <= max_length:
            return content

        # 截取前max_length个字符，并在合适的位置截断
        summary = content[:max_length]

        # 尝试在句号、感叹号或问号处截断
        for punct in ['。', '！', '？', '.', '!', '?']:
            last_punct = summary.rfind(punct)
            if last_punct > max_length * 0.7:  # 如果标点位置不太靠前
                return summary[:last_punct + 1]

        # 如果没有找到合适的标点，就在最后一个空格处截断
        last_space = summary.rfind(' ')
        if last_space > max_length * 0.8:
            return summary[:last_space] + '...'

        return summary + '...'

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行CSDN爬虫任务

        Args:
            params: 任务参数，包含article_url

        Returns:
            Dict: 执行结果
        """
        try:
            article_url = params.get('article_url')
            if not article_url:
                return {
                    'success': False,
                    'message': '缺少文章URL参数',
                    'data': None
                }

            # 解析文章
            article_data = self.parse_article(article_url)

            if article_data:
                return {
                    'success': True,
                    'message': '文章解析成功',
                    'data': article_data
                }
            else:
                return {
                    'success': False,
                    'message': '文章解析失败',
                    'data': None
                }

        except Exception as e:
            logger.error(f"执行CSDN爬虫任务失败: {e}")
            return {
                'success': False,
                'message': f'执行失败: {str(e)}',
                'data': None
            }
