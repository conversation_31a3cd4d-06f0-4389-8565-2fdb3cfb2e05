2025-06-06 15:27:38,232 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 15:27:38,233 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 15:27:38,234 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 15:27:38,234 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 15:27:38,234 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 15:27:38,683 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 15:27:39,324 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 15:27:39,330 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 15:27:39,688 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-06 15:27:39,697 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 15:27:39,700 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 15:27:39,703 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 15:27:39,706 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 15:27:39,708 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 15:27:39,716 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 15:27:39,749 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 15:27:39,823 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 15:27:39,824 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 15:27:39,830 - werkzeug - INFO -  * Restarting with stat
2025-06-06 15:27:40,616 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 15:27:40,617 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 15:27:40,618 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 15:27:41,657 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 15:27:41,661 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 15:27:41,674 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 15:27:43,473 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET / HTTP/1.1" 200 -
2025-06-06 15:27:43,585 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,586 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,597 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,630 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,649 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,669 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,670 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,686 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,698 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,698 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-06 15:27:43,704 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,729 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 15:27:43,940 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 15:27:43,956 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 15:27:44,444 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:44,449 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:44,487 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:44] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 15:27:55,961 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:55] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-06 15:27:56,926 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:27:56,945 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:27:56] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 15:28:40,914 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 15:28:40,920 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:28:40,924 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:28:40,926 - backend.spiders.csdn_spider - INFO - 尝试标准请求...
2025-06-06 15:28:41,529 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-06-06 15:28:41,533 - backend.spiders.csdn_spider - INFO - 等待 15.3 秒后重试...
2025-06-06 15:28:57,368 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-06-06 15:28:57,369 - backend.spiders.csdn_spider - INFO - 等待 22.0 秒后重试...
2025-06-06 15:29:19,846 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-06-06 15:29:19,847 - backend.spiders.csdn_spider - INFO - 等待 27.3 秒后重试...
2025-06-06 15:29:52,139 - backend.spiders.csdn_spider - WARNING - 请求失败 (第4次): HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/********* (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-06-06 15:29:52,140 - backend.spiders.csdn_spider - INFO - 等待 19.9 秒后重试...
2025-06-06 15:30:12,441 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-06-06 15:30:12,442 - backend.spiders.csdn_spider - WARNING - 标准请求失败: 达到最大重试次数
2025-06-06 15:30:12,442 - backend.spiders.csdn_spider - INFO - 标准请求失败，尝试VIP账号...
2025-06-06 15:30:12,464 - backend.utils.vip_account_pool - INFO - 刷新csdn账号池完成，共0个账号
2025-06-06 15:30:12,464 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 15:30:12,464 - backend.spiders.csdn_spider - WARNING - VIP账号请求也失败: 没有可用的CSDN VIP账号
2025-06-06 15:30:12,465 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-06-06 15:30:13,004 - backend.spiders.csdn_spider - ERROR - 解析文章失败: 'CSDNSpider' object has no attribute '_handle_vip_content_by_type'
2025-06-06 15:30:13,005 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:30:13,005 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-06-06 15:30:13,006 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-06-06 15:30:13,007 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-06-06 15:30:13,007 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:30:13] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-06-06 15:30:13,007 - backend.spiders.csdn_spider - INFO - 尝试标准请求...
2025-06-06 15:30:13,357 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-06-06 15:30:13,358 - backend.spiders.csdn_spider - INFO - 等待 12.5 秒后重试...
2025-06-06 15:30:14,375 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:30:14] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-06-06 15:30:26,275 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-06-06 15:30:26,276 - backend.spiders.csdn_spider - INFO - 等待 21.3 秒后重试...
2025-06-06 15:30:47,839 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-06-06 15:30:47,848 - backend.spiders.csdn_spider - INFO - 等待 21.5 秒后重试...
2025-06-06 15:31:09,827 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第4次)
2025-06-06 15:31:09,828 - backend.spiders.csdn_spider - INFO - 等待 18.1 秒后重试...
2025-06-06 15:31:28,426 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第5次)
2025-06-06 15:31:28,426 - backend.spiders.csdn_spider - WARNING - 标准请求失败: 达到最大重试次数
2025-06-06 15:31:28,427 - backend.spiders.csdn_spider - INFO - 标准请求失败，尝试VIP账号...
2025-06-06 15:31:28,427 - backend.utils.vip_account_pool - WARNING - 没有可用的csdn账号
2025-06-06 15:31:28,427 - backend.spiders.csdn_spider - WARNING - VIP账号请求也失败: 没有可用的CSDN VIP账号
2025-06-06 15:31:28,428 - backend.spiders.csdn_spider - INFO - 尝试简单请求方法...
2025-06-06 15:31:28,894 - backend.spiders.csdn_spider - ERROR - 解析文章失败: 'CSDNSpider' object has no attribute '_handle_vip_content_by_type'
2025-06-06 15:31:28,899 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250606_153128.html
2025-06-06 15:31:28,900 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-06-06 15:31:28,900 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章.html (大小: 5148 字节)
2025-06-06 15:31:28,901 - backend.utils.mailer - INFO - 附件 CSDN文章.html 添加成功
2025-06-06 15:31:28,928 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-06-06 15:31:29,126 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-06-06 15:31:29,483 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-06-06 15:31:30,053 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-06-06 15:31:30,053 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-06-06 15:31:30,054 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250606_153128.html
2025-06-06 15:32:45,385 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:32:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:37:45,372 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:37:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:42:45,374 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 15:42:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 15:43:22,757 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\__init__.py', reloading
2025-06-06 15:43:23,153 - werkzeug - INFO -  * Restarting with stat
2025-06-06 20:59:11,348 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 20:59:11,352 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 20:59:11,352 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 20:59:11,353 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 20:59:11,353 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 20:59:11,870 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 20:59:12,633 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 20:59:12,637 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 20:59:12,872 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 20:59:12,879 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 20:59:12,884 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 20:59:12,887 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 20:59:12,891 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 20:59:12,895 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 20:59:12,902 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 20:59:12,940 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 20:59:13,010 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 20:59:13,011 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 20:59:13,020 - werkzeug - INFO -  * Restarting with stat
2025-06-06 20:59:13,849 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 20:59:13,851 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 20:59:13,852 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 20:59:13,852 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 20:59:13,853 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 20:59:15,500 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 20:59:15,543 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 20:59:15,562 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 20:59:15,584 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 20:59:17,808 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:17] "GET / HTTP/1.1" 200 -
2025-06-06 20:59:18,141 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,141 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,187 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,188 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,204 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,249 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,266 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,269 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-06 20:59:18,271 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,291 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,293 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,325 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-06 20:59:18,413 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 20:59:18,424 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-06 20:59:18,914 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 20:59:18,935 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-06 20:59:18,979 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:18] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-06 20:59:29,882 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 20:59:29] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-06-06 21:01:34,231 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:01:34,592 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:01:35,749 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:01:35,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:01:35,751 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:01:37,252 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:01:37,330 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:01:37,364 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:01:37,381 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:01:50,632 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:01:50,850 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:01:51,876 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:01:51,877 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:01:51,878 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:01:51,878 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:01:51,878 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:01:53,157 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:01:53,221 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:01:53,236 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:01:53,246 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:08,621 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:09,064 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:09,984 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:09,985 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:09,986 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:09,986 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:09,986 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:11,342 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:02:11,405 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:02:11,429 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:02:11,447 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:47,113 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:47,410 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:48,250 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:48,251 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:48,251 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:49,444 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:02:49,506 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:02:49,531 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:02:49,547 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:02:57,742 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:02:57,971 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:02:58,791 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:02:58,792 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:02:58,792 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:02:58,793 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:02:58,793 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:02:59,995 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:00,077 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:00,097 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:03:00,121 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:03:11,340 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-06-06 21:03:11,664 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:03:12,515 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:12,516 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:12,516 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:12,517 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:12,517 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:03:13,680 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:13,734 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:13,750 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 21:03:13,763 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-06 21:03:47,918 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:47,919 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:47,920 - superspider - INFO - 定时任务调度器初始化成功
2025-06-06 21:03:48,461 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-06 21:03:48,931 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-06 21:03:48,935 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-06 21:03:49,151 - superspider - ERROR - 注册CSDN API路由失败: No module named 'backend.utils.email_sender'
2025-06-06 21:03:49,160 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-06 21:03:49,164 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-06 21:03:49,167 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-06 21:03:49,171 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-06 21:03:49,176 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-06 21:03:49,182 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-06 21:03:49,221 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-06 21:03:49,265 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-06 21:03:49,265 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 21:03:49,267 - werkzeug - INFO -  * Restarting with stat
2025-06-06 21:03:50,111 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-06 21:03:50,113 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-06 21:03:50,113 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-06 21:03:50,114 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-06 21:03:50,114 - superspider - INFO - 定时任务调度器初始化成功
