import{ar as a,an as d,ao as u}from"./index-Cb4A4-Xi.js";import{GLTFLoader as m}from"./glTFLoader-D-NF4fEj.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./bone-CgNwSK5F.js";import"./rawTexture-PjZ4PTsN.js";import"./assetContainer-CIn78ZXO.js";import"./objectModelMapping-D3Nr8hfO.js";const o="KHR_materials_dispersion";class l{constructor(s){this.name=o,this.order=174,this._loader=s,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,e,r){return m.LoadExtensionAsync(s,e,this.name,(i,p)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(s,e,r)),t.push(this._loadDispersionPropertiesAsync(i,e,r,p)),Promise.all(t).then(()=>{})})}_loadDispersionPropertiesAsync(s,e,r,i){if(!(r instanceof a))throw new Error(`${s}: Material type not supported`);return!r.subSurface.isRefractionEnabled||!i.dispersion||(r.subSurface.isDispersionEnabled=!0,r.subSurface.dispersion=i.dispersion),Promise.resolve()}}d(o);u(o,!0,n=>new l(n));export{l as KHR_materials_dispersion};
//# sourceMappingURL=KHR_materials_dispersion-D-GNBOzO.js.map
